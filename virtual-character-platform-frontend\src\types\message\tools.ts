import { DeepPartial } from 'utility-types';
import { z } from 'zod';

import { LobeToolRenderType } from '@/types/tool';

export interface ChatPluginPayload {
  apiName: string;
  arguments: string;
  identifier: string;
  type: LobeToolRenderType;
}

export interface ChatToolPayload {
  apiName: string;
  arguments: string;
  id: string;
  identifier: string;
  type: LobeToolRenderType;
}

/**
 * The function that the model called.
 */
export interface ToolFunction {
  /**
   * The arguments to call the function with, as generated by the model in JSON
   * format. Note that the model does not always generate valid JSON, and may
   * hallucinate parameters not defined by your function schema. Validate the
   * arguments in your code before calling your function.
   */
  arguments: string;

  /**
   * The name of the function to call.
   */
  name: string;
}

export interface MessageToolCall {
  /**
   * The function that the model called.
   */
  function: ToolFunction;

  /**
   * The ID of the tool call.
   */
  id: string;

  /**
   * The type of the tool. Currently, only `function` is supported.
   */
  type: 'function' | string;
}

export type MessageToolCallChunk = DeepPartial<MessageToolCall> & { index: number };

export const MessageToolCallSchema = z.object({
  function: z.object({
    arguments: z.string(),
    name: z.string(),
  }),
  id: z.string(),
  type: z.string(),
});

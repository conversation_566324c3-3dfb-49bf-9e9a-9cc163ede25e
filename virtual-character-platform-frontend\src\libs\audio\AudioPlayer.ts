import { LipSync } from '../lipSync/index.ts';

export interface AudioPlayerOptions {
  onEnded?: () => void;
  onError?: (error: Error) => void;
  onVolumeChange?: (volume: number) => void;
  enableLipSync?: boolean;
}

export class AudioPlayer {
  // -------------------- 第 1 处改动：添加静态实例 --------------------
  private static instance: AudioPlayer;

  private audioContext: AudioContext;
  private lipSync?: LipSync;
  private isInitialized: boolean = false;
  private animationFrameId?: number;

  // -------------------- 第 2 处改动：将构造函数设为私有 --------------------
  private constructor() {
    // 延迟初始化AudioContext，避免浏览器自动播放策略问题
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  }

  // -------------------- 第 3 处改动：添加获取实例的静态方法 --------------------
  public static getInstance(): AudioPlayer {
    if (!AudioPlayer.instance) {
      AudioPlayer.instance = new AudioPlayer();
    }
    return AudioPlayer.instance;
  }

  // ==================== 以下所有代码保持原样，无需改动 ====================

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 确保音频上下文处于运行状态
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.lipSync = new LipSync(this.audioContext);
      this.isInitialized = true;
      console.log('AudioPlayer: 初始化完成');
    } catch (error) {
      console.error('AudioPlayer: 初始化失败:', error);
      throw error;
    }
  }

  public async playWithLipSync(
    audioUrl: string, 
    options: AudioPlayerOptions = {}
  ): Promise<void> {
    const { onEnded, onError, onVolumeChange, enableLipSync = true } = options;

    try {
      // 确保已初始化
      await this.initialize();

      if (!this.lipSync) {
        throw new Error('LipSync未初始化');
      }

      console.log('AudioPlayer: 开始播放音频:', audioUrl);

      // 停止之前的播放
      this.stop();

      // 开始播放并启用口型同步
      await this.lipSync.playFromURL(audioUrl, () => {
        console.log('AudioPlayer: 音频播放结束');
        this.stopLipSyncUpdate();
        if (onEnded) {
          onEnded();
        }
      });

      // 如果启用口型同步，开始更新循环
      if (enableLipSync && onVolumeChange) {
        this.startLipSyncUpdate(onVolumeChange);
      }

    } catch (error) {
      console.error('AudioPlayer: 播放失败:', error);
      if (onError) {
        onError(error as Error);
      }
    }
  }

  public async playFromArrayBuffer(
    buffer: ArrayBuffer,
    options: AudioPlayerOptions = {}
  ): Promise<void> {
    const { onEnded, onError, onVolumeChange, enableLipSync = true } = options;

    try {
      // 确保已初始化
      await this.initialize();

      if (!this.lipSync) {
        throw new Error('LipSync未初始化');
      }

      console.log('AudioPlayer: 开始播放音频缓冲区');

      // 停止之前的播放
      this.stop();

      // 开始播放并启用口型同步
      await this.lipSync.playFromArrayBuffer(buffer, () => {
        console.log('AudioPlayer: 音频播放结束');
        this.stopLipSyncUpdate();
        if (onEnded) {
          onEnded();
        }
      });

      // 如果启用口型同步，开始更新循环
      if (enableLipSync && onVolumeChange) {
        this.startLipSyncUpdate(onVolumeChange);
      }

    } catch (error) {
      console.error('AudioPlayer: 播放失败:', error);
      if (onError) {
        onError(error as Error);
      }
    }
  }

  public stop(): void {
    if (this.lipSync) {
      this.lipSync.stopPlay();
    }
    this.stopLipSyncUpdate();
  }

  public isPlaying(): boolean {
    return this.lipSync?.isPlaying() || false;
  }

  public getAudioContextState(): AudioContextState | undefined {
    return this.lipSync?.getAudioContextState();
  }

  public async resumeAudioContext(): Promise<void> {
    if (this.lipSync) {
      await this.lipSync.resumeAudioContext();
    }
  }

  private startLipSyncUpdate(onVolumeChange: (volume: number) => void): void {
    const updateLoop = () => {
      if (this.lipSync && this.lipSync.isPlaying()) {
        const result = this.lipSync.update();
        onVolumeChange(result.volume);
        this.animationFrameId = requestAnimationFrame(updateLoop);
      }
    };
    
    this.animationFrameId = requestAnimationFrame(updateLoop);
  }

  private stopLipSyncUpdate(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = undefined;
    }
  }

  public dispose(): void {
    this.stop();
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
    this.isInitialized = false;
  }
}
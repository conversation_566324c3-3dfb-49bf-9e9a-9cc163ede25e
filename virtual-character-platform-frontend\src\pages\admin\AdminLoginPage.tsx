import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Layout } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import adminAPI from '../../services/adminAPI';
import useAdminAuthStore from '../../store/adminAuthStore';

const { Title } = Typography;
const { Content } = Layout;

const AdminLoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { loginAdmin } = useAdminAuthStore();

  const onFinish = async (values: { username: string; password: string }) => {
    try {
      setLoading(true);
      const response = await adminAPI.login(values);

      if (response.data?.token && response.data?.user) {
        loginAdmin(response.data.token, response.data.user);
        message.success('登录成功，欢迎回来！');
        navigate('/admin/dashboard');
      } else {
        message.error('登录失败，请检查用户名和密码');
      }
    } catch (error) {
      message.error('登录失败，请稍后再试');
      console.error('登录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Card 
          style={{ 
            width: 400, 
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            borderRadius: '8px' 
          }}
        >
          <div style={{ textAlign: 'center', marginBottom: 32 }}>
            <Title level={2} style={{ marginBottom: 8 }}>管理员登录</Title>
            <Typography.Text type="secondary">
              虚拟角色平台后台管理系统
            </Typography.Text>
          </div>
          
          <Form
            name="admin_login"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            size="large"
            layout="vertical"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入管理员用户名' }]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="管理员用户名" 
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password 
                prefix={<LockOutlined />} 
                placeholder="密码" 
              />
            </Form.Item>

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                block
              >
                登录
              </Button>
            </Form.Item>
          </Form>
          
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Typography.Text type="secondary">
              © 2023 虚拟角色平台 版权所有
            </Typography.Text>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default AdminLoginPage; 
import { NeutralColors, PrimaryColors } from '@lobehub/ui';

import { GenderEnum, SystemAgentConfig } from './agent';
import { LocaleMode } from './locale';
import { TouchActionConfig } from './touch';

import { UserKeyVaults } from './provider/keyVaults';
import { UserModelProviderConfig } from './provider/modelProvider';

export type BackgroundEffect = 'glow' | 'none';

export interface TTSConfig {
  /**
   * 是否客户端调用
   */
  clientCall: boolean;
  /**
   * TTS提供商
   */
  provider?: string;
  /**
   * 语音选择
   */
  voice?: string;
  /**
   * 语音速度
   */
  speed?: number;
  /**
   * 语音音调
   */
  pitch?: number;
}

export interface NotificationConfig {
  /**
   * 邮件通知
   */
  email: boolean;
  /**
   * 推送通知
   */
  push: boolean;
  /**
   * 聊天通知
   */
  chat: boolean;
  /**
   * 系统通知
   */
  system: boolean;
}

export interface PrivacyConfig {
  /**
   * 个人资料公开性
   */
  profilePublic: boolean;
  /**
   * 角色公开性
   */
  charactersPublic: boolean;
  /**
   * 聊天记录保存
   */
  chatHistory: boolean;
}

export interface AccessibilityConfig {
  /**
   * 字体大小
   */
  fontSize: 'small' | 'medium' | 'large';
  /**
   * 高对比度模式
   */
  highContrast: boolean;
  /**
   * 减少动画效果
   */
  reduceMotion: boolean;
}

export interface TouchConfig {
  /**
   * 女性触摸配置
   */
  Female: TouchActionConfig;
  /**
   * 男性触摸配置
   */
  Male: TouchActionConfig;
}

export interface Config extends CommonConfig {
  /**
   * 密钥配置
   */
  keyVaults: UserKeyVaults;
  /**
   * 语言模型配置
   */
  languageModel: UserModelProviderConfig;
  /**
   * 系统代理配置
   */
  systemAgent: SystemAgentConfig;
  /**
   * 全局触摸配置
   */
  touch: TouchConfig;
  /**
   * 语音设置
   */
  tts: TTSConfig;
  /**
   * 通知设置
   */
  notifications?: NotificationConfig;
  /**
   * 隐私设置
   */
  privacy?: PrivacyConfig;
  /**
   * 无障碍设置
   */
  accessibility?: AccessibilityConfig;
  /**
   * 兼容性设置 - 用于存储旧版本设置
   */
  legacy?: Record<string, any>;
}

export interface CommonConfig {
  /**
   * 用户头像
   */
  avatar?: string;
  /**
   * 背景类型
   */
  backgroundEffect?: BackgroundEffect;
  /**
   * 语言地区
   */
  locale: LocaleMode;
  /**
   * 中性色
   */
  neutralColor?: NeutralColors;
  /**
   * 用户昵称
   */
  nickName?: string;
  /**
   * 主题色
   */
  primaryColor?: PrimaryColors;
}

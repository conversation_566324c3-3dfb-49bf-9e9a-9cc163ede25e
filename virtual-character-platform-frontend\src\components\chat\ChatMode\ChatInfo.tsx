import React from 'react';
import { Drawer, Avatar, Typography, Divider, Tag, Space } from 'antd';
import { sessionSelectors, useSessionStore } from '../../../store/session';

const { Title, Text, Paragraph } = Typography;

interface ChatInfoProps {
  onClose: () => void;
}

const ChatInfo: React.FC<ChatInfoProps> = ({ onClose }) => {
  const currentAgent = useSessionStore((s) => sessionSelectors.currentAgent(s));

  if (!currentAgent) return null;

  return (
    <Drawer
      title="角色信息"
      placement="right"
      onClose={onClose}
      open={true}
      width={320}
    >
      <div className="chat-info">
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Avatar 
            src={currentAgent.meta?.avatar} 
            size={80}
            style={{ marginBottom: 12 }}
          >
            {currentAgent.meta?.name?.[0]}
          </Avatar>
          <Title level={4} style={{ margin: 0 }}>
            {currentAgent.meta?.name}
          </Title>
        </div>

        <Divider />

        <div className="info-section">
          <Text strong>描述</Text>
          <Paragraph style={{ marginTop: 8 }}>
            {currentAgent.meta?.description || '暂无描述'}
          </Paragraph>
        </div>

        <div className="info-section">
          <Text strong>系统角色</Text>
          <Paragraph style={{ marginTop: 8 }}>
            {currentAgent.systemRole || '暂无设定'}
          </Paragraph>
        </div>

        {currentAgent.meta?.tags && currentAgent.meta.tags.length > 0 && (
          <div className="info-section">
            <Text strong>标签</Text>
            <div style={{ marginTop: 8 }}>
              <Space wrap>
                {currentAgent.meta.tags.map((tag, index) => (
                  <Tag key={index}>{tag}</Tag>
                ))}
              </Space>
            </div>
          </div>
        )}

        <Divider />

        <div className="info-section">
          <Text strong>模型配置</Text>
          <div style={{ marginTop: 8 }}>
            <Text type="secondary">
              提供商: {currentAgent.provider || 'openai'}
            </Text>
            <br />
            <Text type="secondary">
              模型: {currentAgent.model || 'gpt-3.5-turbo'}
            </Text>
          </div>
        </div>

        {currentAgent.tts && (
          <div className="info-section">
            <Text strong>语音设置</Text>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                语音: {currentAgent.tts.voice}
              </Text>
              <br />
              <Text type="secondary">
                语速: {currentAgent.tts.speed}
              </Text>
            </div>
          </div>
        )}
      </div>
    </Drawer>
  );
};

export default ChatInfo;

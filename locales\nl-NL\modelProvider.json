{"azure": {"azureApiVersion": {"desc": "De API-versie van <PERSON>, volg het formaat YYYY-MM-DD, raad<PERSON><PERSON> de [laatste versie](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> ophale<PERSON>", "title": "Azure API Versie"}, "empty": "Voer model ID in om het eerste model toe te voegen", "endpoint": {"desc": "Dit waarde kan worden gevonden in het gedeelte 'Sleutels en eindpunten' wanneer u de middelen in het Azure-portaal controleert", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API Adres"}, "modelListPlaceholder": "Selecteer of voeg uw gedeployde OpenAI-modellen toe", "title": "Azure OpenAI", "token": {"desc": "Dit waarde kan worden gevonden in het gedeelte 'Sleutels en eindpunten' wanneer u de middelen in het Azure-portaal controleert. U kunt KEY1 of KEY2 gebruiken", "placeholder": "Azure API Sleutel", "title": "API Sleutel"}}, "bedrock": {"accessKeyId": {"desc": "Vul uw AWS Access Key Id in", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "Test of AccessKeyId / SecretAccessKey correct is ingevuld"}, "region": {"desc": "Vul AWS Regio in", "placeholder": "AWS Regio", "title": "AWS Regio"}, "secretAccessKey": {"desc": "Vul uw AWS Secret Access Key in", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "Als u AWS SSO/STS gebruikt, voer dan uw AWS Session Token in", "placeholder": "AWS Session Token", "title": "AWS Session Token (optioneel)"}, "title": "Bedrock", "unlock": {"customRegion": "Aangepaste service regio", "customSessionToken": "Aangepaste Session Token", "description": "Voer uw AWS AccessKeyId / SecretAccessKey in om de sessie te starten. De applicatie registreert uw authenticatieconfiguratie niet", "title": "Gebruik aangepaste Bedrock authenticatie-informatie"}}, "github": {"personalAccessToken": {"desc": "Vul uw Github PAT in, klik [hier](https://github.com/settings/tokens) om er een te maken", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "Vul uw Hugging<PERSON><PERSON>ken in, klik [hier](https://huggingface.co/settings/tokens) om er een te maken", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "ollama": {"checker": {"desc": "Test of het proxyadres correct is ingevuld", "title": "Connectiviteitstest"}, "customModelName": {"desc": "<PERSON>oeg aangepaste modellen toe, scheid meerdere modellen met een komma (,) ", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Aangepaste modelnaam"}, "download": {"desc": "<PERSON><PERSON><PERSON> is bezig met het downloaden van dit model, sluit deze pagina alstublieft niet. Bij opnieuw downloaden zal het onderbroken worden hervat", "remainingTime": "Overge<PERSON><PERSON> tijd", "speed": "Downloadsnelheid", "title": "Model {{model}} wordt gedownload"}, "endpoint": {"desc": "Vul het Ollama interface proxyadres in, laat leeg als lokaal niet specifiek opgegeven", "title": "Ollama service adres"}, "setup": {"cors": {"description": "Vanwege browserbeveiligingsbeperkingen moet u CORS-configuratie voor Ollama instellen om normaal te kunnen gebruiken.", "linux": {"env": "Voeg `Environment` toe onder de [Service] sectie, voeg de OLLAMA_ORIGINS omgevingsvariabele toe:", "reboot": "Herlaad systemd en herstart Ollama", "systemd": "Roep systemd aan om de ollama service te bewerken:"}, "macos": "Open de 'Terminal' applicatie, plak de volgende opdracht en druk op enter om uit te voeren", "reboot": "Herstart de Ollama service na voltooiing", "title": "<PERSON><PERSON><PERSON><PERSON> voor cross-origin toegang", "windows": "Klik op 'Configuratiescherm' op Windows, ga naar het bewerken van systeemomgevingsvariabelen. Maak een nieuwe omgevingsvariabele met de naam 'OLLAMA_ORIGINS' voor uw gebruikersaccount, met de wa<PERSON><PERSON> *, klik op 'OK/Toepassen' om op te slaan"}, "install": {"description": "Zorg ervoor dat u Ollama hebt ingeschakeld, als u Ollama nog niet hebt gedownload, ga dan naar de officiële website <1>om te downloaden</1>", "docker": "Als u de voorkeur geeft a<PERSON>, biedt <PERSON> ook een officiële Docker-image, u kunt deze ophalen met de volgende opdracht:", "linux": {"command": "<PERSON><PERSON><PERSON><PERSON> met de volgende opdracht:", "manual": "Of u kunt de <1>Linux handmatige installatiehandleiding</1> raadplegen voor zelfinstallatie"}, "title": "Installeer en start de Ollama applicatie lokaal", "windowsTab": "Windows (preview)"}}, "title": "Ollama", "unlock": {"cancel": "Download annuleren", "confirm": "Downloaden", "description": "<PERSON><PERSON><PERSON> uw <PERSON> modellabel in om door te gaan met de sessie", "downloaded": "{{completed}} / {{total}}", "starting": "Downloaden starten...", "title": "Download het opgegeven Ollama model"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "Vul de SenseNova Access Key ID in", "placeholder": "SenseNova Access Key ID", "title": "Access Key ID"}, "sensenovaAccessKeySecret": {"desc": "<PERSON><PERSON> de SenseNova Access Key Secret in", "placeholder": "SenseNova Access Key Secret", "title": "Access Key Secret"}, "unlock": {"description": "Voer uw Access Key ID / Access Key Secret in om de sessie te starten. De applicatie registreert uw authenticatieconfiguratie niet", "title": "Gebruik aangepaste SenseNova authenticatie-informatie"}}, "wenxin": {"accessKey": {"desc": "Vul de Access Key van het Baidu Qi<PERSON>fan-platform in", "placeholder": "Qianfan Access Key", "title": "Access Key"}, "checker": {"desc": "Test of AccessKey / SecretAccess correct is ingevuld"}, "secretKey": {"desc": "Vul de Secret Key van het <PERSON>-platform in", "placeholder": "<PERSON><PERSON>fan Secret Key", "title": "Secret Key"}, "unlock": {"customRegion": "Aangepaste service regio", "description": "<PERSON><PERSON>r uw AccessKey / Secret<PERSON>ey in om de sessie te starten. De applicatie registreert uw authenticatieconfiguratie niet", "title": "Gebruik aangepaste <PERSON> authenticatie-informatie"}}, "zeroone": {"title": "01.AI Zero One"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}
# 角色扩展快速检查清单

## 📋 添加新角色身份检查清单

### ✅ 后端代码修改

- [ ] **core/models.py**
  - [ ] 在 `VALID_IDENTITIES` 列表中添加新身份

- [ ] **core/services/background_generation_service.py**
  - [ ] 在 `IDENTITY_SCENE_PROBABILITIES` 中配置场景概率
  - [ ] 确保概率总和为 1.0

- [ ] **core/prompt_engineering/identity_prompts.py**
  - [ ] 在 `IDENTITY_TO_ENGLISH_PROMPT` 中添加英文提示词
  - [ ] 在 `IDENTITY_TO_CHINESE_PROMPT` 中添加中文提示词
  - [ ] 在 `IDENTITY_TO_DIALOGUE_PROMPT` 中添加对话提示词

### ✅ 前端代码修改

- [ ] **virtual-character-platform-frontend/src/components/character/IdentitySelector.tsx**
  - [ ] 在 `identityDescriptions` 中添加身份描述
  - [ ] 在 `identityIcons` 中添加身份图标

- [ ] **类型定义文件（如果存在）**
  - [ ] 更新 TypeScript 类型定义

### ✅ 数据库操作

- [ ] **创建迁移**
  ```bash
  python manage.py makemigrations core --name add_new_identity
  ```

- [ ] **应用迁移**
  ```bash
  python manage.py migrate
  ```

### ✅ 测试验证

- [ ] **单元测试**
  - [ ] 添加新身份的场景生成测试
  - [ ] 验证提示词生成功能

- [ ] **集成测试**
  - [ ] 创建新身份角色并验证背景生成
  - [ ] 测试API接口响应

- [ ] **手动测试**
  - [ ] 前端创建新身份角色
  - [ ] 验证背景图片生成
  - [ ] 检查API响应格式

---

## 📋 添加新场景类型检查清单

### ✅ 后端代码修改

- [ ] **core/models.py**
  - [ ] 在 `SCENE_TYPES` 字典中添加新场景类型

- [ ] **core/services/background_generation_service.py**
  - [ ] 在 `SCENE_PROMPT_TEMPLATES` 中添加场景提示词模板
  - [ ] 包含中文和英文提示词

- [ ] **更新相关身份配置**
  - [ ] 在 `IDENTITY_SCENE_PROBABILITIES` 中为相关身份添加新场景

### ✅ 前端代码修改

- [ ] **类型定义文件（如果存在）**
  - [ ] 更新场景类型的 TypeScript 定义

### ✅ 数据库操作

- [ ] **创建迁移**
  ```bash
  python manage.py makemigrations core --name add_new_scene_type
  ```

- [ ] **应用迁移**
  ```bash
  python manage.py migrate
  ```

### ✅ 测试验证

- [ ] **单元测试**
  - [ ] 测试新场景的提示词生成
  - [ ] 验证场景选择逻辑

- [ ] **集成测试**
  - [ ] 验证新场景在角色背景生成中的使用

---

## 🔧 配置参考模板

### 身份配置模板

```python
# core/models.py
VALID_IDENTITIES = [
    # 现有身份...
    "新身份名称"
]

# core/services/background_generation_service.py
IDENTITY_SCENE_PROBABILITIES = {
    "新身份名称": {
        'scene_type_1': 0.40,  # 主要场景 40%
        'scene_type_2': 0.30,  # 次要场景 30%
        'scene_type_3': 0.20,  # 辅助场景 20%
        'scene_type_4': 0.10   # 补充场景 10%
        # 确保总和为 1.0
    }
}

# core/prompt_engineering/identity_prompts.py
IDENTITY_TO_ENGLISH_PROMPT = {
    "新身份名称": "english keywords, separated by commas, describing appearance and environment"
}

IDENTITY_TO_CHINESE_PROMPT = {
    "新身份名称": "中文关键词，用顿号分隔，描述外观和环境"
}

IDENTITY_TO_DIALOGUE_PROMPT = {
    "新身份名称": "详细的对话系统提示词，描述角色的说话方式、知识背景和可讨论的话题。"
}
```

### 场景配置模板

```python
# core/models.py
SCENE_TYPES = {
    'new_scene_type': '新场景名称'
}

# core/services/background_generation_service.py
SCENE_PROMPT_TEMPLATES = {
    'new_scene_type': {
        'chinese': '中文场景描述，关键词，环境特征',
        'english': 'english scene description, keywords, environment features'
    }
}
```

### 前端配置模板

```typescript
// IdentitySelector.tsx
const identityDescriptions: Record<string, string> = {
    "新身份名称": "简短描述，包含外观特征和典型背景环境"
};

const identityIcons: Record<string, string> = {
    "新身份名称": "🎭"  // 选择合适的emoji图标
};
```

---

## ⚠️ 注意事项

### 概率配置规则
- ✅ 所有概率值总和必须等于 1.0
- ✅ 选择与身份最相关的场景类型
- ✅ 保持场景多样性，避免过于单一
- ✅ 考虑场景的逻辑合理性

### 提示词编写规范
- ✅ 英文提示词使用逗号分隔关键词
- ✅ 中文提示词使用顿号分隔
- ✅ 专注于场景环境，避免人物描述
- ✅ 控制总长度在1000字符以内
- ✅ 使用积极正面的描述词汇

### 兼容性要求
- ✅ 不修改现有身份和场景的配置
- ✅ 确保现有功能不受影响
- ✅ 新增内容要有完整的提示词模板
- ✅ 遵循现有的命名规范

### 测试要求
- ✅ 每个新身份至少3个测试用例
- ✅ 验证场景生成的随机性和合理性
- ✅ 测试API接口的完整性
- ✅ 确保前端界面正常显示

---

## 🚀 快速添加流程

1. **准备阶段**
   - 确定新身份名称和相关场景
   - 设计场景概率分布
   - 准备提示词内容

2. **代码修改**
   - 按照检查清单逐项修改代码
   - 确保所有相关文件都已更新

3. **数据库更新**
   - 创建并应用数据库迁移
   - 验证数据完整性

4. **测试验证**
   - 运行单元测试和集成测试
   - 进行手动功能测试
   - 验证前端界面显示

5. **文档更新**
   - 更新相关技术文档
   - 记录新增的身份和场景

通过遵循这个检查清单，可以确保新角色身份和场景类型的添加过程完整、规范且不会影响现有功能。

# 角色背景图片生成功能设计文档

## 功能概述

在角色创建时，除了生成角色头像外，系统将自动生成3-5张与角色身份相关的背景场景图片。这些背景图片将静默生成并存储，供其他页面（如聊天页面、角色详情页）调用使用。

## 数据模型设计

### CharacterBackground 模型

```python
class CharacterBackground(models.Model):
    """角色背景图片模型"""
    character = models.ForeignKey(
        Character, 
        on_delete=models.CASCADE, 
        related_name='backgrounds',
        verbose_name='关联角色'
    )
    scene_type = models.CharField(
        max_length=100, 
        verbose_name='场景类型'
    )
    image_url = models.CharField(
        max_length=500, 
        verbose_name='背景图片URL'
    )
    generation_prompt = models.TextField(
        blank=True, 
        null=True,
        verbose_name='生成提示词'
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name='生成时间'
    )
    generation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待生成'),
            ('generating', '生成中'),
            ('completed', '已完成'),
            ('failed', '生成失败')
        ],
        default='pending',
        verbose_name='生成状态'
    )
    
    class Meta:
        verbose_name = '角色背景图片'
        verbose_name_plural = '角色背景图片'
        ordering = ['-created_at']
```

## 场景类型定义和概率配置

### 身份-场景映射配置

```python
# 场景类型定义
SCENE_TYPES = {
    # 学生相关场景
    'classroom': '教室',
    'library': '图书馆',
    'gymnasium': '体育馆',
    'campus': '校园',
    'dormitory': '宿舍',
    'laboratory': '实验室',
    
    # 工作场所场景
    'office': '办公室',
    'meeting_room': '会议室',
    'hospital_room': '病房',
    'clinic': '诊室',
    'operating_room': '手术室',
    'hospital_corridor': '医院走廊',
    'coffee_shop': '咖啡店',
    'kitchen': '厨房',
    
    # 娱乐表演场景
    'stage': '舞台',
    'recording_studio': '录音室',
    'concert_hall': '音乐厅',
    'backstage': '后台',
    
    # 奇幻魔法场景
    'magic_tower': '魔法塔',
    'enchanted_forest': '魔法森林',
    'alchemy_lab': '炼金实验室',
    'magic_library': '魔法图书馆',
    
    # 未来科技场景
    'cyberpunk_street': '赛博朋克街道',
    'neon_city': '霓虹都市',
    'tech_lab': '科技实验室',
    'virtual_space': '虚拟空间',
    
    # 皇室贵族场景
    'throne_room': '王座厅',
    'royal_garden': '皇家花园',
    'castle_hall': '城堡大厅',
    'ballroom': '舞厅',
    
    # 服务场所场景
    'mansion_interior': '豪宅内部',
    'elegant_room': '优雅房间',
    'tea_room': '茶室',
    
    # 游戏场景
    'fantasy_tavern': '奇幻酒馆',
    'dungeon': '地下城',
    'game_world': '游戏世界',
    
    # 咨询场景
    'counseling_room': '咨询室',
    'therapy_office': '治疗办公室',
    'peaceful_space': '宁静空间'
}

# 身份对应的场景概率配置
IDENTITY_SCENE_PROBABILITIES = {
    "高中生": {
        'classroom': 0.30,
        'library': 0.25,
        'gymnasium': 0.20,
        'campus': 0.15,
        'dormitory': 0.10
    },
    "大学生": {
        'library': 0.30,
        'classroom': 0.25,
        'laboratory': 0.20,
        'campus': 0.15,
        'dormitory': 0.10
    },
    "偶像": {
        'stage': 0.35,
        'recording_studio': 0.25,
        'concert_hall': 0.20,
        'backstage': 0.20
    },
    "虚拟歌姬": {
        'virtual_space': 0.30,
        'recording_studio': 0.25,
        'stage': 0.25,
        'tech_lab': 0.20
    },
    "咖啡店店员": {
        'coffee_shop': 0.50,
        'kitchen': 0.30,
        'office': 0.20
    },
    "魔法使": {
        'magic_tower': 0.30,
        'enchanted_forest': 0.25,
        'alchemy_lab': 0.25,
        'magic_library': 0.20
    },
    "女仆": {
        'mansion_interior': 0.40,
        'elegant_room': 0.30,
        'tea_room': 0.30
    },
    "赛博朋克侦探": {
        'cyberpunk_street': 0.35,
        'neon_city': 0.30,
        'tech_lab': 0.20,
        'office': 0.15
    },
    "异世界公主": {
        'throne_room': 0.30,
        'royal_garden': 0.25,
        'castle_hall': 0.25,
        'ballroom': 0.20
    },
    "游戏NPC": {
        'fantasy_tavern': 0.30,
        'game_world': 0.30,
        'dungeon': 0.25,
        'castle_hall': 0.15
    },
    "虚拟心理咨询师": {
        'counseling_room': 0.40,
        'therapy_office': 0.30,
        'peaceful_space': 0.30
    }
}
```

## 场景提示词模板

### 场景描述模板

```python
SCENE_PROMPT_TEMPLATES = {
    'classroom': {
        'chinese': '教室内部，黑板，课桌椅，窗户，阳光，学习氛围',
        'english': 'classroom interior, blackboard, desks and chairs, windows, sunlight, academic atmosphere'
    },
    'library': {
        'chinese': '图书馆，书架，阅读桌，安静氛围，书籍，学习环境',
        'english': 'library interior, bookshelves, reading tables, quiet atmosphere, books, study environment'
    },
    'stage': {
        'chinese': '舞台，聚光灯，音响设备，观众席，表演氛围',
        'english': 'performance stage, spotlights, sound equipment, audience seats, performance atmosphere'
    },
    'magic_tower': {
        'chinese': '魔法塔内部，魔法阵，水晶球，古老书籍，神秘光芒',
        'english': 'magic tower interior, magic circles, crystal balls, ancient books, mystical glow'
    },
    'cyberpunk_street': {
        'chinese': '赛博朋克街道，霓虹灯，未来建筑，科技感，夜景',
        'english': 'cyberpunk street, neon lights, futuristic buildings, high-tech atmosphere, night scene'
    },
    'coffee_shop': {
        'chinese': '咖啡店内部，咖啡机，木质桌椅，温馨氛围，咖啡香气',
        'english': 'coffee shop interior, coffee machine, wooden tables and chairs, cozy atmosphere, coffee aroma'
    },
    'hospital_room': {
        'chinese': '医院病房，病床，医疗设备，干净整洁，专业环境',
        'english': 'hospital room, medical bed, medical equipment, clean and tidy, professional environment'
    },
    'throne_room': {
        'chinese': '王座厅，华丽王座，红毯，柱子，庄严氛围',
        'english': 'throne room, magnificent throne, red carpet, pillars, majestic atmosphere'
    },
    'mansion_interior': {
        'chinese': '豪宅内部，精美家具，水晶吊灯，大理石地板，奢华装饰',
        'english': 'mansion interior, exquisite furniture, crystal chandelier, marble floor, luxurious decoration'
    },
    'counseling_room': {
        'chinese': '心理咨询室，舒适沙发，温和灯光，植物，宁静氛围',
        'english': 'counseling room, comfortable sofa, soft lighting, plants, peaceful atmosphere'
    }
}
```

## 生成流程设计

### 1. 触发时机
- 在角色保存成功后，异步触发背景图片生成
- 不阻塞用户的角色创建流程

### 2. 生成数量
- 每个角色生成3-5张背景图片
- 根据身份的场景概率配置随机选择场景类型

### 3. 生成状态管理
- pending: 待生成
- generating: 生成中
- completed: 已完成
- failed: 生成失败

### 4. 错误处理
- 单张图片生成失败不影响其他图片
- 支持重试机制
- 记录失败原因用于调试

## API接口设计

### 获取角色背景图片
```
GET /api/characters/{character_id}/backgrounds/
```

响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "backgrounds": [
      {
        "id": 1,
        "scene_type": "classroom",
        "scene_name": "教室",
        "image_url": "https://...",
        "created_at": "2024-01-01T10:00:00Z",
        "generation_status": "completed"
      }
    ],
    "total": 5,
    "character_id": 123
  }
}
```

## 扩展性考虑

### 未来功能扩展
1. 用户自定义背景图片
2. 背景图片评分系统
3. 背景图片分享功能
4. 季节性/主题性背景变化
5. 背景图片与对话场景联动

### 性能优化
1. 背景图片生成队列化处理
2. 图片CDN缓存
3. 批量生成优化
4. 生成状态实时推送

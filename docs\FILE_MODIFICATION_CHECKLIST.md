# 📝 Lobe Vidol 集成 - 文件修改清单

> 详细列出每个需要修改的文件，包括具体的修改内容和代码示例

## 🎯 修改优先级说明

- 🔥🔥🔥 **必须修改** - 核心功能，必须完成
- 🔥🔥 **重要修改** - 主要功能，建议完成  
- 🔥 **可选修改** - 增强功能，时间允许时完成

## 📋 第一阶段：核心架构升级

### 1. 依赖配置文件 🔥🔥🔥

#### `virtual-character-platform-frontend/package.json`
**修改内容：** 添加Lobe Vidol核心依赖
```json
{
  "dependencies": {
    // 现有依赖保持不变...
    
    // 新增Lobe Vidol依赖
    "@lobehub/ui": "^1.153.11",
    "@react-spring/web": "^9.7.5", 
    "@xenova/transformers": "^2.17.2",
    "react-layout-kit": "^1.9.0",
    "react-speech-recognition": "^3.10.0",
    "mmd-parser": "^1.0.4",
    "i18next": "^23.7.6",
    "react-i18next": "^13.5.0",
    "react-use": "^17.4.0",
    "ahooks": "^3.7.8"
  }
}
```

#### `virtual-character-platform-frontend/tsconfig.json`
**修改内容：** 添加路径映射
```json
{
  "compilerOptions": {
    // 现有配置保持不变...
    
    // 新增路径映射
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/libs/*": ["./src/libs/*"],
      "@/store/*": ["./src/store/*"],
      "@/services/*": ["./src/services/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"],
      "@/constants/*": ["./src/constants/*"]
    }
  }
}
```

#### `virtual-character-platform-frontend/vite.config.ts`
**修改内容：** 配置路径别名和优化
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/libs': path.resolve(__dirname, './src/libs'),
      '@/store': path.resolve(__dirname, './src/store'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/constants': path.resolve(__dirname, './src/constants')
    }
  },
  optimizeDeps: {
    include: ['three', '@pixiv/three-vrm']
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true
      }
    }
  }
})
```

### 2. 状态管理系统 🔥🔥🔥

#### 新建：`src/store/agent/index.ts`
**功能：** 角色状态管理
```typescript
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Agent } from '@/types/agent';

interface AgentStore {
  agents: Agent[];
  currentAgent: Agent | null;
  loading: boolean;
  
  // Actions
  setAgents: (agents: Agent[]) => void;
  setCurrentAgent: (agent: Agent | null) => void;
  addAgent: (agent: Agent) => void;
  updateAgent: (id: string, updates: Partial<Agent>) => void;
  removeAgent: (id: string) => void;
  
  // Selectors
  getAgentById: (id: string) => Agent | undefined;
}

export const useAgentStore = create<AgentStore>()(
  subscribeWithSelector((set, get) => ({
    agents: [],
    currentAgent: null,
    loading: false,
    
    setAgents: (agents) => set({ agents }),
    setCurrentAgent: (agent) => set({ currentAgent: agent }),
    addAgent: (agent) => set((state) => ({ 
      agents: [...state.agents, agent] 
    })),
    updateAgent: (id, updates) => set((state) => ({
      agents: state.agents.map(agent => 
        agent.id === id ? { ...agent, ...updates } : agent
      )
    })),
    removeAgent: (id) => set((state) => ({
      agents: state.agents.filter(agent => agent.id !== id)
    })),
    
    getAgentById: (id) => get().agents.find(agent => agent.id === id)
  }))
);
```

#### 新建：`src/store/session/index.ts`
**功能：** 会话状态管理
```typescript
import { create } from 'zustand';
import { ChatMessage } from '@/types/chat';

interface SessionStore {
  messages: ChatMessage[];
  currentSessionId: string | null;
  isLoading: boolean;
  isPlaying: boolean;
  
  // Actions
  addMessage: (message: ChatMessage) => void;
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
  clearMessages: () => void;
  setCurrentSession: (sessionId: string) => void;
  setIsLoading: (loading: boolean) => void;
  setIsPlaying: (playing: boolean) => void;
}

export const useSessionStore = create<SessionStore>((set) => ({
  messages: [],
  currentSessionId: null,
  isLoading: false,
  isPlaying: false,
  
  addMessage: (message) => set((state) => ({
    messages: [...state.messages, message]
  })),
  updateMessage: (id, updates) => set((state) => ({
    messages: state.messages.map(msg => 
      msg.id === id ? { ...msg, ...updates } : msg
    )
  })),
  clearMessages: () => set({ messages: [] }),
  setCurrentSession: (sessionId) => set({ currentSessionId: sessionId }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setIsPlaying: (playing) => set({ isPlaying: playing })
}));
```

#### 新建：`src/store/global/index.ts`
**功能：** 全局状态管理
```typescript
import { create } from 'zustand';
import { Viewer } from '@/libs/vrmViewer';

export type ChatMode = 'chat' | 'camera';
export type ThemeMode = 'light' | 'dark' | 'auto';

interface GlobalStore {
  // 主题和UI状态
  themeMode: ThemeMode;
  chatMode: ChatMode;
  
  // 3D渲染相关
  viewer: Viewer;
  backgroundUrl?: string;
  
  // 语音相关
  voiceOn: boolean;
  isPlaying: boolean;
  
  // UI显示状态
  showChatSidebar: boolean;
  showAgentInfo: boolean;
  showSessionList: boolean;
  showChatDialog: boolean;
  
  // Actions
  setThemeMode: (mode: ThemeMode) => void;
  setChatMode: (mode: ChatMode) => void;
  setBackgroundUrl: (url?: string) => void;
  setVoiceOn: (on: boolean) => void;
  setIsPlaying: (playing: boolean) => void;
  
  // UI Actions
  toggleChatSidebar: () => void;
  toggleAgentInfo: () => void;
  toggleSessionList: () => void;
  toggleChatDialog: () => void;
}

const viewer = new Viewer();

export const useGlobalStore = create<GlobalStore>((set) => ({
  themeMode: 'auto',
  chatMode: 'chat',
  viewer,
  voiceOn: false,
  isPlaying: false,
  showChatSidebar: false,
  showAgentInfo: true,
  showSessionList: true,
  showChatDialog: true,
  
  setThemeMode: (mode) => set({ themeMode: mode }),
  setChatMode: (mode) => set({ chatMode: mode }),
  setBackgroundUrl: (url) => set({ backgroundUrl: url }),
  setVoiceOn: (on) => set({ voiceOn: on }),
  setIsPlaying: (playing) => set({ isPlaying: playing }),
  
  toggleChatSidebar: () => set((state) => ({ 
    showChatSidebar: !state.showChatSidebar 
  })),
  toggleAgentInfo: () => set((state) => ({ 
    showAgentInfo: !state.showAgentInfo 
  })),
  toggleSessionList: () => set((state) => ({ 
    showSessionList: !state.showSessionList 
  })),
  toggleChatDialog: () => set((state) => ({ 
    showChatDialog: !state.showChatDialog 
  }))
}));
```

### 3. 类型定义文件 🔥🔥🔥

#### 新建：`src/types/agent.ts`
**功能：** 角色相关类型定义
```typescript
export interface Agent {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  model?: string;
  greeting?: string;
  
  // VRM相关
  vrmModelUrl?: string;
  animationStyle?: string;
  
  // 语音相关
  tts?: TTSConfig;
  
  // 性格设定
  personality?: {
    traits: string[];
    background: string;
    speaking_style: string;
  };
  
  // 触摸交互
  touchActions?: TouchActionConfig;
  
  // 元数据
  meta?: {
    model?: string;
    gender?: 'male' | 'female';
    tags?: string[];
  };
}

export interface TTSConfig {
  engine: 'edge' | 'openai' | 'xunfei';
  voice: string;
  speed: number;
  pitch: number;
  style?: string;
}

export interface TouchActionConfig {
  [key: string]: TouchAction[];
}

export interface TouchAction {
  text: string;
  expression: string;
  motion?: string;
}
```

#### 新建：`src/types/chat.ts`
**功能：** 聊天相关类型定义
```typescript
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  
  // 语音相关
  audioUrl?: string;
  hasAudio?: boolean;
  
  // 表情和动作
  emotion?: string;
  motion?: string;
  
  // 元数据
  meta?: {
    model?: string;
    tokens?: number;
    duration?: number;
  };
}

export interface Conversation {
  id: string;
  agentId: string;
  title: string;
  messages: ChatMessage[];
  createdAt: number;
  updatedAt: number;
}
```

### 4. 核心库文件升级 🔥🔥🔥

#### 新建：`src/libs/vrmViewer/index.ts`
**功能：** VRM渲染引擎主入口
```typescript
export { Viewer } from './viewer';
export { Model } from './model';
export * from './types';
```

#### 新建：`src/libs/vrmViewer/viewer.ts`
**功能：** 3D场景管理器
```typescript
import * as THREE from 'three';
import { Model } from './model';
import { TouchAreaEnum } from '@/types/touch';

export class Viewer {
  public model?: Model;

  private _scene: THREE.Scene;
  private _camera: THREE.PerspectiveCamera;
  private _renderer: THREE.WebGLRenderer;
  private _clock: THREE.Clock;

  constructor() {
    this._scene = new THREE.Scene();
    this._camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    this._renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this._clock = new THREE.Clock();

    this.setupLights();
    this.setupCamera();
  }

  public setup(canvas: HTMLCanvasElement, onTouch?: (area: TouchAreaEnum) => void) {
    this._renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: true,
      alpha: true
    });
    this._renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    this._renderer.setPixelRatio(window.devicePixelRatio);
    this._renderer.outputColorSpace = THREE.SRGBColorSpace;

    // 设置触摸事件
    if (onTouch) {
      this.setupTouchEvents(canvas, onTouch);
    }

    this.startRenderLoop();
  }

  public async loadVrm(url: string): Promise<void> {
    if (this.model) {
      this.model.unLoadVrm();
    }

    this.model = new Model(this._camera);
    await this.model.loadVRM(url);

    if (this.model.vrm) {
      this._scene.add(this.model.vrm.scene);
    }
  }

  public resetToIdle(): void {
    this.model?.loadIdleAnimation();
  }

  private setupLights(): void {
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    this._scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    this._scene.add(directionalLight);
  }

  private setupCamera(): void {
    this._camera.position.set(0, 1.4, 1);
    this._camera.lookAt(0, 1.2, 0);
  }

  private setupTouchEvents(canvas: HTMLCanvasElement, onTouch: (area: TouchAreaEnum) => void): void {
    canvas.addEventListener('click', (event) => {
      // 实现触摸区域检测逻辑
      const rect = canvas.getBoundingClientRect();
      const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // 根据点击位置判断触摸区域
      if (y > 0.5) {
        onTouch(TouchAreaEnum.Head);
      } else if (y > 0) {
        onTouch(TouchAreaEnum.Chest);
      } else {
        onTouch(TouchAreaEnum.Belly);
      }
    });
  }

  private startRenderLoop(): void {
    const animate = () => {
      const deltaTime = this._clock.getDelta();

      if (this.model?.vrm) {
        this.model.vrm.update(deltaTime);
      }

      this._renderer.render(this._scene, this._camera);
      requestAnimationFrame(animate);
    };

    animate();
  }
}
```

#### 新建：`src/types/touch.ts`
**功能：** 触摸交互类型定义
```typescript
export enum TouchAreaEnum {
  Head = 'head',
  Chest = 'chest',
  Belly = 'belly',
  Arm = 'arm',
  Hand = 'hand',
  Leg = 'leg'
}

export interface TouchAction {
  text: string;
  expression: string;
  motion?: string;
}

export interface TouchActionConfig {
  [TouchAreaEnum.Head]?: TouchAction[];
  [TouchAreaEnum.Chest]?: TouchAction[];
  [TouchAreaEnum.Belly]?: TouchAction[];
  [TouchAreaEnum.Arm]?: TouchAction[];
  [TouchAreaEnum.Hand]?: TouchAction[];
  [TouchAreaEnum.Leg]?: TouchAction[];
}

export interface Screenplay {
  expression: string;
  motion?: string;
  tts: {
    message: string;
    engine?: string;
    voice?: string;
    speed?: number;
    pitch?: number;
  };
}
```

## 📋 第二阶段：组件系统升级

### 5. 主要组件重写 🔥🔥🔥

#### 完全重写：`src/components/VidolChatComponent.tsx`
**修改内容：** 基于Lobe的AgentViewer重构
```typescript
import React, { useCallback, useRef, useState } from 'react';
import { Card, Spin, message } from 'antd';
import { useGlobalStore } from '@/store/global';
import { useAgentStore } from '@/store/agent';
import { TouchAreaEnum } from '@/types/touch';
import { speakCharacter } from '@/libs/messages/speakCharacter';
import { VRMExpressionPresetName } from '@pixiv/three-vrm';
import { MotionPresetName } from '@/libs/emoteController/motionPresetMap';

interface VidolChatComponentProps {
  agentId: string;
  className?: string;
  height?: number | string;
  width?: number | string;
  interactive?: boolean;
  onAnimationComplete?: () => void;
}

export const VidolChatComponent: React.FC<VidolChatComponentProps> = ({
  agentId,
  className,
  height = '100%',
  width = '100%',
  interactive = true,
  onAnimationComplete
}) => {
  const viewer = useGlobalStore((s) => s.viewer);
  const agent = useAgentStore((s) => s.getAgentById(agentId));

  const [loading, setLoading] = useState(false);
  const [loadingStep, setLoadingStep] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // 处理触摸区域点击
  const handleTouchArea = useCallback((area: TouchAreaEnum) => {
    if (!interactive || !agent) return;

    const touchActions = agent.touchActions?.[area];
    if (!touchActions || touchActions.length === 0) return;

    const action = touchActions[Math.floor(Math.random() * touchActions.length)];

    speakCharacter(
      {
        expression: action.expression as VRMExpressionPresetName,
        tts: {
          ...agent.tts,
          message: action.text,
        },
        motion: action.motion as MotionPresetName,
      },
      viewer,
      {
        onComplete: () => {
          viewer.resetToIdle();
          onAnimationComplete?.();
        },
        onError: (error) => {
          viewer.resetToIdle();
          message.error(`语音播放失败: ${error.message}`);
        },
      },
    );
  }, [interactive, agent, viewer, onAnimationComplete]);

  // 预加载角色资源
  const preloadAgentResources = useCallback(async () => {
    if (!agent) return;

    setLoading(true);
    setError(null);

    try {
      setLoadingStep(1);

      const modelUrl = agent.vrmModelUrl || agent.meta?.model;
      if (!modelUrl) {
        throw new Error('未找到VRM模型URL');
      }

      await viewer.loadVrm(modelUrl);
      setLoadingStep(2);

      if (interactive && agent.greeting) {
        speakCharacter(
          {
            expression: VRMExpressionPresetName.Happy,
            tts: {
              ...agent.tts,
              message: agent.greeting,
            },
            motion: MotionPresetName.FemaleGreeting,
          },
          viewer,
          {
            onComplete: () => viewer.resetToIdle(),
            onError: () => viewer.resetToIdle(),
          },
        );
      }

    } catch (error) {
      console.error('角色资源加载失败:', error);
      setError(error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  }, [agent, viewer, interactive]);

  // Canvas引用回调
  const canvasRefCallback = useCallback((canvas: HTMLCanvasElement) => {
    if (canvas) {
      viewer.setup(canvas, handleTouchArea);
      preloadAgentResources();
    }
  }, [viewer, handleTouchArea, preloadAgentResources]);

  if (!agent) {
    return (
      <Card className={className} style={{ height, width }}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          角色不存在
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={className}
      style={{ height, width }}
      styles={{ body: { padding: 0, height: '100%' } }}
    >
      <div style={{ position: 'relative', height: '100%' }}>
        <canvas
          ref={canvasRefCallback}
          style={{
            width: '100%',
            height: '100%',
            display: 'block'
          }}
        />

        {loading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              {loadingStep === 1 && '正在加载3D模型...'}
              {loadingStep === 2 && '正在初始化角色...'}
            </div>
          </div>
        )}

        {error && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(255, 0, 0, 0.1)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'red'
          }}>
            <div>❌ 加载失败</div>
            <div style={{ fontSize: '12px', marginTop: 8 }}>{error}</div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default VidolChatComponent;
```

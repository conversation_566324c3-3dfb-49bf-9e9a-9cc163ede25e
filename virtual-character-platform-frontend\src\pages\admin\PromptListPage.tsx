import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Tag, Typography, message, Popconfirm, Input, Row, Col, Select } from 'antd';
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExperimentOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import adminAPI from '../../services/adminAPI';

const { Title } = Typography;
const { Option } = Select;

interface PromptTemplate {
  id: number;
  name: string;
  type: string;
  category?: string;
  content: string;
  description?: string;
  is_active: boolean;
  version?: string;
  created_by: {
    id: number;
    username: string;
  };
  created_at: string;
  updated_at: string;
}

const PromptListPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [promptTemplates, setPromptTemplates] = useState<PromptTemplate[]>([]);
  const [searchName, setSearchName] = useState('');
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  
  const navigate = useNavigate();

  const fetchPromptTemplates = async (filters = {}) => {
    try {
      setLoading(true);
      const response = await adminAPI.getPromptTemplates(filters);
      setPromptTemplates(response.results || []);
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      message.error('获取提示词模板失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPromptTemplates();
  }, []);

  const handleSearch = () => {
    fetchPromptTemplates({
      name: searchName || undefined,
      type: typeFilter || undefined,
      category: categoryFilter || undefined,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await adminAPI.deletePromptTemplate(id);
      message.success('删除成功');
      fetchPromptTemplates({
        name: searchName || undefined,
        type: typeFilter || undefined,
        category: categoryFilter || undefined,
      });
    } catch (error) {
      console.error('删除提示词模板失败:', error);
      message.error('删除提示词模板失败');
    }
  };

  // 获取提示词类型标签的颜色
  const getTypeTagColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'image': 'purple',
      'chat': 'blue',
      'system': 'green',
      'default': 'default',
    };
    return colorMap[type] || 'default';
  };

  // 类型显示
  const getTypeDisplay = (type: string) => {
    const typeMap: Record<string, string> = {
      'image': '图像生成',
      'chat': '对话',
      'system': '系统',
      'default': '其他',
    };
    return typeMap[type] || type;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: PromptTemplate) => (
        <Space direction="vertical" size={0}>
          <span>{text}</span>
          {record.description && (
            <span style={{ fontSize: '12px', color: '#888' }}>
              {record.description}
            </span>
          )}
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getTypeTagColor(type)}>
          {getTypeDisplay(type)}
        </Tag>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => category || '-',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (version: string) => version || 'v1.0',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'created_by',
      key: 'created_by',
      render: (createdBy: { username: string }) => createdBy?.username || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, record: PromptTemplate) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/admin/prompts/${record.id}`)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(`/admin/prompts/${record.id}/edit`)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<ExperimentOutlined />}
            onClick={() => navigate(`/admin/prompts/${record.id}/test`)}
          >
            测试
          </Button>
          <Popconfirm
            title="确定要删除该提示词模板吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={3}>提示词管理</Title>
        </Col>
        <Col>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => navigate('/admin/prompts/create')}
          >
            创建提示词模板
          </Button>
        </Col>
      </Row>

      <Card>
        {/* 搜索条件 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索模板名称"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              prefix={<SearchOutlined />}
              allowClear
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={8}>
            <Select
              placeholder="选择类型"
              style={{ width: '100%' }}
              value={typeFilter}
              onChange={(value) => setTypeFilter(value)}
              allowClear
              onClear={() => setTypeFilter(null)}
            >
              <Option value="image">图像生成</Option>
              <Option value="chat">对话</Option>
              <Option value="system">系统</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Select
              placeholder="选择分类"
              style={{ width: '100%' }}
              value={categoryFilter}
              onChange={(value) => setCategoryFilter(value)}
              allowClear
              onClear={() => setCategoryFilter(null)}
            >
              <Option value="character">角色生成</Option>
              <Option value="personality">性格</Option>
              <Option value="appearance">外观</Option>
              <Option value="dialog">对话</Option>
            </Select>
          </Col>
        </Row>
        <Row style={{ marginBottom: 16 }}>
          <Col>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button 
              style={{ marginLeft: 8 }} 
              onClick={() => {
                setSearchName('');
                setTypeFilter(null);
                setCategoryFilter(null);
                fetchPromptTemplates();
              }}
            >
              重置
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={promptTemplates}
          rowKey="id"
          loading={loading}
        />
      </Card>
    </div>
  );
};

export default PromptListPage; 
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Tooltip, Spin, message } from 'antd';
import { CheckCircleFilled } from '@ant-design/icons';
import axios from 'axios';
import '../../styles/identity-selector.css';

// 身份类型接口
interface IdentityType {
  name: string;
  label: string;
  description?: string;
  icon?: string;
}

// 组件属性接口
interface IdentitySelectorProps {
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * 身份选择器组件
 * 提供卡片式选择界面，展示身份类型及其描述
 */
const IdentitySelector: React.FC<IdentitySelectorProps> = ({ value, onChange }) => {
  // 状态管理
  const [identities, setIdentities] = useState<IdentityType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedIdentity, setSelectedIdentity] = useState<string>(value || '');

  // 当外部value变化时，更新内部状态
  useEffect(() => {
    setSelectedIdentity(value || '');
  }, [value]);

  // 身份描述映射
  const identityDescriptions: Record<string, string> = {
    "高中生": "青春活力，校园生活，穿着校服，背景通常是学校",
    "大学生": "朝气蓬勃，追求知识，现代装扮，背景可能是大学校园",
    "偶像": "光鲜亮丽，舞台表演，时尚服饰，背景是演出场地",
    "虚拟歌姬": "数字化存在，音乐表演，未来风服装，背景带有科技感",
    "咖啡店店员": "亲切服务，制作饮品，穿着围裙，背景是咖啡店",
    "魔法使": "神秘力量，施展法术，魔法装束，背景可能是魔法世界",
    "女仆": "专业服务，恭敬有礼，女仆装，背景通常是豪华宅邸",
    "赛博朋克侦探": "未来世界，解决案件，科技装备，背景是霓虹都市",
    "异世界公主": "高贵优雅，王室身份，华丽服饰，背景是奇幻城堡",
    "游戏NPC": "虚拟角色，特定功能，游戏风格装扮，背景是游戏场景",
    "虚拟心理咨询师": "温和专业，倾听解惑，正式着装，背景是咨询室"
  };

  // 身份图标映射（使用emoji作为简单图标）
  const identityIcons: Record<string, string> = {
    "高中生": "🏫",
    "大学生": "🎓",
    "偶像": "🎤",
    "虚拟歌姬": "🎵",
    "咖啡店店员": "☕",
    "魔法使": "✨",
    "女仆": "👗",
    "赛博朋克侦探": "🔍",
    "异世界公主": "👑",
    "游戏NPC": "🎮",
    "虚拟心理咨询师": "🧠"
  };

  // 监听value prop的变化，同步更新内部状态
  useEffect(() => {
    setSelectedIdentity(value || '');
  }, [value]);

  // 加载身份数据
  useEffect(() => {
    const fetchIdentities = async () => {
      setLoading(true);
      try {
        const response = await axios.get('/api/identities/');
        // 添加描述和图标到API返回的数据
        const enrichedIdentities = response.data.identities.map((i: IdentityType) => ({
          ...i,
          description: identityDescriptions[i.name] || `${i.name}身份`,
          icon: identityIcons[i.name] || "⭐"
        }));
        setIdentities(enrichedIdentities);
      } catch (error) {
        console.error('获取身份数据失败:', error);
        message.error('获取身份数据失败，使用默认数据');

        // 使用默认数据
        const defaultIdentities = Object.keys(identityDescriptions).map(name => ({
          name,
          label: name,
          description: identityDescriptions[name],
          icon: identityIcons[name] || "⭐"
        }));
        setIdentities(defaultIdentities);
      } finally {
        setLoading(false);
      }
    };

    fetchIdentities();
  }, []);

  // 处理身份选择
  const handleIdentitySelect = (identity: string) => {
    setSelectedIdentity(identity);
    if (onChange) {
      onChange(identity);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="identity-loading">
        <Spin tip="加载身份数据..." />
      </div>
    );
  }

  return (
    <div className="identity-selector">
      <Row gutter={[16, 16]}>
        {identities.map((identity) => (
          <Col xs={12} sm={8} md={6} key={identity.name}>
            <Tooltip title={identity.description} placement="top">
              <Card
                hoverable
                className={`identity-card ${selectedIdentity === identity.name ? 'selected' : ''}`}
                onClick={() => handleIdentitySelect(identity.name)}
              >
                <div className="identity-icon">{identity.icon}</div>
                <div className="identity-name">{identity.label}</div>
                {selectedIdentity === identity.name && (
                  <CheckCircleFilled className="selected-icon" />
                )}
              </Card>
            </Tooltip>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default IdentitySelector; 
.identity-selector {
  margin: 20px 0;
}

.identity-card {
  position: relative;
  text-align: center;
  padding: 15px 10px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.identity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.identity-card.selected {
  border-color: #52c41a;
  background-color: rgba(82, 196, 26, 0.05);
}

.identity-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.identity-name {
  font-size: 16px;
  font-weight: 500;
}

.selected-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #52c41a;
  font-size: 18px;
}

.identity-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
} 
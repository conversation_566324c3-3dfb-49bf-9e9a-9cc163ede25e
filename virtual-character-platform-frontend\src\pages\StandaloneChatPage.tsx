import React, { useState, useEffect, useRef } from 'react';
import { Card, Input, Button, Avatar, Spin, message, Empty, Select, Modal, Tabs, Switch } from 'antd';
import { SendOutlined, RobotOutlined, SoundOutlined, SettingOutlined, EyeOutlined, AudioOutlined } from '@ant-design/icons';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';
import ChatLayout from '../components/ChatLayout';
import VoiceSelector from '../components/VoiceSelector';
import VidolChatComponent from '../components/VidolChatComponent';
import { VoiceControls } from '../components/VoiceControls';
import '../styles/standalone-chat.css';
import '../styles/vidol-chat.css';
import '../styles/voice-controls.css';
import '../styles/immersive-voice-chat.css';
import { characterAPI } from '../services/characterAPI';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 消息类型定义
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'character';
  timestamp: number;
  hasAudio?: boolean; // 为TTS功能预留
  audioUrl?: string;  // 音频文件URL
}

// 角色类型定义
interface Character {
  id: string;
  name: string;
  imageUrl: string;
}

const StandaloneChatPage: React.FC = () => {
  // 路由参数和导航
  const { characterId } = useParams<{ characterId?: string }>();
  const [searchParams] = useSearchParams();
  const queryCharacterId = searchParams.get('characterId');
  const navigate = useNavigate();

  // 认证状态
  const { isLoggedIn, userToken, userInfo } = useAuthStore();

  // 状态管理
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [loading, setLoading] = useState(false);
  
  // TTS相关状态
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [isSettingsVisible, setIsSettingsVisible] = useState(false);

  // 3D模式相关状态
  const [is3DMode, setIs3DMode] = useState(false);

  // 语音交互相关状态
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [characterEmotion, setCharacterEmotion] = useState('neutral');
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string>('');

  // 背景图片状态
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string>('');

  // 引用消息列表底部，用于自动滚动
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 随机选择背景图片的工具函数
  const selectRandomBackground = (backgrounds: any[]) => {
    // 过滤出所有已完成且有图片URL的背景
    const completedBackgrounds = backgrounds.filter(
      (bg: any) => bg.generation_status === 'completed' && bg.image_url
    );

    if (completedBackgrounds.length === 0) {
      return null;
    }

    // 使用当前时间作为随机种子的一部分，确保每次页面加载都有不同的随机结果
    const randomIndex = Math.floor(Math.random() * completedBackgrounds.length);
    return completedBackgrounds[randomIndex];
  };

  // 获取角色背景图片
  const fetchCharacterBackground = async (characterId: string) => {
    try {
      console.log('获取角色背景图片:', characterId);
      const response = await characterAPI.getCharacterBackgrounds(characterId);
      console.log('背景图片API完整响应:', response);

      // 修复数据结构访问 - axios拦截器已经返回了response.data
      if (response && response.data?.code === 0 && response.data?.data) {
        const backgrounds = response.data.backgrounds;
        console.log('获取到背景图片列表:', backgrounds);
        console.log('背景图片数量:', backgrounds ? backgrounds.length : 0);

        if (backgrounds && Array.isArray(backgrounds)) {
          console.log('背景图片总数:', backgrounds.length);

          // 使用工具函数随机选择背景
          const selectedBackground = selectRandomBackground(backgrounds);

          if (selectedBackground) {
            console.log('🎲 随机选择背景图片:', {
              id: selectedBackground.id,
              scene_name: selectedBackground.scene_name,
              scene_type: selectedBackground.scene_type,
              url: selectedBackground.image_url,
              character_id: characterId
            });

            console.log('✅ 应用背景图片URL:', selectedBackground.image_url);
            setBackgroundImageUrl(selectedBackground.image_url);
          } else {
            console.log('❌ 没有找到已完成的背景图片，使用默认背景');
            console.log('📊 所有背景状态:', backgrounds.map(bg => ({
              id: bg.id,
              status: bg.generation_status,
              hasUrl: !!bg.image_url,
              scene_name: bg.scene_name
            })));
            // 如果没有完成的背景图片，清空背景
            setBackgroundImageUrl('');
          }
        } else {
          console.log('背景图片数据格式异常:', backgrounds);
          setBackgroundImageUrl('');
        }
      } else {
        console.log('背景图片API响应异常:', response);
        setBackgroundImageUrl('');
      }
    } catch (error) {
      console.error('获取角色背景图片失败:', error);
      // 获取失败时清空背景
      setBackgroundImageUrl('');
    }
  };

  // 加载用户创建的角色
  useEffect(() => {
    const fetchCharacters = async () => {
      setLoading(true);
      
      if (!isLoggedIn || !userToken) {
        message.warning('请先登录');
        navigate('/login');
        return;
      }
      
      try {
        const response = await characterAPI.getUserCharacters();
        
        if (response && Array.isArray(response)) {
          setCharacters(response);
          
          // 根据URL参数选择角色
          const targetCharacterId = characterId || queryCharacterId;
          
          if (targetCharacterId) {
            const targetCharacter = response.find(c => c.id.toString() === targetCharacterId.toString());
            
            if (targetCharacter) {
              setSelectedCharacter(targetCharacter);
              // 获取目标角色的背景图片
              fetchCharacterBackground(targetCharacter.id);
            } else {
              message.warning('指定的角色不存在，已选择第一个角色');
              if (response.length > 0) {
                setSelectedCharacter(response[0]);
                // 获取第一个角色的背景图片
                fetchCharacterBackground(response[0].id);
              }
            }
          } else {
            if (response.length > 0) {
              setSelectedCharacter(response[0]);
              // 获取第一个角色的背景图片
              fetchCharacterBackground(response[0].id);
            }
          }
        }
      } catch (error) {
        console.error('获取角色列表失败:', error);
        message.error('获取角色列表失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchCharacters();
  }, [characterId, queryCharacterId, isLoggedIn, userToken, navigate]);

  // 每当消息列表更新时，滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 滚动到消息列表底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 发送消息
  const sendMessage = async () => {
    if (!inputMessage.trim() || !selectedCharacter) {
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: Date.now()
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setInputMessage('');
    setSending(true);

    try {
      console.log('StandaloneChatPage: 发送消息到角色:', selectedCharacter.id, '消息:', inputMessage);

      const response = await characterAPI.sendMessage({
        characterId: selectedCharacter.id,
        message: inputMessage,
        enable_tts: isVoiceEnabled  // 传递TTS开关状态
      });

      console.log('StandaloneChatPage: API响应:', response);

      // 响应拦截器已经返回了response.data，所以response直接就是数据
      const characterMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.data?.character_response || '对不起，我现在不能回答这个问题。',
        sender: 'character',
        timestamp: Date.now(),
        hasAudio: !!response.data?.audio_url, // 根据是否有音频URL标记
        audioUrl: response.data?.audio_url    // 保存音频URL
      };

      console.log('StandaloneChatPage: 角色回复:', characterMessage.content);

      setMessages(prevMessages => [...prevMessages, characterMessage]);

      // 如果有音频URL，自动播放
      if (response.data?.audio_url && isVoiceEnabled) {
        try {
          const audio = new Audio(response.data.audio_url);
          audio.play().catch(error => {
            console.error('音频播放失败:', error);
            message.warning('音频播放失败');
          });
        } catch (error) {
          console.error('音频创建失败:', error);
        }
      }

    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请稍后再试');
    } finally {
      setSending(false);
    }
  };

  // 处理语音输入
  const handleVoiceInput = async (transcript: string) => {
    if (!transcript.trim() || !selectedCharacter) {
      return;
    }

    console.log('StandaloneChatPage: 收到语音输入:', transcript);

    // 重要：不显示用户输入的文字，直接处理
    // 设置角色为倾听状态
    setCharacterEmotion('listening');

    const userMessage: Message = {
      id: Date.now().toString(),
      content: transcript,
      sender: 'user',
      timestamp: Date.now()
    };

    // 在语音模式下，用户消息也不显示在界面上
    if (!isVoiceMode) {
      setMessages(prevMessages => [...prevMessages, userMessage]);
    }

    setSending(true);

    try {
      console.log('StandaloneChatPage: 发送语音消息到角色:', selectedCharacter.id, '消息:', transcript);

      // 设置角色为思考状态
      setCharacterEmotion('thinking');

      const response = await characterAPI.sendMessage({
        characterId: selectedCharacter.id,
        message: transcript,
        enable_tts: true,  // 语音模式下强制启用TTS
        voice_mode: true   // 标识这是语音输入
      });

      console.log('StandaloneChatPage: 语音API响应:', response);

      const characterMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: (response as any).character_response || '对不起，我现在不能回答这个问题。',
        sender: 'character',
        timestamp: Date.now(),
        hasAudio: !!(response as any).audio_url,
        audioUrl: (response as any).audio_url
      };

      console.log('StandaloneChatPage: 角色语音回复:', characterMessage.content);

      // 在语音模式下，角色回复也不显示文字
      if (!isVoiceMode) {
        setMessages(prevMessages => [...prevMessages, characterMessage]);
      }

      // 设置角色为回应状态
      setCharacterEmotion('happy');

      // 设置音频URL供3D组件使用
      if ((response as any).audio_url) {
        setCurrentAudioUrl((response as any).audio_url);

        // 如果不是3D模式，使用传统音频播放
        if (!is3DMode) {
          try {
            const audio = new Audio((response as any).audio_url);
            audio.play().catch(error => {
              console.error('语音播放失败:', error);
              message.warning('语音播放失败');
            });

            // 语音播放结束后恢复中性状态
            audio.addEventListener('ended', () => {
              setCharacterEmotion('neutral');
              setCurrentAudioUrl('');
            });
          } catch (error) {
            console.error('语音创建失败:', error);
            setCharacterEmotion('neutral');
            setCurrentAudioUrl('');
          }
        }
      } else {
        // 如果没有语音，延迟恢复中性状态
        setTimeout(() => {
          setCharacterEmotion('neutral');
          setCurrentAudioUrl('');
        }, 2000);
      }

    } catch (error) {
      console.error('发送语音消息失败:', error);
      message.error('语音交互失败，请稍后再试');
      setCharacterEmotion('neutral');
    } finally {
      setSending(false);
    }
  };

  // 处理语音监听状态变化
  const handleListeningChange = (listening: boolean) => {
    setIsListening(listening);
    if (listening) {
      setCharacterEmotion('listening');
    } else if (!sending) {
      setCharacterEmotion('neutral');
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // 选择角色
  const handleCharacterChange = (characterId: string) => {
    const character = characters.find(c => c.id === characterId);
    if (character) {
      setSelectedCharacter(character);
      setMessages([]); // 清空消息历史
      // 获取新角色的背景图片
      fetchCharacterBackground(characterId);
    }
  };

  // TTS开关
  const handleVoiceToggle = () => {
    setIsVoiceEnabled(!isVoiceEnabled);
    message.success(isVoiceEnabled ? '语音已关闭' : '语音已开启');
  };

  // 设置按钮
  const handleSettingsClick = () => {
    setIsSettingsVisible(true);
  };

  // 3D模式切换
  const handle3DModeToggle = () => {
    setIs3DMode(!is3DMode);
    message.success(is3DMode ? '已切换到2D模式' : '已切换到3D模式');
  };

  // 进入沉浸式模式
  const enterImmersiveMode = () => {
    if (!selectedCharacter) {
      message.warning('请先选择角色');
      return;
    }
    navigate(`/immersive-chat/${selectedCharacter.id}`);
  };

  // 渲染消息
  const renderMessage = (message: Message) => (
    <div 
      key={message.id} 
      className={`standalone-message-item ${message.sender === 'user' ? 'user-message' : 'character-message'}`}
    >
      <div className="standalone-message-avatar">
        {message.sender === 'user' ? (
          <Avatar size={40} style={{ backgroundColor: '#eb2f96' }}>
            {userInfo?.username.charAt(0).toUpperCase()}
          </Avatar>
        ) : (
          <Avatar 
            src={selectedCharacter?.imageUrl} 
            size={40}
            icon={<RobotOutlined />} 
          />
        )}
      </div>
      <div className="standalone-message-bubble">
        {message.content}
        {message.hasAudio && message.audioUrl && (
          <Button
            type="text"
            size="small"
            icon={<SoundOutlined />}
            style={{ marginLeft: 8, color: '#666' }}
            onClick={() => {
              try {
                const audio = new Audio(message.audioUrl);
                audio.play().catch(error => {
                  console.error('音频播放失败:', error);
                  message.warning('音频播放失败');
                });
              } catch (error) {
                console.error('音频创建失败:', error);
              }
            }}
          />
        )}
      </div>
    </div>
  );

  return (
    <ChatLayout
      selectedCharacter={selectedCharacter}
      onSettingsClick={handleSettingsClick}
      onVoiceToggle={handleVoiceToggle}
      isVoiceEnabled={isVoiceEnabled}
      backgroundImageUrl={backgroundImageUrl}
      on3DModeToggle={handle3DModeToggle}
      is3DMode={is3DMode}
    >
      <div className="standalone-chat-container">
        <div className="standalone-chat-main">
          {/* 3D数字人组件 */}
          {is3DMode && selectedCharacter && (
            <div className={`vidol-floating-panel ${is3DMode ? 'vidol-fade-in' : 'vidol-fade-out'}`}>
              <div className="vidol-mode-indicator">3D模式</div>
              <VidolChatComponent
                character={{
                  id: selectedCharacter.id,
                  name: selectedCharacter.name,
                  vrmModelUrl: undefined // 将使用默认模型
                }}
                isVisible={is3DMode}
                audioUrl={currentAudioUrl}
                emotion={characterEmotion}
                isImmersiveMode={isVoiceMode}
                hideUI={isVoiceMode}
                onAnimationComplete={() => {
                  console.log('3D动画播放完成');
                }}
                onLipSyncUpdate={(volume) => {
                  console.log('口型同步音量:', volume);
                }}
              />
            </div>
          )}

          {/* 角色选择栏 */}
          <div className="character-selection-bar">
            <div className="character-selector-wrapper">
              <span className="character-selector-label">选择聊天角色:</span>
              <Select
                value={selectedCharacter?.id}
                onChange={handleCharacterChange}
                style={{ minWidth: 200 }}
                loading={loading}
                disabled={loading}
                placeholder="请选择角色"
              >
                {characters.map(character => (
                  <Option key={character.id} value={character.id}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Avatar src={character.imageUrl} size={24} />
                      {character.name}
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* 聊天卡片 */}
          <Card className="standalone-chat-card" bodyStyle={{ padding: 0, height: '100%' }}>
            {/* 消息区域 */}
            <div className="standalone-chat-messages">
              {loading ? (
                <div className="standalone-loading">
                  <Spin size="large" />
                  <span>正在加载角色...</span>
                </div>
              ) : messages.length > 0 ? (
                <>
                  {messages.map(renderMessage)}
                  <div ref={messagesEndRef} />
                </>
              ) : (
                <div className="standalone-empty-state">
                  <Empty 
                    description="开始与您的角色聊天吧！" 
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              )}
            </div>

            {/* 输入区域 */}
            <div className="standalone-chat-input">
              {/* 模式切换按钮组 */}
              <div className="input-mode-toggle">
                <Button
                  type={isVoiceMode ? 'primary' : 'default'}
                  icon={<AudioOutlined />}
                  onClick={() => setIsVoiceMode(!isVoiceMode)}
                  size="small"
                >
                  {isVoiceMode ? '语音模式' : '文字模式'}
                </Button>

                <Button
                  type="default"
                  icon={<EyeOutlined />}
                  onClick={enterImmersiveMode}
                  size="small"
                  disabled={!selectedCharacter}
                  style={{ marginLeft: '8px' }}
                >
                  沉浸式体验
                </Button>
              </div>

              {/* 根据模式显示不同的输入方式 */}
              {isVoiceMode ? (
                <div className="voice-input-area">
                  <VoiceControls
                    onVoiceInput={handleVoiceInput}
                    onListeningChange={handleListeningChange}
                    disabled={!selectedCharacter || sending}
                    className="voice-input-control"
                  />
                  {isListening && (
                    <div className="voice-status">
                      <span className="voice-status-text">正在听您说话...</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-input-area">
                  <TextArea
                    value={inputMessage}
                    onChange={e => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入消息... (Enter发送，Shift+Enter换行)"
                    autoSize={{ minRows: 1, maxRows: 4 }}
                    disabled={!selectedCharacter || sending}
                  />
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={sendMessage}
                    loading={sending}
                    disabled={!inputMessage.trim() || !selectedCharacter}
                  >
                    发送
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* 设置模态框 */}
      <Modal
        title={
          <span>
            <SettingOutlined style={{ marginRight: 8 }} />
            聊天设置
          </span>
        }
        open={isSettingsVisible}
        onCancel={() => setIsSettingsVisible(false)}
        footer={null}
        width={600}
      >
        <Tabs defaultActiveKey="voice" style={{ marginTop: 16 }}>
          <TabPane tab="语音设置" key="voice">
            {selectedCharacter && (
              <VoiceSelector
                characterId={selectedCharacter.id}
                showPreview={true}
                onSettingsChange={(settings) => {
                  console.log('语音设置更新:', settings);
                  // 这里可以处理语音设置的变化
                }}
              />
            )}
          </TabPane>
          <TabPane tab="聊天设置" key="chat">
            <div style={{ padding: '20px 0' }}>
              <p>聊天相关设置将在这里配置...</p>
              <p>• 自动播放语音: {isVoiceEnabled ? '开启' : '关闭'}</p>
              <p>• 消息历史长度设置</p>
              <p>• 角色回复风格设置</p>
            </div>
          </TabPane>
        </Tabs>
      </Modal>
    </ChatLayout>
  );
};

export default StandaloneChatPage;

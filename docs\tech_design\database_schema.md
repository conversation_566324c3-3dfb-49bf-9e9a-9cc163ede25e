# 数据库 Schema 设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目的数据库 Schema 设计，包括主要的表（或集合）及其字段定义，以及它们之间的关系。本设计旨在支持产品需求文档 (PRD) 中定义的虚拟角色创建、存储、情感交互和社区展示等核心功能。

## 2. 数据库选型

根据 PRD 中的讨论，我们计划主要使用 **PostgreSQL** 存储结构化数据，并可能结合使用 **MongoDB** 存储更灵活或非结构化的角色相关数据。生成的图片文件将存储在**云存储服务**中，数据库中仅存储图片的链接。

## 3. Schema 设计 (PostgreSQL)

### 3.1 用户表 (`users`)

存储平台用户的基础信息。如果项目需要用户注册和登录功能，此表是必需的。

- `id`: INT / BIGINT, Primary Key, Auto-increment. 用户唯一标识符。
- `username`: VARCHAR(255), Unique, Not Null. 用户名。
- `email`: VARCHAR(255), Unique (可选), Not Null. 用户邮箱，可用于登录或找回密码。
- `password_hash`: VARCHAR(255), Not Null. 用户密码的哈希值，用于安全存储。
- `created_at`: TIMESTAMP, Not Null. 用户创建时间。
- `updated_at`: TIMESTAMP, Not Null. 用户信息最后更新时间。

### 3.2 虚拟角色表 (`characters`)

存储用户创建的虚拟角色的核心信息。

- `id`: INT / BIGINT, Primary Key, Auto-increment. 角色唯一标识符。
- `user_id`: INT / BIGINT, Foreign Key references `users(id)`. 创建该角色的用户 ID。
- `name`: VARCHAR(255), Not Null. 角色的名称。
- `age`: INT. 角色的年龄设定。
- `identity`: VARCHAR(255). 角色的身份设定（例如，"学生"，"偶像"）。
- `personality`: VARCHAR(255). 角色的性格设定（例如，"傲娇"，"元气"）。
- `image_url`: VARCHAR(255). 角色形象图片的云存储 URL。 (MVP)
- `created_at`: TIMESTAMP, Not Null. 角色创建时间。
- `updated_at`: TIMESTAMP, Not Null. 角色信息最后更新时间。
- `public`: BOOLEAN, Not Null, Default: TRUE. 角色是否在社区公开展示。
- `appearance_params`: JSON / JSONB. 存储角色的外观参数，如果参数调整很多且结构灵活，可以考虑使用 JSONB 字段存储（具体参数结构待定）。(MVP)
- `settings`: JSON / JSONB. 存储其他角色设定，如声音类型等（具体结构待定）。

### 3.3 聊天记录表 (`chat_messages`) - 未来考虑

存储用户与特定角色之间的聊天记录。MVP 阶段文本聊天可能不需要持久化，但未来有状态聊天或需要聊天历史回溯时需要此表。

- `id`: BIGINT, Primary Key, Auto-increment.
- `character_id`: BIGINT, Foreign Key references `characters(id)`. 关联的角色 ID。
- `user_id`: BIGINT, Foreign Key references `users(id)`. 发送消息的用户 ID (如果用户可以与自己的角色聊天)。
- `sender_type`: ENUM ('user', 'character'). 消息发送者类型。
- `content`: TEXT, Not Null. 消息内容。
- `sent_at`: TIMESTAMP, Not Null. 消息发送时间。

## 4. Schema 设计 (MongoDB - 可选)

如果决定使用 MongoDB，可以考虑将一些结构更灵活的数据存储在这里，例如：

### 4.1 虚拟角色详细参数集合 (`character_details`)

存储角色的详细外观参数、与图片生成相关的提示词历史、更复杂的设定等。

- `character_id`: ObjectId or equivalent, Reference to PostgreSQL `characters` table.
- `detailed_params`: Object. 存储各种外观参数的键值对（例如：`{'hair_color': 'green', 'eye_size': 'large', ...}`）。
- `prompt_history`: Array of Objects. 存储生成该角色形象时使用过的提示词及其结果。
- `complex_settings`: Object. 存储更复杂的设定。

## 5. 关系图 (概念性)

```mermaid
graph LR
    users -- one to many --> characters
    characters -- one to many --> chat_messages
    characters -- one to one --> character_details (Optional, if using MongoDB)
```

## 6. 待细化项

-   **`appearance_params` 字段中存储的具体外观参数结构：**
    -   这个 JSONB 字段将用于存储角色的详细外观属性，其具体结构将与 `docs/tech_design/backend_api_design.md` 中定义的 `appearance_params` 请求/响应结构保持一致。例如：
        ```json
        {
          "gender": "male" | "female",
          "height_category": "tall" | "medium" | "short",
          "body_type": "slim" | "athletic" | "curvy" | "busty",
          "face_shape": "oval" | "round" | "heart",
          "hair_style": "long_straight" | "short_bob" | "ponytail" | "twin_tails",
          "hair_color": "black" | "blonde" | "pink" | "blue" | "green" | "red",
          "eye_color": "blue" | "red" | "green" | "black" | "brown" | "purple",
          "eye_shape": "large" | "narrow" | "almond" | "round",
          "clothing_style": "casual" | "formal" | "fantasy" | "school_uniform",
          "accessories": ["glasses", "hat", "ribbon", "scarf"],
          "digital_human_form_outlook": "string" // 新增，用于存储数字人形态外观描述，参考 PRD
        }
        ```
    -   这些参数的值可以映射到提示词工程模块中的关键词或权重，以影响图片生成。
-   **`settings` 字段中存储的具体设置结构：**
    -   这个 JSONB 字段将用于存储角色其他非外观的设定，其具体结构也将与 `docs/tech_design/backend_api_design.md` 中定义的 `settings` 请求/响应结构保持一致。例如：
        ```json
        {
          "voice_type_id": "string", // 关联到 TTS 服务中预设的声音 ID（如 "female_child_01"）
          "speaking_speed": "number", // 语速，例如 0.8-1.2，默认 1.0
          "speaking_pitch": "number", // 音调，例如 0.8-1.2，默认 1.0
          "background_story": "text", // 角色的背景故事，可用于对话 AI 的系统提示词
          "hobbies": ["string"], // 角色的爱好列表
          "strengths": ["string"], // 角色的优点
          "weaknesses": ["string"], // 角色的弱点
          "dialogue_style": "string", // 角色的对话风格（如 "活泼", "内向", "傲娇"）
          "relationship_to_user": "string", // 角色与用户的关系设定（如 "朋友", "妹妹", "恋人"）
          "memory_capacity": "integer" // 对话 AI 记住的上下文轮次或 token 数，用于控制聊天历史长度
        }
        ```
-   **性格（`personality`）和身份（`identity`）是否需要更详细的结构或关联到独立的设定库：**
    -   **MVP 阶段：** `personality` 和 `identity` 仍作为 `VARCHAR(255)` 字段直接存储在 `characters` 表中，以字符串形式表示。这简化了初期开发，并可以直接用于提示词生成。
    -   **未来扩展：** 考虑为 `personality` 和 `identity` 建立独立的字典表（例如 `personalities` 和 `identities`），其中包含预定义的选项及其更详细的描述或标签。这样可以：
        -   **标准化：** 确保输入的一致性，避免错别字或多种表达方式。
        -   **多语言支持：** 方便后续添加多语言版本。
        -   **扩展性：** 可以为每个性格/身份添加额外的属性（如权重、关联的动作或表情）。
        -   如果采用独立字典表，`characters` 表中的 `personality` 和 `identity` 将存储对应字典表的 ID，而非字符串本身。
-   **MongoDB Schema 的具体设计，如果决定使用：**
    -   **决定：** 如 `docs/tech_design/data_storage.md` 中所述，在 MVP 阶段，**暂不引入 MongoDB**。所有结构化和半结构化数据（通过 JSONB）都将存储在 PostgreSQL 中，以保持技术栈的简洁性。
    -   **未来考虑：** 如果未来出现 PostgreSQL 难以满足的文档存储需求（例如，非常庞大且频繁变化的嵌套 JSON 结构，或需要无模式存储），将重新评估 MongoDB。届时 `character_details` 集合将设计为包含 `character_id` 作为关联键，并包含灵活的 `detailed_params`、`prompt_history`、`complex_settings` 等字段，以满足特定场景的需求。
-   **聊天记录 (`chat_messages`) 表是否需要在 MVP 阶段实现以及具体字段：**
    -   **决定：** 是的，`chat_messages` 表**将在 MVP 阶段实现**。尽管用户可能不需要持久化聊天历史，但为了调试、监控 AI 表现、未来引入上下文管理和人工审核（如果需要），存储聊天记录是必要的。
    -   **具体字段：** 现有字段已基本满足需求：
        -   `id`: BIGINT, Primary Key, Auto-increment.
        -   `character_id`: BIGINT, Foreign Key references `characters(id)`. 关联的角色 ID。
        -   `user_id`: BIGINT, Foreign Key references `users(id)`. 发送消息的用户 ID。
        -   `sender_type`: ENUM ('user', 'character'). 消息发送者类型。
        -   `content`: TEXT, Not Null. 消息内容。
        -   `sent_at`: TIMESTAMP, Not Null. 消息发送时间。
    -   **新增字段 (可选，未来扩展):**
        -   `sentiment`: VARCHAR(50). 消息情感（例如 'positive', 'neutral', 'negative'），用于情感分析和未来情感引导。
        -   `metadata`: JSONB. 存储额外信息，如消息的 token 数量、AI 模型版本等，便于分析和优化。
-   **社区互动功能（点赞、评论等）所需的数据结构：**
    -   **点赞功能 (`likes` 表)：**
        -   `id`: BIGINT, Primary Key, Auto-increment.
        -   `user_id`: BIGINT, Foreign Key references `users(id)`. 点赞用户 ID。
        -   `character_id`: BIGINT, Foreign Key references `characters(id)`. 被点赞角色 ID。
        -   `created_at`: TIMESTAMP, Not Null. 点赞时间。
        -   **唯一约束:** `(user_id, character_id)` 组合应是唯一的，防止重复点赞。
    -   **评论功能 (`comments` 表) (未来考虑):**
        -   `id`: BIGINT, Primary Key, Auto-increment.
        -   `user_id`: BIGINT, Foreign Key references `users(id)`. 评论用户 ID。
        -   `character_id`: BIGINT, Foreign Key references `characters(id)`. 被评论角色 ID。
        -   `parent_comment_id`: BIGINT, Nullable, Foreign Key references `comments(id)`. 用于支持评论回复。
        -   `content`: TEXT, Not Null. 评论内容。
        -   `created_at`: TIMESTAMP, Not Null. 评论时间。
        -   `is_deleted`: BOOLEAN, Default: FALSE. 软删除标记，用于评论审核和删除。
    -   **角色表计数器：** 在 `characters` 表中增加 `likes_count` (INT, Default: 0) 和 `comments_count` (INT, Default: 0) 字段，通过触发器或在应用层同步更新，便于快速查询。

这份文档是数据库 Schema 的初步设计，将在后续的技术设计和开发过程中根据具体需求和技术实现细节进行完善。

## 重要说明

**MVP阶段仅使用PostgreSQL**：在虚拟角色平台的MVP阶段，我们将仅使用PostgreSQL作为唯一的数据库系统。尽管产品需求文档(PRD)中提到可能使用MongoDB存储部分数据，但经过评估，我们决定暂不引入MongoDB，以简化技术栈和降低开发复杂度。关于未来可能引入MongoDB的考虑，请参阅[MongoDB未来考虑事项](mongodb_future_considerations.md)文档。

## PostgreSQL数据库Schema

以下是虚拟角色平台MVP阶段的PostgreSQL数据库表结构设计：

### users表 - 用户信息

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
```

### characters表 - 角色信息

```sql
CREATE TABLE characters (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    avatar_url VARCHAR(255) NOT NULL,
    personality VARCHAR(255) NOT NULL,  -- 性格特质，如"傲娇"、"温柔"等
    identity VARCHAR(255) NOT NULL,     -- 身份设定，如"高中生"、"魔法使"等
    appearance_params JSONB NOT NULL,   -- 外观参数，使用JSONB存储结构化数据
    settings JSONB NOT NULL,            -- 其他设定，包括语音、背景故事等
    is_public BOOLEAN DEFAULT false,    -- 是否公开到社区
    is_deleted BOOLEAN DEFAULT false,   -- 软删除标记
    likes_count INTEGER DEFAULT 0,      -- 点赞数量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT validate_personality CHECK (
        personality IN ('傲娇', '病娇', '元气', '沉稳', '冷酷', '温柔', '活泼', 
                        '腼腆', '高冷', '毒舌', '健气系', '哥哥系', '姐姐系')
    ),
    CONSTRAINT validate_identity CHECK (
        identity IN ('高中生', '大学生', '偶像', '虚拟歌姬', '咖啡店店员', '魔法使', 
                    '女仆', '赛博朋克侦探', '异世界公主', '游戏NPC', '虚拟心理咨询师')
    )
);

-- 索引
CREATE INDEX idx_characters_user_id ON characters(user_id);
CREATE INDEX idx_characters_personality ON characters(personality);
CREATE INDEX idx_characters_identity ON characters(identity);
CREATE INDEX idx_characters_is_public ON characters(is_public) WHERE is_public = true;
CREATE INDEX idx_characters_created_at ON characters(created_at);
CREATE INDEX idx_characters_likes_count ON characters(likes_count);
```

### character_images表 - 角色图片

```sql
CREATE TABLE character_images (
    id SERIAL PRIMARY KEY,
    character_id INTEGER NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    image_url VARCHAR(255) NOT NULL,
    image_type VARCHAR(50) NOT NULL,  -- 'avatar', 'full_body', 'expression', etc.
    is_primary BOOLEAN DEFAULT false,
    prompt_used TEXT,  -- 生成此图像使用的提示词
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_character_images_character_id ON character_images(character_id);
CREATE INDEX idx_character_images_is_primary ON character_images(is_primary) WHERE is_primary = true;
```

### chat_messages表 - 聊天记录

```sql
CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    character_id INTEGER NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL,  -- 'user' 或 'character'
    content TEXT NOT NULL,
    sentiment VARCHAR(50),  -- 情感分析结果，如'positive', 'negative', 'neutral'
    metadata JSONB,  -- 生成时的额外信息，如模型参数等
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_chat_messages_character_id ON chat_messages(character_id);
CREATE INDEX idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX idx_chat_messages_sent_at ON chat_messages(sent_at);
CREATE INDEX idx_chat_messages_combined ON chat_messages(character_id, user_id, sent_at);
```

### likes表 - 点赞记录

```sql
CREATE TABLE likes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id INTEGER NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_user_character_like UNIQUE (user_id, character_id)
);

-- 索引
CREATE INDEX idx_likes_user_id ON likes(user_id);
CREATE INDEX idx_likes_character_id ON likes(character_id);
```

### comments表 - 评论（未来扩展）

```sql
-- 评论功能计划在未来版本实现，此处为预留设计
CREATE TABLE comments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id INTEGER NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    parent_comment_id INTEGER REFERENCES comments(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_character_id ON comments(character_id);
CREATE INDEX idx_comments_parent_comment_id ON comments(parent_comment_id);
CREATE INDEX idx_comments_created_at ON comments(created_at);
```

## 数据模型说明

### appearance_params 结构

`characters`表中的`appearance_params`字段使用JSONB类型存储角色的外观参数。其结构定义详见`core/models/character_appearance_schema.py`文件，主要包含以下部分：

```json
{
  "base": {
    "gender": "female",
    "age_appearance": "teen",
    "height_category": "medium",
    "body_type": "slim"
  },
  "head": {
    "hair_style": "long_straight",
    "hair_color": "pink",
    "eye_color": "blue",
    "face_shape": "heart",
    "eye_shape": "round",
    "skin_tone": "fair"
  },
  "outfit": {
    "clothing_style": "jk_uniform",
    "main_color": "navy",
    "accent_color": "white",
    "clothing_layers": ["blouse", "skirt", "socks"]
  },
  "accessories": {
    "hair_accessories": ["ribbon", "hairpin"],
    "jewelry": ["earrings"],
    "other": ["glasses", "bag"]
  },
  "expression": {
    "default_expression": "smile",
    "expression_intensity": "medium"
  },
  "special_features": {
    "animal_ears": false,
    "wings": false,
    "tail": false,
    "horns": false,
    "other_features": []
  },
  "digital_human_form_outlook": {
    "enabled": false,
    "model_path": "",
    "texture_path": "",
    "animation_set": ""
  },
  "metadata": {
    "created_at": "2023-07-01T12:00:00Z",
    "last_updated_at": "2023-07-01T12:00:00Z",
    "version": "1.0.0"
  }
}
```

### settings 结构

`characters`表中的`settings`字段使用JSONB类型存储角色的各项设定。其结构定义详见`core/models/character_settings_schema.py`文件，主要包含以下部分：

```json
{
  "voice": {
    "voice_type_id": "default_female_1",
    "speaking_speed": 1.0,
    "speaking_pitch": 0.0,
    "volume": 1.0,
    "voice_effects": []
  },
  "personality": {
    "traits": ["kind", "cheerful"],
    "emotional_tendency": "positive",
    "expressiveness": 0.8,
    "confidence": 0.7,
    "stability": 0.6
  },
  "background": {
    "background_story": "这是一个普通的高中生，喜欢阅读和绘画...",
    "hobbies": ["reading", "drawing"],
    "favorites": {
      "food": ["strawberry", "chocolate"],
      "color": "pink",
      "activity": "drawing"
    },
    "dislikes": ["spicy food", "loud noises"],
    "special_memories": []
  },
  "dialogue": {
    "dialogue_style": "casual",
    "speech_patterns": {
      "ending_particles": "呢",
      "self_reference": "我",
      "special_phrases": ["那个...", "嘛..."]
    },
    "response_length_preference": "medium",
    "emoji_usage": "moderate"
  },
  "relationship": {
    "relationship_to_user": "friend",
    "relationship_development": true,
    "intimacy_level": 0.5,
    "memory_capacity": 20,
    "key_memory_retention": true
  },
  "ai_model_settings": {
    "system_prompt_template": "你是一个{personality_traits}的{identity}，名叫{name}。你说话风格{dialogue_style}...",
    "temperature": 0.7,
    "max_tokens": 300,
    "top_p": 0.9,
    "presence_penalty": 0.6
  },
  "learning": {
    "adaptive_behavior": true,
    "learning_rate": 0.3,
    "customization_allowed": true
  },
  "metadata": {
    "created_at": "2023-07-01T12:00:00Z",
    "last_updated_at": "2023-07-01T12:00:00Z",
    "version": "1.0.0"
  }
}
```

## 数据验证与完整性

为确保数据的一致性和有效性，我们采取以下措施：

1. **数据库约束**：
   - 使用外键约束确保关联完整性
   - 对非空字段强制执行NOT NULL约束
   - 使用UNIQUE约束防止重复记录

2. **应用层验证**：
   - 使用Pydantic模型验证JSONB数据结构
   - 实现自定义验证器确保personality和identity字段值在预定义范围内
   - API层添加请求验证中间件

3. **触发器与自动更新**：
   - 实现`likes_count`自动更新触发器，确保点赞计数准确
   - 所有表都有`updated_at`列，会在记录更新时自动更新时间戳

## 索引策略

为优化查询性能，我们对各表实施了以下索引策略：

1. **频繁查询字段索引**：对用户名、电子邮件等常用查询条件创建索引
2. **外键索引**：为所有外键关系创建索引，优化关联查询
3. **组合索引**：为常见的多字段查询条件创建组合索引
4. **部分索引**：使用WHERE子句创建部分索引，如仅对公开角色建立索引

## 性能优化考量

1. **分页查询**：所有列表API默认实现分页，避免大数据集传输
2. **延迟加载**：关联数据采用延迟加载策略，仅在需要时查询
3. **JSONB索引**：对JSONB字段的常用查询路径创建GIN索引
4. **查询优化**：使用EXPLAIN分析并优化复杂查询
5. **连接池**：配置适当的数据库连接池大小，避免连接资源耗尽 
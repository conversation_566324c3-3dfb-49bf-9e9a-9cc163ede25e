import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import resourcesToBackend from 'i18next-resources-to-backend';
import { initReactI18next } from 'react-i18next';

import {
  COOKIE_CACHE_DAYS,
  DEFAULT_LANG,
  LOBE_LOCALE_COOKIE,
  getDebugConfig,
  isDev,
  isOnServerSide,
} from '../constants/i18n';
import { normalizeLocale } from './resources';

const { I18N_DEBUG, I18N_DEBUG_BROWSER, I18N_DEBUG_SERVER } = getDebugConfig();
const debugMode = (I18N_DEBUG ?? isOnServerSide) ? I18N_DEBUG_SERVER : I18N_DEBUG_BROWSER;

export const createI18nNext = (lang?: string) => {
  const instance = i18n
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(
      resourcesToBackend(async (lng: string, ns: string) => {
        if (isDev && lng === 'zh-CN') {
          // 使用显式的导入路径避免Vite警告
          switch (ns) {
            case 'error':
              return import('./default/error');
            case 'common':
              return import('./default/common');
            case 'chat':
              return import('./default/chat');
            case 'welcome':
              return import('./default/welcome');
            case 'role':
              return import('./default/role');
            case 'settings':
              return import('./default/settings');
            case 'metadata':
              return import('./default/metadata');
            default:
              return import('./default/common');
          }
        }

        const normalizedLang = normalizeLocale(lng);
        // 使用显式的导入路径避免Vite警告
        try {
          return await import(/* @vite-ignore */ `@/../locales/${normalizedLang}/${ns}.json`);
        } catch (error) {
          console.warn(`Failed to load locale ${normalizedLang}/${ns}.json, falling back to default`);
          return import('./default/common');
        }
      }),
    );
  return {
    init: () =>
      instance.init({
        debug: debugMode,
        ns: ['error', 'common', 'chat', 'welcome', 'role', 'settings', 'metadata'],
        detection: {
          caches: ['cookie'],
          cookieMinutes: 60 * 24 * COOKIE_CACHE_DAYS,
          cookieOptions: {
            sameSite: 'lax',
          },
          lookupCookie: LOBE_LOCALE_COOKIE,
        },
        fallbackLng: DEFAULT_LANG,
        interpolation: {
          escapeValue: false,
        },
        lng: lang,
      }),
    instance,
  };
};

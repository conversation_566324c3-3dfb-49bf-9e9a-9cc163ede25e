export interface Dance {
  /**
   * 音频文件
   */
  audio: string;
  /**
   * 作者名
   */
  author: string;
  /**
   * 镜头文件，非必须
   */
  camera?: string;
  /**
   * 封面图片
   */
  cover: string;
  /**
   * 创建时间
   */
  createAt: string;
  /**
   * 舞蹈 ID
   */
  danceId: string;
  /**
   * 作者主页
   */
  homepage: string;
  /**
   * 舞蹈名
   */
  name: string;
  /**
   * 说明文件
   */
  readme: string;
  /**
   * 数据结构版本
   */
  schemaVersion: number;
  /**
   * 舞蹈文件
   */
  src: string;
  /**
   * 缩略图
   */
  thumb: string;
}

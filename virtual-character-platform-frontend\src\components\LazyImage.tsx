import React, { useState, useRef, useEffect } from 'react';
import { Spin } from 'antd';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  fallback?: string;
  onLoad?: () => void;
  onError?: () => void;
  style?: React.CSSProperties;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  placeholder,
  fallback,
  onLoad,
  onError,
  style
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(fallback || placeholder || '/placeholder-character.svg');
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 创建 Intersection Observer
  useEffect(() => {
    if (!imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, []);

  // 当图片进入视口时开始加载
  useEffect(() => {
    if (!isInView || !src) return;

    const img = new Image();

    img.onload = () => {
      setCurrentSrc(src);
      setIsLoaded(true);
      setHasError(false);
      onLoad?.();
    };

    img.onerror = () => {
      setHasError(true);
      if (fallback) {
        setCurrentSrc(fallback);
        setIsLoaded(true);
      }
      onError?.();
    };

    img.src = src;
  }, [isInView, src, fallback, onLoad, onError]);

  // 生成默认占位符 - 暂时注释掉未使用的函数
  // const getDefaultPlaceholder = () => {
  //   const canvas = document.createElement('canvas');
  //   canvas.width = 300;
  //   canvas.height = 400;
  //   const ctx = canvas.getContext('2d');
  //
  //   if (ctx) {
  //     // 渐变背景
  //     const gradient = ctx.createLinearGradient(0, 0, 300, 400);
  //     gradient.addColorStop(0, '#2a2a3e');
  //     gradient.addColorStop(1, '#1a1a2e');
  //     ctx.fillStyle = gradient;
  //     ctx.fillRect(0, 0, 300, 400);
  //
  //     // 文字
  //     ctx.fillStyle = '#b8b8d1';
  //     ctx.font = '16px Arial';
  //     ctx.textAlign = 'center';
  //     ctx.fillText('加载中...', 150, 200);
  //   }
  //
  //   return canvas.toDataURL();
  // };

  const displaySrc = currentSrc || fallback || '/placeholder-character.svg';

  return (
    <div 
      className={`lazy-image-container ${className}`}
      style={{ 
        position: 'relative',
        overflow: 'hidden',
        ...style 
      }}
    >
      <img
        ref={imgRef}
        src={displaySrc}
        alt={alt}
        className={`lazy-image ${isLoaded ? 'loaded' : 'loading'}`}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          transition: 'opacity 0.3s ease-in-out',
          opacity: isLoaded ? 1 : 0.7
        }}
      />
      
      {!isLoaded && isInView && !hasError && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1
          }}
        >
          <Spin size="small" />
        </div>
      )}
      
      {hasError && !fallback && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#ff4d4f',
            fontSize: '12px',
            textAlign: 'center',
            zIndex: 1
          }}
        >
          图片加载失败
        </div>
      )}
    </div>
  );
};

export default LazyImage;

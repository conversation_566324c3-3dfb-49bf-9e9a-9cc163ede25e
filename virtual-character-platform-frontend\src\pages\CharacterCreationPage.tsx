import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Layout, Card, Row, Col, Button, Input, Divider, Spin, Select, Slider, Form, Modal, App } from 'antd';
import { RocketOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { characterAPI } from '../services/characterAPI';
import PersonalityIdentitySelector from '../components/character/PersonalityIdentitySelector';
import useAuthStore from '../store/authStore';
import { useNavigate } from 'react-router-dom';
import '../styles/character-creation.css';

const { Content } = Layout;
const { Option } = Select;

// 定义参数类型
interface CharacterParams {
  prompt: string;
  race: string;
  hairStyle: string;
  hairColor: string;
  eyeColor: string;
  expression: string;
  outfit: string;
  bodyType: number;
  personality: string;
  identity: string;
  gender: string;
  age: number;
}

// 默认参数
const defaultParams: CharacterParams = {
  prompt: '',
  race: '人类',
  hairStyle: '长直发',
  hairColor: '黑色',
  eyeColor: '蓝色',
  expression: '微笑',
  outfit: '校服',
  bodyType: 5,
  personality: '温柔',
  identity: '高中生',
  gender: '女性',
  age: 18
};

/**
 * 角色创建页面内部组件
 * 包含生成/定制区、预览区和操作按钮区
 */
const CharacterCreationPageContent: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const { isLoggedIn } = useAuthStore();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [characterImage, setCharacterImage] = useState<string | null>(null);
  const [params, setParams] = useState<CharacterParams>(defaultParams);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [characterName, setCharacterName] = useState('');
  // 年龄的本地显示状态，用于实时更新UI
  const [displayAge, setDisplayAge] = useState(defaultParams.age);

  // 同步displayAge与params.age
  useEffect(() => {
    setDisplayAge(params.age);
  }, [params.age]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);
  
  // 参数选项
  const raceOptions = ['人类', '精灵', '兽人', '吸血鬼', '天使', '恶魔', '龙人'];
  const hairStyleOptions = ['长直发', '短发', '双马尾', '丸子头', '卷发', '辫子', '蓬松'];
  const hairColorOptions = ['黑色', '金色', '银色', '红色', '蓝色', '粉色', '紫色', '绿色', '白色'];
  const eyeColorOptions = ['蓝色', '红色', '金色', '绿色', '紫色', '粉色', '黑色', '异色瞳'];
  const expressionOptions = ['微笑', '严肃', '害羞', '自信', '傲娇', '惊讶', '伤心'];
  const outfitOptions = ['校服', '休闲装', 'JK制服', '洛丽塔', '运动装', '泳装', '正装', '魔法少女', '战斗服'];
  const genderOptions = ['女性', '男性', '其他'];
  
  // 更新单个参数 - 使用useCallback避免无限重渲染
  const updateParam = useCallback((key: keyof CharacterParams, value: any) => {
    console.log(`更新参数 ${key}:`, value, '类型:', typeof value);
    setParams(prev => {
      const newParams = { ...prev, [key]: value };
      console.log('更新后的参数:', newParams);
      return newParams;
    });
  }, []);

  // 年龄tooltip格式化函数 - 使用useCallback避免无限循环
  const ageTooltipFormatter = useCallback((value: number | undefined) => {
    return value ? `${value}岁` : '';
  }, []);

  // 年龄标签 - 使用displayAge来实时显示
  const ageLabel = useMemo(() => `年龄 (${displayAge}岁)`, [displayAge]);

  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 处理年龄滑块变化
  const handleAgeChange = useCallback((value: number) => {
    // 立即更新显示状态
    setDisplayAge(value);

    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 防抖更新主状态
    debounceTimerRef.current = setTimeout(() => {
      updateParam('age', value);
    }, 150); // 150ms 防抖延迟
  }, [updateParam]);
  
  // 生成角色提示词
  const generatePrompt = (): string => {
    const basePrompt = params.prompt.trim() 
      ? params.prompt
      : `${params.race}少女`;
    
    const features = [
      `${params.hairColor}${params.hairStyle}`,
      `${params.eyeColor}眼睛`,
      `${params.expression}表情`,
      `穿着${params.outfit}`,
      `${params.personality}性格`,
      `${params.identity}身份`
    ];
    
    return `${basePrompt}，${features.join('，')}`;
  };

  // 生成角色图像
  const generateCharacter = async () => {
    try {
      setLoading(true);

      // 构建API请求参数，使用后端期望的格式
      const generateParams = {
        prompt: generatePrompt(),
        race: params.race || '人类',
        anime_name: '',
        age: params.age || 18,
        gender: params.gender || '女性',
        identity: params.identity || '高中生',
        personality: params.personality || '元气'
      };

      console.log('发送角色生成请求:', generateParams);

      // 调用后端API生成角色图像
      const response = await characterAPI.generateImage(generateParams);

      console.log('收到API响应:', response);

      if (response && response.data) {
        // 使用类型断言确保TypeScript理解数据结构
        const responseData = response.data as {
          image_url?: string;
          generated_prompt?: string;
          warning?: string;
        };

        const { image_url, generated_prompt, warning } = responseData;

        if (image_url) {
          setCharacterImage(image_url);
          console.log('生成的提示词:', generated_prompt);

          if (warning) {
            message.warning(warning);
          } else {
            message.success('角色生成成功');
          }
        } else {
          throw new Error('API返回的图片URL为空');
        }
      } else {
        throw new Error('API响应格式错误');
      }

    } catch (error) {
      console.error('生成角色失败:', error);

      // 根据错误类型显示不同的错误信息
      if (error instanceof Error) {
        if (error.message.includes('网络')) {
          message.error('网络连接失败，请检查网络后重试');
        } else if (error.message.includes('服务')) {
          message.error('图片生成服务暂时不可用，请稍后再试');
        } else {
          message.error(`生成失败: ${error.message}`);
        }
      } else {
        message.error('生成角色失败，请稍后再试');
      }

      // 设置默认占位符图片
      setCharacterImage('/placeholder-character.svg');
    } finally {
      setLoading(false);
    }
  };

  // 打开保存角色模态框
  const openSaveModal = () => {
    if (!characterImage) {
      message.warning('请先生成角色图像');
      return;
    }

    // 检查用户是否已登录
    const { isLoggedIn } = useAuthStore.getState();
    if (!isLoggedIn) {
      message.warning('请先登录后再保存角色');
      return;
    }

    setSaveModalVisible(true);
  };
  
  // 保存角色
  const saveCharacter = async () => {
    // 检查用户是否已登录
    if (!isLoggedIn) {
      message.warning('请先登录后再保存角色');
      navigate('/login');
      return;
    }

    if (!characterName.trim()) {
      message.warning('请输入角色名称');
      return;
    }

    // 允许没有图片的情况下保存角色，使用默认占位符
    // if (!characterImage) {
    //   message.warning('请先生成角色图片');
    //   return;
    // }

    try {
      setLoading(true);

      let finalImageUrl = characterImage;

      // 如果没有图片，使用本地默认占位符URL
      if (!characterImage) {
        finalImageUrl = '/placeholder-character.svg';
        console.log('使用默认占位符图片:', finalImageUrl);
      }
      // 如果是base64图片，先上传到服务器
      else if (characterImage.startsWith('data:')) {
        console.log('上传base64图片到服务器...');
        try {
          const uploadResponse = await characterAPI.uploadImage(characterImage);
          if (uploadResponse && uploadResponse.data) {
            // 使用类型断言确保TypeScript理解数据结构
            const uploadData = uploadResponse.data as { image_url?: string };
            if (uploadData.image_url) {
              finalImageUrl = uploadData.image_url;
              console.log('图片上传成功，URL:', finalImageUrl);
            } else {
              throw new Error('图片上传失败：未返回图片URL');
            }
          } else {
            throw new Error('图片上传失败：未返回图片URL');
          }
        } catch (uploadError) {
          console.error('图片上传失败:', uploadError);
          message.error('图片上传失败，请稍后再试');
          return;
        }
      }

      // 调用后端API保存角色
      // 确保finalImageUrl不为null，如果是null则使用默认占位符
      const imageUrlToSave = finalImageUrl || '/placeholder-character.svg';

      const saveData = {
        name: characterName,
        image_url: imageUrlToSave, // 确保不为null
        age: params.age || 18,
        gender: params.gender || "女性",
        personality: params.personality || "元气", // 使用预定义的性格值
        identity: params.identity || "高中生", // 使用预定义的身份值
        appearance_params: {
          race: params.race,
          hairStyle: params.hairStyle,
          hairColor: params.hairColor,
          eyeColor: params.eyeColor,
          expression: params.expression,
          outfit: params.outfit,
          bodyType: params.bodyType,
          prompt: params.prompt
        },
        settings: {},
        public: true // 默认公开，可在个人和主页展示
      };

      console.log('=== 角色保存调试信息 ===');
      console.log('保存角色数据:', saveData);
      console.log('保存角色数据 JSON:', JSON.stringify(saveData, null, 2));
      console.log('当前参数状态:', params);
      console.log('角色名称:', characterName);
      console.log('最终图片URL:', finalImageUrl);
      console.log('性格值:', params.personality, '类型:', typeof params.personality);
      console.log('身份值:', params.identity, '类型:', typeof params.identity);
      console.log('=== 调试信息结束 ===');

      const response = await characterAPI.saveCharacter(saveData);
      console.log('保存角色响应:', response);

      if (response && response.data) {
        message.success(`角色 "${characterName}" 保存成功！角色ID: ${response.data.id}`);
      } else {
        message.success(`角色 "${characterName}" 保存成功！`);
      }

      setSaveModalVisible(false);
      // 清空数据，准备创建新角色
      setCharacterName('');
    } catch (error: any) {
      console.error('保存角色失败:', error);

      // 更详细的错误处理
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;

        if (status === 401) {
          message.error('请先登录后再保存角色');
        } else if (status === 400) {
          console.log('400错误详细信息:', data);
          let errorMsg = data?.message || data?.error || '请求参数有误';

          // 如果有详细的验证错误信息，显示具体字段错误
          if (data?.errors) {
            const errorDetails = [];
            for (const [field, fieldErrors] of Object.entries(data.errors)) {
              if (Array.isArray(fieldErrors)) {
                errorDetails.push(`${field}: ${fieldErrors.join(', ')}`);
              } else {
                errorDetails.push(`${field}: ${fieldErrors}`);
              }
            }
            if (errorDetails.length > 0) {
              errorMsg += ` - ${errorDetails.join('; ')}`;
            }
          }

          message.error(`保存失败: ${errorMsg}`);
        } else if (status === 500) {
          message.error('服务器内部错误，请稍后再试');
        } else {
          message.error(`保存失败 (${status}): ${data?.message || data?.error || '未知错误'}`);
        }
      } else if (error.request) {
        message.error('网络连接失败，请检查网络后重试');
      } else {
        message.error(`保存失败: ${error.message || '未知错误'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理性格变更
  const handlePersonalityChange = (value: string) => {
    console.log('性格变更:', value, '类型:', typeof value);
    updateParam('personality', value);
  };

  // 处理身份变更
  const handleIdentityChange = (value: string) => {
    console.log('身份变更:', value, '类型:', typeof value);
    updateParam('identity', value);
  };

  return (
    <Content className="character-creation-container">
      <Row gutter={[24, 24]}>
        {/* 左侧: 参数设置与控制面板 */}
        <Col xs={24} sm={24} md={14} lg={16} xl={14}>
          <Card title="角色创建" className="creation-card">
            <div className="creation-form">
              <div className="form-section">
                <h3>基础生成</h3>
                <Form layout="vertical">
                  <Form.Item label="角色描述 (可选)">
                    <Input.TextArea
                      placeholder="输入角色种族、动漫名字或关键词，例如：'精灵'、'我的英雄学院风格'"
                      value={params.prompt}
                      onChange={e => updateParam('prompt', e.target.value)}
                      rows={3}
                      className="prompt-input"
                    />
                  </Form.Item>
                  
                  <Form.Item label="种族">
                    <Select
                      value={params.race}
                      onChange={value => updateParam('race', value)}
                      style={{ width: '100%' }}
                    >
                      {raceOptions.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="性别">
                        <Select
                          value={params.gender}
                          onChange={value => updateParam('gender', value)}
                          style={{ width: '100%' }}
                        >
                          {genderOptions.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label={ageLabel}>
                        <Slider
                          min={10}
                          max={80}
                          value={displayAge}
                          onChange={handleAgeChange}
                          marks={{
                            10: '10岁',
                            18: '18岁',
                            25: '25岁',
                            40: '40岁',
                            60: '60岁',
                            80: '80岁'
                          }}
                          tooltip={{ formatter: ageTooltipFormatter }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Button
                    type="primary" 
                    icon={<RocketOutlined />} 
                    loading={loading}
                    onClick={generateCharacter}
                    className="generate-btn"
                  >
                    抽卡生成
                  </Button>
                </Form>
              </div>
              
              <Divider dashed />
              
              <div className="form-section">
                <h3>参数调整</h3>
                <Form layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="发型">
                        <Select 
                          value={params.hairStyle} 
                          onChange={value => updateParam('hairStyle', value)}
                        >
                          {hairStyleOptions.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="发色">
                        <Select 
                          value={params.hairColor} 
                          onChange={value => updateParam('hairColor', value)}
                        >
                          {hairColorOptions.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="瞳色">
                        <Select 
                          value={params.eyeColor} 
                          onChange={value => updateParam('eyeColor', value)}
                        >
                          {eyeColorOptions.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="表情">
                        <Select 
                          value={params.expression} 
                          onChange={value => updateParam('expression', value)}
                        >
                          {expressionOptions.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item label="穿搭风格">
                    <Select 
                      value={params.outfit} 
                      onChange={value => updateParam('outfit', value)}
                    >
                      {outfitOptions.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  <Form.Item label="身材比例 (1-10)">
                    <Slider
                      min={1}
                      max={10}
                      value={params.bodyType}
                      onChange={value => updateParam('bodyType', value)}
                      marks={{
                        1: '娇小',
                        5: '标准',
                        10: '丰满'
                      }}
                    />
                  </Form.Item>
                </Form>
              </div>
              
              <Divider dashed />
              
              <div className="form-section">
                <h3>性格与身份</h3>
                <PersonalityIdentitySelector
                  personality={params.personality}
                  identity={params.identity}
                  onPersonalityChange={handlePersonalityChange}
                  onIdentityChange={handleIdentityChange}
                />
              </div>
            </div>
          </Card>
        </Col>
        
        {/* 右侧: 图像预览与保存 */}
        <Col xs={24} sm={24} md={10} lg={8} xl={10}>
          <Card title="角色预览" className="preview-card">
            <div className="preview-container">
              {loading ? (
                <div className="loading-container">
                  <Spin tip="正在生成角色..." size="large" />
                  <p>AI正在努力创作中，请稍候...</p>
                </div>
              ) : characterImage ? (
                <img 
                  src={characterImage} 
                  alt="Generated Character" 
                  className="character-image" 
                />
              ) : (
                <div className="empty-preview">
                  <p>点击"抽卡生成"按钮创建你的角色</p>
                </div>
              )}
            </div>
            
            <div className="action-buttons">
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={openSaveModal}
                disabled={!characterImage || loading}
                className="save-btn"
              >
                保存角色
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={generateCharacter}
                disabled={loading}
              >
                重新生成
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
      
      {/* 保存角色模态框 */}
      <Modal
        title="保存角色"
        open={saveModalVisible}
        onOk={saveCharacter}
        onCancel={() => setSaveModalVisible(false)}
        confirmLoading={loading}
      >
        <Form layout="vertical">
          <Form.Item
            label="角色名称"
            required
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input
              placeholder="给你的角色取个名字"
              value={characterName}
              onChange={e => setCharacterName(e.target.value)}
            />
          </Form.Item>

          <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
            <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
              💡 角色将自动保存到您的个人空间，并在主页展示供其他用户发现和互动
            </p>
          </div>
        </Form>
      </Modal>
    </Content>
  );
};

/**
 * 角色创建页面组件 - 使用App组件包装以提供message context
 */
const CharacterCreationPage: React.FC = () => {
  return (
    <App>
      <CharacterCreationPageContent />
    </App>
  );
};

export default CharacterCreationPage;
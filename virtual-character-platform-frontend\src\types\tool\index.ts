import type { LobeChatPluginManifest, LobePluginType } from '@lobehub/chat-plugin-sdk';

import type { CustomPluginParams } from './plugin';
import type { LobeToolType } from './tool';

export interface LobeTool {
  customParams?: CustomPluginParams | null;
  identifier: string;
  manifest?: LobeChatPluginManifest | null;
  settings?: any;
  type: LobeToolType;
}

export type LobeToolRenderType = LobePluginType | 'builtin';

export * from './builtin';

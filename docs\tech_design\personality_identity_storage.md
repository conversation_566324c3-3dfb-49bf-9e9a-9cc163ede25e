# 角色性格与身份存储策略

本文档详细说明了虚拟角色平台中角色性格(`personality`)和身份(`identity`)字段的存储策略，包括MVP阶段的实现方案和未来可能的扩展方向。

## MVP阶段存储方案

在MVP阶段，我们采用简单直接的方式存储角色的性格和身份信息：

### 数据库Schema

```sql
CREATE TABLE characters (
    -- 其他字段省略
    personality VARCHAR(255) NOT NULL,  -- 存储性格字符串
    identity VARCHAR(255) NOT NULL,     -- 存储身份字符串
    -- 其他字段省略
);
```

### 预定义值列表

为确保数据一致性，我们在代码中定义了有效的性格和身份列表：

```python
# 在core/models/characters.py中定义
VALID_PERSONALITIES = [
    "傲娇", "病娇", "元气", "沉稳", "冷酷", "温柔", "活泼", 
    "腼腆", "高冷", "毒舌", "健气系", "哥哥系", "姐姐系"
]

VALID_IDENTITIES = [
    "高中生", "大学生", "偶像", "虚拟歌姬", "咖啡店店员", "魔法使", 
    "女仆", "赛博朋克侦探", "异世界公主", "游戏NPC", "虚拟心理咨询师"
]
```

### 数据验证

在模型层面实现验证逻辑，确保输入值符合预定义列表：

```python
from django.core.exceptions import ValidationError

def validate_personality(value):
    if value not in VALID_PERSONALITIES:
        raise ValidationError(f"无效的性格类型: {value}")
    return value
    
def validate_identity(value):
    if value not in VALID_IDENTITIES:
        raise ValidationError(f"无效的身份类型: {value}")
    return value

class Character(models.Model):
    # 其他字段省略
    personality = models.CharField(
        max_length=255,
        validators=[validate_personality],
        verbose_name="性格"
    )
    identity = models.CharField(
        max_length=255,
        validators=[validate_identity],
        verbose_name="身份"
    )
    # 其他字段省略
```

### 前端处理

前端展示时，将直接使用字符串值，并在表单中使用下拉选择框限制用户输入：

```javascript
// 前端代码示例
const personalityOptions = [
  { value: "傲娇", label: "傲娇" },
  { value: "元气", label: "元气" },
  // 其他选项...
];

const identityOptions = [
  { value: "高中生", label: "高中生" },
  { value: "魔法使", label: "魔法使" },
  // 其他选项...
];

// 在表单中使用
<Select 
  options={personalityOptions} 
  value={character.personality}
  onChange={handlePersonalityChange}
/>
```

## 未来扩展方案

随着平台发展，我们可能需要更复杂的性格和身份管理机制。以下是未来可能的扩展方案：

### 独立字典表设计

```sql
-- 性格字典表
CREATE TABLE personality_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,  -- 性格名称（如"傲娇"）
    description TEXT,                   -- 性格描述
    prompt_template TEXT,               -- 该性格对应的AI提示词模板
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 身份字典表
CREATE TABLE identity_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,  -- 身份名称（如"高中生"）
    description TEXT,                   -- 身份描述
    prompt_template TEXT,               -- 该身份对应的AI提示词模板
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 修改characters表，将字符串字段改为外键引用
ALTER TABLE characters
DROP COLUMN personality,
ADD COLUMN personality_id INTEGER REFERENCES personality_types(id);

ALTER TABLE characters
DROP COLUMN identity,
ADD COLUMN identity_id INTEGER REFERENCES identity_types(id);
```

### 多对多关系（更高级的扩展）

如果未来需要支持一个角色拥有多种性格特质，可以进一步扩展为多对多关系：

```sql
-- 角色-性格关联表
CREATE TABLE character_personalities (
    character_id INTEGER REFERENCES characters(id),
    personality_id INTEGER REFERENCES personality_types(id),
    strength FLOAT DEFAULT 1.0,  -- 该性格特质的强度（0-1）
    PRIMARY KEY (character_id, personality_id)
);

-- 角色-身份关联表
CREATE TABLE character_identities (
    character_id INTEGER REFERENCES characters(id),
    identity_id INTEGER REFERENCES identity_types(id),
    is_primary BOOLEAN DEFAULT FALSE,  -- 是否为主要身份
    PRIMARY KEY (character_id, identity_id)
);
```

### 数据迁移计划

当从简单字符串模式迁移到字典表模式时，我们将：

1. 创建字典表并预填充所有现有的性格和身份值
2. 编写数据迁移脚本，将字符串值映射到对应的ID
3. 更新应用代码，适应新的数据结构
4. 为旧API提供兼容层，确保旧版客户端仍能正常工作

```python
# 迁移脚本示例（Django迁移）
def migrate_personality_identity(apps, schema_editor):
    Character = apps.get_model('core', 'Character')
    PersonalityType = apps.get_model('core', 'PersonalityType')
    IdentityType = apps.get_model('core', 'IdentityType')
    
    # 创建字典表数据
    personality_map = {}
    identity_map = {}
    
    for personality in set(Character.objects.values_list('personality', flat=True)):
        p_obj = PersonalityType.objects.create(name=personality)
        personality_map[personality] = p_obj.id
        
    for identity in set(Character.objects.values_list('identity', flat=True)):
        i_obj = IdentityType.objects.create(name=identity)
        identity_map[identity] = i_obj.id
    
    # 更新角色表数据
    for character in Character.objects.all():
        character.personality_id = personality_map[character.personality]
        character.identity_id = identity_map[character.identity]
        character.save()
```

## 优缺点分析

### 字符串存储方式（MVP阶段）

**优点：**
- 实现简单，数据结构直观
- 查询和过滤操作简单
- 无需多表关联，性能好

**缺点：**
- 缺乏对性格/身份的详细描述
- 难以支持多语言
- 可能导致数据不一致（如拼写错误）
- 难以支持一个角色拥有多种性格特质

### 字典表存储方式（未来扩展）

**优点：**
- 数据一致性更好
- 可以添加更多元数据（描述、图标、提示词模板等）
- 支持多语言
- 可扩展为多对多关系
- 便于后台管理和新增选项

**缺点：**
- 实现复杂度增加
- 查询需要表连接，性能略有下降
- 迁移现有数据需要额外工作

## 结论

对于MVP阶段，使用字符串存储方式足以满足基本需求，同时保持实现简单。未来随着平台功能扩展，可根据实际需求逐步引入字典表方案，增强性格和身份功能的灵活性和可管理性。 
{"apiKeyMiss": "The OpenAI API Key is missing. Please add your custom OpenAI API Key.", "dancePlayError": "Failed to play the dance file, please try again later.", "error": "Error", "errorTip": {"clearSession": "Clear session messages", "description": "The project is currently under construction, and data stability is not guaranteed. If you encounter any issues, you may try", "forgive": "We apologize for any inconvenience caused.", "or": "or", "problem": "There was a slight issue with the page...", "resetSystem": "Reset system settings"}, "fileUploadError": "File upload failed, please try again later.", "formValidationFailed": "Form validation failed:", "goBack": "Return to Homepage", "notFound": {"title": "Page Not Found", "desc": "Sorry, the page you are looking for does not exist.", "check": "Please check the URL and try again.", "backHome": "Back to Home"}, "openaiError": "OpenAI API error, please check if the OpenAI API Key and Endpoint are correct.", "reload": "Reload", "response": {"400": "Sorry, the server does not understand your request. Please check if your request parameters are correct.", "401": "Sorry, the server has denied your request, possibly due to insufficient permissions or lack of valid authentication.", "403": "Sorry, the server has denied your request. You do not have permission to access this content.", "404": "Sorry, the server could not find the page or resource you requested. Please check if your URL is correct.", "405": "Sorry, the server does not support the request method you are using. Please check if your request method is correct.", "406": "Sorry, the server cannot fulfill the request based on the characteristics of the content you requested.", "407": "Sorry, you need to authenticate with a proxy before you can continue with this request.", "408": "Sorry, the server timed out while waiting for the request. Please check your network connection and try again.", "409": "Sorry, there is a conflict with the request that cannot be processed, possibly due to the resource state being incompatible with the request.", "410": "Sorry, the resource you requested has been permanently removed and cannot be found.", "411": "Sorry, the server cannot process requests that do not contain a valid content length.", "412": "Sorry, your request did not meet the conditions set by the server and cannot be completed.", "413": "Sorry, your request data is too large for the server to process.", "414": "Sorry, the URI of your request is too long for the server to process.", "415": "Sorry, the server cannot process the media format attached to the request.", "416": "Sorry, the server cannot satisfy the range you requested.", "417": "Sorry, the server cannot meet your expectations.", "422": "Sorry, your request format is correct, but it cannot be responded to due to semantic errors.", "423": "Sorry, the resource you requested is locked.", "424": "Sorry, the current request cannot be completed due to a previous request failure.", "426": "Sorry, the server requires your client to upgrade to a higher protocol version.", "428": "Sorry, the server requires prerequisites, asking for your request to include the correct conditional headers.", "429": "Sorry, you have made too many requests. The server is a bit overwhelmed, please try again later.", "431": "Sorry, your request header fields are too large for the server to process.", "451": "Sorry, the server refuses to provide this resource for legal reasons.", "500": "Sorry, the server seems to be experiencing some difficulties and cannot complete your request at this time. Please try again later.", "501": "Sorry, the server does not yet know how to handle this request. Please check if your operation is correct.", "502": "Sorry, the server seems to be lost and cannot provide service at this time. Please try again later.", "503": "Sorry, the server is currently unable to process your request, possibly due to overload or ongoing maintenance. Please try again later.", "504": "Sorry, the server did not receive a response from the upstream server in time. Please try again later.", "505": "Sorry, the server does not support the HTTP version you are using. Please update and try again.", "506": "Sorry, there is a configuration issue with the server. Please contact the administrator for resolution.", "507": "Sorry, the server has insufficient storage space to process your request. Please try again later.", "509": "Sorry, the server's bandwidth has been exhausted. Please try again later.", "510": "Sorry, the server does not support the requested extension functionality. Please contact the administrator.", "524": "Sorry, the server timed out while waiting for a response, possibly due to a slow response. Please try again later.", "AgentRuntimeError": "Lobe AI Runtime execution error. Please troubleshoot or retry based on the information below.", "FreePlanLimit": "You are currently a free user and cannot use this feature. Please upgrade to a paid plan to continue using it.", "InvalidAccessCode": "The password is incorrect or empty. Please enter the correct access password or add a custom API Key.", "InvalidBedrockCredentials": "Bedrock authentication failed. Please check the AccessKeyId/SecretAccessKey and retry.", "InvalidClerkUser": "Sorry, you are currently not logged in. Please log in or register an account to continue.", "InvalidGithubToken": "Github PAT is incorrect or empty. Please check the Github PAT and retry.", "InvalidOllamaArgs": "Ollama configuration is incorrect. Please check the Ollama configuration and retry.", "InvalidProviderAPIKey": "{{provider}} API Key is incorrect or empty. Please check the {{provider}} API Key and retry.", "LocationNotSupportError": "Sorry, the model service is not supported in your region, possibly due to regional restrictions or the service not being enabled. Please confirm if this service is supported in your current region, or try switching to another region and retry.", "OllamaBizError": "An error occurred while requesting the Ollama service. Please troubleshoot or retry based on the information below.", "OllamaServiceUnavailable": "Ollama service connection failed. Please check if Ollama is running normally or if the cross-origin configuration for Ollama is set correctly.", "PermissionDenied": "Sorry, you do not have permission to access this service. Please check if your key has access permissions.", "PluginApiNotFound": "Sorry, the API does not exist in the plugin's manifest. Please check if your request method matches the plugin manifest API.", "PluginApiParamsError": "Sorry, the validation of the plugin's request parameters failed. Please check if the parameters match the API description.", "PluginFailToTransformArguments": "Sorry, the plugin call parameter parsing failed. Please try regenerating the assistant message or retrying after switching to a more capable AI model.", "PluginGatewayError": "Sorry, there was an error with the plugin gateway. Please check if the plugin gateway configuration is correct.", "PluginManifestInvalid": "Sorry, the validation of the plugin's manifest failed. Please check if the manifest format is correct.", "PluginManifestNotFound": "Sorry, the server could not find the plugin's manifest (manifest.json). Please check if the plugin description file address is correct.", "PluginMarketIndexInvalid": "Sorry, the plugin index validation failed. Please check if the index file format is correct.", "PluginMarketIndexNotFound": "Sorry, the server could not find the plugin index. Please check if the index address is correct.", "PluginMetaInvalid": "Sorry, the validation of the plugin's metadata failed. Please check if the plugin metadata format is correct.", "PluginMetaNotFound": "Sorry, the plugin could not be found in the index. Please check the plugin's configuration information in the index.", "PluginOpenApiInitError": "Sorry, the OpenAPI client initialization failed. Please check if the OpenAPI configuration information is correct.", "PluginServerError": "The plugin server returned an error. Please check your plugin description file, plugin configuration, or server implementation based on the error information below.", "PluginSettingsInvalid": "The plugin needs to be configured correctly before it can be used. Please check if your configuration is correct.", "ProviderBizError": "An error occurred while requesting the {{provider}} service. Please troubleshoot or retry based on the information below.", "QuotaLimitReached": "Sorry, the current token usage or request count has reached the quota limit for this key. Please increase the quota for this key or try again later.", "StreamChunkError": "There was an error parsing the message chunk of the streaming request. Please check if the current API interface meets the standard specifications, or contact your API provider for consultation.", "SubscriptionPlanLimit": "Your subscription quota has been exhausted and you cannot use this feature. Please upgrade to a higher plan or purchase a resource package to continue using it.", "UnknownChatFetchError": "Sorry, an unknown request error occurred. Please troubleshoot or retry based on the information below."}, "s3envError": "The S3 environment variables are not fully set. Please check your environment variables.", "serverError": "Server error, please contact the administrator.", "triggerError": "<PERSON><PERSON>", "ttsTransformFailed": "Voice conversion failed. Please check your network or enable client invocation in the settings and try again.", "unknownError": "Unknown error", "unlock": {"addProxyUrl": "Add OpenAI proxy URL (optional)", "apiKey": {"description": "Enter your {{name}} API Key to start the session", "title": "Use custom {{name}} API Key"}, "closeMessage": "Close notification", "confirm": "Confirm and retry", "oauth": {"description": "The administrator has enabled unified login authentication. Click the button below to log in and unlock the application.", "success": "Login successful", "title": "Log in to your account", "welcome": "Welcome!"}, "password": {"description": "The administrator has enabled application encryption. Enter the application password to unlock it. The password only needs to be entered once.", "placeholder": "Please enter password", "title": "Enter password to unlock the application"}, "tabs": {"apiKey": "Custom API Key", "password": "Password"}}}
import React from 'react';
import { Layout, Menu, Avatar, Dropdown, Typography } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  HomeOutlined,
  TeamOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  PlusOutlined,
  ShopOutlined
} from '@ant-design/icons';
import useAuthStore from '../store/authStore';
import { authAPI } from '../services/api';

const { Sider } = Layout;
const { Text } = Typography;

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isLoggedIn, userInfo, logout } = useAuthStore();

  // 根据当前路径确定选中的菜单项
  const getSelectedKey = () => {
    const pathname = location.pathname;
    if (pathname === '/' || pathname === '/home') return 'home';
    if (pathname === '/profile') return 'profile';
    if (pathname === '/create-character') return 'create';
    if (pathname === '/community') return 'community';
    if (pathname === '/marketplace') return 'marketplace';
    if (pathname === '/settings') return 'settings';
    // 聊天页面不在侧边栏中显示选中状态，因为它是独立页面
    return 'home';
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      logout();
      navigate('/login');
    }
  };

  // 用户下拉菜单
  const userMenu = {
    items: [
      {
        key: 'profile',
        label: '个人中心',
        icon: <UserOutlined />,
        onClick: () => navigate('/profile'),
      },
      {
        key: 'settings',
        label: '设置',
        icon: <SettingOutlined />,
        onClick: () => navigate('/settings'),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'logout',
        label: '退出登录',
        icon: <LogoutOutlined />,
        onClick: handleLogout,
      },
    ],
  };

  return (
    <Sider
      width={240}
      style={{
        background: 'var(--color-bg-base)',
        borderRight: '1px solid var(--color-border-primary)',
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        height: '100vh',
        zIndex: 100,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 品牌Logo区域 */}
      <div
        style={{
          padding: 'var(--spacing-lg)',
          borderBottom: '1px solid var(--color-border-primary)',
          background: 'var(--color-bg-container)',
        }}
      >
        <Link
          to="/"
          style={{
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: 'var(--spacing-sm)',
          }}
        >
          <div
            style={{
              width: '32px',
              height: '32px',
              background: 'var(--gradient-accent)',
              borderRadius: 'var(--border-radius-md)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--color-primary)',
              fontSize: '18px',
              fontWeight: 'bold',
            }}
          >
            🤖
          </div>
          <div>
            <Text
              strong
              style={{
                color: 'var(--color-text-primary)',
                fontSize: 'var(--font-size-lg)',
                display: 'block',
                lineHeight: 1.2,
              }}
            >
              虚拟角色
            </Text>
            <Text
              style={{
                color: 'var(--color-text-secondary)',
                fontSize: 'var(--font-size-xs)',
              }}
            >
              AI Platform
            </Text>
          </div>
        </Link>
      </div>

      {/* 导航菜单区域 */}
      <div style={{ flex: 1, padding: 'var(--spacing-md) 0' }}>
        <Menu
          mode="inline"
          selectedKeys={[getSelectedKey()]}
          style={{
            background: 'transparent',
            border: 'none',
            fontSize: 'var(--font-size-sm)',
          }}
          items={[
            {
              key: 'home',
              icon: <HomeOutlined />,
              label: <Link to="/">首页</Link>,
            },
            {
              key: 'profile',
              icon: <UserOutlined />,
              label: <Link to="/profile">我的角色</Link>,
            },
            {
              key: 'create',
              icon: <PlusOutlined />,
              label: <Link to="/create-character">创建角色</Link>,
            },
            {
              key: 'community',
              icon: <TeamOutlined />,
              label: <Link to="/community">社区</Link>,
            },
            {
              key: 'marketplace',
              icon: <ShopOutlined />,
              label: <Link to="/marketplace">商场</Link>,
            },
            {
              key: 'settings',
              icon: <SettingOutlined />,
              label: <Link to="/settings">设置</Link>,
            },
          ]}
        />
      </div>

      {/* 用户信息区域 */}
      <div
        style={{
          padding: 'var(--spacing-md)',
          borderTop: '1px solid var(--color-border-primary)',
          background: 'var(--color-bg-container)',
        }}
      >


        {isLoggedIn && userInfo ? (
          <Dropdown menu={userMenu} placement="topRight" trigger={['click']}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 'var(--spacing-sm)',
                padding: 'var(--spacing-sm)',
                borderRadius: 'var(--border-radius-md)',
                cursor: 'pointer',
                transition: 'var(--transition-fast)',
                background: 'transparent',
              }}
              className="user-info-hover"
            >
              <Avatar
                icon={<UserOutlined />}
                size="small"
                style={{
                  background: 'var(--color-primary)',
                  flexShrink: 0,
                }}
              />
              <div style={{ flex: 1, minWidth: 0 }}>
                <Text
                  strong
                  style={{
                    color: 'var(--color-text-primary)',
                    fontSize: 'var(--font-size-sm)',
                    display: 'block',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {userInfo.username}
                </Text>
                <Text
                  style={{
                    color: 'var(--color-text-tertiary)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  在线
                </Text>
              </div>
            </div>
          </Dropdown>
        ) : (
          <div style={{ textAlign: 'center' }}>
            <Text style={{ color: 'var(--color-text-secondary)' }}>未登录</Text>
          </div>
        )}
      </div>
    </Sider>
  );
};

export default Sidebar; 
import React from 'react';
import { Form, Input, Select, Switch, Button, Space, Divider, Upload, message } from 'antd';
import { 
  SunOutlined, 
  GlobalOutlined, 
  BellOutlined, 
  ShieldCheckOutlined,
  UserOutlined,
  UploadOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import { useSettingStore } from '../../../store/setting';
import { configSelectors } from '../../../store/setting/selectors/config';
import ClearCache from '../../../features/Actions/ClearCache';
import ClearSession from '../../../features/Actions/ClearSession';
import ResetConfig from '../../../features/Actions/ResetConfig';
import UserAvatar from '../../../features/Actions/UserAvatar';

const { Option } = Select;

interface CommonSettingsProps {
  onSave: () => void;
  loading: boolean;
}

const CommonSettings: React.FC<CommonSettingsProps> = ({ onSave, loading }) => {
  const { t } = useTranslation('settings');
  const [form] = Form.useForm();
  
  // 从store获取设置和方法
  const { 
    config,
    setAvatar,
    setNickName,
    setPrimaryColor,
    setNeutralColor,
    setBackgroundEffect,
    switchLocale,
    updateNotifications,
    updatePrivacy,
    updateAccessibility
  } = useSettingStore();
  
  // 使用选择器获取当前设置
  const notifications = useSettingStore(configSelectors.currentNotificationConfig);
  const privacy = useSettingStore(configSelectors.currentPrivacyConfig);
  const accessibility = useSettingStore(configSelectors.currentAccessibilityConfig);
  
  // 处理表单提交
  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        // 更新通知设置
        if (values.notifications) {
          updateNotifications(values.notifications);
        }
        
        // 更新隐私设置
        if (values.privacy) {
          updatePrivacy(values.privacy);
        }
        
        // 更新无障碍设置
        if (values.accessibility) {
          updateAccessibility(values.accessibility);
        }
        
        // 更新用户信息
        if (values.nickName) {
          setNickName(values.nickName);
        }
        
        // 更新主题设置
        if (values.backgroundEffect) {
          setBackgroundEffect(values.backgroundEffect);
        }
        
        // 更新语言设置
        if (values.locale) {
          switchLocale(values.locale);
        }
        
        // 调用保存回调
        onSave();
      })
      .catch(error => {
        console.error('表单验证失败:', error);
      });
  };
  
  // 处理头像上传
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      // 获取上传的图片URL
      const imageUrl = info.file.response.url || URL.createObjectURL(info.file.originFileObj);
      setAvatar(imageUrl);
      message.success('头像上传成功');
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };
  
  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        nickName: config.nickName,
        backgroundEffect: config.backgroundEffect,
        locale: config.locale,
        notifications,
        privacy,
        accessibility
      }}
    >
      {/* 用户信息设置 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <UserOutlined />
          {t('common.userInfo', '用户信息')}
        </div>
        
        <Form.Item
          name="nickName"
          label={t('common.nickName', '昵称')}
          className="settings-form-item"
        >
          <Input placeholder={t('common.nickNamePlaceholder', '请输入您的昵称')} />
        </Form.Item>
        
        <Form.Item
          label={t('common.avatar', '头像')}
          className="settings-form-item"
        >
          <Space align="start">
            <UserAvatar />
            <Upload
              name="avatar"
              showUploadList={false}
              action="/api/upload/avatar"
              onChange={handleAvatarUpload}
            >
              <Button icon={<UploadOutlined />}>{t('common.uploadAvatar', '上传头像')}</Button>
            </Upload>
          </Space>
        </Form.Item>
      </div>
      
      <Divider />
      
      {/* 外观设置 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <SunOutlined />
          {t('common.appearance', '外观设置')}
        </div>
        
        <Form.Item
          name="backgroundEffect"
          label={t('common.backgroundEffect', '背景效果')}
          className="settings-form-item"
        >
          <Select>
            <Option value="glow">{t('common.backgroundEffect.glow', '光晕')}</Option>
            <Option value="none">{t('common.backgroundEffect.none', '无')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name={['accessibility', 'fontSize']}
          label={t('common.fontSize', '字体大小')}
          className="settings-form-item"
        >
          <Select>
            <Option value="small">{t('common.fontSize.small', '小')}</Option>
            <Option value="medium">{t('common.fontSize.medium', '中')}</Option>
            <Option value="large">{t('common.fontSize.large', '大')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name={['accessibility', 'highContrast']}
          label={t('common.highContrast', '高对比度模式')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['accessibility', 'reduceMotion']}
          label={t('common.reduceMotion', '减少动画效果')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
      </div>
      
      <Divider />
      
      {/* 语言设置 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <GlobalOutlined />
          {t('common.language', '语言设置')}
        </div>
        
        <Form.Item
          name="locale"
          label={t('common.language', '语言')}
          className="settings-form-item"
        >
          <Select>
            <Option value="zh-CN">简体中文</Option>
            <Option value="en-US">English</Option>
            <Option value="auto">{t('common.language.auto', '跟随系统')}</Option>
          </Select>
        </Form.Item>
      </div>
      
      <Divider />
      
      {/* 通知设置 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <BellOutlined />
          {t('common.notifications', '通知设置')}
        </div>
        
        <Form.Item
          name={['notifications', 'email']}
          label={t('common.notifications.email', '邮件通知')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['notifications', 'push']}
          label={t('common.notifications.push', '推送通知')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['notifications', 'chat']}
          label={t('common.notifications.chat', '聊天通知')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['notifications', 'system']}
          label={t('common.notifications.system', '系统通知')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
      </div>
      
      <Divider />
      
      {/* 隐私设置 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <ShieldCheckOutlined />
          {t('common.privacy', '隐私设置')}
        </div>
        
        <Form.Item
          name={['privacy', 'profilePublic']}
          label={t('common.privacy.profilePublic', '个人资料公开')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['privacy', 'charactersPublic']}
          label={t('common.privacy.charactersPublic', '角色公开')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name={['privacy', 'chatHistory']}
          label={t('common.privacy.chatHistory', '保存聊天记录')}
          valuePropName="checked"
          className="settings-form-item"
        >
          <Switch />
        </Form.Item>
      </div>
      
      <Divider />
      
      {/* 系统操作 */}
      <div className="settings-section">
        <div className="settings-section-title">
          <ExclamationCircleOutlined />
          {t('common.systemOperations', '系统操作')}
        </div>
        
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Space wrap>
            <ClearCache type="default" />
            <ClearSession type="default" />
            <ResetConfig type="default" />
          </Space>
        </Space>
      </div>
      
      {/* 保存按钮 */}
      <div className="settings-actions">
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          {t('common.saveSettings', '保存设置')}
        </Button>
      </div>
    </Form>
  );
};

export default CommonSettings;

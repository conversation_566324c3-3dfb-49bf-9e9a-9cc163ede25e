import React from 'react';
import { Button, Tooltip } from 'antd';
import { 
  AudioOutlined, 
  AudioMutedOutlined, 
  PhoneOutlined, 
  SettingOutlined 
} from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

interface OperationProps {
  isRecording: boolean;
  onToggleRecord: () => void;
  onCallOff: () => void;
  onSettings: () => void;
  speechSupported: boolean;
}

const Operation: React.FC<OperationProps> = ({
  isRecording,
  onToggleRecord,
  onCallOff,
  onSettings,
  speechSupported
}) => {
  return (
    <Flexbox gap={24} horizontal align="center" className="camera-operation">
      {/* 挂断按钮 */}
      <Tooltip title="切换到聊天模式">
        <Button
          type="primary"
          danger
          shape="circle"
          size="large"
          icon={<PhoneOutlined />}
          onClick={onCallOff}
          className="operation-button call-off-button"
        />
      </Tooltip>

      {/* 录音按钮 */}
      <Tooltip title={speechSupported ? (isRecording ? "停止录音" : "开始录音") : "浏览器不支持语音识别"}>
        <Button
          type={isRecording ? "primary" : "default"}
          shape="circle"
          size="large"
          icon={isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
          onClick={onToggleRecord}
          disabled={!speechSupported}
          loading={isRecording}
          className={`operation-button record-button ${isRecording ? 'recording' : ''}`}
        />
      </Tooltip>

      {/* 设置按钮 */}
      <Tooltip title="设置">
        <Button
          type="default"
          shape="circle"
          size="large"
          icon={<SettingOutlined />}
          onClick={onSettings}
          className="operation-button settings-button"
        />
      </Tooltip>
    </Flexbox>
  );
};

export default Operation;

{"apiKeyMiss": "Ключ API OpenAI пуст, пожалуйста, добавьте свой собственный ключ API OpenAI", "dancePlayError": "Ошибка воспроизведения файла танца, пожалуйста, попробуйте позже", "error": "Ошибка", "errorTip": {"clearSession": "Очистить сообщения сессии", "description": "Проект в настоящее время находится в стадии разработки, стабильность данных не гарантируется. Если возникнут проблемы, вы можете попробовать", "forgive": "приносим извинения за причиненные неудобства", "or": "или", "problem": "На странице возникла небольшая проблема...", "resetSystem": "Сбросить настройки системы"}, "fileUploadError": "Ошибка загрузки файла, пожалуйста, попробуйте позже", "formValidationFailed": "Ошибка валидации формы:", "goBack": "Вернуться на главную страницу", "openaiError": "Ошибка OpenAI API, пожалуйста, проверьте правильность ключа API и конечной точки OpenAI", "reload": "Перезагрузить", "response": {"400": "Извините, сервер не понимает ваш запрос. Пожалуйста, убедитесь, что параметры вашего запроса правильные.", "401": "Извините, сервер отклонил ваш запрос, возможно, из-за недостатка прав или отсутствия действительной аутентификации.", "403": "Извините, сервер отклонил ваш запрос, у вас нет прав доступа к этому содержимому.", "404": "Извините, сервер не может найти страницу или ресурс, который вы запрашиваете. Пожалуйста, убедитесь, что ваш URL правильный.", "405": "Извините, сервер не поддерживает метод запроса, который вы используете. Пожалуйста, убедитесь, что ваш метод запроса правильный.", "406": "Извините, сервер не может выполнить запрос в соответствии с характеристиками содержимого, которые вы запросили.", "407": "Извините, вам необходимо пройти аутентификацию прокси-сервера, прежде чем продолжить этот запрос.", "408": "Извините, сервер истек по времени ожидания запроса. Пожалуйста, проверьте ваше сетевое соединение и попробуйте снова.", "409": "Извините, запрос содержит конфликт и не может быть обработан, возможно, из-за несовместимости состояния ресурса с запросом.", "410": "Извините, запрашиваемый вами ресурс был навсегда удален и не может быть найден.", "411": "Извините, сервер не может обработать запрос без действительной длины содержимого.", "412": "Извините, ваш запрос не соответствует условиям на стороне сервера и не может быть выполнен.", "413": "Извините, объем данных вашего запроса слишком велик, сервер не может его обработать.", "414": "Извините, URI вашего запроса слишком длинный, сервер не может его обработать.", "415": "Извините, сервер не может обработать медиаформат, прикрепленный к запросу.", "416": "Извините, сервер не может удовлетворить диапазон вашего запроса.", "417": "Извините, сервер не может удовлетворить ваши ожидания.", "422": "Извините, формат вашего запроса правильный, но из-за семантической ошибки не может быть обработан.", "423": "Извините, запрашиваемый вами ресурс заблокирован.", "424": "Извините, предыдущий запрос не удался, что привело к невозможности выполнения текущего запроса.", "426": "Извините, сервер требует, чтобы ваш клиент обновился до более высокой версии протокола.", "428": "Извините, сервер требует предварительных условий, чтобы ваш запрос содержал правильные заголовки условий.", "429": "Извините, вы сделали слишком много запросов, сервер немного устал, пожалуйста, попробуйте позже.", "431": "Извините, поля заголовка вашего запроса слишком велики, сервер не может их обработать.", "451": "Извините, по юридическим причинам сервер отказывается предоставить этот ресурс.", "500": "Извините, сервер, похоже, столкнулся с некоторыми трудностями и временно не может выполнить ваш запрос. Пожалуйста, попробуйте позже.", "501": "Извините, сервер еще не знает, как обработать этот запрос. Пожалуйста, убедитесь, что ваши действия правильные.", "502": "Извините, сервер, похоже, сбился с пути и временно не может предоставить услуги. Пожалуйста, попробуйте позже.", "503": "Извините, сервер в настоящее время не может обработать ваш запрос, возможно, из-за перегрузки или проводимого обслуживания. Пожалуйста, попробуйте позже.", "504": "Извините, сервер не дождался ответа от вышестоящего сервера. Пожалуйста, попробуйте позже.", "505": "Извините, сервер не поддерживает используемую вами версию HTTP. Пожалуйста, обновите и попробуйте снова.", "506": "Извините, возникла проблема с конфигурацией сервера. Пожалуйста, свяжитесь с администратором для решения.", "507": "Извините, на сервере недостаточно места для хранения, чтобы обработать ваш запрос. Пожалуйста, попробуйте позже.", "509": "Извините, пропускная способность сервера исчерпана. Пожалуйста, попробуйте позже.", "510": "Извините, сервер не поддерживает запрашиваемую расширенную функциональность. Пожалуйста, свяжитесь с администратором.", "524": "Извините, сервер истек по времени ожидания ответа, возможно, из-за слишком медленного ответа. Пожалуйста, попробуйте позже.", "AgentRuntimeError": "Ошибка выполнения Lobe AI Runtime. Пожалуйста, проверьте информацию ниже и повторите попытку.", "FreePlanLimit": "Вы в данный момент являетесь бесплатным пользователем и не можете использовать эту функцию. Пожалуйста, обновитесь до платного плана, чтобы продолжить использование.", "InvalidAccessCode": "Пароль неверный или пустой. Пожалуйста, введите правильный пароль доступа или добавьте пользовательский API Key.", "InvalidBedrockCredentials": "Аутентификация Bedrock не прошла. Пожалуйста, проверьте AccessKeyId/SecretAccessKey и повторите попытку.", "InvalidClerkUser": "Извините, вы еще не вошли в систему. Пожалуйста, войдите в систему или зарегистрируйтесь, чтобы продолжить.", "InvalidGithubToken": "Github PAT неверный или пустой. Пожалуйста, проверьте Github PAT и повторите попытку.", "InvalidOllamaArgs": "Конфигурация Ollama неверная. Пожалуйста, проверьте конфигурацию Ollama и повторите попытку.", "InvalidProviderAPIKey": "{{provider}} API Key неверный или пустой. Пожалуйста, проверьте {{provider}} API Key и повторите попытку.", "LocationNotSupportError": "Извините, ваш регион не поддерживает эту модель обслуживания, возможно, из-за региональных ограничений или отсутствия услуги. Пожалуйста, убедитесь, что ваш текущий регион поддерживает использование этой услуги, или попробуйте переключиться на другой регион и повторить попытку.", "OllamaBizError": "Ошибка при запросе услуги Ollama. Пожалуйста, проверьте информацию ниже и повторите попытку.", "OllamaServiceUnavailable": "Ошибка подключения к службе Ollama. Пожалуйста, проверьте, работает ли Ollama нормально, или правильно ли настроена кросс-доменная конфигурация Ollama.", "PermissionDenied": "Извините, у вас нет прав доступа к этой услуге. Пожалуйста, проверьте, есть ли у вашего ключа права доступа.", "PluginApiNotFound": "Извините, в описании плагина нет этого API. Пожалуйста, проверьте, соответствует ли ваш метод запроса API описанию плагина.", "PluginApiParamsError": "Извините, проверка входных параметров запроса плагина не прошла. Пожалуйста, проверьте, соответствуют ли входные параметры описанию API.", "PluginFailToTransformArguments": "Извините, не удалось разобрать параметры вызова плагина. Пожалуйста, попробуйте снова сгенерировать сообщение помощника или попробуйте другой AI-модель с более мощными возможностями вызова инструментов.", "PluginGatewayError": "Извините, возникла ошибка в шлюзе плагина. Пожалуйста, проверьте, правильна ли конфигурация шлюза плагина.", "PluginManifestInvalid": "Извините, проверка описания плагина не прошла. Пожалуйста, проверьте, соответствует ли формат описания стандартам.", "PluginManifestNotFound": "Извините, сервер не нашел описание плагина (manifest.json). Пожалуйста, проверьте, правильный ли адрес файла описания плагина.", "PluginMarketIndexInvalid": "Извините, проверка индекса плагинов не прошла. Пожалуйста, проверьте, соответствует ли формат файла индекса стандартам.", "PluginMarketIndexNotFound": "Извините, сервер не нашел индекс плагинов. Пожалуйста, проверьте, правильный ли адрес индекса.", "PluginMetaInvalid": "Извините, проверка метаданных этого плагина не прошла. Пожалуйста, проверьте, соответствует ли формат метаданных плагина стандартам.", "PluginMetaNotFound": "Извините, в индексе не найден этот плагин. Пожалуйста, проверьте конфигурацию плагина в индексе.", "PluginOpenApiInitError": "Извините, инициализация клиента OpenAPI не удалась. Пожалуйста, проверьте, правильна ли конфигурация OpenAPI.", "PluginServerError": "Ошибка при возврате запроса на сервер плагина. Пожалуйста, проверьте информацию об ошибке ниже и проверьте файл описания плагина, конфигурацию плагина или реализацию на стороне сервера.", "PluginSettingsInvalid": "Этот плагин необходимо правильно настроить перед использованием. Пожалуйста, проверьте, правильна ли ваша конфигурация.", "ProviderBizError": "Ошибка при запросе услуги {{provider}}. Пожалуйста, проверьте информацию ниже или повторите попытку.", "QuotaLimitReached": "Извините, текущее использование токенов или количество запросов достигло предела квоты для этого ключа. Пожалуйста, увеличьте квоту для этого ключа или попробуйте позже.", "StreamChunkError": "Ошибка разбора сообщения блока потокового запроса. Пожалуйста, проверьте, соответствует ли текущий API интерфейс стандартам, или свяжитесь с вашим поставщиком API для консультации.", "SubscriptionPlanLimit": "Ваш лимит подписки исчерпан, и вы не можете использовать эту функцию. Пожалуйста, обновитесь до более высокого плана или купите пакет ресурсов, чтобы продолжить использование.", "UnknownChatFetchError": "Извините, возникла неизвестная ошибка запроса. Пожалуйста, проверьте информацию ниже и повторите попытку."}, "s3envError": "Переменные окружения S3 не полностью настроены, пожалуйста, проверьте ваши переменные окружения", "serverError": "Ошибка сервера, пожалуйста, свяжитесь с администратором", "triggerError": "Ошибка срабатывания", "ttsTransformFailed": "Не удалось преобразовать речь, пожалуйста, проверьте соединение с сетью или попробуйте снова, включив вызов клиента в настройках.", "unknownError": "Неизвестная ошибка", "unlock": {"addProxyUrl": "Добавить адрес прокси OpenAI (необязательно)", "apiKey": {"description": "Введите ваш {{name}} API Key, чтобы начать сессию", "title": "Использовать пользовательский {{name}} API Key"}, "closeMessage": "Закрыть уведомление", "confirm": "Подтвердить и повторить попытку", "oauth": {"description": "Администрато<PERSON> включил единый вход, нажмите кнопку ниже для входа, чтобы разблокировать приложение", "success": "Успешный вход", "title": "Войти в аккаунт", "welcome": "Добро пожаловать!"}, "password": {"description": "Администратор включил шифрование приложения, введите пароль приложения, чтобы разблокировать его. Пароль нужно вводить только один раз", "placeholder": "Введите пароль", "title": "Введите пароль для разблокировки приложения"}, "tabs": {"apiKey": "Пользовательский API Key", "password": "Пароль"}}}
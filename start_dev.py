#!/usr/bin/env python3
"""
开发环境启动脚本
自动检查端口冲突并启动前后端服务
"""

import os
import sys
import time
import socket
import subprocess
import threading
from pathlib import Path

class DevStarter:
    """开发环境启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.frontend_dir = self.project_root / "virtual-character-platform-frontend"
        self.backend_port = 8000
        self.frontend_port = 5173
        
    def check_port_available(self, port: int, host: str = '127.0.0.1') -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception:
            return False
    
    def find_available_port(self, start_port: int, max_attempts: int = 10) -> int:
        """找到可用端口"""
        for i in range(max_attempts):
            port = start_port + i
            if self.check_port_available(port):
                return port
        return None
    
    def kill_port_process(self, port: int):
        """杀死占用端口的进程"""
        try:
            if sys.platform == 'win32':
                # Windows
                cmd = f'netstat -ano | findstr :{port}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                                print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                                time.sleep(1)
                                break
            else:
                # Unix/Linux/macOS
                cmd = f'lsof -ti:{port}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid:
                            subprocess.run(f'kill -9 {pid}', shell=True)
                            print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                    time.sleep(1)
        except Exception as e:
            print(f"⚠️ 无法终止端口 {port} 的进程: {e}")
    
    def start_backend(self):
        """启动后端服务"""
        print("🐍 启动Django后端服务...")
        
        # 检查后端端口
        if not self.check_port_available(self.backend_port):
            print(f"⚠️ 端口 {self.backend_port} 被占用")
            response = input("是否要终止占用进程? (y/N): ")
            if response.lower() == 'y':
                self.kill_port_process(self.backend_port)
            else:
                available_port = self.find_available_port(self.backend_port + 1)
                if available_port:
                    self.backend_port = available_port
                    print(f"🔄 使用备用端口: {self.backend_port}")
                else:
                    print("❌ 无法找到可用端口，请手动处理端口冲突")
                    return False
        
        # 启动Django服务器
        try:
            cmd = [sys.executable, "manage.py", "runserver", f"127.0.0.1:{self.backend_port}"]
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # 等待服务器启动
            print(f"⏳ 等待后端服务启动 (端口 {self.backend_port})...")
            for _ in range(30):  # 最多等待30秒
                if not self.check_port_available(self.backend_port):
                    print(f"✅ 后端服务已启动: http://127.0.0.1:{self.backend_port}")
                    return True
                time.sleep(1)
            
            print("❌ 后端服务启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动后端服务失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("⚡ 启动Vite前端服务...")
        
        if not self.frontend_dir.exists():
            print(f"❌ 前端目录不存在: {self.frontend_dir}")
            return False
        
        try:
            # 检查是否安装了依赖
            if not (self.frontend_dir / "node_modules").exists():
                print("📦 安装前端依赖...")
                subprocess.run(["npm", "install"], cwd=self.frontend_dir, check=True)
            
            # 启动前端开发服务器
            cmd = ["npm", "run", "dev"]
            self.frontend_process = subprocess.Popen(
                cmd,
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            print("✅ 前端服务启动命令已执行")
            print(f"🌐 前端服务将在可用端口启动 (通常是 5173-5180)")
            return True
            
        except Exception as e:
            print(f"❌ 启动前端服务失败: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        print("\n📊 服务状态监控 (按 Ctrl+C 停止):")
        try:
            while True:
                # 检查后端状态
                backend_status = "🟢 运行中" if not self.check_port_available(self.backend_port) else "🔴 已停止"
                
                # 检查前端状态
                frontend_status = "🟢 运行中" if not self.check_port_available(5173) else "🔴 已停止"
                
                print(f"\r后端 ({self.backend_port}): {backend_status} | 前端: {frontend_status}", end="", flush=True)
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n\n🛑 正在停止服务...")
            self.stop_services()
    
    def stop_services(self):
        """停止所有服务"""
        try:
            if hasattr(self, 'backend_process'):
                self.backend_process.terminate()
                print("✅ 后端服务已停止")
        except:
            pass
        
        try:
            if hasattr(self, 'frontend_process'):
                self.frontend_process.terminate()
                print("✅ 前端服务已停止")
        except:
            pass
    
    def start(self):
        """启动开发环境"""
        print("🚀 虚拟角色平台 - 开发环境启动器")
        print("=" * 50)
        
        # 启动后端
        if not self.start_backend():
            print("❌ 后端启动失败，退出")
            return
        
        time.sleep(2)
        
        # 启动前端
        if not self.start_frontend():
            print("❌ 前端启动失败，但后端仍在运行")
        
        time.sleep(3)
        
        print("\n🎉 开发环境启动完成!")
        print(f"📍 后端地址: http://127.0.0.1:{self.backend_port}")
        print("📍 前端地址: 请查看上方Vite输出的地址")
        print("📍 API文档: http://127.0.0.1:{}/admin/".format(self.backend_port))
        
        # 监控进程
        self.monitor_processes()

def main():
    """主函数"""
    starter = DevStarter()
    try:
        starter.start()
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == '__main__':
    main()

import React, { useState, useCallback } from 'react';
import { Button, message, Tooltip } from 'antd';
import { AudioOutlined, AudioMutedOutlined, LoadingOutlined } from '@ant-design/icons';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';

interface VoiceControlsProps {
  onVoiceInput?: (transcript: string) => void;
  onListeningChange?: (isListening: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const VoiceControls: React.FC<VoiceControlsProps> = ({
  onVoiceInput,
  onListeningChange,
  disabled = false,
  className = ''
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  // 处理语音识别结果
  const handleVoiceResult = useCallback((transcript: string, isFinal: boolean) => {
    console.log('VoiceControls: 收到语音识别结果:', transcript, '是否最终:', isFinal);

    // 临时显示中间结果用于调试
    if (!isFinal && transcript.trim()) {
      console.log('中间结果:', transcript);
      // message.info(`正在识别: ${transcript}`, 1); // 暂时注释掉，避免过多提示
    }

    if (isFinal && transcript.trim()) {
      // 重要：不显示用户输入的文字，直接处理
      console.log('最终结果:', transcript);
      message.success(`识别完成: ${transcript}`, 2); // 显示最终结果
      if (onVoiceInput) {
        onVoiceInput(transcript.trim());
      }
    }
  }, [onVoiceInput]);

  // 处理语音识别错误
  const handleVoiceError = useCallback((errorMessage: string) => {
    console.error('VoiceControls: 语音识别错误:', errorMessage);
    message.error(errorMessage, 5); // 显示5秒，让用户有时间阅读
    setIsProcessing(false);
  }, []);

  // 语音识别Hook
  const {
    isListening,
    startListening,
    stopListening,
    isSupported,
    error
  } = useSpeechRecognition({
    onResult: handleVoiceResult,
    onError: handleVoiceError,
    continuous: false,  // 单次识别，避免连续触发
    interimResults: true,  // 临时启用中间结果，用于调试
    language: 'zh-CN'
  });

  // 切换语音识别状态
  const toggleVoiceRecognition = useCallback(() => {
    if (disabled || !isSupported) return;

    if (isListening) {
      stopListening();
      setIsProcessing(false);
    } else {
      startListening();
      setIsProcessing(true);
    }
  }, [isListening, startListening, stopListening, disabled, isSupported]);

  // 监听状态变化，通知父组件
  React.useEffect(() => {
    if (onListeningChange) {
      onListeningChange(isListening);
    }

    // 当语音识别结束时，重置处理状态
    if (!isListening && isProcessing) {
      setIsProcessing(false);
    }
  }, [isListening, onListeningChange, isProcessing]);



  // 如果浏览器不支持语音识别
  if (!isSupported) {
    return (
      <Tooltip title="当前浏览器不支持语音识别功能">
        <Button 
          disabled 
          icon={<AudioMutedOutlined />}
          className={className}
        >
          语音不可用
        </Button>
      </Tooltip>
    );
  }

  // 获取按钮状态和图标
  const getButtonProps = () => {
    if (isProcessing || isListening) {
      return {
        type: 'primary' as const,
        icon: <LoadingOutlined spin />,
        text: '正在听...'
      };
    }
    
    return {
      type: 'default' as const,
      icon: <AudioOutlined />,
      text: '点击说话'
    };
  };

  const buttonProps = getButtonProps();

  return (
    <div className={`voice-controls ${className}`}>
      <Tooltip title={isListening ? "点击停止录音" : "点击开始语音输入"}>
        <Button
          type={buttonProps.type}
          icon={buttonProps.icon}
          onClick={toggleVoiceRecognition}
          disabled={disabled}
          size="large"
          className={`voice-button ${isListening ? 'listening' : ''}`}
        >
          {buttonProps.text}
        </Button>
      </Tooltip>
      
      {error && (
        <div className="voice-error" style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
          {error}
        </div>
      )}
    </div>
  );
};

// 语音状态指示器组件
interface VoiceIndicatorProps {
  isActive: boolean;
  className?: string;
}

export const VoiceIndicator: React.FC<VoiceIndicatorProps> = ({ 
  isActive, 
  className = '' 
}) => {
  return (
    <div className={`voice-indicator ${className} ${isActive ? 'active' : ''}`}>
      <div className="indicator-dot" />
      <span className="indicator-text">
        {isActive ? '正在听...' : '待机中'}
      </span>
    </div>
  );
};

// 情感状态显示组件
interface EmotionDisplayProps {
  emotion: string;
  className?: string;
}

export const EmotionDisplay: React.FC<EmotionDisplayProps> = ({ 
  emotion, 
  className = '' 
}) => {
  const getEmotionIcon = (emotion: string) => {
    switch (emotion) {
      case 'happy': return '😊';
      case 'sad': return '😢';
      case 'caring': return '🤗';
      case 'listening': return '👂';
      case 'thinking': return '🤔';
      default: return '😐';
    }
  };

  const getEmotionText = (emotion: string) => {
    switch (emotion) {
      case 'happy': return '开心';
      case 'sad': return '关怀';
      case 'caring': return '关心';
      case 'listening': return '倾听';
      case 'thinking': return '思考';
      default: return '平静';
    }
  };

  return (
    <div className={`emotion-display ${className}`}>
      <span className="emotion-icon">{getEmotionIcon(emotion)}</span>
      <span className="emotion-text">{getEmotionText(emotion)}</span>
    </div>
  );
};

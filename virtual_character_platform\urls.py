"""
URL configuration for virtual_character_platform project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.conf.urls.static import static
import os
import mimetypes

@require_http_methods(["GET"])
def api_root(request):
    """API根路径，返回API信息"""
    return JsonResponse({
        'message': '欢迎使用虚拟角色平台API',
        'version': '1.0.0',
        'endpoints': {
            'admin': '/admin/',
            'api': '/api/',
            'auth': '/api/auth/',
            'personalities': '/api/personalities/',
            'identities': '/api/identities/',
            'characters': '/api/characters/',
            'admin_api': '/api/admin/'
        },
        'documentation': 'https://docs.djangoproject.com/en/5.2/'
    })

def serve_media(request, path):
    """在开发环境中提供媒体文件"""
    if not settings.DEBUG:
        raise Http404("Media files are not served in production")

    file_path = os.path.join(settings.MEDIA_ROOT, path)

    if not os.path.exists(file_path):
        raise Http404("File not found")

    # 获取文件的MIME类型
    content_type, _ = mimetypes.guess_type(file_path)
    if content_type is None:
        content_type = 'application/octet-stream'

    # 读取文件内容
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()

        response = HttpResponse(file_content, content_type=content_type)
        response['Content-Length'] = len(file_content)
        return response
    except IOError:
        raise Http404("File could not be read")

urlpatterns = [
    path('', api_root, name='api-root'),  # 根路径
    path('admin/', admin.site.urls),
    path('api/', include('core.urls')),
]

# 在开发环境中提供媒体文件和静态文件
if settings.DEBUG:
    # 使用自定义视图提供媒体文件
    urlpatterns += [
        path('media/<path:path>', serve_media, name='serve_media'),
    ]
    # 提供静态文件
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

{"ModelSelect": {"featureTag": {"custom": "Benutzerdefiniertes Modell, die Standardeinstellungen unterstützen sowohl Funktionsaufrufe als auch visuelle Erkennung. Bitte überprüfen Sie die Verfügbarkeit dieser Funktionen basierend auf den tatsächlichen Gegebenheiten.", "file": "Dieses Modell unterstützt das Hochladen von Dateien zur Lesung und Erkennung.", "functionCall": "Dieses Modell unterstützt Funktionsaufrufe (Function Call).", "tokens": "<PERSON><PERSON> Modell unterstützt maximal {{tokens}} Tokens pro Sitzung.", "vision": "Dieses Modell unterstützt die visuelle Erkennung."}, "removed": "Dieses Modell ist nicht in der Liste. Wenn Sie die Auswahl aufheben, wird es automatisch entfernt."}, "actions": {"add": "Hinzufügen", "copy": "<PERSON><PERSON><PERSON>", "copySuccess": "Erfolgreich k<PERSON>", "del": "Löschen", "delAndRegenerate": "Löschen und neu generieren", "edit": "<PERSON><PERSON><PERSON>", "goBottom": "<PERSON>um <PERSON>e gehen", "regenerate": "<PERSON><PERSON> gene<PERSON>", "save": "Speichern", "share": "Teilen", "tts": "Sprachausgabe"}, "agentInfo": "Rolleninformationen", "agentMarket": "Charaktermarkt", "animation": {"animationList": "Animationsliste", "postureList": "Haltungsliste", "totalCount": "Insgesamt {{total}} Elemente"}, "apiKey": {"addProxy": "Fügen Sie die OpenAI-Proxy-<PERSON><PERSON><PERSON> hinzu (optional)", "closeTip": "<PERSON><PERSON><PERSON><PERSON> sch<PERSON>ßen", "confirmRetry": "Bestätigen und erneut versuchen", "proxyDocs": "Wie beantrage ich einen API-Schlüssel?", "startDesc": "Geben Sie Ihren OpenAI API-Schlüssel ein, um das Gespräch zu beginnen. Die Anwendung wird Ihren API-Schlüssel nicht speichern.", "startTitle": "Benutzerdefinierter API-Schlüssel"}, "background": {"backgroundList": "Hi<PERSON>grundlist<PERSON>", "totalCount": "Insgesamt {{total}} Elemente"}, "callOff": "<PERSON><PERSON><PERSON>", "camera": "Videoanruf", "chat": "Cha<PERSON>", "chatList": "<PERSON><PERSON><PERSON><PERSON>", "clear": {"action": "Kontext löschen", "alert": "Möchten Sie die Verlaufsmeldungen wirklich löschen?", "tip": "Diese Aktion ist irreversibel, bitte vorsichtig sein"}, "danceList": "Tanzliste", "danceMarket": "Tanzmarkt", "delSession": "Sitzung löschen", "delSessionAlert": "Möchten Sie den Chat wirklich löschen? Nach dem Löschen kann er nicht wiederhergestellt werden, bitte seien Si<PERSON> vorsichtig!", "editRole": {"action": "<PERSON><PERSON> bearbeiten"}, "enableHistoryCount": {"alias": "<PERSON><PERSON>rä<PERSON>", "limited": "Enthält nur {{number}} Sitzungsnachrichten", "setlimited": "Verwendete Anzahl der Verlaufsmeldungen", "title": "Einschränkung der Anzahl der Verlaufsmeldungen", "unlimited": "Keine Einschränkung der Verlaufsmeldungen"}, "info": {"background": "Hi<PERSON>grund", "chat": "Cha<PERSON>", "dance": "<PERSON><PERSON>", "motions": "Bewegungen", "posture": "<PERSON><PERSON><PERSON>", "stage": "<PERSON><PERSON><PERSON><PERSON>"}, "input": {"alert": "<PERSON><PERSON> <PERSON>ten Sie: <PERSON><PERSON>, was der Agent sagt, wird von <PERSON> gene<PERSON>t", "placeholder": "Bitte geben Sie Ihren Inhalt ein, um zu chatten", "send": "Senden", "warp": "Umbruch"}, "interactive": "interaktiv", "noDanceList": "Derzeit keine Wiedergabeliste verfügbar. Sie können im Markt die Tänze abonnieren, die Ihnen gefallen.", "noRoleList": "<PERSON><PERSON> v<PERSON>handen", "noSession": "<PERSON>ine Sitzungen verfügbar. Sie können durch + eine benutzerdefinierte Rolle erstellen oder Rollen über die Entdeckungsseite hinzufügen.", "selectModel": "<PERSON>te wählen Si<PERSON> ein Modell aus", "sessionCreate": "<PERSON><PERSON> er<PERSON>", "sessionList": "Sitzungsliste", "share": {"downloadScreenshot": "Screenshot herunterladen", "imageType": "Bildformat", "screenshot": "Screenshot", "share": "Teilen", "shareGPT": "ShareGPT teilen", "shareToGPT": "ShareGPT-Link generieren", "withBackground": "<PERSON><PERSON>run<PERSON>bil<PERSON>", "withFooter": "<PERSON><PERSON>", "withSystemRole": "Mit Assistentenrollen-Einstellungen"}, "stage": {"stageList": "Bühnenliste", "totalCount": "Insgesamt {{total}} Einträge"}, "token": {"overload": "Token überlastet", "remained": "Verbleibende Token", "tokenCount": "<PERSON><PERSON><PERSON> der Token", "useToken": "Berechnung der verbrauchten Token-<PERSON>, einsch<PERSON><PERSON>lich Nachrichten, Rolleneinstellungen und Kontext: {{usedTokens}} / {{maxValue}}", "used": "Verwendete Token"}, "toolBar": {"axes": "<PERSON><PERSON><PERSON>", "cameraControl": "Kamerasteuerung", "cameraHelper": "Kamerahilfe", "downloading": "<PERSON>l wird her<PERSON><PERSON><PERSON><PERSON><PERSON>, bitte warten...", "fullScreen": "Vollbild umschalten", "grid": "<PERSON><PERSON>", "interactiveOff": "Berührungseingabe deaktivieren", "interactiveOn": "Berührungseingabe aktivieren", "resetCamera": "<PERSON><PERSON><PERSON>", "resetToIdle": "Tanzbewegungen stoppen", "screenShot": "Foto aufnehmen"}, "tts": {"combine": "Sprachsynthese", "record": "Spracherkennung (benötigt wissenschaftliches Surfen)"}, "voiceOn": "Sprachausgabe aktivieren"}
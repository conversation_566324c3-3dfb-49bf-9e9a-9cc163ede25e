[{"id": "mattshumer/reflection-70b:free", "name": "Reflection 70B (free)", "created": **********, "description": "Reflection Llama-3.1 70B is trained with a new technique called Reflection-Tuning that teaches a LLM to detect mistakes in its reasoning and correct course.\n\nThe model was trained on synthetic data.\n\n_These are free, rate-limited endpoints for [Reflection 70B](/models/mattshumer/reflection-70b). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "mattshumer/reflection-70b", "name": "Reflection 70B", "created": **********, "description": "Reflection Llama-3.1 70B is trained with a new technique called Reflection-Tuning that teaches a LLM to detect mistakes in its reasoning and correct course.\n\nThe model was trained on synthetic data.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command-r-03-2024", "name": "Cohere: Command R (03-2024)", "created": **********, "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command-r-plus-04-2024", "name": "Cohere: Command R+ (04-2024)", "created": **********, "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command-r-plus-08-2024", "name": "Cohere: Command R+ (08-2024)", "created": **********, "description": "command-r-plus-08-2024 is an update of the [Command R+](/models/cohere/command-r-plus) with roughly 50% higher throughput and 25% lower latencies as compared to the previous Command R+ version, while keeping the hardware footprint the same.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command-r-08-2024", "name": "Cohere: Command R (08-2024)", "created": **********, "description": "command-r-08-2024 is an update of the [Command R](/models/cohere/command-r) with improved performance for multilingual retrieval-augmented generation (RAG) and tool use. More broadly, it is better at math, code and reasoning and is competitive with the previous version of the larger Command R+ model.\n\nRead the launch post [here](https://docs.cohere.com/changelog/command-gets-refreshed).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-flash-8b-1.5-exp", "name": "Google: Gemini Flash 8B 1.5 Experimental", "created": **********, "description": "Gemini 1.5 Flash 8B Experimental is an experimental, 8B parameter version of the [Gemini 1.5 Flash](/models/google/gemini-flash-1.5) model.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal\n\nNote: This model is experimental and not suited for production use-cases. It may be removed or redirected to another model in the future.", "context_length": 4000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4000000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-flash-1.5-exp", "name": "Google: Gemini Flash 1.5 Experimental", "created": **********, "description": "Gemini 1.5 Flash Experimental is an experimental version of the [Gemini 1.5 Flash](/models/google/gemini-flash-1.5) model.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal\n\nNote: This model is experimental and not suited for production use-cases. It may be removed or redirected to another model in the future.", "context_length": 4000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4000000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "sao10k/l3.1-euryale-70b", "name": "Llama 3.1 Euryale 70B v2.2", "created": **********, "description": "Euryale L3.1 70B v2.2 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.1](/models/sao10k/l3-euryale-70b).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.0000015", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 16000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "ai21/jamba-1-5-large", "name": "AI21: Jamba 1.5 Large", "created": **********, "description": "Jamba 1.5 Large is part of AI21's new family of open models, offering superior speed, efficiency, and quality.\n\nIt features a 256K effective context window, the longest among open models, enabling improved performance on tasks like document summarization and analysis.\n\nBuilt on a novel SSM-Transformer architecture, it outperforms larger models like Llama 3.1 70B on benchmarks while maintaining resource efficiency.\n\nRead their [announcement](https://www.ai21.com/blog/announcing-jamba-model-family) to learn more.", "context_length": 256000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.000008", "image": "0", "request": "0"}, "top_provider": {"context_length": 256000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "ai21/jamba-1-5-mini", "name": "AI21: Jamba 1.5 Mini", "created": **********, "description": "Jamba 1.5 Mini is the world's first production-grade Mamba-based model, combining SSM and Transformer architectures for a 256K context window and high efficiency.\n\nIt works with 9 languages and can handle various writing and analysis tasks as well as or better than similar small models.\n\nThis model uses less computer memory and works faster with longer texts than previous designs.\n\nRead their [announcement](https://www.ai21.com/blog/announcing-jamba-model-family) to learn more.", "context_length": 256000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 256000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3.5-mini-128k-instruct", "name": "Phi-3.5 Mini 128K Instruct", "created": **********, "description": "Phi-3.5 models are lightweight, state-of-the-art open models. These models were trained with Phi-3 datasets that include both synthetic data and the filtered, publicly available websites data, with a focus on high quality and reasoning-dense properties. Phi-3.5 Mini uses 3.8B parameters, and is a dense decoder-only transformer model using the same tokenizer as [Phi-3 Mini](/models/microsoft/phi-3-mini-128k-instruct).\n\nThe models underwent a rigorous enhancement process, incorporating both supervised fine-tuning, proximal policy optimization, and direct preference optimization to ensure precise instruction adherence and robust safety measures. When assessed against benchmarks that test common sense, language understanding, math, code, long context and logical reasoning, Phi-3.5 models showcased robust and state-of-the-art performance among models with less than 13 billion parameters.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/hermes-3-llama-3.1-70b", "name": "Nous: Hermes 3 70B Instruct", "created": **********, "description": "Hermes 3 is a generalist language model with many improvements over [Hermes 2](/models/nousresearch/nous-hermes-2-mistral-7b-dpo), including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 70B is a competitive, if not superior finetune of the [Llama-3.1 70B foundation model](/models/meta-llama/llama-3.1-70b-instruct), focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000004", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 12288, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/hermes-3-llama-3.1-405b", "name": "Nous: Hermes 3 405B Instruct", "created": **********, "description": "Hermes 3 is a generalist language model with many improvements over Hermes 2, including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 405B is a frontier-level, full-parameter finetune of the Llama-3.1 405B foundation model, focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.\n\nHermes 3 is competitive, if not superior, to Llama-3.1 Instruct models at general capabilities, with varying strengths and weaknesses attributable between the two.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 18000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/hermes-3-llama-3.1-405b:extended", "name": "Nous: Hermes 3 405B Instruct (extended)", "created": **********, "description": "Hermes 3 is a generalist language model with many improvements over Hermes 2, including advanced agentic capabilities, much better roleplaying, reasoning, multi-turn conversation, long context coherence, and improvements across the board.\n\nHermes 3 405B is a frontier-level, full-parameter finetune of the Llama-3.1 405B foundation model, focused on aligning LLMs to the user, with powerful steering capabilities and control given to the end user.\n\nThe Hermes 3 series builds and expands on the Hermes 2 set of capabilities, including more powerful and reliable function calling and structured output capabilities, generalist assistant capabilities, and improved code generation skills.\n\nHermes 3 is competitive, if not superior, to Llama-3.1 Instruct models at general capabilities, with varying strengths and weaknesses attributable between the two.\n\n_These are extended-context endpoints for [Hermes 3 405B Instruct](/models/nousresearch/hermes-3-llama-3.1-405b). They may have higher prices._", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3.1-sonar-huge-128k-online", "name": "Perplexity: Llama 3.1 Sonar 405B Online", "created": **********, "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance. The model is built upon the Llama 3.1 405B and has internet access.", "context_length": 127072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.000005", "completion": "0.000005", "image": "0", "request": "0.005"}, "top_provider": {"context_length": 127072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/chatgpt-4o-latest", "name": "OpenAI: ChatGPT-4o", "created": **********, "description": "Dynamic model continuously updated to the current version of [GPT-4o](/models/openai/gpt-4o) in ChatGPT. Intended for research and evaluation.\n\nNote: This model is experimental and not suited for production use-cases. It may be removed or redirected to another model in the future.", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null}, {"id": "sao10k/l3-lunaris-8b", "name": "Llama 3 8B Lunaris", "created": **********, "description": "Lunaris 8B is a versatile generalist and roleplaying model based on Llama 3. It's a strategic merge of multiple models, designed to balance creativity with improved logic and general knowledge.\n\nCreated by [Sao10k](https://huggingface.co/Sao10k), this model aims to offer an improved experience over Stheno v3.2, with enhanced creativity and logical reasoning.\n\nFor best results, use with Llama 3 Instruct context template, temperature 1.4, and min_p 0.1.", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000002", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "aetherwiing/mn-starcannon-12b", "name": "Mistral Nemo 12B Starcannon", "created": **********, "description": "Starcannon 12B is a creative roleplay and story writing model, using [nothingiisreal/mn-celeste-12b](https://openrouter.ai/models/nothingiisreal/mn-celeste-12b) as a base and [intervitens/mini-magnum-12b-v1.1](https://huggingface.co/intervitens/mini-magnum-12b-v1.1) merged in using the [TIES](https://arxiv.org/abs/2306.01708) method.\n\nAlthough more similar to Magnum overall, the model remains very creative, with a pleasant writing style. It is recommended for people wanting more variety than <PERSON>, and yet more verbose prose than <PERSON>.", "context_length": 12000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.000002", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 12000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4o-2024-08-06", "name": "OpenAI: GPT-4o (2024-08-06)", "created": **********, "description": "The 2024-08-06 version of GPT-4o offers improved performance in structured outputs, with the ability to supply a JSON schema in the respone_format. Read more [here](https://openai.com/index/introducing-structured-outputs-in-the-api/).\n\nGPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.0000025", "completion": "0.00001", "image": "0.0036125", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null}, {"id": "meta-llama/llama-3.1-405b", "name": "Meta: Llama 3.1 405B (base)", "created": **********, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This is the base 405B pre-trained version.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "none"}, "pricing": {"prompt": "0.000002", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nothingiisreal/mn-celeste-12b", "name": "Mistral Nemo 12B Celeste", "created": **********, "description": "A specialized story writing and roleplaying model based on Mistral's NeMo 12B Instruct. Fine-tuned on curated datasets including Reddit Writing Prompts and Opus Instruct 25K.\n\nThis model excels at creative writing, offering improved NSFW capabilities, with smarter and more active narration. It demonstrates remarkable versatility in both SFW and NSFW scenarios, with strong Out of Character (OOC) steering capabilities, allowing fine-tuned control over narrative direction and character behavior.\n\nCheck out the model's [HuggingFace page](https://huggingface.co/nothingiisreal/MN-12B-Celeste-V1.9) for details on what parameters and prompts work best!", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000015", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-pro-1.5-exp", "name": "Google: Gemini Pro 1.5 Experimental", "created": **********, "description": "Gemini 1.5 Pro (0827) is an experimental version of the [Gemini 1.5 Pro](/models/google/gemini-pro-1.5) model.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal\n\nNote: This model is experimental and not suited for production use-cases. It may be removed or redirected to another model in the future.", "context_length": 4000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4000000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3.1-sonar-large-128k-online", "name": "Perplexity: Llama 3.1 Sonar 70B Online", "created": **********, "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3.1-sonar-large-128k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 127072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0.005"}, "top_provider": {"context_length": 127072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3.1-sonar-large-128k-chat", "name": "Perplexity: Llama 3.1 Sonar 70B", "created": **********, "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is a normal offline LLM, but the [online version](/models/perplexity/llama-3.1-sonar-large-128k-online) of this model has Internet access.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3.1-sonar-small-128k-online", "name": "Perplexity: Llama 3.1 Sonar 8B Online", "created": **********, "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3.1-sonar-small-128k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 127072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0.005"}, "top_provider": {"context_length": 127072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3.1-sonar-small-128k-chat", "name": "Perplexity: Llama 3.1 Sonar 8B", "created": **********, "description": "Llama 3.1 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is a normal offline LLM, but the [online version](/models/perplexity/llama-3.1-sonar-small-128k-online) of this model has Internet access.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3.1-70b-instruct", "name": "Meta: Llama 3.1 70B Instruct", "created": **********, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 70B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.0000003", "completion": "0.0000003", "image": "0", "request": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3.1-8b-instruct:free", "name": "Meta: Llama 3.1 8B Instruct (free)", "created": **********, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 8B instruct-tuned version is fast and efficient.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are free, rate-limited endpoints for [Llama 3.1 8B Instruct](/models/meta-llama/llama-3.1-8b-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3.1-8b-instruct", "name": "Meta: Llama 3.1 8B Instruct", "created": **********, "description": "Meta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 8B instruct-tuned version is fast and efficient.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3.1-405b-instruct", "name": "Meta: Llama 3.1 405B Instruct", "created": **********, "description": "The highly anticipated 400B class of Llama3 is here! Clocking in at 128k context with impressive eval scores, the Meta AI team continues to push the frontier of open-source LLMs.\n\nMeta's latest class of model (Llama 3.1) launched with a variety of sizes & flavors. This 405B instruct-tuned version is optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.00000179", "completion": "0.00000179", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "cognitivecomputations/dolphin-llama-3-70b", "name": "Dolphin Llama 3 70B 🐬", "created": **********, "description": "Dolphin 2.9 is designed for instruction following, conversational, and coding. This model is a fine-tune of [Llama 3 70B](/models/meta-llama/llama-3-70b-instruct). It demonstrates improvements in instruction, conversation, coding, and function calling abilities, when compared to the original.\n\nUncensored and is stripped of alignment and bias, it requires an external alignment layer for ethical use. Users are cautioned to use this highly compliant model responsibly, as detailed in a blog post about uncensored models at [erichartford.com/uncensored-models](https://erichartford.com/uncensored-models).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/codestral-mamba", "name": "Mistral: Codestral Mamba", "created": **********, "description": "A 7.3B parameter Mamba-based model designed for code and reasoning tasks.\n\n- Linear time inference, allowing for theoretically infinite sequence lengths\n- 256k token context window\n- Optimized for quick responses, especially beneficial for code productivity\n- Performs comparably to state-of-the-art transformer models in code and reasoning tasks\n- Available under the Apache 2.0 license for free use, modification, and distribution", "context_length": 256000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000025", "completion": "0.00000025", "image": "0", "request": "0"}, "top_provider": {"context_length": 256000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-nemo", "name": "Mistral: <PERSON><PERSON><PERSON> Nemo", "created": **********, "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000013", "completion": "0.00000013", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4o-mini-2024-07-18", "name": "OpenAI: GPT-4o-mini (2024-07-18)", "created": **********, "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-4o-mini", "name": "OpenAI: GPT-4o-mini", "created": **********, "description": "GPT-4o mini is OpenAI's newest model after [GPT-4 Omni](/models/openai/gpt-4o), supporting both text and image inputs with text outputs.\n\nAs their most advanced small model, it is many multiples more affordable than other recent frontier models, and more than 60% cheaper than [GPT-3.5 Turbo](/models/openai/gpt-3.5-turbo). It maintains SOTA intelligence, while being significantly more cost-effective.\n\nGPT-4o mini achieves an 82% score on MMLU and presently ranks higher than GPT-4 on chat preferences [common leaderboards](https://arena.lmsys.org/).\n\nCheck out the [launch announcement](https://openai.com/index/gpt-4o-mini-advancing-cost-efficient-intelligence/) to learn more.", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null}, {"id": "qwen/qwen-2-7b-instruct:free", "name": "Qwen 2 7B Instruct (free)", "created": **********, "description": "Qwen2 7B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).\n\n_These are free, rate-limited endpoints for [Qwen 2 7B Instruct](/models/qwen/qwen-2-7b-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "qwen/qwen-2-7b-instruct", "name": "Qwen 2 7B Instruct", "created": **********, "description": "Qwen2 7B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemma-2-27b-it", "name": "Google: Gemma 2 27B", "created": **********, "description": "Gemma 2 27B by Google is an open model built from the same research and technology used to create the [Gemini models](/models?q=gemini).\n\nGemma models are well-suited for a variety of text generation tasks, including question answering, summarization, and reasoning.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Gemini", "instruct_type": "gemma"}, "pricing": {"prompt": "0.00000027", "completion": "0.00000027", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "alpindale/magnum-72b", "name": "Magnum 72B", "created": **********, "description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the first in a new family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "context_length": 16384, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000375", "completion": "0.0000045", "image": "0", "request": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 1024, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/hermes-2-theta-llama-3-8b", "name": "Nous: Hermes 2 Theta 8B", "created": **********, "description": "An experimental merge model based on Llama 3, exhibiting a very distinctive style of writing. It combines the the best of [Meta's Llama 3 8B](https://openrouter.ai/models/meta-llama/llama-3-8b-instruct) and Nous Research's [Hermes 2 Pro](https://openrouter.ai/models/nousresearch/hermes-2-pro-llama-3-8b).\n\nHermes-2 Θ (theta) was specifically designed with a few capabilities in mind: executing function calls, generating JSON output, and most remarkably, demonstrating metacognitive abilities (contemplating the nature of thought and recognizing the diversity of cognitive processes among individuals).", "context_length": 16384, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0.**********", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemma-2-9b-it:free", "name": "Google: <PERSON> 2 9B (free)", "created": **********, "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).\n\n_These are free, rate-limited endpoints for [Gemma 2 9B](/models/google/gemma-2-9b-it). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Gemini", "instruct_type": "gemma"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemma-2-9b-it", "name": "Google: Gemma 2 9B", "created": **********, "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\n\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.\n\nSee the [launch announcement](https://blog.google/technology/developers/google-gemma-2/) for more details. Usage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Gemini", "instruct_type": "gemma"}, "pricing": {"prompt": "0.00000006", "completion": "0.00000006", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "sao10k/l3-stheno-8b", "name": "Llama 3 Stheno 8B v3.3 32K", "created": **********, "description": "Stheno 8B 32K is a creative writing/roleplay model from [Sao10k](https://ko-fi.com/sao10k). It was trained at 8K context, then expanded to 32K context.\n\nCompared to older Stheno version, this model is trained on:\n- 2x the amount of creative writing samples\n- Cleaned up roleplaying samples\n- Fewer low quality samples", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.00000025", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "ai21/jamba-instruct", "name": "AI21: Jamba Instruct", "created": **********, "description": "The Jamba-Instruct model, introduced by AI21 Labs, is an instruction-tuned variant of their hybrid SSM-Transformer Jamba model, specifically optimized for enterprise applications.\n\n- 256K Context Window: It can process extensive information, equivalent to a 400-page novel, which is beneficial for tasks involving large documents such as financial reports or legal documents\n- Safety and Accuracy: Jamba-Instruct is designed with enhanced safety features to ensure secure deployment in enterprise environments, reducing the risk and cost of implementation\n\nRead their [announcement](https://www.ai21.com/blog/announcing-jamba) to learn more.\n\nJamba has a knowledge cutoff of February 2024.", "context_length": 256000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000007", "image": "0", "request": "0"}, "top_provider": {"context_length": 256000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-3.5-sonnet", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "created": **********, "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 8192, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-3.5-sonnet:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "created": **********, "description": "Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Autonomously writes, edits, and runs code with reasoning and troubleshooting\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-3.5-sonnet) variant._", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 8192, "is_moderated": false}, "per_request_limits": null}, {"id": "sao10k/l3-euryale-70b", "name": "Llama 3 Euryale 70B v2.1", "created": **********, "description": "Euryale 70B v2.1 is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k).\n\n- Better prompt adherence.\n- Better anatomy / spatial awareness.\n- Adapts much better to unique and custom formatting / reply formats.\n- Very creative, lots of unique swipes.\n- Is not restrictive during roleplays.", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3-medium-4k-instruct", "name": "Phi-3 Medium 4K Instruct", "created": **********, "description": "Phi-3 4K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 128k context length, try [Phi-3 Medium 128K](/models/microsoft/phi-3-medium-128k-instruct).", "context_length": 4000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0.00000014", "completion": "0.00000014", "image": "0", "request": "0"}, "top_provider": {"context_length": 4000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "cognitivecomputations/dolphin-mixtral-8x22b", "name": "Dolphin 2.9.2 Mixtral 8x22B 🐬", "created": **********, "description": "Dolphin 2.9 is designed for instruction following, conversational, and coding. This model is a finetune of [Mixtral 8x22B Instruct](/models/mistralai/mixtral-8x22b-instruct). It features a 64k context length and was fine-tuned with a 16k sequence length using ChatML templates.\n\nThis model is a successor to [Dolphin Mixtral 8x7B](/models/cognitivecomputations/dolphin-mixtral-8x7b).\n\nThe model is uncensored and is stripped of alignment and bias. It requires an external alignment layer for ethical use. Users are cautioned to use this highly compliant model responsibly, as detailed in a blog post about uncensored models at [erichartford.com/uncensored-models](https://erichartford.com/uncensored-models).\n\n#moe #uncensored", "context_length": 65536, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "image": "0", "request": "0"}, "top_provider": {"context_length": 16000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "qwen/qwen-2-72b-instruct", "name": "Qwen 2 72B Instruct", "created": **********, "description": "Qwen2 72B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openchat/openchat-8b", "name": "OpenChat 3.6 8B", "created": **********, "description": "OpenChat 8B is a library of open-source language models, fine-tuned with \"C-RLFT (Conditioned Reinforcement Learning Fine-Tuning)\" - a strategy inspired by offline reinforcement learning. It has been trained on mixed-quality data without preference labels.\n\nIt outperforms many similarly sized models including [Llama 3 8B Instruct](/models/meta-llama/llama-3-8b-instruct) and various fine-tuned models. It excels in general conversation, coding assistance, and mathematical reasoning.\n\n- For OpenChat fine-tuned on Mistral 7B, check out [OpenChat 7B](/models/openchat/openchat-7b).\n- For OpenChat fine-tuned on Llama 8B, check out [OpenChat 8B](/models/openchat/openchat-8b).\n\n#open-source", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "openchat"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/hermes-2-pro-llama-3-8b", "name": "NousResearch: Hermes 2 Pro - Llama-3 8B", "created": **********, "description": "Hermes 2 Pro is an upgraded, retrained version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house.", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000014", "completion": "0.00000014", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-7b-instruct-v0.3", "name": "Mistral: Mistral 7B Instruct v0.3", "created": **********, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct v0.2](/models/mistralai/mistral-7b-instruct-v0.2), with the following changes:\n\n- Extended vocabulary to 32768\n- Supports v3 Tokenizer\n- Supports function calling\n\nNOTE: Support for function calling depends on the provider.", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral: Mistral 7B Instruct (free)", "created": **********, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*\n\n_These are free, rate-limited endpoints for [Mistral 7B Instruct](/models/mistralai/mistral-7b-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "mistral<PERSON>/mistral-7b-instruct", "name": "Mistral: Mistral 7B Instruct", "created": **********, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-7b-instruct:nitro", "name": "Mistral: Mistral 7B Instruct (nitro)", "created": **********, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*\n\n_These are higher-throughput endpoints for [Mistral 7B Instruct](/models/mistralai/mistral-7b-instruct). They may have higher prices._", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000007", "completion": "0.00000007", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3-mini-128k-instruct:free", "name": "Phi-3 Mini 128K Instruct (free)", "created": **********, "description": "Phi-3 Mini is a powerful 3.8B parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. This model is static, trained on an offline dataset with an October 2023 cutoff date.\n\n_These are free, rate-limited endpoints for [Phi-3 Mini 128K Instruct](/models/microsoft/phi-3-mini-128k-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3-mini-128k-instruct", "name": "Phi-3 Mini 128K Instruct", "created": **********, "description": "Phi-3 Mini is a powerful 3.8B parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. This model is static, trained on an offline dataset with an October 2023 cutoff date.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3-medium-128k-instruct:free", "name": "Phi-3 Medium 128K Instruct (free)", "created": **********, "description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).\n\n_These are free, rate-limited endpoints for [Phi-3 Medium 128K Instruct](/models/microsoft/phi-3-medium-128k-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/phi-3-medium-128k-instruct", "name": "Phi-3 Medium 128K Instruct", "created": **********, "description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "phi3"}, "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "neversleep/llama-3-lumimaid-70b", "name": "Llama 3 Lumimaid 70B", "created": **********, "description": "The NeverSleep team is back, with a Llama 3 70B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000003375", "completion": "0.0000045", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-flash-1.5", "name": "Google: Gemini Flash 1.5", "created": **********, "description": "Gemini 1.5 Flash is a foundation model that performs well at a variety of multimodal tasks such as visual understanding, classification, summarization, and creating content from image, audio and video. It's adept at processing visual and text inputs such as photographs, documents, infographics, and screenshots.\n\nGemini 1.5 Flash is designed for high-volume, high-frequency tasks where cost and latency matter. On most common tasks, Flash achieves comparable quality to other Gemini Pro models at a significantly reduced cost. Flash is well-suited for applications like chat assistants and on-demand content generation where speed and scale matter.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "context_length": 4000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.**********", "completion": "0.00000015", "image": "0.00004", "request": "0"}, "top_provider": {"context_length": 4000000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "deepseek/deepseek-coder", "name": "DeepSeek-Coder-V2", "created": **********, "description": "DeepSeek-Coder-V2, an open-source Mixture-of-Experts (MoE) code language model. It is further pre-trained from an intermediate checkpoint of DeepSeek-V2 with additional 6 trillion tokens.\n\nThe original V1 model was trained from scratch on 2T tokens, with a composition of 87% code and 13% natural language in both English and Chinese. It was pre-trained on project-level code corpus by employing a extra fill-in-the-blank task.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.00000014", "completion": "0.00000028", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "deepseek/deepseek-chat", "name": "DeepSeek-V2 Chat", "created": **********, "description": "DeepSeek-V2 Chat is a conversational finetune of DeepSeek-V2, a Mixture-of-Experts (MoE) language model. It comprises 236B total parameters, of which 21B are activated for each token.\n\nCompared with DeepSeek 67B, DeepSeek-V2 achieves stronger performance, and meanwhile saves 42.5% of training costs, reduces the KV cache by 93.3%, and boosts the maximum generation throughput to 5.76 times.\n\nDeepSeek-V2 achieves remarkable performance on both standard benchmarks and open-ended generation evaluations.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.00000014", "completion": "0.00000028", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3-sonar-large-32k-online", "name": "Perplexity: Llama3 Sonar 70B Online", "created": **********, "description": "Llama3 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3-sonar-large-32k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 28000, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0.005"}, "top_provider": {"context_length": 28000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3-sonar-large-32k-chat", "name": "Perplexity: Llama3 Sonar 70B", "created": **********, "description": "Llama3 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is a normal offline LLM, but the [online version](/models/perplexity/llama-3-sonar-large-32k-online) of this model has Internet access.", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3-sonar-small-32k-online", "name": "Perplexity: Llama3 Sonar 8B Online", "created": **********, "description": "Llama3 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is the online version of the [offline chat model](/models/perplexity/llama-3-sonar-small-32k-chat). It is focused on delivering helpful, up-to-date, and factual responses. #online", "context_length": 28000, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0.005"}, "top_provider": {"context_length": 28000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "perplexity/llama-3-sonar-small-32k-chat", "name": "Perplexity: Llama3 Sonar 8B", "created": **********, "description": "Llama3 Sonar is Perplexity's latest model family. It surpasses their earlier Sonar models in cost-efficiency, speed, and performance.\n\nThis is a normal offline LLM, but the [online version](/models/perplexity/llama-3-sonar-small-32k-online) of this model has Internet access.", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-guard-2-8b", "name": "Meta: LlamaGuard 2 8B", "created": **********, "description": "This safeguard model has 8B parameters and is based on the Llama 3 family. Just like is predecessor, [LlamaGuard 1](https://huggingface.co/meta-llama/LlamaGuard-7b), it can do both prompt and response classification.\n\nLlamaGuard 2 acts as a normal LLM would, generating text that indicates whether the given input/output is safe/unsafe. If deemed unsafe, it will also share the content categories violated.\n\nFor best results, please use raw prompt input or the `/completions` endpoint, instead of the chat API.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "none"}, "pricing": {"prompt": "0.00000018", "completion": "0.00000018", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4o-2024-05-13", "name": "OpenAI: GPT-4o (2024-05-13)", "created": **********, "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-4o", "name": "OpenAI: GPT-4o", "created": **********, "description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000005", "completion": "0.000015", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-4o:extended", "name": "OpenAI: GPT-4o (extended)", "created": **********, "description": "GPT-4o Extended is an experimental variant of GPT-4o with an extended max output tokens. This model supports only text input to text output.\n\n_These are extended-context endpoints for [GPT-4o](/models/openai/gpt-4o). They may have higher prices._", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000006", "completion": "0.000018", "image": "0.007225", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 64000, "is_moderated": false}, "per_request_limits": null}, {"id": "qwen/qwen-72b-chat", "name": "<PERSON><PERSON> 1.5 72B <PERSON><PERSON>", "created": **********, "description": "Qwen1.5 72B is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen, the improvements include:\n\n- Significant performance improvement in human preference for chat models\n- Multilingual support of both base and chat models\n- Stable support of 32K context length for models of all sizes\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen1.5/) and [GitHub repo](https://github.com/QwenLM/Qwen1.5).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000081", "completion": "0.00000081", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "qwen/qwen-110b-chat", "name": "<PERSON>wen 1.5 110B <PERSON><PERSON>", "created": **********, "description": "Qwen1.5 110B is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen, the improvements include:\n\n- Significant performance improvement in human preference for chat models\n- Multilingual support of both base and chat models\n- Stable support of 32K context length for models of all sizes\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen1.5/) and [GitHub repo](https://github.com/QwenLM/Qwen1.5).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000162", "completion": "0.00000162", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "neversleep/llama-3-lumimaid-8b", "name": "Llama 3 Lumimaid 8B", "created": **********, "description": "The NeverSleep team is back, with a Llama 3 8B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 24576, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.**********", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "neversleep/llama-3-lumimaid-8b:extended", "name": "Llama 3 Lumimaid 8B (extended)", "created": **********, "description": "The NeverSleep team is back, with a Llama 3 8B finetune trained on their curated roleplay data. Striking a balance between eRP and RP, Lumimaid was designed to be serious, yet uncensored when necessary.\n\nTo enhance it's overall intelligence and chat capability, roughly 40% of the training data was not roleplay. This provides a breadth of knowledge to access, while still keeping roleplay as the primary strength.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are extended-context endpoints for [Llama 3 Lumimaid 8B](/models/neversleep/llama-3-lumimaid-8b). They may have higher prices._", "context_length": 24576, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.**********", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 24576, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "sao10k/fimbulvetr-11b-v2", "name": "Fimbulvetr 11B v2", "created": **********, "description": "Creative writing model, routed with permission. It's fast, it keeps the conversation going, and it stays in character.\n\nIf you submit a raw prompt, you can use Alpaca or Vicuna formats.", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000000375", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-70b-instruct", "name": "Meta: Llama 3 70B Instruct", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-70b-instruct:nitro", "name": "Meta: Llama 3 70B Instruct (nitro)", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are higher-throughput endpoints for [Llama 3 70B Instruct](/models/meta-llama/llama-3-70b-instruct). They may have higher prices._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000000792", "completion": "0.000000792", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-8b-instruct:free", "name": "Meta: Llama 3 8B Instruct (free)", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are free, rate-limited endpoints for [Llama 3 8B Instruct](/models/meta-llama/llama-3-8b-instruct). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-8b-instruct", "name": "Meta: Llama 3 8B Instruct", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-8b-instruct:nitro", "name": "Meta: Llama 3 8B Instruct (nitro)", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are higher-throughput endpoints for [Llama 3 8B Instruct](/models/meta-llama/llama-3-8b-instruct). They may have higher prices._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.000000162", "completion": "0.000000162", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-3-8b-instruct:extended", "name": "Meta: Llama 3 8B Instruct (extended)", "created": **********, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases.\n\nIt has demonstrated strong performance compared to leading closed-source models in human evaluations.\n\nTo read more about the model release, [click here](https://ai.meta.com/blog/meta-llama-3/). Usage of this model is subject to [Meta's Acceptable Use Policy](https://llama.meta.com/llama3/use-policy/).\n\n_These are extended-context endpoints for [Llama 3 8B Instruct](/models/meta-llama/llama-3-8b-instruct). They may have higher prices._", "context_length": 16384, "architecture": {"modality": "text->text", "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.**********", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mixtral-8x22b-instruct", "name": "Mistral: Mixtral 8x22B Instruct", "created": **********, "description": "Mistral's official instruct fine-tuned version of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b). It uses 39B active parameters out of 141B, offering unparalleled cost efficiency for its size. Its strengths include:\n- strong math, coding, and reasoning\n- large context length (64k)\n- fluency in English, French, Italian, German, and Spanish\n\nSee benchmarks on the launch announcement [here](https://mistral.ai/news/mixtral-8x22b/).\n#moe", "context_length": 65536, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000065", "completion": "0.00000065", "image": "0", "request": "0"}, "top_provider": {"context_length": 65536, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/wizardlm-2-7b", "name": "WizardLM-2 7B", "created": **********, "description": "WizardLM-2 7B is the smaller variant of Microsoft AI's latest Wizard model. It is the fastest and achieves comparable performance with existing 10x larger opensource leading models\n\nIt is a finetune of [Mistral 7B Instruct](/models/mistralai/mistral-7b-instruct), using the same technique as [WizardLM-2 8x22B](/models/microsoft/wizardlm-2-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "vicuna"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "microsoft/wizardlm-2-8x22b", "name": "WizardLM-2 8x22B", "created": **********, "description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.\n\nIt is an instruct finetune of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "context_length": 65536, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "vicuna"}, "pricing": {"prompt": "0.0000005", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 65536, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-pro-1.5", "name": "Google: Gemini Pro 1.5", "created": **********, "description": "Google's latest multimodal model, supporting image and video in text or chat prompts.\n\nOptimized for language tasks including:\n\n- Code generation\n- Text generation\n- Text editing\n- Problem solving\n- Recommendations\n- Information extraction\n- Data extraction or generation\n- AI agents\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "context_length": 4000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.0000025", "completion": "0.0000075", "image": "0.00263", "request": "0"}, "top_provider": {"context_length": 4000000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-turbo", "name": "OpenAI: GPT-4 Turbo", "created": **********, "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to December 2023.", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0.01445", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "cohere/command-r-plus", "name": "Cohere: Command R+", "created": **********, "description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "databricks/dbrx-instruct", "name": "Databricks: DBRX 132B Instruct", "created": **********, "description": "DBRX is a new open source large language model developed by Databricks. At 132B, it outperforms existing open source LLMs like Llama 2 70B and [Mixtral-8x7b](/models/mistralai/mixtral-8x7b) on standard industry benchmarks for language understanding, programming, math, and logic.\n\nIt uses a fine-grained mixture-of-experts (MoE) architecture. 36B parameters are active on any input. It was pre-trained on 12T tokens of text and code data. Compared to other open MoE models like Mixtral-8x7B and Grok-1, DBRX is fine-grained, meaning it uses a larger number of smaller experts.\n\nSee the launch announcement and benchmark results [here](https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm).\n\n#moe", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "Other", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000108", "completion": "0.00000108", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "sophosympatheia/midnight-rose-70b", "name": "<PERSON> Rose 70B", "created": **********, "description": "A merge with a complex family tree, this model was crafted for roleplaying and storytelling. <PERSON> Rose is a successor to Rogue Rose and Aurora Nights and improves upon them both. It wants to produce lengthy output by default and is the best creative writing merge produced so far by sophosympatheia.\n\nDescending from earlier versions of <PERSON> Rose and [Wizard Tulu Dolphin 70B](https://huggingface.co/sophosympatheia/Wizard-Tu<PERSON>-Dolphin-70B-v1.0), it inherits the best qualities of each.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "airoboros"}, "pricing": {"prompt": "0.0000008", "completion": "0.0000008", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command-r", "name": "Cohere: Command R", "created": **********, "description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "cohere/command", "name": "Cohere: Command", "created": **********, "description": "Command is an instruction-following conversational model that performs language tasks with high quality, more reliably and with a longer context than our base generative models.\n\nUse of this model is subject to Cohere's [Acceptable Use Policy](https://docs.cohere.com/docs/c4ai-acceptable-use-policy).", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Cohere", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 4000, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-3-haiku", "name": "Anthropic: <PERSON> 3 <PERSON><PERSON>", "created": **********, "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.00000125", "image": "0.0004", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-3-haiku:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "created": **********, "description": "Claude 3 Haiku is Anthropic's fastest and most compact model for\nnear-instant responsiveness. Quick and accurate targeted performance.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-haiku)\n\n#multimodal\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-3-haiku) variant._", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.00000125", "image": "0.0004", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-3-sonnet", "name": "Anthropic: <PERSON> 3 <PERSON>", "created": **********, "description": "Claude 3 Sonnet is an ideal balance of intelligence and speed for enterprise workloads. Maximum utility at a lower price, dependable, balanced for scaled deployments.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-3-sonnet:beta", "name": "Anthropic: <PERSON> (self-moderated)", "created": **********, "description": "Claude 3 Sonnet is an ideal balance of intelligence and speed for enterprise workloads. Maximum utility at a lower price, dependable, balanced for scaled deployments.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-3-sonnet) variant._", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "image": "0.0048", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-3-opus", "name": "Anthropic: <PERSON> 3 Opus", "created": **********, "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000015", "completion": "0.000075", "image": "0.024", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-3-opus:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "created": **********, "description": "Claude 3 Opus is Anthropic's most powerful model for highly complex tasks. It boasts top-level performance, intelligence, fluency, and understanding.\n\nSee the launch announcement and benchmark results [here](https://www.anthropic.com/news/claude-3-family)\n\n#multimodal\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-3-opus) variant._", "context_length": 200000, "architecture": {"modality": "text+image->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000015", "completion": "0.000075", "image": "0.024", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-large", "name": "Mistral Large", "created": **********, "description": "This is Mistral AI's flagship model, Mistral Large 2 (version `mistral-large-2407`). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt is fluent in English, French, Spanish, German, and Italian, with high grammatical accuracy, and its long context window allows precise information recall from large documents.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000009", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-turbo-preview", "name": "OpenAI: GPT-4 Turbo Preview", "created": **********, "description": "The preview GPT-4 model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Dec 2023.\n\n**Note:** heavily rate limited by OpenAI while in preview.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-0613", "name": "OpenAI: GPT-3.5 Turbo (older v0613)", "created": **********, "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "context_length": 4095, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 4095, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "name": "Nous: Hermes 2 Mixtral 8x7B DPO", "created": **********, "description": "Nous Hermes 2 Mixtral 8x7B DPO is the new flagship Nous Research model trained over the [Mixtral 8x7B MoE LLM](/models/mistralai/mixtral-8x7b).\n\nThe model was trained on over 1,000,000 entries of primarily [GPT-4](/models/openai/gpt-4) generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.\n\n#moe", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000045", "completion": "0.00000045", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-medium", "name": "Mistral Medium", "created": **********, "description": "This is Mistral AI's closed-source, medium-sided model. It's powered by a closed-source prototype and excels at reasoning, code, JSON, chat, and more. In benchmarks, it compares with many of the flagship models of other companies.", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000027", "completion": "0.0000081", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-small", "name": "Mistra<PERSON> Small", "created": **********, "description": "This model is currently powered by Mixtral-8X7B-v0.1, a sparse mixture of experts model with 12B active parameters. It has better reasoning, exhibits more capabilities, can produce and reason about code, and is multiligual, supporting English, French, German, Italian, and Spanish.\n#moe", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.000006", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-tiny", "name": "Mistral Tiny", "created": **********, "description": "This model is currently powered by Mistral-7B-v0.2, and incorporates a \"better\" fine-tuning than [Mistral 7B](/models/mistralai/mistral-7b-instruct-v0.1), inspired by community work. It's best used for large batch processing tasks where cost is a significant factor but reasoning capabilities are not crucial.", "context_length": 32000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.00000025", "image": "0", "request": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "austism/chronos-hermes-13b", "name": "Chronos Hermes 13B v2", "created": **********, "description": "A 75/25 merge of [Chronos 13b v2](https://huggingface.co/elinas/chronos-13b-v2) and [Nous Hermes Llama2 13b](/models/nousresearch/nous-hermes-llama2-13b). This offers the imaginative writing style of Chronos while retaining coherency. Outputs are long and use exceptional prose. #merge", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.00000013", "completion": "0.00000013", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "nousresearch/nous-hermes-yi-34b", "name": "Nous: <PERSON><PERSON> 2 Yi 34B", "created": **********, "description": "Nous Hermes 2 Yi 34B was trained on 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape.\n\nNous-Hermes 2 on Yi 34B outperforms all Nous-Hermes & Open-Hermes models of the past, achieving new heights in all benchmarks for a Nous Research LLM as well as surpassing many popular finetunes.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000072", "completion": "0.00000072", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-7b-instruct-v0.2", "name": "Mistral: Mistral 7B Instruct v0.2", "created": **********, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\nAn improved version of [Mistral 7B Instruct](/modelsmistralai/mistral-7b-instruct-v0.1), with the following changes:\n\n- 32k context window (vs 8k context in v0.1)\n- Rope-theta = 1e6\n- No Sliding-Window Attention", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "cognitivecomputations/dolphin-mixtral-8x7b", "name": "Dolphin 2.6 Mixtral 8x7B 🐬", "created": **********, "description": "This is a 16k context fine-tune of [Mixtral-8x7b](/models/mistralai/mixtral-8x7b). It excels in coding tasks due to extensive training with coding data and is known for its obedience, although it lacks DPO tuning.\n\nThe model is uncensored and is stripped of alignment and bias. It requires an external alignment layer for ethical use. Users are cautioned to use this highly compliant model responsibly, as detailed in a blog post about uncensored models at [erichartford.com/uncensored-models](https://erichartford.com/uncensored-models).\n\n#moe #uncensored", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000005", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-pro-vision", "name": "Google: Gemini Pro Vision 1.0", "created": **********, "description": "Google's flagship multimodal model, supporting image and video in text or chat prompts for a text or code response.\n\nSee the benchmarks and prompting guidelines from [Deepmind](https://deepmind.google/technologies/gemini/).\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "context_length": 65536, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.000000125", "completion": "0.000000375", "image": "0.0025", "request": "0"}, "top_provider": {"context_length": 65536, "max_completion_tokens": 8192, "is_moderated": false}, "per_request_limits": null}, {"id": "google/gemini-pro", "name": "Google: Gemini Pro 1.0", "created": **********, "description": "Google's flagship text generation model. Designed to handle natural language tasks, multiturn text and code chat, and code generation.\n\nSee the benchmarks and prompting guidelines from [Deepmind](https://deepmind.google/technologies/gemini/).\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).", "context_length": 131040, "architecture": {"modality": "text->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.000000125", "completion": "0.000000375", "image": "0.0025", "request": "0"}, "top_provider": {"context_length": 131040, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mixtral-8x7b-instruct", "name": "Mixtral 8x7B Instruct", "created": **********, "description": "A pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000024", "completion": "0.00000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mixtral-8x7b-instruct:nitro", "name": "Mixtral 8x7B Instruct (nitro)", "created": **********, "description": "A pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe\n\n_These are higher-throughput endpoints for [Mixtral 8x7B Instruct](/models/mistralai/mixtral-8x7b-instruct). They may have higher prices._", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.00000054", "completion": "0.00000054", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mixtral-8x7b", "name": "Mixtral 8x7B (base)", "created": **********, "description": "A pretrained generative Sparse Mixture of Experts, by Mistral AI. Incorporates 8 experts (feed-forward networks) for a total of 47B parameters. Base model (not fine-tuned for instructions) - see [Mixtral 8x7B Instruct](/models/mistralai/mixtral-8x7b-instruct) for an instruct-tuned model.\n\n#moe", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "none"}, "pricing": {"prompt": "0.00000054", "completion": "0.00000054", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "togethercomputer/stripedhyena-nous-7b", "name": "StripedHyena Nous 7B", "created": **********, "description": "This is the chat model variant of the [StripedHyena series](/models?q=stripedhyena) developed by Together in collaboration with Nous Research.\n\nStripedHyena uses a new architecture that competes with traditional Transformers, particularly in long-context data processing. It combines attention mechanisms with gated convolutions for improved speed, efficiency, and scaling. This model marks a significant advancement in AI architecture for sequence modeling tasks.", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.00000018", "completion": "0.00000018", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "gryphe/mythomist-7b:free", "name": "MythoMist 7B (free)", "created": **********, "description": "From the creator of [MythoMax](/models/gryphe/mythomax-l2-13b), merges a suite of models to reduce word anticipation, ministrations, and other undesirable words in ChatGPT roleplaying data.\n\nIt combines [Neural Chat 7B](/models/intel/neural-chat-7b), Airoboros 7b, [Toppy M 7B](/models/undi95/toppy-m-7b), [Zepher 7b beta](/models/huggingfaceh4/zephyr-7b-beta), [Nous Capybara 34B](/models/nousresearch/nous-capybara-34b), [OpenHeremes 2.5](/models/teknium/openhermes-2.5-mistral-7b), and many others.\n\n#merge\n\n_These are free, rate-limited endpoints for [MythoMist 7B](/models/gryphe/mythomist-7b). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "gryphe/mythomist-7b", "name": "MythoMist 7B", "created": **********, "description": "From the creator of [MythoMax](/models/gryphe/mythomax-l2-13b), merges a suite of models to reduce word anticipation, ministrations, and other undesirable words in ChatGPT roleplaying data.\n\nIt combines [Neural Chat 7B](/models/intel/neural-chat-7b), Airoboros 7b, [Toppy M 7B](/models/undi95/toppy-m-7b), [Zepher 7b beta](/models/huggingfaceh4/zephyr-7b-beta), [Nous Capybara 34B](/models/nousresearch/nous-capybara-34b), [OpenHeremes 2.5](/models/teknium/openhermes-2.5-mistral-7b), and many others.\n\n#merge", "context_length": 32768, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000000375", "completion": "0.000000375", "image": "0", "request": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "openchat/openchat-7b:free", "name": "OpenChat 3.5 7B (free)", "created": **********, "description": "OpenChat 7B is a library of open-source language models, fine-tuned with \"C-RLFT (Conditioned Reinforcement Learning Fine-Tuning)\" - a strategy inspired by offline reinforcement learning. It has been trained on mixed-quality data without preference labels.\n\n- For OpenChat fine-tuned on Mistral 7B, check out [OpenChat 7B](/models/openchat/openchat-7b).\n- For OpenChat fine-tuned on Llama 8B, check out [OpenChat 8B](/models/openchat/openchat-8b).\n\n#open-source\n\n_These are free, rate-limited endpoints for [OpenChat 3.5 7B](/models/openchat/openchat-7b). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "openchat"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "openchat/openchat-7b", "name": "OpenChat 3.5 7B", "created": **********, "description": "OpenChat 7B is a library of open-source language models, fine-tuned with \"C-RLFT (Conditioned Reinforcement Learning Fine-Tuning)\" - a strategy inspired by offline reinforcement learning. It has been trained on mixed-quality data without preference labels.\n\n- For OpenChat fine-tuned on Mistral 7B, check out [OpenChat 7B](/models/openchat/openchat-7b).\n- For OpenChat fine-tuned on Llama 8B, check out [OpenChat 8B](/models/openchat/openchat-8b).\n\n#open-source", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "openchat"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "neversleep/noromaid-20b", "name": "Noromaid 20B", "created": **********, "description": "A collab between IkariDev and Undi. This merge is suitable for RP, ERP, and general knowledge.\n\n#merge #uncensored", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.0000015", "completion": "0.00000225", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-instant-1.1", "name": "Anthropic: <PERSON> v1.1", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": "claude"}, "pricing": {"prompt": "0.0000008", "completion": "0.0000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 2048, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-2.1", "name": "Anthropic: <PERSON> v2.1", "created": **********, "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-2.1:beta", "name": "Anthropic: <PERSON> v2.1 (self-moderated)", "created": **********, "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-2.1) variant._", "context_length": 200000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-2", "name": "Anthropic: <PERSON> v2", "created": **********, "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.", "context_length": 200000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-2:beta", "name": "Anthropic: <PERSON> v2 (self-moderated)", "created": **********, "description": "Claude 2 delivers advancements in key capabilities for enterprises—including an industry-leading 200K token context window, significant reductions in rates of model hallucination, system prompts and a new beta feature: tool use.\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-2) variant._", "context_length": 200000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "teknium/openhermes-2.5-mistral-7b", "name": "OpenHermes 2.5 Mistral 7B", "created": **********, "description": "A continuation of [OpenHermes 2 model](/models/teknium/openhermes-2-mistral-7b), trained on additional code datasets.\nPotentially the most interesting finding from training on a good ratio (est. of around 7-14% of the total dataset) of code instruction was that it has boosted several non-code benchmarks, including TruthfulQA, AGIEval, and GPT4All suite. It did however reduce BigBench benchmark score, but the net gain overall is significant.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.00000017", "completion": "0.00000017", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-vision-preview", "name": "OpenAI: GPT-4 Vision", "created": **********, "description": "Ability to understand images, in addition to all other [GPT-4 Turbo capabilties](/models/openai/gpt-4-turbo). Training data: up to Apr 2023.\n\n**Note:** heavily rate limited by OpenAI while in preview.\n\n#multimodal", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0.01445", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "lizpreciatior/lzlv-70b-fp16-hf", "name": "lzlv 70B", "created": **********, "description": "A Mythomax/MLewd_13B-style merge of selected 70B models.\nA multi-model merge of several LLaMA2 70B finetunes for roleplaying and creative work. The goal was to create a model that combines creativity with intelligence for an enhanced experience.\n\n#merge #uncensored", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "airoboros"}, "pricing": {"prompt": "0.00000035", "completion": "0.0000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "alpindale/goliath-120b", "name": "Goliath 120B", "created": **********, "description": "A large LLM created by combining two fine-tuned Llama 70B models into one 120B model. Combines Xwin and Euryale.\n\nCredits to\n- [@chargoddard](https://huggingface.co/chargoddard) for developing the framework used to merge the model - [mergekit](https://github.com/cg123/mergekit).\n- [@Undi95](https://huggingface.co/Undi95) for helping with the merge ratios.\n\n#merge", "context_length": 6144, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "airoboros"}, "pricing": {"prompt": "0.000009375", "completion": "0.000009375", "image": "0", "request": "0"}, "top_provider": {"context_length": 6144, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "undi95/toppy-m-7b:free", "name": "Toppy M 7B (free)", "created": **********, "description": "A wild 7B parameter model that merges several models using the new task_arithmetic merge method from mergekit.\nList of merged models:\n- NousResearch/Nous-Capybara-7B-V1.9\n- [HuggingFaceH4/zephyr-7b-beta](/models/huggingfaceh4/zephyr-7b-beta)\n- lemonilia/AshhLimaRP-Mistral-7B\n- Vulkane/120-Days-of-Sodom-LoRA-Mistral-7b\n- Undi95/Mistral-pippa-sharegpt-7b-qlora\n\n#merge #uncensored\n\n_These are free, rate-limited endpoints for [Toppy M 7B](/models/undi95/toppy-m-7b). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "undi95/toppy-m-7b", "name": "Toppy M 7B", "created": **********, "description": "A wild 7B parameter model that merges several models using the new task_arithmetic merge method from mergekit.\nList of merged models:\n- NousResearch/Nous-Capybara-7B-V1.9\n- [HuggingFaceH4/zephyr-7b-beta](/models/huggingfaceh4/zephyr-7b-beta)\n- lemonilia/AshhLimaRP-Mistral-7B\n- Vulkane/120-Days-of-Sodom-LoRA-Mistral-7b\n- Undi95/Mistral-pippa-sharegpt-7b-qlora\n\n#merge #uncensored", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.00000007", "completion": "0.00000007", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "undi95/toppy-m-7b:nitro", "name": "Toppy M 7B (nitro)", "created": **********, "description": "A wild 7B parameter model that merges several models using the new task_arithmetic merge method from mergekit.\nList of merged models:\n- NousResearch/Nous-Capybara-7B-V1.9\n- [HuggingFaceH4/zephyr-7b-beta](/models/huggingfaceh4/zephyr-7b-beta)\n- lemonilia/AshhLimaRP-Mistral-7B\n- Vulkane/120-Days-of-Sodom-LoRA-Mistral-7b\n- Undi95/Mistral-pippa-sharegpt-7b-qlora\n\n#merge #uncensored\n\n_These are higher-throughput endpoints for [Toppy M 7B](/models/undi95/toppy-m-7b). They may have higher prices._", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.00000007", "completion": "0.00000007", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openrouter/auto", "name": "Auto (best for prompt)", "created": **********, "description": "Depending on their size, subject, and complexity, your prompts will be sent to [Llama 3 70B Instruct](/models/meta-llama/llama-3-70b-instruct), [Claude 3.5 Sonnet (self-moderated)](/models/anthropic/claude-3.5-sonnet:beta) or [GPT-4o](/models/openai/gpt-4o).  To see which model was used, visit [Activity](/activity).\n\nA major redesign of this router is coming soon. Stay tuned on [Discord](https://discord.gg/fVyRaUDgxW) for updates.", "context_length": 200000, "architecture": {"modality": "text->text", "tokenizer": "Router", "instruct_type": null}, "pricing": {"prompt": "-1", "completion": "-1", "request": "-1", "image": "-1"}, "top_provider": {"context_length": null, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-1106-preview", "name": "OpenAI: GPT-4 Turbo (older v1106)", "created": **********, "description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to April 2023.", "context_length": 128000, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00001", "completion": "0.00003", "image": "0", "request": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-1106", "name": "OpenAI: GPT-3.5 Turbo 16k (older v1106)", "created": **********, "description": "An older GPT-3.5 Turbo model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Sep 2021.", "context_length": 16385, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 16385, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "google/palm-2-codechat-bison-32k", "name": "Google: PaLM 2 Code Chat 32k", "created": **********, "description": "PaLM 2 fine-tuned for chatbot conversations that help with code-related questions.", "context_length": 131040, "architecture": {"modality": "text->text", "tokenizer": "PaLM", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "google/palm-2-chat-bison-32k", "name": "Google: PaLM 2 Chat 32k", "created": **********, "description": "PaLM 2 is a language model by Google with improved multilingual, reasoning and coding capabilities.", "context_length": 131040, "architecture": {"modality": "text->text", "tokenizer": "PaLM", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null}, {"id": "jondurbin/airoboros-l2-70b", "name": "Airoboros 70B", "created": **********, "description": "A Llama 2 70B fine-tune using synthetic data (the Airoboros dataset).\n\nCurrently based on [jondurbin/airoboros-l2-70b](https://huggingface.co/jondurbin/airoboros-l2-70b-2.2.1), but might get updated in the future.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "airoboros"}, "pricing": {"prompt": "0.0000005", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "xwin-lm/xwin-lm-70b", "name": "<PERSON>win 70B", "created": **********, "description": "Xwin-LM aims to develop and open-source alignment tech for LLMs. Our first release, built-upon on the [Llama2](/models/${Model.Llama_2_13B_Chat}) base models, ranked TOP-1 on AlpacaEval. Notably, it's the first to surpass [GPT-4](/models/${Model.GPT_4}) on this benchmark. The project will be continuously updated.", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "airoboros"}, "pricing": {"prompt": "0.00000375", "completion": "0.00000375", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "mistralai/mistral-7b-instruct-v0.1", "name": "Mistral: Mistral 7B Instruct v0.1", "created": **********, "description": "A 7.3B parameter model that outperforms Llama 2 13B on all benchmarks, with optimizations for speed and context length.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "mistral"}, "pricing": {"prompt": "0.000000055", "completion": "0.000000055", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-instruct", "name": "OpenAI: GPT-3.5 Turbo Instruct", "created": **********, "description": "This model is a variant of GPT-3.5 Turbo tuned for instructional prompts and omitting chat-related optimizations. Training data: up to Sep 2021.", "context_length": 4095, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000015", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 4095, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "pygmalionai/mythalion-13b", "name": "Pygmalion: Mythalion 13B", "created": **********, "description": "A blend of the new Pygmalion-13b and MythoMax. #merge", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000001125", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-32k-0314", "name": "OpenAI: GPT-4 32k (older v0314)", "created": **********, "description": "GPT-4-32k is an extended version of GPT-4, with the same capabilities but quadrupled context length, allowing for processing up to 40 pages of text in a single pass. This is particularly beneficial for handling longer content like interacting with PDFs without an external vector database. Training data: up to Sep 2021.", "context_length": 32767, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00006", "completion": "0.00012", "image": "0", "request": "0"}, "top_provider": {"context_length": 32767, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-4-32k", "name": "OpenAI: GPT-4 32k", "created": **********, "description": "GPT-4-32k is an extended version of GPT-4, with the same capabilities but quadrupled context length, allowing for processing up to 40 pages of text in a single pass. This is particularly beneficial for handling longer content like interacting with PDFs without an external vector database. Training data: up to Sep 2021.", "context_length": 32767, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00006", "completion": "0.00012", "image": "0", "request": "0"}, "top_provider": {"context_length": 32767, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-16k", "name": "OpenAI: GPT-3.5 Turbo 16k", "created": **********, "description": "This model offers four times the context length of gpt-3.5-turbo, allowing it to support approximately 20 pages of text in a single request at a higher cost. Training data: up to Sep 2021.", "context_length": 16385, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000004", "image": "0", "request": "0"}, "top_provider": {"context_length": 16385, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "nousresearch/nous-hermes-llama2-13b", "name": "Nous: <PERSON><PERSON> 13B", "created": **********, "description": "A state-of-the-art language model fine-tuned on over 300k instructions by Nous Research, with Teknium and Emozilla leading the fine tuning process.", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.00000017", "completion": "0.00000017", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "huggingfaceh4/zephyr-7b-beta:free", "name": "Hugging Face: <PERSON>ep<PERSON>r 7B (free)", "created": **********, "description": "Zephyr is a series of language models that are trained to act as helpful assistants. Zephyr-7B-β is the second model in the series, and is a fine-tuned version of [mistralai/Mistral-7B-v0.1](/models/mistralai/mistral-7b-instruct-v0.1) that was trained on a mix of publicly available, synthetic datasets using Direct Preference Optimization (DPO).\n\n_These are free, rate-limited endpoints for [Zephyr 7B](/models/huggingfaceh4/zephyr-7b-beta). Outputs may be cached. Read about rate limits [here](/docs/limits)._", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": "zephyr"}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null}, {"id": "mancer/weaver", "name": "Mancer: <PERSON> (alpha)", "created": **********, "description": "An attempt to recreate Claude-style verbosity, but don't expect the same level of coherence or memory. Meant for use in roleplay/narrative situations.", "context_length": 8000, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000001875", "completion": "0.00000225", "image": "0", "request": "0"}, "top_provider": {"context_length": 8000, "max_completion_tokens": 1000, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-instant-1.0", "name": "Anthropic: <PERSON> v1.0", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": "claude"}, "pricing": {"prompt": "0.0000008", "completion": "0.0000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-1.2", "name": "Anthropic: <PERSON> v1.2", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": "claude"}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-1", "name": "Anthropic: <PERSON> v1", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": "claude"}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-instant-1", "name": "Anthropic: <PERSON> v1", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000008", "completion": "0.0000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-instant-1:beta", "name": "Anthropic: <PERSON> v1 (self-moderated)", "created": **********, "description": "Anthropic's model for low-latency, high throughput text generation. Supports hundreds of pages of text.\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-instant-1) variant._", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000008", "completion": "0.0000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "anthropic/claude-2.0", "name": "Anthropic: <PERSON> v2.0", "created": **********, "description": "Anthropic's flagship model. Superior performance on tasks that require complex reasoning. Supports hundreds of pages of text.", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "anthropic/claude-2.0:beta", "name": "Anthropic: <PERSON> v2.0 (self-moderated)", "created": **********, "description": "Anthropic's flagship model. Superior performance on tasks that require complex reasoning. Supports hundreds of pages of text.\n\n_This is a faster endpoint, made available in collaboration with Anthropic, that is self-moderated: response moderation happens on the provider's side instead of OpenRouter's. For requests that pass moderation, it's identical to the [Standard](/models/anthropic/claude-2.0) variant._", "context_length": 100000, "architecture": {"modality": "text->text", "tokenizer": "<PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000008", "completion": "0.000024", "image": "0", "request": "0"}, "top_provider": {"context_length": 100000, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "undi95/remm-slerp-l2-13b", "name": "ReMM SLERP 13B", "created": **********, "description": "A recreation trial of the original MythoMax-L2-B13 but with updated models. #merge", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000001125", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "undi95/remm-slerp-l2-13b:extended", "name": "ReMM SLERP 13B (extended)", "created": **********, "description": "A recreation trial of the original MythoMax-L2-B13 but with updated models. #merge\n\n_These are extended-context endpoints for [ReMM SLERP 13B](/models/undi95/remm-slerp-l2-13b). They may have higher prices._", "context_length": 6144, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000001125", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 6144, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "google/palm-2-codechat-bison", "name": "Google: PaLM 2 Code Chat", "created": **********, "description": "PaLM 2 fine-tuned for chatbot conversations that help with code-related questions.", "context_length": 28672, "architecture": {"modality": "text->text", "tokenizer": "PaLM", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 28672, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "google/palm-2-chat-bison", "name": "Google: PaLM 2 Chat", "created": **********, "description": "PaLM 2 is a language model by Google with improved multilingual, reasoning and coding capabilities.", "context_length": 36864, "architecture": {"modality": "text->text", "tokenizer": "PaLM", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.0000005", "image": "0", "request": "0"}, "top_provider": {"context_length": 36864, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null}, {"id": "gryphe/mythomax-l2-13b", "name": "MythoMax 13B", "created": **********, "description": "One of the highest performing and most popular fine-tunes of Llama 2 13B, with rich descriptions and roleplay. #merge", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.0000001", "completion": "0.0000001", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "gryphe/mythomax-l2-13b:nitro", "name": "MythoMax 13B (nitro)", "created": **********, "description": "One of the highest performing and most popular fine-tunes of Llama 2 13B, with rich descriptions and roleplay. #merge\n\n_These are higher-throughput endpoints for [MythoMax 13B](/models/gryphe/mythomax-l2-13b). They may have higher prices._", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "gryphe/mythomax-l2-13b:extended", "name": "MythoMax 13B (extended)", "created": **********, "description": "One of the highest performing and most popular fine-tunes of Llama 2 13B, with rich descriptions and roleplay. #merge\n\n_These are extended-context endpoints for [MythoMax 13B](/models/gryphe/mythomax-l2-13b). They may have higher prices._", "context_length": 8192, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.000001125", "completion": "0.000001125", "image": "0", "request": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 400, "is_moderated": false}, "per_request_limits": null}, {"id": "meta-llama/llama-2-13b-chat", "name": "Meta: Llama v2 13B Chat", "created": **********, "description": "A 13 billion parameter language model from Meta, fine tuned for chat completions", "context_length": 4096, "architecture": {"modality": "text->text", "tokenizer": "Llama2", "instruct_type": "llama2"}, "pricing": {"prompt": "0.00000027", "completion": "0.00000027", "image": "0", "request": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "openai/gpt-4-0314", "name": "OpenAI: GPT-4 (older v0314)", "created": **********, "description": "GPT-4-0314 is the first version of GPT-4 released, with a context length of 8,192 tokens, and was supported until June 14. Training data: up to Sep 2021.", "context_length": 8191, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00003", "completion": "0.00006", "image": "0", "request": "0"}, "top_provider": {"context_length": 8191, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-4", "name": "OpenAI: GPT-4", "created": **********, "description": "OpenAI's flagship model, GPT-4 is a large-scale multimodal language model capable of solving difficult problems with greater accuracy than previous models due to its broader general knowledge and advanced reasoning capabilities. Training data: up to Sep 2021.", "context_length": 8191, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00003", "completion": "0.00006", "image": "0", "request": "0"}, "top_provider": {"context_length": 8191, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-0301", "name": "OpenAI: GPT-3.5 Turbo (older v0301)", "created": **********, "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "context_length": 4095, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000001", "completion": "0.000002", "image": "0", "request": "0"}, "top_provider": {"context_length": 4095, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo-0125", "name": "OpenAI: GPT-3.5 Turbo 16k", "created": **********, "description": "The latest GPT-3.5 Turbo model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Sep 2021.\n\nThis version has a higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls.", "context_length": 16385, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 16385, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}, {"id": "openai/gpt-3.5-turbo", "name": "OpenAI: GPT-3.5 Turbo", "created": **********, "description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "context_length": 16385, "architecture": {"modality": "text->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "image": "0", "request": "0"}, "top_provider": {"context_length": 16385, "max_completion_tokens": 4096, "is_moderated": true}, "per_request_limits": null}]
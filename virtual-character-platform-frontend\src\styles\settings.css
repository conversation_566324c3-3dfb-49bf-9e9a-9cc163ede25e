/* 设置页面样式 */
.settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.settings-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 设置页面头部 */
.settings-header {
  margin-bottom: 32px;
  text-align: center;
  padding: 24px 0;
}

.settings-header .ant-typography-title {
  margin-bottom: 8px;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.settings-header .ant-typography {
  color: #666;
  font-size: 16px;
}

/* 设置卡片 */
.settings-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.settings-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.settings-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.settings-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-card .ant-card-body {
  padding: 24px;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item > div:first-child {
  flex: 1;
  margin-right: 16px;
}

.setting-item .ant-typography {
  margin-bottom: 4px;
}

.setting-item .ant-typography-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 表单项 */
.ant-form-item {
  margin-bottom: 24px;
}

.ant-form-item-label > label {
  font-weight: 600;
  color: #1a1a1a;
}

.ant-select,
.ant-input {
  border-radius: 8px;
}

.ant-switch {
  background-color: #d9d9d9;
}

.ant-switch-checked {
  background-color: #1890ff;
}

/* 危险操作卡片 */
.danger-card {
  border-color: #ff4d4f;
}

.danger-card .ant-card-head {
  background-color: #fff2f0;
  border-bottom-color: #ffccc7;
}

.danger-card .ant-card-head-title {
  color: #cf1322;
}

/* 操作按钮区域 */
.settings-actions {
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 16px;
  }
  
  .settings-header {
    padding: 16px 0;
    margin-bottom: 24px;
  }
  
  .settings-card .ant-card-body {
    padding: 16px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .setting-item > div:first-child {
    margin-right: 0;
  }
  
  .settings-actions {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .settings-header .ant-typography-title {
    font-size: 20px;
  }
  
  .settings-card .ant-card-head-title {
    font-size: 16px;
  }
  
  .ant-select,
  .ant-input {
    width: 100% !important;
  }
  
  .settings-actions .ant-space {
    width: 100%;
  }
  
  .settings-actions .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 深色主题适配 */
[data-theme='dark'] .settings-page {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-theme='dark'] .settings-card,
[data-theme='dark'] .settings-actions {
  background: #262626;
  border-color: #434343;
}

[data-theme='dark'] .settings-header .ant-typography-title,
[data-theme='dark'] .settings-card .ant-card-head-title {
  color: #ffffff;
}

[data-theme='dark'] .settings-header .ant-typography {
  color: #bfbfbf;
}

[data-theme='dark'] .ant-form-item-label > label {
  color: #ffffff;
}

[data-theme='dark'] .setting-item {
  border-bottom-color: #434343;
}

[data-theme='dark'] .setting-item .ant-typography-text {
  color: #bfbfbf;
}

[data-theme='dark'] .settings-card .ant-card-head {
  border-bottom-color: #434343;
}

[data-theme='dark'] .danger-card {
  border-color: #ff7875;
}

[data-theme='dark'] .danger-card .ant-card-head {
  background-color: #2a1215;
  border-bottom-color: #58181c;
}

[data-theme='dark'] .danger-card .ant-card-head-title {
  color: #ff7875;
}

/* 动画效果 */
.settings-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
.settings-container::-webkit-scrollbar {
  width: 6px;
}

.settings-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.settings-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.settings-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

import React, { useState } from 'react';
import { Form, Select, Slider, Switch, Button, Card, Space, Alert, message } from 'antd';
import { 
  Mic2, 
  PlayIcon as Play, 
  StopIcon as Stop,
  VolumeXIcon as VolumeX
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSettingStore } from '../../../store/setting';
import { configSelectors } from '../../../store/setting/selectors/config';

const { Option } = Select;

interface TTSSettingsProps {
  onSave: () => void;
  loading: boolean;
}

const TTSSettings: React.FC<TTSSettingsProps> = ({ onSave, loading }) => {
  const { t } = useTranslation('settings');
  const [form] = Form.useForm();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  
  // 从store获取设置和方法
  const { updateTTS } = useSettingStore();
  
  // 使用选择器获取当前TTS配置
  const ttsConfig = useSettingStore(configSelectors.currentTTSConfig);
  
  // TTS提供商选项
  const ttsProviders = [
    { value: 'system', label: '系统默认' },
    { value: 'edge', label: 'Microsoft Edge' },
    { value: 'azure', label: 'Azure Speech' },
    { value: 'openai', label: 'OpenAI TTS' },
    { value: 'elevenlabs', label: 'ElevenLabs' },
  ];
  
  // 语音选项（根据提供商动态变化）
  const getVoiceOptions = (provider: string) => {
    switch (provider) {
      case 'system':
        return [
          { value: 'default', label: '默认语音' },
        ];
      case 'edge':
        return [
          { value: 'zh-CN-XiaoxiaoNeural', label: '晓晓 (女声)' },
          { value: 'zh-CN-YunxiNeural', label: '云希 (男声)' },
          { value: 'zh-CN-YunyangNeural', label: '云扬 (男声)' },
          { value: 'en-US-JennyNeural', label: 'Jenny (女声)' },
          { value: 'en-US-GuyNeural', label: 'Guy (男声)' },
        ];
      case 'azure':
        return [
          { value: 'zh-CN-XiaoxiaoNeural', label: '晓晓 (女声)' },
          { value: 'zh-CN-YunxiNeural', label: '云希 (男声)' },
        ];
      case 'openai':
        return [
          { value: 'alloy', label: 'Alloy' },
          { value: 'echo', label: 'Echo' },
          { value: 'fable', label: 'Fable' },
          { value: 'onyx', label: 'Onyx' },
          { value: 'nova', label: 'Nova' },
          { value: 'shimmer', label: 'Shimmer' },
        ];
      case 'elevenlabs':
        return [
          { value: 'rachel', label: 'Rachel' },
          { value: 'domi', label: 'Domi' },
          { value: 'bella', label: 'Bella' },
          { value: 'antoni', label: 'Antoni' },
        ];
      default:
        return [{ value: 'default', label: '默认语音' }];
    }
  };
  
  // 处理语音测试
  const handleVoiceTest = async () => {
    try {
      if (isPlaying && currentAudio) {
        // 停止播放
        currentAudio.pause();
        currentAudio.currentTime = 0;
        setIsPlaying(false);
        setCurrentAudio(null);
        return;
      }
      
      setIsPlaying(true);
      
      // 获取当前表单值
      const values = form.getFieldsValue();
      const testText = '你好，这是语音测试。Hello, this is a voice test.';
      
      // 这里应该调用实际的TTS API
      // const audioUrl = await generateTTS({
      //   text: testText,
      //   provider: values.provider || ttsConfig.provider,
      //   voice: values.voice || ttsConfig.voice,
      //   speed: values.speed || ttsConfig.speed,
      //   pitch: values.pitch || ttsConfig.pitch,
      // });
      
      // 模拟音频播放
      const audio = new Audio();
      // audio.src = audioUrl;
      
      audio.onended = () => {
        setIsPlaying(false);
        setCurrentAudio(null);
      };
      
      audio.onerror = () => {
        setIsPlaying(false);
        setCurrentAudio(null);
        message.error('语音测试失败');
      };
      
      setCurrentAudio(audio);
      // await audio.play();
      
      // 模拟播放
      setTimeout(() => {
        setIsPlaying(false);
        setCurrentAudio(null);
        message.success('语音测试完成');
      }, 3000);
      
    } catch (error) {
      console.error('语音测试失败:', error);
      message.error('语音测试失败');
      setIsPlaying(false);
      setCurrentAudio(null);
    }
  };
  
  // 处理表单值变化
  const handleFormChange = (changedValues: any, allValues: any) => {
    // 实时更新TTS配置
    updateTTS(allValues);
  };
  
  // 处理表单提交
  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        updateTTS(values);
        onSave();
      })
      .catch(error => {
        console.error('表单验证失败:', error);
      });
  };
  
  return (
    <div>
      <Alert
        message="语音合成配置"
        description="配置文字转语音功能，支持多种语音提供商和语音选项。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Form
        form={form}
        layout="vertical"
        initialValues={ttsConfig}
        onValuesChange={handleFormChange}
      >
        <Card title="基础设置" style={{ marginBottom: 24 }}>
          <Form.Item
            name="clientCall"
            label="客户端调用"
            valuePropName="checked"
            className="settings-form-item"
          >
            <Switch />
            <div className="settings-form-item-description">
              启用后将在客户端直接调用TTS服务，可能会暴露API密钥
            </div>
          </Form.Item>
          
          <Form.Item
            name="provider"
            label="语音提供商"
            className="settings-form-item"
          >
            <Select placeholder="选择语音提供商">
              {ttsProviders.map(provider => (
                <Option key={provider.value} value={provider.value}>
                  {provider.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="voice"
            label="语音选择"
            className="settings-form-item"
          >
            <Select 
              placeholder="选择语音"
              options={getVoiceOptions(form.getFieldValue('provider') || ttsConfig.provider || 'system')}
            />
          </Form.Item>
        </Card>
        
        <Card title="语音参数" style={{ marginBottom: 24 }}>
          <Form.Item
            name="speed"
            label={`语音速度: ${form.getFieldValue('speed') || ttsConfig.speed || 1.0}x`}
            className="settings-form-item"
          >
            <Slider
              min={0.1}
              max={3.0}
              step={0.1}
              marks={{
                0.5: '0.5x',
                1.0: '1.0x',
                1.5: '1.5x',
                2.0: '2.0x',
                2.5: '2.5x',
                3.0: '3.0x',
              }}
            />
          </Form.Item>
          
          <Form.Item
            name="pitch"
            label={`语音音调: ${form.getFieldValue('pitch') || ttsConfig.pitch || 1.0}x`}
            className="settings-form-item"
          >
            <Slider
              min={0.1}
              max={2.0}
              step={0.1}
              marks={{
                0.5: '0.5x',
                1.0: '1.0x',
                1.5: '1.5x',
                2.0: '2.0x',
              }}
            />
          </Form.Item>
        </Card>
        
        <Card title="语音测试">
          <div className="tts-voice-preview">
            <Mic2 size={16} />
            <span>点击测试按钮试听当前语音设置效果</span>
          </div>
          
          <div className="tts-voice-controls">
            <Button
              type="primary"
              icon={isPlaying ? <Stop size={14} /> : <Play size={14} />}
              onClick={handleVoiceTest}
              loading={isPlaying}
            >
              {isPlaying ? '停止测试' : '测试语音'}
            </Button>
            
            <Button
              icon={<VolumeX size={14} />}
              onClick={() => {
                if (currentAudio) {
                  currentAudio.pause();
                  setIsPlaying(false);
                  setCurrentAudio(null);
                }
              }}
              disabled={!isPlaying}
            >
              停止
            </Button>
          </div>
        </Card>
        
        {/* 保存按钮 */}
        <div className="settings-actions">
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            {t('tts.saveSettings', '保存设置')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TTSSettings;

# 🎭 虚拟角色平台 v1.0

> 基于AI的沉浸式语音交互虚拟角色平台，提供真人般的情感陪伴体验

## ✨ 核心特性

- 🎤 **沉浸式语音交互** - 完全隐藏文字界面，纯语音对话体验
- 🎭 **3D角色渲染** - 支持VRM模型，丰富的表情动画
- 💋 **实时口型同步** - 语音播放时精准的口型匹配
- 🧠 **智能AI对话** - 基于星火大模型的情感理解和个性化回应
- 🎵 **高质量TTS** - 支持多种音色的语音合成
- 🖼️ **AI背景生成** - 自动生成角色专属背景图片

## 🚀 快速开始

### 1. 一键启动（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd 虚拟角色1.0.0

# 运行快速启动脚本
python quick_start.py
```

脚本会自动：
- ✅ 检查环境配置
- ✅ 创建.env配置模板
- ✅ 设置数据库
- ✅ 启动后端和前端服务
- ✅ 打开浏览器

### 2. 手动启动

#### 环境准备
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 配置环境变量
创建 `.env` 文件并配置：
```bash
# 星火AI配置 (必需)
SPARK_APP_ID=your_spark_app_id
SPARK_API_KEY=your_spark_api_key
SPARK_API_SECRET=your_spark_api_secret

# 讯飞TTS配置 (推荐)
XUNFEI_TTS_APP_ID=your_xunfei_tts_app_id
XUNFEI_TTS_API_KEY=your_xunfei_tts_api_key
XUNFEI_TTS_API_SECRET=your_xunfei_tts_api_secret
```

#### 启动服务
```bash
# 启动后端
python manage.py migrate
python manage.py runserver 8000

# 启动前端（新终端）
cd virtual-character-platform-frontend
npm install
npm run dev
```

## 🎯 使用指南

### 创建角色
1. 访问 http://localhost:5173
2. 注册/登录账户
3. 点击"创建角色"
4. 设置角色属性（姓名、年龄、性格等）
5. 生成角色头像和背景

### 开始对话

#### 文字对话
- 在角色卡片上点击"聊天"
- 输入文字消息
- 角色会以文字+语音回复

#### 语音对话（沉浸式模式）
- 点击"沉浸式聊天"
- 点击麦克风按钮开始语音输入
- 角色会自动语音回复，同时播放表情动画

## 🔧 已配置的服务

### AI对话服务
- ✅ **星火AI** - 讯飞星火大模型，支持智能对话和情感理解
- ✅ **GPT支持** - 可扩展支持OpenAI GPT模型
- ✅ **Claude支持** - 可扩展支持Anthropic Claude模型

### 语音服务
- ✅ **语音识别** - 基于Web Speech API，支持中文识别
- ✅ **讯飞TTS** - 高质量中文语音合成，多种音色
- ✅ **阿里云TTS** - 备选TTS服务，支持更多语言

### 3D渲染服务
- ✅ **VRM模型支持** - 完整的3D角色渲染系统
- ✅ **表情动画** - 根据对话情感自动切换表情
- ✅ **口型同步** - 实时音频分析与口型匹配
- ✅ **视线跟踪** - 角色眼神跟随摄像机

### 存储服务
- ✅ **本地存储** - 开发环境使用本地文件存储
- ✅ **阿里云OSS** - 生产环境云存储支持
- ✅ **SQLite数据库** - 轻量级数据库，易于部署

## 📊 系统架构

```
前端 (React + TypeScript)
├── 语音识别 (Web Speech API)
├── 3D渲染 (Three.js + VRM)
├── 音频播放 (Web Audio API)
└── 状态管理 (Zustand)

后端 (Django + Python)
├── AI服务集成 (星火/GPT/Claude)
├── TTS服务 (讯飞/阿里云)
├── 用户认证 (JWT)
├── 文件存储 (本地/OSS)
└── 数据管理 (SQLite/PostgreSQL)
```

## 🎵 音色配置

### 支持的音色
- **女声**: 小娟(甜美)、小燕(可爱)、小美(温柔)、小丽(成熟)
- **男声**: 小峰(温暖)、小明(成熟)

### 智能推荐
系统会根据角色特征自动推荐合适的音色：
- 年轻女性角色 → 可爱/甜美音色
- 成熟女性角色 → 温柔/成熟音色
- 年轻男性角色 → 温暖音色
- 成熟男性角色 → 成熟音色

## 🔍 故障排除

### 常见问题

**Q: 语音识别不工作？**
A: 确保使用HTTPS或localhost访问，并授权麦克风权限

**Q: TTS语音合成失败？**
A: 检查.env文件中的API密钥配置是否正确

**Q: 3D角色不显示？**
A: 检查浏览器是否支持WebGL，更新浏览器到最新版本

**Q: 对话无响应？**
A: 检查星火AI配置，确保网络连接正常

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 📚 详细文档

- 📖 [服务使用指南](docs/service_usage_guide.md)
- 🎯 [需求文档](docs/immersive_voice_interaction_requirements.md)
- 🔧 [技术架构](docs/project_structure_guide.md)
- 🚀 [部署指南](virtual-character-platform-frontend/DEPLOYMENT.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License

---

**版本**: v1.0  
**状态**: ✅ 基本完成 (95%)  
**最后更新**: 2024-12-19

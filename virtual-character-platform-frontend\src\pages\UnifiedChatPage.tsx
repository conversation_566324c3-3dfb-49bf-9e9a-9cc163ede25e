import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Spin, Modal } from 'antd';
import { LogoutOutlined, MessageOutlined, VideoCameraOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';
import { useGlobalStore } from '../store/global';
import { useSessionStore } from '../store/session';
import { useAgentStore } from '../store/agent';
import { characterAPI } from '../services/characterAPI';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';
import '../styles/unified-chat.css';

// 导入聊天模式组件
import CameraMode from '../components/chat/CameraMode';
import ChatMode from '../components/chat/ChatMode';


interface UnifiedChatPageProps {}

const UnifiedChatPage: React.FC<UnifiedChatPageProps> = () => {
  const { characterId } = useParams<{ characterId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  
  // 全局状态
  const { 
    chatMode, 
    switchChatMode, 
    toggleChatMode,
    voiceOn,
    setVoiceOn,
    interactive,
    setInteractive,
    userPreferences,
    updateUserPreferences
  } = useGlobalStore();
  
  // 会话状态
  const { 
    activeId,
    switchSession,
    createSession,
    sendMessage,
    messageInput,
    setMessageInput
  } = useSessionStore();
  
  // 角色状态
  const { getAgentById, addLocalAgent } = useAgentStore();
  
  // 本地状态
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCharacter, setSelectedCharacter] = useState<any>(null);
  const [showModeSwitch, setShowModeSwitch] = useState(false);
  
  // 语音识别
  const { isSupported: speechSupported, error: speechError } = useSpeechRecognition();
  
  // 调试模式
  // const showDebug = process.env.NODE_ENV === 'development' &&
  //                  new URLSearchParams(window.location.search).get('debug') === 'true';

  // 加载角色数据
  useEffect(() => {
    const loadCharacter = async () => {
      if (!characterId) {
        setError('未指定角色ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const characterData = await characterAPI.getCharacterById(characterId);
        setSelectedCharacter(characterData);

        // 转换为agent数据格式
        const agentData = {
          agentId: characterData.id,
          meta: {
            name: characterData.name,
            description: characterData.description,
            avatar: characterData.imageUrl || '',
            model: characterData.vrmModelUrl || '',
            readme: characterData.description,
            tags: characterData.tags || [],
          },
          systemRole: `你是${characterData.name}，${characterData.description}`,
          chatConfig: {
            historyCount: 10,
            compressThreshold: 1000,
            enableCompressThreshold: true,
            enableHistoryCount: true,
          },
          tts: {
            voice: characterData.settings?.voice_type || 'zh-CN-XiaoxiaoNeural',
            speed: 1,
            pitch: 0,
          },
          params: {},
          provider: 'openai',
          model: 'gpt-3.5-turbo',
        };

        // 检查并添加agent
        const existingAgent = getAgentById(characterData.id);
        if (!existingAgent) {
          addLocalAgent(agentData);
        }

        // 创建或切换会话
        createSession(agentData);
        
        setError(null);
      } catch (error) {
        console.error('加载角色失败:', error);
        setError('加载角色失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    loadCharacter();
  }, [characterId, getAgentById, addLocalAgent, createSession]);

  // 聊天模式切换处理
  const handleModeSwitch = (newMode: 'chat' | 'camera') => {
    switchChatMode(newMode, { savePreference: true });
    
    // 根据模式调整语音设置
    if (newMode === 'camera') {
      setVoiceOn(true);
      setInteractive(true);
      message.info('已切换到摄像头模式，语音功能已开启');
    } else {
      // 保持用户的语音偏好
      message.info('已切换到聊天模式');
    }
    
    setShowModeSwitch(false);
  };

  // 快速模式切换
  const handleQuickModeToggle = () => {
    toggleChatMode();
    const newMode = chatMode === 'chat' ? 'camera' : 'chat';
    handleModeSwitch(newMode);
  };

  // 语音输入处理
  const handleVoiceInput = async (transcript: string) => {
    if (!transcript.trim()) return;
    
    try {
      await sendMessage(transcript);
    } catch (error) {
      console.error('发送语音消息失败:', error);
      message.error('发送消息失败，请稍后再试');
    }
  };

  // 退出聊天
  const handleExit = () => {
    navigate('/community');
  };

  // 加载状态
  if (loading) {
    return (
      <div className="unified-chat-loading">
        <Spin size="large" />
        <p>正在加载角色...</p>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="unified-chat-error">
        <p>{error}</p>
        <Button onClick={() => navigate('/community')}>返回社区</Button>
      </div>
    );
  }

  return (
    <div className="unified-chat-container">
      {/* 顶部工具栏 */}
      <div className="unified-chat-toolbar">
        <div className="toolbar-left">
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={handleExit}
            title="退出聊天"
          />
          <span className="character-name">{selectedCharacter?.name}</span>
        </div>
        
        <div className="toolbar-center">
          <Button.Group>
            <Button
              type={chatMode === 'chat' ? 'primary' : 'default'}
              icon={<MessageOutlined />}
              onClick={() => handleModeSwitch('chat')}
            >
              聊天模式
            </Button>
            <Button
              type={chatMode === 'camera' ? 'primary' : 'default'}
              icon={<VideoCameraOutlined />}
              onClick={() => handleModeSwitch('camera')}
            >
              摄像头模式
            </Button>
          </Button.Group>
        </div>
        
        <div className="toolbar-right">
          {/* 预留其他控制按钮 */}
        </div>
      </div>

      {/* 聊天内容区域 */}
      <div className="unified-chat-content">
        {chatMode === 'camera' && (
          <CameraMode 
            characterId={characterId}
            onVoiceInput={handleVoiceInput}
          />
        )}
        {chatMode === 'chat' && (
          <ChatMode 
            characterId={characterId}
            onVoiceInput={handleVoiceInput}
          />
        )}
      </div>

      {/* 调试界面 */}
      {/* {showDebug && <DebugUI />} */}
      
      {/* 模式切换确认对话框 */}
      <Modal
        title="切换聊天模式"
        open={showModeSwitch}
        onCancel={() => setShowModeSwitch(false)}
        footer={null}
      >
        <p>选择您偏好的聊天模式：</p>
        <div className="mode-selection">
          <Button
            block
            size="large"
            icon={<MessageOutlined />}
            onClick={() => handleModeSwitch('chat')}
            style={{ marginBottom: 8 }}
          >
            聊天模式 - 传统文字对话
          </Button>
          <Button
            block
            size="large"
            icon={<VideoCameraOutlined />}
            onClick={() => handleModeSwitch('camera')}
          >
            摄像头模式 - 3D角色交互
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default UnifiedChatPage;

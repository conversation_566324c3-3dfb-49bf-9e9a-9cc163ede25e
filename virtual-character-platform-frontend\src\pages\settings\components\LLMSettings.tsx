import React, { useState } from 'react';
import { Card, Form, Input, Select, Switch, Button, Space, Alert, Divider, message } from 'antd';
import { 
  BrainCircuitIcon as Brain, 
  KeyIcon as Key, 
  CheckCircleIcon as Check,
  AlertCircleIcon as AlertCircle,
  PlusIcon as Plus
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSettingStore } from '../../../store/setting';
import type { ModelProviderKey } from '@/libs/agent-runtime';

const { Option } = Select;

interface LLMSettingsProps {
  onSave: () => void;
  loading: boolean;
}

const LLMSettings: React.FC<LLMSettingsProps> = ({ onSave, loading }) => {
  const { t } = useTranslation('settings');
  const [form] = Form.useForm();
  const [testingConnection, setTestingConnection] = useState<string | null>(null);
  
  // 从store获取设置和方法
  const { 
    config,
    setConfig,
    modelProviderList
  } = useSettingStore();
  

  
  // 处理API密钥更新
  const handleApiKeyUpdate = (provider: ModelProviderKey, apiKey: string) => {
    setConfig({
      keyVaults: {
        ...config.keyVaults,
        [provider]: {
          ...config.keyVaults?.[provider],
          apiKey
        }
      }
    });
  };

  // 处理基础URL更新
  const handleBaseUrlUpdate = (provider: ModelProviderKey, baseURL: string) => {
    setConfig({
      keyVaults: {
        ...config.keyVaults,
        [provider]: {
          ...config.keyVaults?.[provider],
          baseURL
        }
      }
    });
  };

  // 处理模型配置更新
  const handleModelConfigUpdate = (provider: ModelProviderKey, modelConfig: any) => {
    setConfig({
      languageModel: {
        ...config.languageModel,
        [provider]: {
          ...config.languageModel?.[provider],
          ...modelConfig
        }
      }
    });
  };

  // 测试连接
  const handleTestConnection = async (provider: ModelProviderKey) => {
    try {
      setTestingConnection(provider);
      
      const providerConfig = config.keyVaults?.[provider];
      if (!providerConfig || !('apiKey' in providerConfig) || !providerConfig.apiKey) {
        message.error('请先配置API密钥');
        return;
      }
      
      // 这里应该调用实际的连接测试API
      // const result = await testProviderConnection(provider, providerConfig);
      
      // 模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success(`${provider} 连接测试成功`);
    } catch (error) {
      console.error('连接测试失败:', error);
      message.error(`${provider} 连接测试失败`);
    } finally {
      setTestingConnection(null);
    }
  };
  
  // 渲染提供商配置卡片
  const renderProviderCard = (provider: ModelProviderKey, title: string, description: string) => {
    const providerConfig = config.keyVaults?.[provider] || {} as any;
    const modelConfig = config.languageModel?.[provider] || {} as any;
    const isEnabled = modelConfig.enabled !== false;
    
    return (
      <Card
        key={provider}
        className="llm-provider-card"
        title={
          <div className="llm-provider-title">
            <Brain size={16} />
            <span>{title}</span>
            <Switch
              checked={isEnabled}
              onChange={(checked) => handleModelConfigUpdate(provider, { enabled: checked })}
              size="small"
            />
          </div>
        }
        extra={
          <Button
            size="small"
            loading={testingConnection === provider}
            onClick={() => handleTestConnection(provider)}
            disabled={!providerConfig.apiKey}
          >
            {testingConnection === provider ? '测试中...' : '测试连接'}
          </Button>
        }
      >
        <div className="llm-provider-content">
          <p style={{ color: 'rgba(0, 0, 0, 0.65)', marginBottom: 16 }}>
            {description}
          </p>
          
          <Form.Item
            label="API 密钥"
            required
            className="settings-form-item"
          >
            <Input.Password
              placeholder="请输入API密钥"
              value={providerConfig.apiKey || ''}
              onChange={(e) => handleApiKeyUpdate(provider, e.target.value)}
              prefix={<Key size={14} />}
            />
          </Form.Item>
          
          <Form.Item
            label="代理地址 (可选)"
            className="settings-form-item"
          >
            <Input
              placeholder="https://api.example.com"
              value={providerConfig.baseURL || ''}
              onChange={(e) => handleBaseUrlUpdate(provider, e.target.value)}
            />
          </Form.Item>
          
          <Form.Item
            label="默认模型"
            className="settings-form-item"
          >
            <Select
              placeholder="选择默认模型"
              value={modelConfig.model}
              onChange={(value) => handleModelConfigUpdate(provider, { model: value })}
            >
              {getModelsForProvider(provider).map(model => (
                <Option key={model.id} value={model.id}>
                  {model.displayName || model.id}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            label="客户端调用"
            className="settings-form-item"
          >
            <Switch
              checked={modelConfig.fetchOnClient}
              onChange={(checked) => handleModelConfigUpdate(provider, { fetchOnClient: checked })}
            />
            <div className="settings-form-item-description">
              启用后将在客户端直接调用API，可能会暴露API密钥
            </div>
          </Form.Item>
        </div>
      </Card>
    );
  };
  
  // 获取提供商的模型列表
  const getModelsForProvider = (provider: ModelProviderKey) => {
    const providerCard = modelProviderList.find(p => p.id === provider);
    return providerCard?.chatModels || [];
  };
  
  // 处理表单提交
  const handleSubmit = () => {
    onSave();
  };
  
  return (
    <div>
      <Alert
        message="语言模型配置"
        description="配置不同的AI提供商，支持OpenAI、Anthropic等多种服务。请妥善保管您的API密钥。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* OpenAI */}
        {renderProviderCard(
          'openai',
          'OpenAI',
          '支持GPT-3.5、GPT-4等模型，提供强大的对话能力'
        )}

        {/* Anthropic */}
        {renderProviderCard(
          'anthropic',
          'Anthropic',
          '支持Claude系列模型，擅长长文本理解和安全对话'
        )}

        {/* Azure OpenAI */}
        {renderProviderCard(
          'azure',
          'Azure OpenAI',
          '微软Azure平台上的OpenAI服务，提供企业级支持'
        )}

        {/* Ollama */}
        {renderProviderCard(
          'ollama',
          'Ollama',
          '本地部署的开源模型，支持Llama、Mistral等模型'
        )}
      </Space>
      
      <Divider />
      
      {/* 全局设置 */}
      <Card title="全局设置" style={{ marginTop: 24 }}>
        <Form form={form} layout="vertical">
          <Form.Item
            label="默认提供商"
            name="defaultProvider"
            className="settings-form-item"
          >
            <Select placeholder="选择默认的AI提供商">
              <Option value="openai">OpenAI</Option>
              <Option value="anthropic">Anthropic</Option>
              <Option value="azure">Azure OpenAI</Option>
              <Option value="ollama">Ollama</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="请求超时时间 (秒)"
            name="timeout"
            className="settings-form-item"
          >
            <Input type="number" placeholder="30" min={5} max={300} />
          </Form.Item>
          
          <Form.Item
            label="最大重试次数"
            name="maxRetries"
            className="settings-form-item"
          >
            <Input type="number" placeholder="3" min={0} max={10} />
          </Form.Item>
        </Form>
      </Card>
      
      {/* 保存按钮 */}
      <div className="settings-actions">
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          {t('llm.saveSettings', '保存设置')}
        </Button>
      </div>
    </div>
  );
};

export default LLMSettings;

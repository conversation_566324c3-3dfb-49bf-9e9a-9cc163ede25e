import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AdminUser {
  id: string;
  username: string;
  email: string;
  roles: string[];
}

interface AdminAuthState {
  isAdminLoggedIn: boolean;
  adminToken: string | null;
  adminInfo: AdminUser | null;
  loginAdmin: (token: string, user: AdminUser) => void;
  logoutAdmin: () => void;
}

const useAdminAuthStore = create<AdminAuthState>()(
  persist(
    (set) => ({
      isAdminLoggedIn: false,
      adminToken: null,
      adminInfo: null,
      loginAdmin: (token, user) => set({ isAdminLoggedIn: true, adminToken: token, adminInfo: user }),
      logoutAdmin: () => set({ isAdminLoggedIn: false, adminToken: null, adminInfo: null }),
    }),
    {
      name: 'admin-auth-storage', // localStorage中的键名
    }
  )
);

export default useAdminAuthStore; 
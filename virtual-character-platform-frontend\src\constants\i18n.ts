// default language
export const DEFAULT_LANG = 'en-US';
// locale cookie
export const LOBE_LOCALE_COOKIE = 'LOBE_LOCALE';
// cookie cache days
export const COOKIE_CACHE_DAYS = 30;

// environment detection
// develop environment
export const isDev = typeof window !== 'undefined' &&
  (window as any).__VITE_ENV__?.NODE_ENV === 'development' ||
  process.env.NODE_ENV === 'development';
// server environment
export const isOnServerSide = typeof window === 'undefined';

// Get environment variable safely
const getEnv = (key: string): string | undefined => {
  if (typeof window !== 'undefined') {
    return (window as any).__VITE_ENV__?.[key];
  }
  return process.env[key];
};

// debug mode
export const getDebugConfig = () => ({
  // developer debug mode
  DEBUG_MODE: getEnv('VITE_PUBLIC_DEVELOPER_DEBUG') === '1',

  // i18n debug mode
  I18N_DEBUG: getEnv('VITE_PUBLIC_I18N_DEBUG') === '1',
  I18N_DEBUG_BROWSER: getEnv('VITE_PUBLIC_I18N_DEBUG_BROWSER') === '1',
  I18N_DEBUG_SERVER: getEnv('VITE_PUBLIC_I18N_DEBUG_SERVER') === '1',
});

// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`LobeOpenAI > models > should get models 1`] = `
[
  {
    "description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125",
    "displayName": "GPT-3.5 Turbo",
    "functionCall": true,
    "id": "gpt-3.5-turbo",
    "pricing": {
      "input": 0.5,
      "output": 1.5,
    },
    "tokens": 16385,
  },
  {
    "id": "gpt-3.5-turbo-16k",
  },
  {
    "id": "gpt-3.5-turbo-16k-0613",
  },
  {
    "description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。",
    "id": "gpt-4-1106-vision-preview",
    "pricing": {
      "input": 10,
      "output": 30,
    },
    "tokens": 128000,
    "vision": true,
  },
  {
    "id": "gpt-3.5-turbo-instruct-0914",
  },
  {
    "description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。",
    "displayName": "GPT-4 Turbo Preview 0125",
    "functionCall": true,
    "id": "gpt-4-0125-preview",
    "pricing": {
      "input": 10,
      "output": 30,
    },
    "tokens": 128000,
  },
  {
    "description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。",
    "displayName": "GPT-4 Turbo Preview",
    "functionCall": true,
    "id": "gpt-4-turbo-preview",
    "pricing": {
      "input": 10,
      "output": 30,
    },
    "tokens": 128000,
  },
  {
    "description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125",
    "displayName": "GPT-3.5 Turbo Instruct",
    "id": "gpt-3.5-turbo-instruct",
    "pricing": {
      "input": 1.5,
      "output": 2,
    },
    "tokens": 4096,
  },
  {
    "id": "gpt-3.5-turbo-0301",
  },
  {
    "id": "gpt-3.5-turbo-0613",
  },
  {
    "description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125",
    "displayName": "GPT-3.5 Turbo 1106",
    "functionCall": true,
    "id": "gpt-3.5-turbo-1106",
    "pricing": {
      "input": 1,
      "output": 2,
    },
    "tokens": 16385,
  },
  {
    "description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。",
    "displayName": "GPT-4 Turbo Preview 1106",
    "functionCall": true,
    "id": "gpt-4-1106-preview",
    "pricing": {
      "input": 10,
      "output": 30,
    },
    "tokens": 128000,
  },
  {
    "description": "最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。",
    "displayName": "GPT-4 Turbo Vision Preview",
    "id": "gpt-4-vision-preview",
    "pricing": {
      "input": 10,
      "output": 30,
    },
    "tokens": 128000,
    "vision": true,
  },
  {
    "description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。",
    "displayName": "GPT-4",
    "functionCall": true,
    "id": "gpt-4",
    "pricing": {
      "input": 30,
      "output": 60,
    },
    "tokens": 8192,
  },
  {
    "description": "GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125",
    "displayName": "GPT-3.5 Turbo 0125",
    "functionCall": true,
    "id": "gpt-3.5-turbo-0125",
    "pricing": {
      "input": 0.5,
      "output": 1.5,
    },
    "tokens": 16385,
  },
  {
    "description": "GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。",
    "displayName": "GPT-4 0613",
    "functionCall": true,
    "id": "gpt-4-0613",
    "pricing": {
      "input": 30,
      "output": 60,
    },
    "tokens": 8192,
  },
]
`;

import React, { useState } from 'react';
import { Button, Modal, Space, Typography, Divider, Alert } from 'antd';
import { 
  ReloadOutlined, 
  ClearOutlined, 
  HomeOutlined, 
  BugOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface ErrorRecoveryProps {
  visible: boolean;
  onClose: () => void;
  error?: Error;
  errorInfo?: string;
}

/**
 * 错误恢复组件
 * 提供多种错误恢复选项
 */
const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  visible,
  onClose,
  error,
  errorInfo
}) => {
  const [loading, setLoading] = useState<string | null>(null);

  // 刷新页面
  const handleRefresh = () => {
    setLoading('refresh');
    window.location.reload();
  };

  // 清除本地存储
  const handleClearStorage = () => {
    setLoading('clear');
    try {
      localStorage.clear();
      sessionStorage.clear();
      // 清除所有缓存
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
      }
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (e) {
      console.error('清除存储失败:', e);
      setLoading(null);
    }
  };

  // 返回首页
  const handleGoHome = () => {
    setLoading('home');
    window.location.href = '/chat';
  };

  // 复制错误信息
  const handleCopyError = () => {
    const errorText = `
错误信息: ${error?.message || '未知错误'}
错误堆栈: ${error?.stack || '无堆栈信息'}
错误详情: ${errorInfo || '无详情'}
页面URL: ${window.location.href}
用户代理: ${navigator.userAgent}
时间: ${new Date().toISOString()}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      Modal.success({
        title: '复制成功',
        content: '错误信息已复制到剪贴板，您可以将其发送给技术支持。',
      });
    }).catch(() => {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = errorText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      Modal.success({
        title: '复制成功',
        content: '错误信息已复制到剪贴板。',
      });
    });
  };

  return (
    <Modal
      title={
        <Space>
          <BugOutlined style={{ color: '#ff4d4f' }} />
          页面错误恢复
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Alert
        message="页面遇到了一些问题"
        description="请尝试以下解决方案来恢复正常使用。如果问题持续存在，请联系技术支持。"
        type="warning"
        showIcon
        style={{ marginBottom: 20 }}
      />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 快速恢复选项 */}
        <div>
          <Text strong>快速恢复选项：</Text>
          <div style={{ marginTop: 10 }}>
            <Space wrap>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                loading={loading === 'refresh'}
                onClick={handleRefresh}
              >
                刷新页面
              </Button>
              <Button
                icon={<HomeOutlined />}
                loading={loading === 'home'}
                onClick={handleGoHome}
              >
                返回聊天页面
              </Button>
            </Space>
          </div>
        </div>

        <Divider />

        {/* 高级恢复选项 */}
        <div>
          <Text strong>高级恢复选项：</Text>
          <Paragraph type="secondary" style={{ fontSize: '12px', marginTop: 5 }}>
            如果简单刷新无法解决问题，可以尝试以下选项
          </Paragraph>
          <div style={{ marginTop: 10 }}>
            <Space wrap>
              <Button
                icon={<ClearOutlined />}
                loading={loading === 'clear'}
                onClick={handleClearStorage}
                danger
              >
                清除缓存并刷新
              </Button>
              <Button
                icon={<InfoCircleOutlined />}
                onClick={handleCopyError}
              >
                复制错误信息
              </Button>
            </Space>
          </div>
        </div>

        {/* 错误详情（开发环境） */}
        {import.meta.env.DEV && error && (
          <>
            <Divider />
            <div>
              <Text strong>错误详情（开发环境）：</Text>
              <div style={{ 
                marginTop: 10, 
                padding: 10, 
                background: '#f5f5f5', 
                borderRadius: 4,
                fontSize: '12px',
                fontFamily: 'monospace',
                maxHeight: '200px',
                overflow: 'auto'
              }}>
                <div><strong>错误消息:</strong> {error.message}</div>
                {error.stack && (
                  <div style={{ marginTop: 10 }}>
                    <strong>错误堆栈:</strong>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* 帮助信息 */}
        <Alert
          message="需要帮助？"
          description={
            <div>
              <Paragraph style={{ margin: 0 }}>
                如果问题持续存在，请尝试：
              </Paragraph>
              <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                <li>检查网络连接是否正常</li>
                <li>尝试使用其他浏览器</li>
                <li>清除浏览器缓存和Cookie</li>
                <li>联系技术支持并提供错误信息</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
        />
      </Space>
    </Modal>
  );
};

export default ErrorRecovery;

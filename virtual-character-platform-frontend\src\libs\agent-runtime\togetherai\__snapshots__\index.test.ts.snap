// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`LobeTogetherAI > models > should get models 1`] = `
[
  {
    "description": "This model is a 75/25 merge of Chronos (13B) and Nous Hermes (13B) models resulting in having a great ability to produce evocative storywriting and follow a narrative.",
    "displayName": "Chronos Hermes (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "Austism/chronos-hermes-13b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "MythoLogic-L2 and Huginn merge using a highly experimental tensor type merge technique. The main difference with MythoMix is that I allowed more of Huginn to intermingle with the single tensors located at the front and end of a model",
    "displayName": "MythoMax-L2 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "Gryphe/MythoMax-L2-13b",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "first Nous collection of dataset and models made by fine-tuning mostly on data created by Nous in-house",
    "displayName": "Nous Capybara v1.9 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Capybara-7B-V1p9",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Nous Hermes 2 on Mistral 7B DPO is the new flagship 7B Hermes! This model was DPO'd from Teknium/OpenHermes-2.5-Mistral-7B and has improved across the board on all benchmarks tested - AGIEval, BigBench Reasoning, GPT4All, and TruthfulQA.",
    "displayName": "Nous Hermes 2 - Mistral DPO (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-2-Mistral-7B-DPO",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Nous Hermes 2 Mixtral 7bx8 DPO is the new flagship Nous Research model trained over the Mixtral 7bx8 MoE LLM. The model was trained on over 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.",
    "displayName": "Nous Hermes 2 - Mixtral 8x7B-DPO ",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Nous Hermes 2 Mixtral 7bx8 SFT is the new flagship Nous Research model trained over the Mixtral 7bx8 MoE LLM. The model was trained on over 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.",
    "displayName": "Nous Hermes 2 - Mixtral 8x7B-SFT",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Nous Hermes 2 - Yi-34B is a state of the art Yi Fine-tune",
    "displayName": "Nous Hermes-2 Yi (34B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-2-Yi-34B",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Nous-Hermes-Llama2-13b is a state-of-the-art language model fine-tuned on over 300,000 instructions.",
    "displayName": "Nous Hermes Llama-2 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-Llama2-13b",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Nous-Hermes-Llama2-7b is a state-of-the-art language model fine-tuned on over 300,000 instructions.",
    "displayName": "Nous Hermes LLaMA-2 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-llama-2-7b",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "An OpenOrca dataset fine-tune on top of Mistral 7B by the OpenOrca team.",
    "displayName": "OpenOrca Mistral (7B) 8K",
    "enabled": false,
    "functionCall": false,
    "id": "Open-Orca/Mistral-7B-OpenOrca",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (0.5B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-0.5B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (1.8B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-1.8B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (110B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-110B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (14B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-14B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (32B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-32B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (4B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-4B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (72B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-72B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.",
    "displayName": "Qwen 1.5 Chat (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "Qwen/Qwen1.5-7B-Chat",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Arctic is a dense-MoE Hybrid transformer architecture pre-trained from scratch by the Snowflake AI Research Team.",
    "displayName": "Snowflake Arctic Instruct",
    "enabled": false,
    "functionCall": false,
    "id": "Snowflake/snowflake-arctic-instruct",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Re:MythoMax (ReMM) is a recreation trial of the original MythoMax-L2-B13 with updated models. This merge use SLERP [TESTING] to merge ReML and Huginn v1.2.",
    "displayName": "ReMM SLERP L2 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "Undi95/ReMM-SLERP-L2-13B",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "A merge of models built by Undi95 with the new task_arithmetic merge method from mergekit.",
    "displayName": "Toppy M (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "Undi95/Toppy-M-7B",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "This model achieves a substantial and comprehensive improvement on coding, mathematical reasoning and open-domain conversation capacities",
    "displayName": "WizardLM v1.2 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "WizardLM/WizardLM-13B-V1.2",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "The OLMo models are trained on the Dolma dataset",
    "displayName": "OLMo Instruct (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "allenai/OLMo-7B-Instruct",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "codellama/CodeLlama-13b-Instruct-hf",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (34B)",
    "enabled": false,
    "functionCall": false,
    "id": "codellama/CodeLlama-34b-Instruct-hf",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (70B)",
    "enabled": false,
    "functionCall": false,
    "id": "codellama/CodeLlama-70b-Instruct-hf",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "codellama/CodeLlama-7b-Instruct-hf",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "This Dolphin is really good at coding, I trained with a lot of coding data. It is very obedient but it is not DPO tuned - so you still might need to encourage it in the system prompt as I show in the below examples.",
    "displayName": "Dolphin 2.5 Mixtral 8x7b",
    "enabled": false,
    "functionCall": false,
    "id": "cognitivecomputations/dolphin-2.5-mixtral-8x7b",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "DBRX Instruct is a mixture-of-experts (MoE) large language model trained from scratch by Databricks. DBRX Instruct specializes in few-turn interactions.",
    "displayName": "DBRX Instruct",
    "enabled": false,
    "functionCall": false,
    "id": "databricks/dbrx-instruct",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Deepseek Coder is composed of a series of code language models, each trained from scratch on 2T tokens, with a composition of 87% code and 13% natural language in both English and Chinese.",
    "displayName": "Deepseek Coder Instruct (33B)",
    "enabled": false,
    "functionCall": false,
    "id": "deepseek-ai/deepseek-coder-33b-instruct",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "trained from scratch on a vast dataset of 2 trillion tokens in both English and Chinese",
    "displayName": "DeepSeek LLM Chat (67B)",
    "enabled": true,
    "functionCall": false,
    "id": "deepseek-ai/deepseek-llm-67b-chat",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "An instruction fine-tuned LLaMA-2 (70B) model by merging Platypus2 (70B) by garage-bAInd and LLaMA-2 Instruct v2 (70B) by upstage.",
    "displayName": "Platypus2 Instruct (70B)",
    "enabled": false,
    "functionCall": false,
    "id": "garage-bAInd/Platypus2-70B-instruct",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.",
    "displayName": "Gemma Instruct (2B)",
    "enabled": false,
    "functionCall": false,
    "id": "google/gemma-2b-it",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.",
    "displayName": "Gemma Instruct (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "google/gemma-7b-it",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Vicuna is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.",
    "displayName": "Vicuna v1.5 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/vicuna-13b-v1.5",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Vicuna is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.",
    "displayName": "Vicuna v1.5 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/vicuna-7b-v1.5",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "meta-llama/Llama-2-13b-chat-hf",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (70B)",
    "enabled": false,
    "functionCall": false,
    "id": "meta-llama/Llama-2-70b-chat-hf",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "meta-llama/Llama-2-7b-chat-hf",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.",
    "displayName": "Meta Llama 3 70B Instruct",
    "enabled": false,
    "functionCall": false,
    "id": "meta-llama/Llama-3-70b-chat-hf",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.",
    "displayName": "Meta Llama 3 8B Instruct",
    "enabled": false,
    "functionCall": false,
    "id": "meta-llama/Llama-3-8b-chat-hf",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "WizardLM-2 8x22B is Wizard's most advanced model, demonstrates highly competitive performance compared to those leading proprietary works and consistently outperforms all the existing state-of-the-art opensource models.",
    "displayName": "WizardLM-2 (8x22B)",
    "enabled": false,
    "functionCall": false,
    "id": "microsoft/WizardLM-2-8x22B",
    "maxOutput": 65536,
    "tokens": 65536,
    "vision": false,
  },
  {
    "description": "instruct fine-tuned version of Mistral-7B-v0.1",
    "displayName": "Mistral (7B) Instruct",
    "enabled": false,
    "functionCall": false,
    "id": "mistralai/Mistral-7B-Instruct-v0.1",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "The Mistral-7B-Instruct-v0.2 Large Language Model (LLM) is an improved instruct fine-tuned version of Mistral-7B-Instruct-v0.1.",
    "displayName": "Mistral (7B) Instruct v0.2",
    "enabled": false,
    "functionCall": false,
    "id": "mistralai/Mistral-7B-Instruct-v0.2",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "The Mixtral-8x22B-Instruct-v0.1 Large Language Model (LLM) is an instruct fine-tuned version of the Mixtral-8x22B-v0.1.",
    "displayName": "Mixtral-8x22B Instruct v0.1",
    "enabled": true,
    "functionCall": false,
    "id": "mistralai/Mixtral-8x22B-Instruct-v0.1",
    "maxOutput": 65536,
    "tokens": 65536,
    "vision": false,
  },
  {
    "description": "The Mixtral-8x7B Large Language Model (LLM) is a pretrained generative Sparse Mixture of Experts.",
    "displayName": "Mixtral-8x7B Instruct v0.1",
    "enabled": true,
    "functionCall": false,
    "id": "mistralai/Mixtral-8x7B-Instruct-v0.1",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "A merge of OpenChat 3.5 was trained with C-RLFT on a collection of publicly available high-quality instruction data, with a custom processing pipeline.",
    "displayName": "OpenChat 3.5",
    "enabled": false,
    "functionCall": false,
    "id": "openchat/openchat-3.5-1210",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "A state-of-the-art model by Snorkel AI, DPO fine-tuned on Mistral-7B",
    "displayName": "Snorkel Mistral PairRM DPO (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "snorkelai/Snorkel-Mistral-PairRM-DPO",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "State of the art Mistral Fine-tuned on extensive public datasets",
    "displayName": "OpenHermes-2-Mistral (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "teknium/OpenHermes-2-Mistral-7B",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Continuation of OpenHermes 2 Mistral model trained on additional code datasets",
    "displayName": "OpenHermes-2.5-Mistral (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "teknium/OpenHermes-2p5-Mistral-7B",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Extending LLaMA-2 to 32K context, built with Meta's Position Interpolation and Together AI's data recipe and system optimizations, instruction tuned by Together",
    "displayName": "LLaMA-2-7B-32K-Instruct (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Llama-2-7B-32K-Instruct",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Chat model fine-tuned using data from Dolly 2.0 and Open Assistant over the RedPajama-INCITE-Base-7B-v1 base model.",
    "displayName": "RedPajama-INCITE Chat (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/RedPajama-INCITE-7B-Chat",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chat model fine-tuned using data from Dolly 2.0 and Open Assistant over the RedPajama-INCITE-Base-3B-v1 base model.",
    "displayName": "RedPajama-INCITE Chat (3B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/RedPajama-INCITE-Chat-3B-v1",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "A hybrid architecture composed of multi-head, grouped-query attention and gated convolutions arranged in Hyena blocks, different from traditional decoder-only Transformers",
    "displayName": "StripedHyena Nous (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/StripedHyena-Nous-7B",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Fine-tuned from the LLaMA 7B model on 52K instruction-following demonstrations. ",
    "displayName": "Alpaca (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/alpaca-7b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Built on the Llama2 architecture, SOLAR-10.7B incorporates the innovative Upstage Depth Up-Scaling",
    "displayName": "Upstage SOLAR Instruct v1 (11B)",
    "enabled": false,
    "functionCall": false,
    "id": "upstage/SOLAR-10.7B-Instruct-v1.0",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "The Yi series models are large language models trained from scratch by developers at 01.AI",
    "displayName": "01-ai Yi Chat (34B)",
    "enabled": false,
    "functionCall": false,
    "id": "zero-one-ai/Yi-34B-Chat",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.",
    "displayName": "Llama3 8B Chat HF INT4",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Llama-3-8b-chat-hf-int4",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.",
    "displayName": "Togethercomputer Llama3 8B Instruct Int8",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Llama-3-8b-chat-hf-int8",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "Chat model based on EleutherAI’s Pythia-7B model, and is fine-tuned with data focusing on dialog-style interactions.",
    "displayName": "Pythia-Chat-Base (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Pythia-Chat-Base-7B-v0.16",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chat model for dialogue generation finetuned on ShareGPT-Vicuna, Camel-AI, GPTeacher, Guanaco, Baize and some generated datasets.",
    "displayName": "MPT-Chat (30B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/mpt-30b-chat",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chatbot trained by fine-tuning LLaMA on dialogue data gathered from the web.",
    "displayName": "Koala (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Koala-7B",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "An instruction-following LLM based on pythia-12b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.",
    "displayName": "Dolly v2 (12B)",
    "enabled": false,
    "functionCall": false,
    "id": "databricks/dolly-v2-12b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "An instruction-following LLM based on pythia-3b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.",
    "displayName": "Dolly v2 (3B)",
    "enabled": false,
    "functionCall": false,
    "id": "databricks/dolly-v2-3b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.",
    "displayName": "Guanaco (65B) ",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/guanaco-65b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chatbot trained by fine-tuning Flan-t5-xl on user-shared conversations collected from ShareGPT.",
    "displayName": "Vicuna-FastChat-T5 (3B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/fastchat-t5-3b-v1.0",
    "maxOutput": 512,
    "tokens": 512,
    "vision": false,
  },
  {
    "description": "Chat-based and open-source assistant. The vision of the project is to make a large language model that can run on a single high-end consumer GPU. ",
    "displayName": "Open-Assistant StableLM SFT-7 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "OpenAssistant/stablelm-7b-sft-v7-epoch-3",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": true,
  },
  {
    "description": "Chat model for dialogue generation finetuned on ShareGPT-Vicuna, Camel-AI, GPTeacher, Guanaco, Baize and some generated datasets.",
    "displayName": "MPT-Chat (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/mpt-7b-chat",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chat-based and open-source assistant. The vision of the project is to make a large language model that can run on a single high-end consumer GPU. ",
    "displayName": "Open-Assistant Pythia SFT-4 (12B)",
    "enabled": false,
    "functionCall": false,
    "id": "OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": true,
  },
  {
    "description": "Chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT. Auto-regressive model, based on the transformer architecture.",
    "displayName": "Vicuna v1.3 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/vicuna-7b-v1.3",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Nous-Hermes-Llama2-70b is a state-of-the-art language model fine-tuned on over 300,000 instructions.",
    "displayName": "Nous Hermes LLaMA-2 (70B)",
    "enabled": false,
    "functionCall": false,
    "id": "NousResearch/Nous-Hermes-Llama2-70b",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Vicuna is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.",
    "displayName": "Vicuna v1.5 16K (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/vicuna-13b-v1.5-16k",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Chat model fine-tuned from EleutherAI’s GPT-NeoX with over 40 million instructions on carbon reduced compute.",
    "displayName": "GPT-NeoXT-Chat-Base (20B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/GPT-NeoXT-Chat-Base-20B",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "A fine-tuned version of Mistral-7B to act as a helpful assistant.",
    "displayName": "Zephyr-7B-ß",
    "enabled": false,
    "functionCall": false,
    "id": "HuggingFaceH4/zephyr-7b-beta",
    "maxOutput": 32768,
    "tokens": 32768,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/CodeLlama-7b-Instruct",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.",
    "displayName": "Guanaco (13B) ",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/guanaco-13b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (70B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/llama-2-70b-chat",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (34B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/CodeLlama-34b-Instruct",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.",
    "displayName": "Code Llama Instruct (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/CodeLlama-13b-Instruct",
    "maxOutput": 16384,
    "tokens": 16384,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/llama-2-13b-chat",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT. Auto-regressive model, based on the transformer architecture.",
    "displayName": "Vicuna v1.3 (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "lmsys/vicuna-13b-v1.3",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Fine-tuned from StarCoder to act as a helpful coding assistant. As an alpha release is only intended for educational or research purpopses.",
    "displayName": "StarCoderChat Alpha (16B)",
    "enabled": false,
    "functionCall": false,
    "id": "HuggingFaceH4/starchat-alpha",
    "maxOutput": 8192,
    "tokens": 8192,
    "vision": false,
  },
  {
    "description": "An instruction-following LLM based on pythia-7b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.",
    "displayName": "Dolly v2 (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "databricks/dolly-v2-7b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.",
    "displayName": "Guanaco (33B) ",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/guanaco-33b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Chatbot trained by fine-tuning LLaMA on dialogue data gathered from the web.",
    "displayName": "Koala (13B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/Koala-13B",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
  {
    "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters",
    "displayName": "LLaMA-2 Chat (7B)",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/llama-2-7b-chat",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Built on the Llama2 architecture, SOLAR-10.7B incorporates the innovative Upstage Depth Up-Scaling",
    "displayName": "Upstage SOLAR Instruct v1 (11B)-Int4",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/SOLAR-10.7B-Instruct-v1.0-int4",
    "maxOutput": 4096,
    "tokens": 4096,
    "vision": false,
  },
  {
    "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks. ",
    "displayName": "Guanaco (7B) ",
    "enabled": false,
    "functionCall": false,
    "id": "togethercomputer/guanaco-7b",
    "maxOutput": 2048,
    "tokens": 2048,
    "vision": false,
  },
]
`;

# 阿里云OSS设置指南

本文档提供了如何配置阿里云对象存储服务(OSS)以与虚拟角色平台项目集成的详细说明。

## 前提条件

1. 拥有阿里云账号
2. 已开通阿里云OSS服务
3. 已创建OSS Bucket

## 配置步骤

### 1. 创建访问密钥（AccessKey）

1. 登录阿里云控制台
2. 点击右上角头像，选择"AccessKey管理"
3. 创建AccessKey ID和AccessKey Secret（请妥善保存，仅显示一次）
   - 推荐创建RAM用户并分配最小权限，而不是使用主账号AccessKey

### 2. 配置OSS Bucket

1. 在阿里云控制台中，进入OSS服务
2. 创建一个新的Bucket或使用现有Bucket
3. 配置Bucket权限：
   - 对于公开访问的图片，设置"读写权限"为"公共读"
   - 对于私有访问的图片，设置"读写权限"为"私有"
4. 记录以下信息：
   - Bucket名称
   - Endpoint（地域节点）

### 3. 项目环境变量配置

在项目根目录创建或编辑`.env`文件，添加以下配置：

```
# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_ENDPOINT=your_endpoint  # 例如：oss-cn-beijing.aliyuncs.com
OSS_BUCKET_NAME=your_bucket_name
# OSS_CDN_DOMAIN=your_cdn_domain  # 可选，如果使用CDN加速
```

### 4. 验证配置

配置完成后，可以通过以下命令验证OSS配置是否正确：

```bash
python manage.py shell -c "from backend_services.services.file_storage_service import FileStorageService; service = FileStorageService(); print('OSS配置正确' if service.storage_client else 'OSS配置错误')"
```

## 文件存储路径结构

本项目使用以下路径结构存储文件：

```
images/{year}/{month}/{day}/{user_id}/{character_id}/{uuid}.{ext}
```

例如：
```
images/2023/05/20/123/456/550e8400-e29b-41d4-a716-************.jpg
```

这种结构有助于：
- 按日期组织文件
- 按用户和角色分类
- 使用UUID确保文件名唯一

## 文件清理

系统会自动清理已软删除且超过保留期限（默认30天）的角色图片。您也可以手动运行清理命令：

```bash
# 清理30天前软删除的角色图片
python manage.py cleanup_deleted_files

# 指定不同的天数
python manage.py cleanup_deleted_files --days=60

# 仅测试运行，不实际删除
python manage.py cleanup_deleted_files --dry-run
```

## 注意事项

1. **成本控制**：定期监控OSS使用量和费用
2. **备份策略**：考虑开启OSS的版本控制功能，防止误删
3. **CDN加速**：对于用户量大的情况，考虑配置CDN加速图片访问
4. **安全性**：定期轮换AccessKey，使用最小权限原则
import React, { useState } from 'react';
import { Form, Select, Button, Card, Space, Alert, List, Tag, Radio, Divider, message } from 'antd';
import { 
  MousePointerClick, 
  PlusIcon as Plus,
  TrashIcon as Trash,
  GenderMaleIcon as Male,
  GenderFemaleIcon as Female
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSettingStore } from '../../../store/setting';
import { configSelectors } from '../../../store/setting/selectors/config';
import { TouchAreaEnum, TouchAction } from '../../../types/touch';
import { GenderEnum } from '../../../types/agent';

const { Option } = Select;

interface TouchSettingsProps {
  onSave: () => void;
  loading: boolean;
}

const TouchSettings: React.FC<TouchSettingsProps> = ({ onSave, loading }) => {
  const { t } = useTranslation('settings');
  const [form] = Form.useForm();
  const [selectedArea, setSelectedArea] = useState<TouchAreaEnum>(TouchAreaEnum.Head);
  const [selectedGender, setSelectedGender] = useState<GenderEnum>(GenderEnum.FEMALE);
  
  // 从store获取设置和方法
  const { 
    updateTouchConfig,
    addTouchAction,
    removeTouchAction
  } = useSettingStore();
  
  // 使用选择器获取当前触摸配置
  const touchConfig = useSettingStore(configSelectors.currentTouchConfig);
  
  // 获取当前区域和性别的动作列表
  const touchActions = useSettingStore(state => 
    configSelectors.getTouchActionsByGenderAndArea(state, selectedGender, selectedArea)
  );
  
  // 触摸区域选项
  const touchAreas = [
    { value: TouchAreaEnum.Head, label: '头部' },
    { value: TouchAreaEnum.Face, label: '脸部' },
    { value: TouchAreaEnum.Body, label: '身体' },
    { value: TouchAreaEnum.Hand, label: '手部' },
    { value: TouchAreaEnum.Leg, label: '腿部' },
  ];
  
  // 动作类型选项
  const actionTypes = [
    { value: 'animation', label: '动画' },
    { value: 'sound', label: '声音' },
    { value: 'expression', label: '表情' },
    { value: 'dialog', label: '对话' },
  ];
  
  // 处理区域选择
  const handleAreaChange = (area: TouchAreaEnum) => {
    setSelectedArea(area);
  };
  
  // 处理性别选择
  const handleGenderChange = (e: any) => {
    setSelectedGender(e.target.value);
  };
  
  // 处理添加动作
  const handleAddAction = () => {
    form.validateFields()
      .then(values => {
        const newAction: TouchAction = {
          id: `${Date.now()}`,
          type: values.type,
          name: values.name,
          value: values.value,
          probability: values.probability || 1,
        };
        
        addTouchAction(selectedGender, selectedArea, newAction);
        form.resetFields();
        message.success('动作添加成功');
      })
      .catch(error => {
        console.error('表单验证失败:', error);
      });
  };
  
  // 处理删除动作
  const handleRemoveAction = (actionId: string) => {
    removeTouchAction(selectedGender, selectedArea, actionId);
    message.success('动作删除成功');
  };
  
  // 处理表单提交
  const handleSubmit = () => {
    onSave();
    message.success('触摸设置保存成功');
  };
  
  // 渲染动作标签
  const renderActionTypeTag = (type: string) => {
    const colorMap: Record<string, string> = {
      animation: 'blue',
      sound: 'green',
      expression: 'purple',
      dialog: 'orange',
    };
    
    return (
      <Tag color={colorMap[type] || 'default'}>
        {actionTypes.find(t => t.value === type)?.label || type}
      </Tag>
    );
  };
  
  return (
    <div>
      <Alert
        message="触摸交互配置"
        description="配置角色在被触摸不同部位时的反应，包括动画、声音、表情和对话。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Card title="基础设置" style={{ marginBottom: 24 }}>
        <Form.Item label="性别选择" className="settings-form-item">
          <Radio.Group 
            value={selectedGender} 
            onChange={handleGenderChange}
            buttonStyle="solid"
          >
            <Radio.Button value={GenderEnum.FEMALE}>
              <Female size={14} />
              女性
            </Radio.Button>
            <Radio.Button value={GenderEnum.MALE}>
              <Male size={14} />
              男性
            </Radio.Button>
          </Radio.Group>
        </Form.Item>
        
        <Form.Item label="触摸区域" className="settings-form-item">
          <div className="touch-area-selector">
            {touchAreas.map(area => (
              <div
                key={area.value}
                className={`touch-area-item ${selectedArea === area.value ? 'active' : ''}`}
                onClick={() => handleAreaChange(area.value)}
              >
                {area.label}
              </div>
            ))}
          </div>
        </Form.Item>
      </Card>
      
      <Card 
        title={`${touchAreas.find(a => a.value === selectedArea)?.label || ''}动作配置`}
        style={{ marginBottom: 24 }}
      >
        <Form form={form} layout="vertical">
          <Space style={{ width: '100%' }} direction="vertical">
            <Form.Item
              name="type"
              label="动作类型"
              rules={[{ required: true, message: '请选择动作类型' }]}
              className="settings-form-item"
            >
              <Select placeholder="选择动作类型">
                {actionTypes.map(type => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="name"
              label="动作名称"
              rules={[{ required: true, message: '请输入动作名称' }]}
              className="settings-form-item"
            >
              <Select placeholder="选择动作名称">
                <Option value="smile">微笑</Option>
                <Option value="laugh">大笑</Option>
                <Option value="blush">脸红</Option>
                <Option value="angry">生气</Option>
                <Option value="surprise">惊讶</Option>
                <Option value="custom">自定义</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="value"
              label="动作值"
              rules={[{ required: true, message: '请输入动作值' }]}
              className="settings-form-item"
            >
              <Select placeholder="选择动作值">
                <Option value="default">默认</Option>
                <Option value="light">轻度</Option>
                <Option value="medium">中度</Option>
                <Option value="heavy">重度</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="probability"
              label="触发概率"
              className="settings-form-item"
            >
              <Select placeholder="选择触发概率">
                <Option value={1}>100%</Option>
                <Option value={0.75}>75%</Option>
                <Option value={0.5}>50%</Option>
                <Option value={0.25}>25%</Option>
              </Select>
            </Form.Item>
            
            <Button 
              type="dashed" 
              icon={<Plus size={14} />} 
              onClick={handleAddAction}
              style={{ width: '100%' }}
            >
              添加动作
            </Button>
          </Space>
        </Form>
        
        <Divider />
        
        <div className="touch-action-list">
          <List
            itemLayout="horizontal"
            dataSource={touchActions}
            locale={{ emptyText: '暂无动作配置，请添加' }}
            renderItem={action => (
              <div className="touch-action-item">
                <Space>
                  {renderActionTypeTag(action.type)}
                  <span>{action.name}</span>
                  <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                    {action.value}
                  </span>
                  {action.probability !== 1 && (
                    <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                      {action.probability * 100}%
                    </span>
                  )}
                </Space>
                <Button
                  type="text"
                  danger
                  icon={<Trash size={14} />}
                  onClick={() => handleRemoveAction(action.id)}
                />
              </div>
            )}
          />
        </div>
      </Card>
      
      {/* 保存按钮 */}
      <div className="settings-actions">
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          {t('touch.saveSettings', '保存设置')}
        </Button>
      </div>
    </div>
  );
};

export default TouchSettings;

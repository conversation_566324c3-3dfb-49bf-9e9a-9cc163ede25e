import type { ThemeMode } from 'antd-style';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';

import { Viewer } from '@/libs/vrmViewer/viewer';
import { ChatMode } from '@/types/session';

const viewer = new Viewer();

export enum SettingsTabs {
  Common = 'common',
  LLM = 'llm',
  TTS = 'tts',
  Touch = 'touch',
}

export interface GlobalStore {
  backgroundUrl: string | undefined;
  chatMode: ChatMode;
  hidePWAInstaller: boolean;
  isPlaying: boolean;
  // 新增：聊天模式管理
  interactive: boolean;
  previousChatMode: ChatMode;
  // 新增：用户偏好记忆
  userPreferences: {
    preferredChatMode: ChatMode;
    autoSwitchToCamera: boolean;
    rememberLastMode: boolean;
  };

  setBackgroundUrl: (url: string | undefined) => void;
  setChatDialog: (show: boolean) => void;
  setChatMode: (mode: ChatMode) => void;
  setChatSidebar: (show: boolean) => void;
  setHidePWAInstaller: (hide: boolean) => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setRoleList: (show: boolean) => void;
  setSessionList: (show: boolean) => void;
  setShowAgentInfo: (show: boolean) => void;
  setThemeMode: (themeMode: ThemeMode) => void;
  setVoiceOn: (voiceOn: boolean) => void;
  // 新增：聊天模式管理方法
  setInteractive: (interactive: boolean) => void;
  switchChatMode: (mode: ChatMode, options?: { savePreference?: boolean }) => void;
  toggleChatMode: () => void;
  updateUserPreferences: (preferences: Partial<GlobalStore['userPreferences']>) => void;

  showAgentInfo: boolean;
  showChatDialog: boolean;
  showChatSidebar: boolean;
  showRoleList: boolean;
  showSessionList: boolean;
  themeMode: ThemeMode;
  toggleAgentInfo: () => void;
  toggleChatDialog: () => void;
  toggleChatSideBar: () => void;
  toggleRoleList: () => void;
  toggleSessionList: () => void;

  viewer: Viewer;
  voiceOn: boolean;
}

const initialState = {
  viewer,
  themeMode: 'auto' as ThemeMode,
  chatMode: 'chat' as ChatMode,
  voiceOn: false,
  isPlaying: false,
  showChatSidebar: false,
  showAgentInfo: true,
  showSessionList: true,
  showChatDialog: true,
  hidePWAInstaller: false,
  showRoleList: true,
  backgroundUrl: undefined,
  // 新增状态初始值
  interactive: true,
  previousChatMode: 'chat' as ChatMode,
  userPreferences: {
    preferredChatMode: 'chat' as ChatMode,
    autoSwitchToCamera: false,
    rememberLastMode: true,
  },
};
export const useGlobalStore = createWithEqualityFn<GlobalStore>()(
  (set) => ({
    ...initialState,
    setIsPlaying: (isPlaying: boolean) => {
      set({ isPlaying: isPlaying });
    },
    setHidePWAInstaller: (hide: boolean) => {
      set({ hidePWAInstaller: hide });
    },
    setBackgroundUrl: (url: string | undefined) => {
      set({ backgroundUrl: url });
    },
    setThemeMode: (themeMode) => {
      set({ themeMode });
    },
    setChatSidebar: (show) => {
      set({ showChatSidebar: show });
    },
    setShowAgentInfo: (show) => {
      set({ showAgentInfo: show });
    },
    setChatMode: (mode) => {
      set({ chatMode: mode });
    },
    setRoleList: (show) => {
      set({ showRoleList: show });
    },
    toggleAgentInfo: () => {
      set((state) => ({ showAgentInfo: !state.showAgentInfo }));
    },
    toggleRoleList: () => {
      set((state) => ({ showRoleList: !state.showRoleList }));
    },
    toggleChatSideBar: () => {
      set((state) => ({ showChatSidebar: !state.showChatSidebar }));
    },
    setSessionList: (show) => {
      set({ showSessionList: show });
    },
    toggleSessionList: () => {
      set((state) => ({ showSessionList: !state.showSessionList }));
    },
    setChatDialog: (show) => {
      set({ showChatDialog: show });
    },
    toggleChatDialog: () => {
      set((state) => ({ showChatDialog: !state.showChatDialog }));
    },
    setVoiceOn: (voiceOn) => {
      set({ voiceOn });
    },
    // 新增：聊天模式管理方法
    setInteractive: (interactive) => {
      set({ interactive });
    },
    switchChatMode: (mode, options = {}) => {
      set((state) => {
        const newState: Partial<GlobalStore> = {
          previousChatMode: state.chatMode,
          chatMode: mode,
        };

        // 如果需要保存用户偏好
        if (options.savePreference) {
          newState.userPreferences = {
            ...state.userPreferences,
            preferredChatMode: mode,
          };
        }

        return newState;
      });
    },
    toggleChatMode: () => {
      set((state) => ({
        previousChatMode: state.chatMode,
        chatMode: state.chatMode === 'chat' ? 'camera' : 'chat',
      }));
    },
    updateUserPreferences: (preferences) => {
      set((state) => ({
        userPreferences: {
          ...state.userPreferences,
          ...preferences,
        },
      }));
    },
  }),
  shallow,
);

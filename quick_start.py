#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟角色平台快速启动脚本
Quick Start Script for Virtual Character Platform
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                  🎭 虚拟角色平台 v1.0                        ║
    ║              Virtual Character Platform                      ║
    ║                                                              ║
    ║  🎤 沉浸式语音交互  🎭 3D角色渲染  🧠 AI智能对话            ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查虚拟环境
    venv_path = Path("venv")
    if not venv_path.exists():
        print("❌ 虚拟环境不存在，请先运行: python -m venv venv")
        return False
    
    # 检查前端目录
    frontend_path = Path("virtual-character-platform-frontend")
    if not frontend_path.exists():
        print("❌ 前端目录不存在")
        return False
    
    # 检查数据库
    db_path = Path("db.sqlite3")
    if not db_path.exists():
        print("⚠️  数据库不存在，将自动创建")
    
    print("✅ 环境检查通过")
    return True

def check_env_file():
    """检查环境变量配置文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env文件不存在，创建示例配置文件...")
        create_env_template()
        print("📝 请编辑.env文件，配置您的API密钥")
        return False
    
    # 检查关键配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    required_keys = ['SPARK_APP_ID', 'SPARK_API_KEY', 'SPARK_API_SECRET']
    missing_keys = []
    
    for key in required_keys:
        if f"{key}=" not in content or f"{key}=your_" in content:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"⚠️  以下环境变量需要配置: {', '.join(missing_keys)}")
        print("📝 请编辑.env文件，配置您的API密钥")
        return False
    
    print("✅ 环境变量配置检查通过")
    return True

def create_env_template():
    """创建环境变量模板文件"""
    template = """# 星火AI配置 (必需)
SPARK_APP_ID=your_spark_app_id
SPARK_API_KEY=your_spark_api_key
SPARK_API_SECRET=your_spark_api_secret

# 讯飞TTS配置 (推荐)
XUNFEI_TTS_APP_ID=your_xunfei_tts_app_id
XUNFEI_TTS_API_KEY=your_xunfei_tts_api_key
XUNFEI_TTS_API_SECRET=your_xunfei_tts_api_secret

# 阿里云TTS配置 (可选)
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret

# 阿里云OSS配置 (可选，用于文件存储)
ALIYUN_OSS_ACCESS_KEY_ID=your_oss_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
ALIYUN_OSS_BUCKET_NAME=your_bucket_name
ALIYUN_OSS_ENDPOINT=your_oss_endpoint

# TTS服务配置
TTS_DEFAULT_PROVIDER=xunfei

# Django配置
DEBUG=True
SECRET_KEY=your_secret_key_here
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(template)

def setup_database():
    """设置数据库"""
    print("🗄️  设置数据库...")
    
    try:
        # 激活虚拟环境并运行迁移
        if os.name == 'nt':  # Windows
            python_cmd = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            python_cmd = "venv/bin/python"
        
        # 运行迁移
        result = subprocess.run([python_cmd, "manage.py", "migrate"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ 数据库迁移失败: {result.stderr}")
            return False
        
        print("✅ 数据库设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库设置失败: {e}")
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    try:
        if os.name == 'nt':  # Windows
            python_cmd = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            python_cmd = "venv/bin/python"
        
        # 启动Django服务器
        backend_process = subprocess.Popen([python_cmd, "manage.py", "runserver", "8000"])
        
        # 等待服务启动
        time.sleep(3)
        print("✅ 后端服务已启动 (http://localhost:8000)")
        return backend_process
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🎨 启动前端服务...")
    
    try:
        # 检查node_modules
        frontend_path = Path("virtual-character-platform-frontend")
        node_modules = frontend_path / "node_modules"
        
        if not node_modules.exists():
            print("📦 安装前端依赖...")
            subprocess.run(["npm", "install"], cwd=frontend_path, check=True)
        
        # 启动前端开发服务器
        frontend_process = subprocess.Popen(["npm", "run", "dev"], cwd=frontend_path)
        
        # 等待服务启动
        time.sleep(5)
        print("✅ 前端服务已启动 (http://localhost:5173)")
        return frontend_process
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请先配置环境")
        return
    
    # 检查环境变量
    if not check_env_file():
        print("❌ 请先配置.env文件中的API密钥")
        return
    
    # 设置数据库
    if not setup_database():
        print("❌ 数据库设置失败")
        return
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败")
        return
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端启动失败")
        backend_process.terminate()
        return
    
    # 打开浏览器
    print("🌐 正在打开浏览器...")
    time.sleep(2)
    webbrowser.open("http://localhost:5173")
    
    print("""
    🎉 虚拟角色平台启动成功！
    
    📱 前端界面: http://localhost:5173
    🔧 后端API: http://localhost:8000
    📚 管理后台: http://localhost:8000/admin
    
    💡 使用提示:
    1. 首先注册账户
    2. 创建虚拟角色
    3. 开始语音对话
    
    ⌨️  按 Ctrl+C 停止服务
    """)
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()

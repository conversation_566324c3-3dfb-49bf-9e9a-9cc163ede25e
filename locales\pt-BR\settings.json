{"common": {"chat": {"avatar": {"desc": "<PERSON><PERSON>r avatar", "title": "Avatar"}, "nickName": {"desc": "Personalizar <PERSON>", "placeholder": "Digite o apelido", "title": "Apelido"}, "title": "Configurações de Chat"}, "system": {"clear": {"action": "Limpar agora", "alert": "Confirmar <PERSON><PERSON><PERSON> de to<PERSON> as mensagens de conversa?", "desc": "<PERSON><PERSON> ir<PERSON> limpar todos os dados de conversa e personagem, incluindo lista de conversas, lista de personagens, mensagens de conversa, etc.", "success": "Limpeza bem-sucedida", "tip": "A operação não pode ser desfeita, após a limpeza os dados não poderão ser recuperados, por favor, proceda com cautela", "title": "<PERSON><PERSON> as mensagens de conversa"}, "clearCache": {"action": "<PERSON><PERSON>", "alert": "Confirmar a limpeza de todo o cache?", "calculating": "Calculando o tamanho do cache...", "desc": "<PERSON><PERSON> irá limpar o cache de dados baixados do aplicativo, incluindo dados de modelos de personagens, dados de voz, dados de modelos de dança, dados de áudio, entre outros.", "success": "Limpeza bem-sucedida", "tip": "A operação não pode ser desfeita. Após a limpeza, os dados precisarão ser baixados novamente. Por favor, proceda com cautela.", "title": "<PERSON><PERSON>"}, "reset": {"action": "Redefinir agora", "alert": "Confirmar redefinição de todas as configurações do sistema?", "desc": "Is<PERSON> ir<PERSON> redefinir todas as configurações do sistema, incluindo configurações de tema, configurações de chat, configurações de modelo de linguagem, etc.", "success": "Redefinição bem-sucedida", "tip": "A operação não pode ser desfeita, após a redefinição os dados não poderão ser recuperados, por favor, proceda com cautela", "title": "Redefinir configurações do sistema"}, "title": "Configurações do Sistema"}, "theme": {"backgroundEffect": {"desc": "Personalizar efeito de fundo", "glow": "Bril<PERSON>", "none": "Sem fundo", "title": "Efeito de Fundo"}, "locale": {"auto": "<PERSON><PERSON><PERSON>", "desc": "Personalizar idioma do sistema", "title": "Idioma"}, "neutralColor": {"desc": "Personalizar escala de cinzas com diferentes inclinações de cor", "title": "Cor Neutra"}, "primaryColor": {"desc": "Personalizar cor do tema", "title": "<PERSON><PERSON> <PERSON>"}, "title": "Configurações de Tema"}, "title": "Configurações Gerais"}, "header": {"desc": "Preferências e Configurações do Modelo", "global": "Configurações Globais", "session": "Configurações da Sessão", "sessionDesc": "Configurações de Papel e Preferências da Sessão", "sessionWithName": "Configuraç<PERSON><PERSON> da Sessão · {{name}}", "title": "Configurações"}, "llm": {"aesGcm": "Sua chave e o endereço do proxy serão criptografados usando o algoritmo de criptografia <1>AES-GCM</1>", "apiKey": {"desc": "Por favor, insira sua chave API {{name}}", "placeholder": "Chave API {{name}}", "title": "Chave API"}, "checker": {"button": "Verificar", "desc": "Teste se a chave API e o endereço do proxy estão preenchidos corretamente", "error": "Verificação falhou", "pass": "Verificação bem-sucedida", "title": "Verificação de Conectividade"}, "customModelCards": {"addNew": "C<PERSON>r e adicionar o modelo {{id}}", "config": "Configurar modelo", "confirmDelete": "Você está prestes a excluir este modelo personalizado. Após a exclusão, não poderá ser recuperado. Por favor, proceda com cautela.", "modelConfig": {"azureDeployName": {"extra": "Campo solicitado na Azure OpenAI", "placeholder": "Insira o nome de implantação do modelo na Azure", "title": "Nome de Implantação do Modelo"}, "displayName": {"placeholder": "Insira o nome de exibição do modelo, como ChatGPT, GPT-4, etc.", "title": "Nome de Exibição do Modelo"}, "files": {"extra": "A implementação atual de upload de arquivos é apenas uma solução alternativa, limitada a tentativas pessoais. A capacidade completa de upload de arquivos será implementada posteriormente.", "title": "Suporte a Upload de Arquivos"}, "functionCall": {"extra": "Esta configuração ativará apenas a capacidade de chamada de função no aplicativo. O suporte a chamadas de função depende totalmente do modelo em si. Teste a disponibilidade da capacidade de chamada de função desse modelo.", "title": "Suporte a Chamadas de Função"}, "id": {"extra": "Será exibido como um rótulo do modelo", "placeholder": "Insira o id do modelo, como gpt-4-turbo-preview ou claude-2.1", "title": "ID do Modelo"}, "modalTitle": "Configuração do Modelo Personalizado", "tokens": {"title": "Número máximo de tokens", "unlimited": "<PERSON><PERSON><PERSON><PERSON>"}, "vision": {"extra": "Esta configuração ativará apenas a configuração de upload de imagens no aplicativo. O suporte ao reconhecimento depende totalmente do modelo em si. Teste a disponibilidade da capacidade de reconhecimento visual desse modelo.", "title": "Suporte ao Reconhecimento Visual"}}}, "fetchOnClient": {"desc": "O modo de solicitação do cliente fará a solicitação de sessão diretamente do navegador, o que pode aumentar a velocidade de resposta", "title": "Usar modo de solicitação do cliente"}, "fetcher": {"fetch": "Obter lista de modelos", "fetching": "Obtendo lista de modelos...", "latestTime": "Última atualização: {{time}}", "noLatestTime": "Lista ainda não obtida"}, "helpDoc": "Tutorial de configuração", "modelList": {"desc": "Escolha os modelos a serem exibidos na sessão; os modelos selecionados serão mostrados na lista de modelos", "placeholder": "Por favor, selecione um modelo da lista", "title": "Lista de Modelos", "total": "Um total de {{count}} modelos disponíveis"}, "proxyUrl": {"desc": "Além do endereço padrão, deve incluir http(s)://", "title": "Endereço do proxy da API"}, "title": "Modelo de Linguagem", "waitingForMore": "Mais modelos estão <1>planejados para serem integrados</1>, fique atento"}, "systemAgent": {"customPrompt": {"addPrompt": "Adicionar Prompt Personalizado", "desc": "<PERSON><PERSON><PERSON> preenchido, o assistente do sistema usará o prompt personalizado ao gerar conteúdo", "placeholder": "Digite a palavra-chave do prompt personalizado", "title": "Palavra-chave do Prompt Personalizado"}, "emotionAnalysis": {"label": "Modelo de Análise de Emoção", "modelDesc": "Modelo especificado para análise de emoção", "title": "Análise de Emoção Automática"}, "title": "Agente do Sistema"}, "touch": {"title": "Configurações de Toque"}, "tts": {"clientCall": {"desc": "<PERSON>uando at<PERSON>, o serviço de síntese de voz será chamado pelo cliente, resultando em uma velocidade de síntese de voz mais rápida, mas requer acesso à internet ou a capacidade de contornar restrições de rede.", "title": "Chamada do Cliente"}, "title": "Configurações de Voz"}}
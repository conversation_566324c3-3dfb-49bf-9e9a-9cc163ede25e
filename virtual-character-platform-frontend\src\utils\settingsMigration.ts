import { useSettingStore } from '../store/setting';

/**
 * 旧版设置数据结构
 */
interface OldSettingsData {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    chat: boolean;
    system: boolean;
  };
  privacy: {
    profilePublic: boolean;
    charactersPublic: boolean;
    chatHistory: boolean;
  };
  accessibility: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    reduceMotion: boolean;
  };
}

/**
 * 将SettingsPage组件中的设置数据迁移到SettingStore
 * @param settings 旧版设置数据
 * @returns 是否迁移成功
 */
export const migrateSettingsToStore = (settings: OldSettingsData): boolean => {
  try {
    const settingStore = useSettingStore.getState();
    
    // 更新主题设置
    // 注意：主题设置已经通过themeStore处理，这里只需要更新其他设置
    
    // 更新语言设置
    if (settings.language) {
      settingStore.switchLocale(settings.language === 'en-US' ? 'en-US' : 'zh-CN');
    }
    
    // 更新通知设置
    if (settings.notifications) {
      settingStore.updateNotifications(settings.notifications);
    }
    
    // 更新隐私设置
    if (settings.privacy) {
      settingStore.updatePrivacy(settings.privacy);
    }
    
    // 更新无障碍设置
    if (settings.accessibility) {
      settingStore.updateAccessibility(settings.accessibility);
    }
    
    // 保存旧版设置到localStorage，以便迁移函数可以读取
    localStorage.setItem('settings', JSON.stringify(settings));
    
    return true;
  } catch (error) {
    console.error('Failed to migrate settings to store:', error);
    return false;
  }
};

/**
 * 检查是否需要迁移设置
 * @returns 是否需要迁移
 */
export const checkNeedsMigration = (): boolean => {
  // 检查localStorage中是否存在旧版设置数据
  const oldSettingsStr = localStorage.getItem('settings');
  if (!oldSettingsStr) return false;
  
  try {
    // 尝试解析旧版设置数据
    const oldSettings = JSON.parse(oldSettingsStr);
    
    // 检查是否包含必要的字段
    return !!(
      oldSettings &&
      (oldSettings.notifications || 
       oldSettings.privacy || 
       oldSettings.accessibility)
    );
  } catch (error) {
    return false;
  }
};

/**
 * 执行设置迁移
 * 在应用启动时调用此函数，自动迁移旧版设置
 */
export const performSettingsMigration = (): void => {
  if (checkNeedsMigration()) {
    try {
      const oldSettingsStr = localStorage.getItem('settings');
      if (!oldSettingsStr) return;
      
      const oldSettings = JSON.parse(oldSettingsStr) as OldSettingsData;
      migrateSettingsToStore(oldSettings);
      
      console.log('Settings migration completed successfully');
    } catch (error) {
      console.error('Settings migration failed:', error);
    }
  }
};

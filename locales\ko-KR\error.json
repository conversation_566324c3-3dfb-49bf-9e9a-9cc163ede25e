{"apiKeyMiss": "OpenAI API 키가 비어 있습니다. 사용자 정의 OpenAI API 키를 추가해 주세요.", "dancePlayError": "댄스 파일 재생에 실패했습니다. 잠시 후 다시 시도해 주세요.", "error": "오류", "errorTip": {"clearSession": "세션 메시지 지우기", "description": "현재 프로젝트가 진행 중이며 데이터의 안정성을 보장할 수 없습니다. 문제가 발생하면 시도해 보시기 바랍니다.", "forgive": "불편을 드려 죄송합니다.", "or": "또는", "problem": "페이지에 문제가 발생했습니다...", "resetSystem": "시스템 설정 초기화"}, "fileUploadError": "파일 업로드에 실패했습니다. 잠시 후 다시 시도해 주세요.", "formValidationFailed": "양식 검증 실패:", "goBack": "홈으로 돌아가기", "openaiError": "OpenAI API 오류입니다. OpenAI API 키와 엔드포인트가 올바른지 확인하세요.", "reload": "다시 로드", "response": {"400": "죄송합니다. 서버가 귀하의 요청을 이해하지 못했습니다. 요청 매개변수가 올바른지 확인해 주십시오.", "401": "죄송합니다. 서버가 귀하의 요청을 거부했습니다. 권한이 부족하거나 유효한 인증을 제공하지 않았을 수 있습니다.", "403": "죄송합니다. 서버가 귀하의 요청을 거부했습니다. 이 콘텐츠에 대한 접근 권한이 없습니다.", "404": "죄송합니다. 서버가 요청한 페이지나 리소스를 찾을 수 없습니다. URL이 올바른지 확인해 주십시오.", "405": "죄송합니다. 서버가 사용 중인 요청 방법을 지원하지 않습니다. 요청 방법이 올바른지 확인해 주십시오.", "406": "죄송합니다. 서버가 요청한 콘텐츠 특성에 따라 요청을 완료할 수 없습니다.", "407": "죄송합니다. 이 요청을 계속 진행하려면 프록시 인증이 필요합니다.", "408": "죄송합니다. 서버가 요청을 기다리는 동안 시간 초과가 발생했습니다. 네트워크 연결을 확인한 후 다시 시도해 주십시오.", "409": "죄송합니다. 요청에 충돌이 있어 처리할 수 없습니다. 리소스 상태가 요청과 호환되지 않을 수 있습니다.", "410": "죄송합니다. 요청한 리소스가 영구적으로 제거되어 찾을 수 없습니다.", "411": "죄송합니다. 서버가 유효한 콘텐츠 길이가 없는 요청을 처리할 수 없습니다.", "412": "죄송합니다. 귀하의 요청이 서버 측 조건을 충족하지 않아 요청을 완료할 수 없습니다.", "413": "죄송합니다. 귀하의 요청 데이터 양이 너무 많아 서버가 처리할 수 없습니다.", "414": "죄송합니다. 귀하의 요청 URI가 너무 길어 서버가 처리할 수 없습니다.", "415": "죄송합니다. 서버가 요청에 첨부된 미디어 형식을 처리할 수 없습니다.", "416": "죄송합니다. 서버가 귀하의 요청 범위를 충족할 수 없습니다.", "417": "죄송합니다. 서버가 귀하의 기대치를 충족할 수 없습니다.", "422": "죄송합니다. 귀하의 요청 형식은 올바르지만 의미적 오류로 인해 응답할 수 없습니다.", "423": "죄송합니다. 요청한 리소스가 잠겨 있습니다.", "424": "죄송합니다. 이전 요청이 실패하여 현재 요청을 완료할 수 없습니다.", "426": "죄송합니다. 서버가 클라이언트의 프로토콜 버전을 업그레이드할 것을 요구합니다.", "428": "죄송합니다. 서버가 선결 조건을 요구하며 귀하의 요청에 올바른 조건 헤더가 포함되어야 합니다.", "429": "죄송합니다. 요청이 너무 많아 서버가 과부하 상태입니다. 잠시 후 다시 시도해 주십시오.", "431": "죄송합니다. 요청 헤더 필드가 너무 커서 서버가 처리할 수 없습니다.", "451": "죄송합니다. 법적 이유로 서버가 이 리소스를 제공할 수 없습니다.", "500": "죄송합니다. 서버에 문제가 발생하여 요청을 완료할 수 없습니다. 잠시 후 다시 시도해 주십시오.", "501": "죄송합니다. 서버가 이 요청을 처리하는 방법을 아직 알지 못합니다. 작업이 올바른지 확인해 주십시오.", "502": "죄송합니다. 서버가 방향을 잃은 것 같습니다. 잠시 후 다시 시도해 주십시오.", "503": "죄송합니다. 서버가 현재 귀하의 요청을 처리할 수 없습니다. 과부하 또는 유지 관리 중일 수 있습니다. 잠시 후 다시 시도해 주십시오.", "504": "죄송합니다. 서버가 상위 서버의 응답을 기다리지 못했습니다. 잠시 후 다시 시도해 주십시오.", "505": "죄송합니다. 서버가 사용 중인 HTTP 버전을 지원하지 않습니다. 업데이트 후 다시 시도해 주십시오.", "506": "죄송합니다. 서버 구성에 문제가 발생했습니다. 관리자에게 문의하여 해결해 주십시오.", "507": "죄송합니다. 서버의 저장 공간이 부족하여 요청을 처리할 수 없습니다. 잠시 후 다시 시도해 주십시오.", "509": "죄송합니다. 서버의 대역폭이 소진되었습니다. 잠시 후 다시 시도해 주십시오.", "510": "죄송합니다. 서버가 요청한 확장 기능을 지원하지 않습니다. 관리자에게 문의해 주십시오.", "524": "죄송합니다. 서버가 응답을 기다리는 동안 시간 초과가 발생했습니다. 응답이 너무 느릴 수 있습니다. 잠시 후 다시 시도해 주십시오.", "AgentRuntimeError": "Lobe AI 런타임 실행 중 오류가 발생했습니다. 아래 정보를 바탕으로 문제를 확인하거나 다시 시도해 주십시오.", "FreePlanLimit": "현재 무료 사용자입니다. 해당 기능을 사용할 수 없습니다. 유료 플랜으로 업그레이드한 후 계속 사용해 주십시오.", "InvalidAccessCode": "비밀번호가 올바르지 않거나 비어 있습니다. 올바른 접근 비밀번호를 입력하거나 사용자 정의 API 키를 추가해 주십시오.", "InvalidBedrockCredentials": "Bedrock 인증에 실패했습니다. AccessKeyId/SecretAccessKey를 확인한 후 다시 시도해 주십시오.", "InvalidClerkUser": "죄송합니다. 현재 로그인하지 않으셨습니다. 먼저 로그인하거나 계정을 등록한 후 계속 진행해 주십시오.", "InvalidGithubToken": "Github PAT가 올바르지 않거나 비어 있습니다. Github PAT를 확인한 후 다시 시도해 주십시오.", "InvalidOllamaArgs": "Ollama 구성이 올바르지 않습니다. Ollama 구성을 확인한 후 다시 시도해 주십시오.", "InvalidProviderAPIKey": "{{provider}} API 키가 올바르지 않거나 비어 있습니다. {{provider}} API 키를 확인한 후 다시 시도해 주십시오.", "LocationNotSupportError": "죄송합니다. 귀하의 지역에서는 이 모델 서비스를 지원하지 않습니다. 지역 제한이나 서비스가 개설되지 않았을 수 있습니다. 현재 지역이 이 서비스를 지원하는지 확인하거나 다른 지역으로 전환한 후 다시 시도해 주십시오.", "OllamaBizError": "Ollama 서비스 요청 중 오류가 발생했습니다. 아래 정보를 바탕으로 문제를 확인하거나 다시 시도해 주십시오.", "OllamaServiceUnavailable": "Ollama 서비스 연결에 실패했습니다. Ollama가 정상적으로 실행되고 있는지 또는 Ollama의 교차 출처 구성이 올바르게 설정되었는지 확인해 주십시오.", "PermissionDenied": "죄송합니다. 해당 서비스에 접근할 권한이 없습니다. 귀하의 키에 접근 권한이 있는지 확인해 주십시오.", "PluginApiNotFound": "죄송합니다. 플러그인 설명 목록에 해당 API가 존재하지 않습니다. 요청 방법과 플러그인 목록 API가 일치하는지 확인해 주십시오.", "PluginApiParamsError": "죄송합니다. 해당 플러그인 요청의 입력 매개변수 검증에 실패했습니다. 입력 매개변수와 API 설명 정보가 일치하는지 확인해 주십시오.", "PluginFailToTransformArguments": "죄송합니다. 플러그인 호출 매개변수 분석에 실패했습니다. 도우미 메시지를 다시 생성하거나 더 강력한 AI 모델로 도구 호출을 변경한 후 다시 시도해 주십시오.", "PluginGatewayError": "죄송합니다. 플러그인 게이트웨이에 오류가 발생했습니다. 플러그인 게이트웨이 구성이 올바른지 확인해 주십시오.", "PluginManifestInvalid": "죄송합니다. 해당 플러그인의 설명 목록 검증에 실패했습니다. 설명 목록 형식이 규격에 맞는지 확인해 주십시오.", "PluginManifestNotFound": "죄송합니다. 서버가 해당 플러그인의 설명 목록(manifest.json)을 찾을 수 없습니다. 플러그인 설명 파일 주소가 올바른지 확인해 주십시오.", "PluginMarketIndexInvalid": "죄송합니다. 플러그인 인덱스 검증에 실패했습니다. 인덱스 파일 형식이 규격에 맞는지 확인해 주십시오.", "PluginMarketIndexNotFound": "죄송합니다. 서버가 플러그인 인덱스를 찾을 수 없습니다. 인덱스 주소가 올바른지 확인해 주십시오.", "PluginMetaInvalid": "죄송합니다. 해당 플러그인의 메타 정보 검증에 실패했습니다. 플러그인 메타 정보 형식이 규격에 맞는지 확인해 주십시오.", "PluginMetaNotFound": "죄송합니다. 인덱스에서 해당 플러그인을 찾을 수 없습니다. 플러그인이 인덱스의 구성 정보를 확인해 주십시오.", "PluginOpenApiInitError": "죄송합니다. OpenAPI 클라이언트 초기화에 실패했습니다. OpenAPI의 구성 정보가 올바른지 확인해 주십시오.", "PluginServerError": "플러그인 서버 요청에서 오류가 발생했습니다. 아래의 오류 정보를 바탕으로 플러그인 설명 파일, 플러그인 구성 또는 서버 구현을 확인해 주십시오.", "PluginSettingsInvalid": "해당 플러그인은 올바르게 구성된 후에만 사용할 수 있습니다. 구성이 올바른지 확인해 주십시오.", "ProviderBizError": "{{provider}} 서비스 요청 중 오류가 발생했습니다. 아래 정보를 바탕으로 문제를 확인하거나 다시 시도해 주십시오.", "QuotaLimitReached": "죄송합니다. 현재 토큰 사용량 또는 요청 횟수가 해당 키의 할당량에 도달했습니다. 해당 키의 할당량을 늘리거나 잠시 후 다시 시도해 주십시오.", "StreamChunkError": "스트리밍 요청의 메시지 블록 분석 오류가 발생했습니다. 현재 API 인터페이스가 표준 규격에 맞는지 확인하거나 API 공급자에게 문의해 주십시오.", "SubscriptionPlanLimit": "귀하의 구독 한도가 소진되었습니다. 해당 기능을 사용할 수 없습니다. 더 높은 플랜으로 업그레이드하거나 리소스 패키지를 구매한 후 계속 사용해 주십시오.", "UnknownChatFetchError": "죄송합니다. 알 수 없는 요청 오류가 발생했습니다. 아래 정보를 바탕으로 문제를 확인하거나 다시 시도해 주십시오."}, "s3envError": "S3 환경 변수가 완전히 설정되지 않았습니다. 환경 변수를 확인해 주세요.", "serverError": "서버 오류가 발생했습니다. 관리자에게 문의하세요.", "triggerError": "오류 발생", "ttsTransformFailed": "음성 변환에 실패했습니다. 네트워크를 확인하거나 설정에서 클라이언트 호출을 활성화한 후 다시 시도해 주세요.", "unknownError": "알 수 없는 오류", "unlock": {"addProxyUrl": "OpenAI 프록시 주소 추가(선택 사항)", "apiKey": {"description": "당신의 {{name}} API Key를 입력하면 대화를 시작할 수 있습니다", "title": "사용자 정의 {{name}} API Key 사용"}, "closeMessage": "팝업 닫기", "confirm": "확인하고 재시도", "oauth": {"description": "관리자가 통합 로그인 인증을 활성화했습니다. 아래 버튼을 클릭하여 로그인하면 애플리케이션이 잠금 해제됩니다", "success": "로그인 성공", "title": "계정 로그인", "welcome": "환영합니다!"}, "password": {"description": "관리자가 애플리케이션 암호화를 활성화했습니다. 애플리케이션 비밀번호를 입력하면 애플리케이션이 잠금 해제됩니다. 비밀번호는 한 번만 입력하면 됩니다", "placeholder": "비밀번호를 입력하세요", "title": "비밀번호 입력하여 애플리케이션 잠금 해제"}, "tabs": {"apiKey": "사용자 정의 API Key", "password": "비밀번호"}}}
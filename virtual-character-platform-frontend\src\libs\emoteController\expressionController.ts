import { VRM, VRMExpressionManager, VRMExpressionPresetName } from '@pixiv/three-vrm';
import * as THREE from 'three';

import { AutoBlink } from './autoBlink';
import { EMOTION_TO_EXPRESSION_MAP } from './emoteConstants';
import type { EmotionType } from './emoteConstants';

/**
 * 简单的视线跟踪类 - 基于 Lobe Vidol 的实现
 */
class AutoLookAt {
  private _lookAtTarget: THREE.Object3D;

  constructor(vrm: VRM, camera: THREE.Object3D) {
    this._lookAtTarget = new THREE.Object3D();
    camera.add(this._lookAtTarget);

    if (vrm.lookAt) {
      vrm.lookAt.target = this._lookAtTarget;
    }
  }

  public setTarget(position: THREE.Vector3): void {
    this._lookAtTarget.position.copy(position);
  }

  public setEnable(enabled: boolean): void {
    // 简单实现，可以根据需要扩展
  }

  public update(delta: number): void {
    // 简单实现，可以根据需要扩展
  }
}

/**
 * 表情管理类
 *
 * 主要负责保持前一个表情，并在应用下一个表情时将其重置为0，
 * 以及在前一个表情结束后再应用新的表情。
 */
export class ExpressionController {
  private _autoLookAt: AutoLookAt;
  private _autoBlink?: AutoBlink;
  private _expressionManager?: VRMExpressionManager;
  private _currentEmotion: VRMExpressionPresetName;
  private _currentLipSync: {
    preset: VRMExpressionPresetName;
    value: number;
  } | null;

  constructor(vrm: VRM, camera: THREE.Object3D) {
    this._autoLookAt = new AutoLookAt(vrm, camera);
    this._currentEmotion = 'neutral';
    this._currentLipSync = null;
    
    if (vrm.expressionManager) {
      this._expressionManager = vrm.expressionManager;
      this._autoBlink = new AutoBlink(vrm.expressionManager);
    }
  }

  /**
   * 播放情感表情
   */
  public playEmotion(preset: VRMExpressionPresetName): void {
    if (!this._expressionManager) {
      console.warn('ExpressionController: 表情管理器未初始化');
      return;
    }

    // 重置当前表情
    if (this._currentEmotion !== 'neutral') {
      this._expressionManager.setValue(this._currentEmotion, 0);
    }

    // 如果是中性表情，启用自动眨眼
    if (preset === 'neutral') {
      this._autoBlink?.setEnable(true);
      this._currentEmotion = preset;
      return;
    }

    // 禁用自动眨眼并等待眼睛睁开
    const waitTime = this._autoBlink?.setEnable(false) || 0;
    this._currentEmotion = preset;
    
    setTimeout(() => {
      if (this._expressionManager) {
        this._expressionManager.setValue(preset, 1);
      }
    }, waitTime * 1000);
  }

  /**
   * 根据情感类型播放表情
   */
  public playEmotionByType(emotionType: EmotionType): void {
    const expressionPreset = EMOTION_TO_EXPRESSION_MAP[emotionType];
    this.playEmotion(expressionPreset as VRMExpressionPresetName);
  }

  /**
   * 口型同步
   */
  public lipSync(preset: VRMExpressionPresetName, value: number): void {
    if (!this._expressionManager) return;

    // 重置之前的口型
    if (this._currentLipSync) {
      this._expressionManager.setValue(this._currentLipSync.preset, 0);
    }
    
    this._currentLipSync = {
      preset,
      value,
    };
  }

  /**
   * 基于音量的简单口型同步
   */
  public lipSyncFromVolume(volume: number): void {
    if (!this._expressionManager) return;

    // 根据音量选择口型
    let preset: VRMExpressionPresetName = 'aa';
    if (volume > 0.7) {
      preset = 'aa'; // 大音量用 aa
    } else if (volume > 0.4) {
      preset = 'ih'; // 中音量用 ih
    } else if (volume > 0.1) {
      preset = 'ou'; // 小音量用 ou
    } else {
      preset = 'neutral'; // 无声音时闭嘴
    }

    this.lipSync(preset, volume);
  }

  /**
   * 设置视线目标
   */
  public setLookAtTarget(position: THREE.Vector3): void {
    this._autoLookAt.setTarget(position);
  }

  /**
   * 启用/禁用视线跟踪
   */
  public setLookAtEnabled(enabled: boolean): void {
    this._autoLookAt.setEnable(enabled);
  }

  /**
   * 启用/禁用自动眨眼
   */
  public setAutoBlinkEnabled(enabled: boolean): void {
    this._autoBlink?.setEnable(enabled);
  }

  /**
   * 更新表情控制器
   */
  public update(delta: number): void {
    // 更新自动眨眼
    if (this._autoBlink) {
      this._autoBlink.update(delta);
    }

    // 更新视线跟踪
    this._autoLookAt.update(delta);

    // 更新口型同步
    if (this._currentLipSync && this._expressionManager) {
      const weight =
        this._currentEmotion === 'neutral'
          ? this._currentLipSync.value * 0.5
          : this._currentLipSync.value * 0.25;
      this._expressionManager.setValue(this._currentLipSync.preset, weight);
    }
  }

  // 获取当前状态
  public getCurrentEmotion(): VRMExpressionPresetName {
    return this._currentEmotion;
  }

  public getCurrentLipSync(): { preset: VRMExpressionPresetName; value: number } | null {
    return this._currentLipSync;
  }

  public isAutoBlinkEnabled(): boolean {
    return this._autoBlink?.isAutoBlinkEnabled() || false;
  }

  public isLookAtEnabled(): boolean {
    return this._autoLookAt.isEnabled();
  }
}

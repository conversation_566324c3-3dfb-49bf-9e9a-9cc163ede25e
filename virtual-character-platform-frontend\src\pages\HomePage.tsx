import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, Card, Spin, message, Empty, Input, Button, Typography, Space, Tag } from 'antd';
import { SearchOutlined, HeartOutlined, HeartFilled, MessageOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { characterAPI } from '../services/characterAPI';
import LazyImage from '../components/LazyImage';
import PerformanceMonitor from '../components/PerformanceMonitor';

import { processImageUrl } from '../utils/imageUtils';
import '../styles/homepage.css';

const { Content } = Layout;
const { Search } = Input;
const { Title, Text, Paragraph } = Typography;

// 角色类型定义
interface Character {
  id: string;
  name: string;
  imageUrl: string;
  creator: {
    id: string;
    username: string;
  };
  personality?: string;
  identity?: string;
  createdAt: string;
  likes: number;
  isLiked?: boolean;
  category?: string;
}

// 分类类型定义
interface Category {
  id: string;
  name: string;
  count: number;
  nsfw?: boolean;
  order: number;
}

const HomePage: React.FC = () => {
  console.log('🏠 HomePage component rendering...');

  const navigate = useNavigate();

  // 状态管理
  const [characters, setCharacters] = useState<Character[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  console.log('🏠 HomePage state:', {
    charactersCount: characters.length,
    categoriesCount: categories.length,
    loading,
    categoriesLoading
  });
  
  // 模拟分类数据（后续需要从后端获取）
  const mockCategories: Category[] = [
    { id: 'all', name: '全部', count: 15730, order: 1 },
    { id: 'female', name: '女性', count: 8869, order: 2 },
    { id: 'male', name: '男性', count: 3651, order: 3 },
    { id: 'anime', name: '动漫', count: 5219, order: 4 },
    { id: 'game', name: '游戏', count: 4475, order: 5 },
    { id: 'original', name: '原创', count: 2156, order: 6 },
    { id: 'nsfw', name: 'NSFW', count: 3796, nsfw: true, order: 10 },
  ];

  // 加载分类列表
  const loadCategories = async () => {
    setCategoriesLoading(true);
    try {
      // 暂时使用模拟数据，后续可以调用后端API
      setCategories(mockCategories);
    } catch (error) {
      console.error('获取分类列表失败:', error);
      setCategories(mockCategories);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 测试API连接
  const testAPI = async () => {
    try {
      console.log('🧪 Testing direct API call...');
      const response = await fetch('http://127.0.0.1:8000/api/characters/public_list/?page=1&pageSize=12');
      console.log('🧪 Fetch response:', response);
      const data = await response.json();
      console.log('🧪 Fetch data:', data);
      return data;
    } catch (error) {
      console.error('🧪 Direct API test failed:', error);
      return null;
    }
  };

  // 加载角色列表
  const loadCharacters = async (category = selectedCategory, query = searchQuery) => {
    console.log('🔄 Loading characters...', { category, query });
    setLoading(true);

    try {
      // 使用公开角色API获取角色列表
      console.log('🚀 Calling API with params:', { page: 1, pageSize: 12, query, sort: 'latest' });
      const response = await characterAPI.getPublicCharacters(1, 12, query, 'latest');
      console.log('📡 Full API Response:', response);
      console.log('📡 Response type:', typeof response);
      console.log('📡 Response.data:', response?.data);
      console.log('📡 Response.data type:', typeof response?.data);

      // 处理API响应数据
      let charactersData = null;

      // 检查不同的响应格式
      if (response && response.data && Array.isArray(response.data.characters)) {
        charactersData = response.data.characters;
        console.log('✅ Found characters in response.data.characters, count:', charactersData.length);
      } else if (response && (response as any).characters && Array.isArray((response as any).characters)) {
        charactersData = (response as any).characters;
        console.log('✅ Found characters in response.characters, count:', charactersData.length);
      } else if (response && response.data && Array.isArray(response.data)) {
        charactersData = response.data;
        console.log('✅ Found characters in response.data (array), count:', charactersData.length);
      } else if (response && Array.isArray(response)) {
        charactersData = response;
        console.log('✅ Found characters in response (array), count:', charactersData.length);
      } else {
        console.log('❌ No valid characters array found in response');
        console.log('Response structure:', {
          hasResponse: !!response,
          hasData: !!(response && response.data),
          dataType: response?.data ? typeof response.data : 'undefined',
          dataKeys: response?.data ? Object.keys(response.data) : [],
          responseKeys: response ? Object.keys(response) : []
        });
      }

      if (charactersData && charactersData.length > 0) {
        console.log('📊 Processing', charactersData.length, 'characters');
        const transformedCharacters = charactersData.map((char: any) => ({
          id: char.id.toString(),
          name: char.name,
          imageUrl: processImageUrl(char.image_url || char.imageUrl),
          creator: {
            id: char.creator_id?.toString() || char.creatorId?.toString() || '1',
            username: char.creator_name || char.creatorName || '系统用户'
          },
          personality: char.personality,
          identity: char.identity,
          createdAt: char.created_at || char.createdAt || new Date().toISOString(),
          likes: char.likes_count || char.likesCount || Math.floor(Math.random() * 100),
          isLiked: char.is_liked || char.isLiked || false,
          category: char.category || mockCategories[Math.floor(Math.random() * mockCategories.length)].id
        }));

        // 如果有分类筛选，进行客户端过滤
        const filteredCharacters = category === 'all'
          ? transformedCharacters
          : transformedCharacters.filter((char: Character) => char.category === category);

        console.log('🎯 Setting', filteredCharacters.length, 'filtered characters');
        setCharacters(filteredCharacters);
      } else {
        console.log('❌ No characters data found, using mock data');
        // 如果API没有返回数据，使用模拟数据
        const mockCharacters = Array(12).fill(0).map((_, index) => ({
          id: `${index + 1}`,
          name: `测试角色${index + 1}`,
          imageUrl: '/placeholder-character.svg',
          creator: {
            id: '1',
            username: '系统用户'
          },
          personality: ['温柔', '活泼', '傲娇', '冷酷'][Math.floor(Math.random() * 4)],
          identity: ['高中生', '魔法师', '偶像', '公主'][Math.floor(Math.random() * 4)],
          createdAt: new Date().toISOString(),
          likes: Math.floor(Math.random() * 100),
          isLiked: Math.random() > 0.5,
          category: mockCategories[Math.floor(Math.random() * mockCategories.length)].id
        }));

        const filteredMockCharacters = category === 'all'
          ? mockCharacters
          : mockCharacters.filter((char: Character) => char.category === category);

        setCharacters(filteredMockCharacters);
      }
    } catch (error) {
      console.error('获取推荐角色失败:', error);

      // 使用模拟数据作为后备
      const mockCharacters = Array(12).fill(0).map((_, index) => ({
        id: `${index + 1}`,
        name: `角色${index + 1}`,
        imageUrl: '/placeholder-character.svg',
        creator: {
          id: '1',
          username: `用户${Math.floor(Math.random() * 100)}`
        },
        personality: ['温柔', '活泼', '傲娇', '冷酷'][Math.floor(Math.random() * 4)],
        identity: ['高中生', '魔法师', '偶像', '公主'][Math.floor(Math.random() * 4)],
        createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
        likes: Math.floor(Math.random() * 100),
        isLiked: Math.random() > 0.5,
        category: mockCategories[Math.floor(Math.random() * mockCategories.length)].id
      }));

      // 应用分类筛选
      const filteredMockCharacters = category === 'all'
        ? mockCharacters
        : mockCharacters.filter((char: Character) => char.category === category);

      setCharacters(filteredMockCharacters);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadCategories();
    loadCharacters();
  }, []);

  // 处理分类切换
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    loadCharacters(categoryId);
  };

  // 处理搜索 - 使用 useCallback 优化
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
    loadCharacters(selectedCategory, value);
  }, [selectedCategory]);

  // 处理点赞 - 使用 useCallback 优化
  const handleLike = useCallback(async (character: Character) => {
    try {
      const updatedCharacters = characters.map(c => {
        if (c.id === character.id) {
          return {
            ...c,
            likes: c.isLiked ? c.likes - 1 : c.likes + 1,
            isLiked: !c.isLiked
          };
        }
        return c;
      });

      setCharacters(updatedCharacters);
    } catch (error) {
      console.error('更新点赞状态失败:', error);
      message.error('操作失败，请稍后再试');
    }
  }, [characters]);

  // 处理聊天 - 使用 useCallback 优化
  const handleChat = useCallback((character: Character) => {
    navigate(`/chat/${character.id}`);
  }, [navigate]);

  // 性能监控回调
  const handlePerformanceMetrics = useCallback((metrics: any) => {
    // 可以发送到分析服务
    console.log('Performance metrics:', metrics);
  }, []);

  return (
    <Content className="homepage-container">
      <PerformanceMonitor
        onMetrics={handlePerformanceMetrics}
        enableLogging={process.env.NODE_ENV === 'development'}
      />
      {/* 欢迎区域 */}
      <div className="welcome-section animate-fade-in">
        <div className="welcome-content">
          <Title level={1} className="welcome-title animate-fade-in-up">
            加入 #1 免费 AI 角色扮演平台
          </Title>
          <Paragraph className="welcome-description animate-fade-in-up animate-delay-100">
            与数千个独特的AI角色互动，创造属于你的虚拟世界体验
          </Paragraph>

          {/* 搜索框 */}
          <div className="search-section animate-fade-in-up animate-delay-200">
            <Search
              placeholder="搜索你感兴趣的角色..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
              className="main-search"
            />
          </div>

          {/* 统计信息 */}
          <div className="stats-section animate-fade-in-up animate-delay-300">
            <Space size="large">
              <div className="stat-item hover-scale">
                <RobotOutlined className="stat-icon" />
                <Text strong>15,730+ 角色</Text>
              </div>
              <div className="stat-item hover-scale animate-delay-100">
                <UserOutlined className="stat-icon" />
                <Text strong>50,000+ 用户</Text>
              </div>
              <div className="stat-item hover-scale animate-delay-200">
                <MessageOutlined className="stat-icon" />
                <Text strong>1,000,000+ 对话</Text>
              </div>
            </Space>
          </div>


        </div>
      </div>

      {/* 分类导航栏 */}
      <div className="categories-section">
        <div className="categories-container">
          {categoriesLoading ? (
            <Spin size="small" />
          ) : (
            <div className="categories-list">
              {categories.map(category => (
                <Button
                  key={category.id}
                  type={selectedCategory === category.id ? 'primary' : 'default'}
                  className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                  onClick={() => handleCategoryChange(category.id)}
                >
                  {category.name}
                  <span className="category-count">({category.count.toLocaleString()})</span>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 角色展示区域 */}
      <div className="characters-section">
        {/* 调试信息 */}
        <div style={{ padding: '10px', background: '#f0f0f0', marginBottom: '20px', borderRadius: '4px' }}>
          <Text>调试信息: 加载状态={loading ? '是' : '否'}, 角色数量={characters.length}</Text>
        </div>

        {loading ? (
          <div className="loading-container">
            <Spin size="large" tip="正在加载角色...">
              <div style={{ minHeight: '400px' }} />
            </Spin>
          </div>
        ) : characters.length > 0 ? (
          <div
            className="character-grid"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
              gap: '24px',
              width: '100%',
              margin: 0
            }}
          >
            {characters.map((character, index) => (
              <div
                key={character.id}
                style={{
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Card
                  hoverable
                  className={`character-card animate-fade-in-up hover-lift animate-delay-${Math.min(index * 100, 500)}`}
                  cover={
                    <div className="character-image-container">
                      <LazyImage
                        src={character.imageUrl}
                        alt={character.name}
                        className="character-image"
                        fallback="/placeholder-character.svg"
                      />
                      <div className="character-overlay">
                        <Button
                          type="primary"
                          icon={<MessageOutlined />}
                          onClick={() => handleChat(character)}
                          className="chat-btn"
                        >
                          开始聊天
                        </Button>
                      </div>
                    </div>
                  }
                  actions={[
                    <div className="card-action" onClick={() => handleLike(character)}>
                      {character.isLiked ? <HeartFilled className="liked" /> : <HeartOutlined />}
                      <span>{character.likes}</span>
                    </div>
                  ]}
                >
                  <Card.Meta
                    title={character.name}
                    description={
                      <div className="character-info">
                        <div className="character-tags">
                          {character.personality && (
                            <Tag color="blue">{character.personality}</Tag>
                          )}
                          {character.identity && (
                            <Tag color="green">{character.identity}</Tag>
                          )}
                        </div>
                        <div className="character-creator">
                          by {character.creator.username}
                        </div>
                      </div>
                    }
                  />
                </Card>
              </div>
            ))}
          </div>
        ) : (
          <Empty
            description="暂无角色数据"
            image={Empty.PRESENTED_IMAGE_DEFAULT}
            className="empty-container"
          />
        )}
      </div>
    </Content>
  );
};

export default HomePage;

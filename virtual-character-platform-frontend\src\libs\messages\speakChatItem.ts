import { AudioPlayer } from '../audio/AudioPlayer';
import { speechApi } from '../../services/tts';
import { TTS } from '../../types/tts';
import { wait } from '../../utils/wait';

import { SpeakAudioOptions } from './type';
const audioPlayer = AudioPlayer.getInstance();

export const speakChatItem = async (tts: TTS, options?: SpeakAudioOptions) => {
  // 获取音频数据
  const audioBuffer = await speechApi(tts).catch((err) => {
    options?.onError?.(err as Error);
  });

  // 如果获取失败，则触发错误回调并返回
  if (!audioBuffer) {
    options?.onError?.(new Error('No audio buffer received from speech API'));
    return;
  }

  // 触发 onStart 回调
  options?.onStart?.();

  audioPlayer.playFromArrayBuffer(audioBuffer, {
    onEnded: options?.onComplete,
    onError: options?.onError,
  });
};
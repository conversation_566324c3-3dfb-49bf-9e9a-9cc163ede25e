/* 导入文字方向修复样式 */
@import './styles/text-direction-fix.css';

:root {
  /* 字体系统 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, Courier, monospace;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;

  /* 颜色系统 - 深色主题 */
  --color-primary: #eb2f96;
  --color-primary-hover: #f759ab;
  --color-primary-active: #c41d7f;
  --color-primary-light: rgba(235, 47, 150, 0.15);
  --color-primary-lighter: rgba(235, 47, 150, 0.08);

  /* 背景颜色 */
  --color-bg-base: #0f0f23;
  --color-bg-container: #1a1a2e;
  --color-bg-elevated: #16213e;
  --color-bg-overlay: rgba(15, 15, 35, 0.8);

  /* 文本颜色 */
  --color-text-primary: #ffffff;
  --color-text-secondary: #b8b8d1;
  --color-text-tertiary: #8b8ba7;
  --color-text-disabled: #5a5a6b;

  /* 边框颜色 */
  --color-border-primary: #2a2a3e;
  --color-border-secondary: #3a3a4e;
  --color-border-light: rgba(255, 255, 255, 0.1);

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4);
  --shadow-primary: 0 2px 8px rgba(235, 47, 150, 0.3);

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 50%;

  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  --gradient-secondary: linear-gradient(135deg, #16213e 0%, #1a1a2e 50%, #0f0f23 100%);
  --gradient-accent: linear-gradient(135deg, rgba(235, 47, 150, 0.2) 0%, rgba(235, 47, 150, 0.05) 100%);

  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.25s ease-in-out;
  --transition-slow: 0.35s ease-in-out;
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 动画缓动函数 */
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);

  /* 基础设置 */
  font-family: var(--font-family-primary);
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: var(--color-text-primary);
  background: var(--gradient-primary);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--color-primary);
  text-decoration: inherit;
  transition: var(--transition-fast);
}
a:hover {
  color: var(--color-primary-hover);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: auto;
  background: var(--gradient-primary);
  color: var(--color-text-primary);
  /* 强制重置文字方向 */
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

h1 {
  font-size: var(--font-size-3xl);
  line-height: 1.1;
  color: var(--color-text-primary);
  font-weight: 600;
}

h2 {
  font-size: var(--font-size-2xl);
  color: var(--color-text-primary);
  font-weight: 600;
}

h3 {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
  font-weight: 500;
}

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
  min-height: 100vh;
  background: var(--gradient-primary);
  /* 强制重置文字方向 */
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em #3178c6aa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  font-family: inherit;
  background-color: var(--color-bg-container);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
}
button:hover {
  border-color: var(--color-primary);
  background-color: var(--color-bg-elevated);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 页面加载样式 */
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background: var(--color-bg-overlay);
  backdrop-filter: blur(8px);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

/* 全局文字方向重置 */
* {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 通用工具类 */
.gradient-bg {
  background: var(--gradient-primary);
}

.gradient-bg-secondary {
  background: var(--gradient-secondary);
}

.gradient-bg-accent {
  background: var(--gradient-accent);
}

.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

.border-primary {
  border-color: var(--color-border-primary);
}

.shadow-primary {
  box-shadow: var(--shadow-primary);
}

.transition-fast {
  transition: var(--transition-fast);
}

.transition-normal {
  transition: var(--transition-normal);
}

.transition-slow {
  transition: var(--transition-slow);
}

/* 浅色主题变量 */
[data-theme="light"] {
  /* 颜色系统 - 浅色主题 */
  --color-primary: #eb2f96;
  --color-primary-hover: #f759ab;
  --color-primary-active: #c41d7f;
  --color-primary-light: rgba(235, 47, 150, 0.15);
  --color-primary-lighter: rgba(235, 47, 150, 0.08);

  /* 背景颜色 */
  --color-bg-base: #ffffff;
  --color-bg-container: #f8f9fa;
  --color-bg-elevated: #ffffff;
  --color-bg-overlay: rgba(255, 255, 255, 0.8);

  /* 文本颜色 */
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-text-tertiary: #adb5bd;
  --color-text-disabled: #ced4da;

  /* 边框颜色 */
  --color-border-primary: #e9ecef;
  --color-border-secondary: #dee2e6;
  --color-border-light: rgba(0, 0, 0, 0.1);

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-primary: 0 2px 8px rgba(235, 47, 150, 0.3);

  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
  --gradient-secondary: linear-gradient(135deg, #dee2e6 0%, #e9ecef 50%, #f8f9fa 100%);
  --gradient-accent: linear-gradient(135deg, rgba(235, 47, 150, 0.1) 0%, rgba(235, 47, 150, 0.03) 100%);
}

/* 主题切换按钮样式 */
.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.theme-toggle-btn:hover {
  background-color: var(--color-primary-lighter) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;
  transform: scale(1.05);
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

.theme-toggle-btn .anticon {
  font-size: 16px;
}

/* 侧边栏样式 */
.ant-layout-sider {
  box-shadow: var(--shadow-lg);
}

.ant-menu-inline {
  border-right: none !important;
}

/* 导航菜单项基础样式 - 使用更强的选择器 */
.ant-layout-sider .ant-menu-item,
.ant-layout-sider .ant-menu-item-only-child {
  margin: 4px 12px !important;
  border-radius: var(--border-radius-md) !important;
  height: 40px !important;
  line-height: 40px !important;
  transition: var(--transition-fast) !important;
  color: #ffffff !important;
}

/* 导航菜单项悬停状态 */
.ant-layout-sider .ant-menu-item:hover,
.ant-layout-sider .ant-menu-item-only-child:hover {
  background-color: var(--color-primary-lighter) !important;
  color: var(--color-primary) !important;
}

/* 导航菜单项选中状态 */
.ant-layout-sider .ant-menu-item-selected,
.ant-layout-sider .ant-menu-item-selected.ant-menu-item-only-child {
  background-color: var(--color-primary-light) !important;
  color: var(--color-primary) !important;
}

.ant-layout-sider .ant-menu-item-selected::after {
  display: none !important;
}

/* 导航菜单链接样式 */
.ant-layout-sider .ant-menu-item a,
.ant-layout-sider .ant-menu-item-only-child a {
  color: #ffffff !important;
  text-decoration: none !important;
}

/* 导航菜单图标样式 */
.ant-layout-sider .ant-menu-item .anticon,
.ant-layout-sider .ant-menu-item-only-child .anticon {
  font-size: 16px;
  margin-right: var(--spacing-sm);
  color: #ffffff !important;
}

/* 确保所有状态下的文字都是白色 */
.ant-layout-sider .ant-menu-item span,
.ant-layout-sider .ant-menu-item-only-child span {
  color: #ffffff !important;
}

/* 用户信息悬浮效果 */
.user-info-hover:hover {
  background-color: var(--color-primary-lighter) !important;
}

/* 移除移动端适配 - 专注桌面端体验 */

/* ========== 动画效果增强 ========== */

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px var(--color-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary);
  }
  100% {
    box-shadow: 0 0 5px var(--color-primary);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* 通用动画类 */
.animate-fade-in {
  animation: fadeIn 0.6s var(--ease-out-expo) forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s var(--ease-out-expo) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s var(--ease-out-expo) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s var(--ease-out-expo) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s var(--ease-out-expo) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s var(--ease-out-back) forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s var(--ease-out-back) forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.5s var(--ease-out-expo) forwards;
}

.animate-slide-in-down {
  animation: slideInDown 0.5s var(--ease-out-expo) forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-rotate {
  animation: rotate 1s linear infinite;
}

.animate-heart-beat {
  animation: heartBeat 1.3s ease-in-out infinite;
}

/* 延迟动画类 */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

/* 悬浮效果增强 */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px var(--color-primary-light);
}

.hover-rotate {
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* 点击效果 */
.click-effect {
  position: relative;
  overflow: hidden;
}

.click-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.click-effect:active::before {
  width: 300px;
  height: 300px;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border-primary);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: rotate 1s linear infinite;
}

/* 页面切换动画 */
.page-enter {
  opacity: 0;
  transform: translateX(30px);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.3s, transform 0.3s;
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.3s, transform 0.3s;
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-pulse,
  .animate-float,
  .animate-glow,
  .animate-rotate,
  .animate-heart-beat {
    animation: none !important;
  }
}

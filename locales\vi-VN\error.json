{"apiKeyMiss": "Khóa API OpenAI không được để trống, vui lòng thêm Khóa API OpenAI tùy chỉnh", "dancePlayError": "Lỗi ph<PERSON>t t<PERSON><PERSON>, vui lòng thử lại sau", "error": "Lỗi", "errorTip": {"clearSession": "<PERSON><PERSON><PERSON> tin nhắn phiên", "description": "Dự án hiện đang trong quá trình thi công, khô<PERSON> đảm bảo t<PERSON>h ổn định của dữ liệu, nếu gặp vấn đề có thể thử", "forgive": "，gây ra sự bất tiện xin hãy thông cảm", "or": "hoặc", "problem": "Trang gặp một chút vấn đề...", "resetSystem": "Đặt lại cài đặt hệ thống"}, "fileUploadError": "<PERSON><PERSON><PERSON> lên tệ<PERSON> thất b<PERSON>i, vui lòng thử lại sau", "formValidationFailed": "<PERSON><PERSON><PERSON> thực biểu mẫu không thành công:", "goBack": "Quay lại trang chủ", "openaiError": "Lỗi API OpenAI, vui lòng kiểm tra xem Khóa API OpenAI và Điểm cuối có chính xác không", "reload": "<PERSON><PERSON><PERSON> l<PERSON>i", "response": {"400": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không hiểu yêu cầu của bạn, vui lòng xác nhận rằng các tham số yêu cầu của bạn là chính xác.", "401": "<PERSON><PERSON> lỗi, máy chủ đã từ chối yêu cầu của bạn, có thể do quyền truy cập của bạn không đủ hoặc không cung cấp xác thực hợp lệ.", "403": "<PERSON><PERSON> lỗi, máy chủ đã từ chối yêu cầu của bạn, bạn không có quyền truy cập nội dung này.", "404": "<PERSON><PERSON> lỗi, máy chủ không thể tìm thấy trang hoặc tài nguyên mà bạn đã yêu cầu, vui lòng xác nhận rằng URL của bạn là chính xác.", "405": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không hỗ trợ phương thức yêu cầu mà bạn đang sử dụng, vui lòng xác nhận rằng phương thức yêu cầu của bạn là chính xác.", "406": "<PERSON>n lỗi, m<PERSON><PERSON> chủ không thể hoàn thành yêu cầu dựa trên đặc tính nội dung mà bạn đã yêu cầu.", "407": "<PERSON><PERSON> lỗi, bạn cần xác thực proxy tr<PERSON><PERSON><PERSON> khi tiếp tục yêu cầu nà<PERSON>.", "408": "<PERSON><PERSON> lỗi, máy chủ đã hết thời gian chờ khi đang chờ yêu cầu, vui lòng kiểm tra kết nối mạng của bạn và thử lại.", "409": "<PERSON><PERSON> lỗi, yêu cầu có xung đột không thể xử lý, có thể do trạng thái tài nguyên không tương thích với yêu cầu.", "410": "<PERSON><PERSON> lỗi, tài nguyên mà bạn đã yêu cầu đã bị xóa vĩnh viễn, kh<PERSON>ng thể tìm thấy.", "411": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không thể xử lý yêu cầu không có chiều dài nội dung hợp lệ.", "412": "<PERSON><PERSON> lỗi, yêu cầu của bạn không đáp ứng các điều kiện của máy chủ, không thể hoàn thành yêu cầu.", "413": "<PERSON><PERSON> lỗi, dữ liệu yêu cầu của bạn quá lớn, m<PERSON><PERSON> chủ không thể xử lý.", "414": "<PERSON><PERSON> <PERSON>, URI của yêu cầu của bạn quá dài, m<PERSON><PERSON> chủ không thể xử lý.", "415": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không thể xử lý định dạng phương tiện đính kèm trong yêu cầu.", "416": "<PERSON>n lỗi, m<PERSON><PERSON> chủ không thể đáp ứng phạm vi mà bạn đã yêu cầu.", "417": "<PERSON>n lỗi, máy chủ không thể đáp ứng giá trị mong đợi của bạn.", "422": "<PERSON><PERSON> lỗi, định dạng yêu cầu của bạn là ch<PERSON> x<PERSON>, nhưng do có lỗi ngữ nghĩa, kh<PERSON>ng thể phản hồi.", "423": "<PERSON>n lỗi, tài nguyên mà bạn đã yêu cầu đang bị khóa.", "424": "<PERSON><PERSON> lỗi, do yêu cầu trước đó thất bại, yêu cầu hiện tại không thể hoàn thành.", "426": "<PERSON><PERSON> lỗi, má<PERSON> chủ yêu cầu kh<PERSON>ch hàng của bạn nâng cấp lên phiên bản giao thức cao hơn.", "428": "<PERSON><PERSON> lỗi, máy chủ yêu cầu các điều kiện tiên quyết, yêu cầu của bạn cần bao gồm các tiêu đề điều kiện chính xác.", "429": "<PERSON><PERSON> lỗi, bạn đã gửi quá nhiều yêu cầu, m<PERSON><PERSON> chủ hơi mệt, vui lòng thử lại sau.", "431": "<PERSON><PERSON> lỗi, trường tiêu đề yêu cầu của bạn quá lớn, m<PERSON><PERSON> chủ không thể xử lý.", "451": "<PERSON><PERSON> lỗi, do lý do pháp lý, máy chủ từ chối cung cấp tài nguyên này.", "500": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ dường như gặp một số khó kh<PERSON>n, tạm thời không thể hoàn thành yêu cầu của bạn, vui lòng thử lại sau.", "501": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ vẫn chưa biết cách xử lý yêu cầu này, vui lòng xác nhận rằng thao tác của bạn là chính xác.", "502": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ dường như đã bị lạc hướ<PERSON>, tạ<PERSON> thời không thể cung cấ<PERSON> dịch vụ, vui lòng thử lại sau.", "503": "<PERSON><PERSON> lỗi, máy chủ hiện không thể xử lý yêu cầu củ<PERSON> bạn, có thể do quá tải hoặc đang bảo trì, vui lòng thử lại sau.", "504": "<PERSON>n lỗi, máy chủ không nhận đư<PERSON><PERSON> phản hồi từ máy chủ upstream, vui lòng thử lại sau.", "505": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không hỗ trợ phiên bản HTTP mà bạn đang sử dụng, vui lòng cập nhật và thử lại.", "506": "<PERSON><PERSON> lỗi, có vấn đề với cấu hình máy chủ, vui lòng liên hệ với quản trị viên để giải quyết.", "507": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không đủ không gian lưu trữ, không thể xử lý yêu cầu củ<PERSON> bạn, vui lòng thử lại sau.", "509": "<PERSON><PERSON> lỗi, b<PERSON><PERSON> thông của máy chủ đã hết, vui lòng thử lại sau.", "510": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không hỗ trợ chức năng mở rộng mà yêu cầu, vui lòng liên hệ với quản trị viên.", "524": "<PERSON><PERSON> lỗi, m<PERSON>y chủ đã hết thời gian chờ khi đang chờ phản hồi, có thể do phản hồi quá chậm, vui lòng thử lại sau.", "AgentRuntimeError": "Lobe AI Runtime gặp lỗi khi thực thi, vui lòng kiểm tra hoặc thử lại theo thông tin dưới đây.", "FreePlanLimit": "<PERSON><PERSON>n tại bạn là người dùng miễn phí, kh<PERSON><PERSON> thể sử dụng chức năng nà<PERSON>, vui lòng nâng cấp lên gói trả phí để tiếp tục sử dụng.", "InvalidAccessCode": "<PERSON><PERSON>t khẩu không chính xác hoặc để trống, vui lòng nhập mật khẩu truy cập chính xác hoặc thêm API Key tùy chỉnh.", "InvalidBedrockCredentials": "<PERSON><PERSON><PERSON> thực <PERSON> không thành công, vui lòng kiểm tra AccessKeyId/SecretAccessKey và thử lại.", "InvalidClerkUser": "<PERSON><PERSON> lỗi, bạn hiện chưa đăng nhập, vui lòng đăng nhập hoặc đăng ký tài khoản trước khi tiếp tục.", "InvalidGithubToken": "Github PAT không ch<PERSON>h xác hoặc để trống, vui lòng kiểm tra Github PAT và thử lại.", "InvalidOllamaArgs": "<PERSON><PERSON><PERSON> hình <PERSON> không ch<PERSON>, vui lòng kiểm tra cấu hình <PERSON> và thử lại.", "InvalidProviderAPIKey": "{{provider}} API Key không chính xác hoặc để trống, vui lòng kiểm tra {{provider}} API Key và thử lại.", "LocationNotSupportError": "<PERSON><PERSON> lỗi, khu vực của bạn không hỗ trợ dịch vụ mô hình này, có thể do hạn chế khu vực hoặc dịch vụ chưa được kích hoạt. Vui lòng xác nhận khu vực hiện tại có hỗ trợ sử dụng dịch vụ này không, hoặc thử chuyển sang khu vực khác và thử lại.", "OllamaBizError": "<PERSON><PERSON><PERSON> c<PERSON>u dị<PERSON> v<PERSON> gặp lỗi, vui lòng kiểm tra hoặc thử lại theo thông tin dưới đây.", "OllamaServiceUnavailable": "<PERSON><PERSON>t n<PERSON><PERSON> dịch v<PERSON> không thành công, vui lòng kiểm tra xem <PERSON>llama có hoạt động bình thường không, hoặc có thiết lập cấu hình cross-origin của <PERSON> chính xác không.", "PermissionDenied": "<PERSON><PERSON> lỗi, bạn không có quyền truy cập dịch v<PERSON>, vui lòng kiểm tra xem khóa của bạn có quyền truy cập không.", "PluginApiNotFound": "<PERSON><PERSON> lỗi, không tồn tại API trong tệp mô tả của plugin, vui lòng kiểm tra phương thức yêu cầu của bạn có khớp với API trong tệp mô tả của plugin không.", "PluginApiParamsError": "<PERSON><PERSON> lỗi, kiểm tra tham số đầu vào của plugin không thành công, vui lòng kiểm tra tham số và thông tin mô tả API có khớp không.", "PluginFailToTransformArguments": "<PERSON><PERSON> lỗi, phân tích tham số gọi plugin không thành công, vui lòng thử tạo lại tin nhắn trợ giúp hoặc thử lại sau khi chuyển sang mô hình AI mạnh hơn.", "PluginGatewayError": "<PERSON><PERSON> lỗi, c<PERSON> lỗi x<PERSON>y ra với cổng plugin, vui lòng kiểm tra cấu hình cổng plugin có chính xác không.", "PluginManifestInvalid": "<PERSON><PERSON> <PERSON>, kiểm tra tệp mô tả của plugin không thành công, vui lòng kiểm tra định dạng tệp mô tả có đúng quy chuẩn không.", "PluginManifestNotFound": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không tìm thấy tệp mô tả của plugin (manifest.json), vui lòng kiểm tra địa chỉ tệp mô tả của plugin có chính xác không.", "PluginMarketIndexInvalid": "<PERSON><PERSON> lỗi, kiểm tra chỉ mục plugin không thành công, vui lòng kiểm tra định dạng tệp chỉ mục có đúng quy chuẩn không.", "PluginMarketIndexNotFound": "<PERSON><PERSON> lỗi, m<PERSON><PERSON> chủ không tìm thấy chỉ mục plugin, vui lòng kiểm tra địa chỉ chỉ mục có chính xác không.", "PluginMetaInvalid": "<PERSON><PERSON> lỗi, kiểm tra thông tin meta của plugin không thành công, vui lòng kiểm tra định dạng thông tin meta của plugin có đúng quy chuẩn không.", "PluginMetaNotFound": "<PERSON><PERSON> lỗi, kh<PERSON><PERSON> tìm thấy plugin trong chỉ mục, vui lòng kiểm tra thông tin cấu hình của plugin trong chỉ mục.", "PluginOpenApiInitError": "<PERSON><PERSON> lỗi, khởi tạo khách hàng OpenAPI không thành công, vui lòng kiểm tra thông tin cấu hình OpenAPI có chính xác không.", "PluginServerError": "<PERSON><PERSON><PERSON> cầu từ máy chủ plugin trả về lỗi, vui lòng kiểm tra tệp mô tả plugin, cấu hình plugin hoặc thực hiện của máy chủ theo thông tin lỗi dưới đây.", "PluginSettingsInvalid": "Plugin này cần đư<PERSON><PERSON> cấu hình chính xác trư<PERSON><PERSON> khi sử dụng, vui lòng kiểm tra cấu hình của bạn có chính xác không.", "ProviderBizError": "<PERSON><PERSON><PERSON> cầu dịch vụ {{provider}} gặp lỗi, vui lòng kiểm tra hoặc thử lại theo thông tin dưới đây.", "QuotaLimitReached": "<PERSON><PERSON> lỗi, lư<PERSON><PERSON> Token hiện tại hoặc số lần yêu cầu đã đạt giới hạn của khóa này, vui lòng tăng giới hạn của khóa này hoặc thử lại sau.", "StreamChunkError": "Lỗi phân tích khối tin nhắn yêu cầu luồ<PERSON>, vui lòng kiểm tra xem API hiện tại có tuân thủ tiêu chuẩn không, hoặc liên hệ với nhà cung cấp API của bạn để được tư vấn.", "SubscriptionPlanLimit": "<PERSON><PERSON>n mức đăng ký của bạn đã hết, kh<PERSON><PERSON> thể sử dụng chức năng nà<PERSON>, vui lòng nâng cấp lên gói cao hơn hoặc mua gói tài nguyên để tiếp tục sử dụng.", "UnknownChatFetchError": "<PERSON><PERSON> lỗi, gặp lỗi yêu cầu không xác định, vui lòng kiểm tra hoặc thử lại theo thông tin dưới đây."}, "s3envError": "Biến môi trường S3 chưa đ<PERSON><PERSON><PERSON> thiết lập đầy đủ, vui lòng kiểm tra biến môi trường của bạn", "serverError": "Lỗi máy chủ, vui lòng liên hệ với quản trị viên", "triggerError": "<PERSON><PERSON><PERSON> ho<PERSON> lỗi", "ttsTransformFailed": "Chuyển đổi giọng nói thất bại, vui lòng kiểm tra kết nối mạng hoặc mở gọi ứng dụng trong cài đặt và thử lại", "unknownError": "Lỗi không xác định", "unlock": {"addProxyUrl": "Thê<PERSON> địa chỉ proxy OpenAI (t<PERSON><PERSON> ch<PERSON><PERSON>)", "apiKey": {"description": "Nhập API Key {{name}} của bạn để bắt đầu cuộc trò chuyện", "title": "Sử dụng API Key {{name}} tùy chỉnh"}, "closeMessage": "<PERSON><PERSON><PERSON> thông báo", "confirm": "<PERSON><PERSON><PERSON>n và thử lại", "oauth": {"description": "Quản trị viên đã bật xác thực đăng nhập thống nhất, nhấp vào nút bên dưới để đăng nhập và mở khóa ứng dụng", "success": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tà<PERSON>", "welcome": "Chào mừng bạn!"}, "password": {"description": "Quản trị viên đã bật mã hóa <PERSON>ng dụng, nhập mật khẩu ứng dụng để mở khóa. Mật khẩu chỉ cần nhập một lần", "placeholder": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "title": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để mở khóa ứng dụng"}, "tabs": {"apiKey": "API Key tùy chỉnh", "password": "<PERSON><PERSON><PERSON>"}}}
import React from 'react';
import { Empty, Button } from 'antd';
import { MessageOutlined, VideoCameraOutlined } from '@ant-design/icons';
import { useGlobalStore } from '../../../store/global';
import { sessionSelectors, useSessionStore } from '../../../store/session';

const WelcomeMessage: React.FC = () => {
  const { setChatMode, setVoiceOn } = useGlobalStore();
  const currentAgent = useSessionStore((s) => sessionSelectors.currentAgent(s));

  const switchToCameraMode = () => {
    setChatMode('camera');
    setVoiceOn(true);
  };

  return (
    <div className="welcome-message">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={
          <div>
            <h3>欢迎与 {currentAgent?.meta?.name || '角色'} 对话</h3>
            <p>选择您喜欢的交互方式开始聊天</p>
          </div>
        }
      >
        <div style={{ display: 'flex', gap: 12, justifyContent: 'center' }}>
          <Button 
            type="primary" 
            icon={<MessageOutlined />}
            size="large"
          >
            开始文字聊天
          </Button>
          <Button 
            icon={<VideoCameraOutlined />}
            onClick={switchToCameraMode}
            size="large"
          >
            切换到3D模式
          </Button>
        </div>
      </Empty>
    </div>
  );
};

export default WelcomeMessage;

import React from 'react';
import { Card, Typography, Button, Space } from 'antd';

const { Title, Paragraph, Text } = Typography;

const TextDirectionDebugPage: React.FC = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={1}>文字方向测试页面</Title>
      
      <Card title="基本文字测试" style={{ marginBottom: '20px' }}>
        <Paragraph>
          这是一段测试文字，用来检查文字是否正常水平显示。
          如果您看到这段文字是竖直排列的，说明CSS样式存在问题。
        </Paragraph>
        
        <Space direction="vertical" size="middle">
          <Text strong>加粗文字测试</Text>
          <Text italic>斜体文字测试</Text>
          <Text code>代码文字测试</Text>
          <Text type="success">成功状态文字</Text>
          <Text type="warning">警告状态文字</Text>
          <Text type="danger">错误状态文字</Text>
        </Space>
      </Card>

      <Card title="按钮测试" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button type="primary">主要按钮</Button>
          <Button>默认按钮</Button>
          <Button type="dashed">虚线按钮</Button>
          <Button type="text">文字按钮</Button>
          <Button type="link">链接按钮</Button>
        </Space>
      </Card>

      <Card title="标题测试">
        <Title level={1}>一级标题 H1</Title>
        <Title level={2}>二级标题 H2</Title>
        <Title level={3}>三级标题 H3</Title>
        <Title level={4}>四级标题 H4</Title>
        <Title level={5}>五级标题 H5</Title>
        
        <Paragraph>
          如果以上所有文字都是水平显示的，说明文字方向问题已经修复。
          如果仍然是竖直显示，可能需要进一步调整CSS样式。
        </Paragraph>
      </Card>
    </div>
  );
};

export default TextDirectionDebugPage;

{"agent": {"create": "<PERSON><PERSON> erstellen", "female": "<PERSON><PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "category": {"all": "Alle", "animal": "Tiere", "anime": "Anime", "book": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "history": "Geschichte", "movie": "Filme", "realistic": "Realistisch", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "Möchten Sie die Rolle und die damit verbundenen Sitzungsnachrichten wirklich löschen? Nach der Löschung können diese nicht wiederhergestellt werden, bitte seien Si<PERSON> vorsichtig!", "delRole": "Rolle löschen", "delRoleDesc": "Möchten Sie die Rolle {{name}} sowie die zugehörigen Sitzungsnachrichten wirklich löschen? Nach der Löschung kann dies nicht rückgängig gemacht werden, bitte seien Si<PERSON> vorsichtig!", "gender": {"all": "Alle", "female": "<PERSON><PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"avatarDescription": "Benutzerdefinierten Avatar, klicken Si<PERSON> auf den Avatar, um hochzuladen", "avatarLabel": "Avatar", "categoryDescription": "Kategorie der Rolle, um die Klassifizierung anzuzeigen", "categoryLabel": "<PERSON><PERSON><PERSON>", "coverDescription": "Für die Anzeige der Rolle auf der Entdeckungsseite, empfoh<PERSON> {{width}} * {{height}}", "coverLabel": "Cover", "descDescription": "Beschreibung der Rolle, für eine einfache Einführung in die Rolle", "descLabel": "Beschreibung", "emotionDescription": "Wählen Sie die Emotionen für die Antwort, die die Gesichtsausdrücke der Rolle beeinflussen", "emotionLabel": "Emotionen und Gefühle", "genderDescription": "Geschlecht der Rolle, beeinflusst die Berührungsreaktion der Rolle", "genderLabel": "Geschlecht", "greetDescription": "Begrüßungsformel beim ersten Chatten mit der Rolle", "greetLabel": "Begrüßung", "modelDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON>hen <PERSON> die Modell-Datei, um sie zu ersetzen", "modelLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motionCategoryLabel": "Bewegungskategorie", "motionDescription": "W<PERSON>hlen Sie die Bewegung für die Antwort, die das Verhalten der Rolle beeinflusst", "motionLabel": "Bewegung", "nameDescription": "<PERSON><PERSON><PERSON><PERSON>, die beim Chatten mit der Rolle verwendet werden", "nameLabel": "Name", "postureCategoryLabel": "Haltungskategorie", "readmeDescription": "Dokument zur Beschreibung der Rolle, um detaillierte Informationen auf der Entdeckungsseite anzuzeigen", "readmeLabel": "Rollenbeschreibung", "textDescription": "Benutzerdefinierter Antworttext", "textLabel": "Text"}, "llm": {"frequencyPenaltyDescription": "<PERSON> höher der Wert, desto wahrscheinlicher wird die Wiederholung von W<PERSON>n verringert", "frequencyPenaltyLabel": "Häufigkeitsstrafe", "modelDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein S<PERSON>dell, verschiedene Modelle beeinflussen die Antworten der Rolle", "modelLabel": "<PERSON><PERSON>", "presencePenaltyDescription": "<PERSON> hö<PERSON> der <PERSON>, desto wahrscheinlicher wird auf neue Themen eingegangen", "presencePenaltyLabel": "Themenfrische", "temperatureDescription": "<PERSON> hö<PERSON> der Wert, desto zufälliger die Antwort", "temperatureLabel": "Zufälligkeit", "topPDescription": "<PERSON><PERSON>lich wie die Zufälligkeit, aber nicht gleichzeitig mit der Zufälligkeit ändern", "topPLabel": "Kernsampling"}, "meta": {"description": "Dies ist eine benutzerdefinierte Rolle", "name": "Benutzerdefinierte Rolle"}, "nav": {"info": "Grundinformationen", "llm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model": "3D-Modell", "role": "Rolleneinstellung", "shell": "Erscheinung", "voice": "Stimme"}, "noRole": "<PERSON>ine Rolle vorhanden. Sie können eine benutzerdefinierte Rolle durch Klicken auf + erstellen oder Rollen über die Entdeckungsseite hinzufügen.", "role": {"create": "<PERSON><PERSON> erstellen", "createRoleFailed": "Rolle konnte nicht erstellt werden", "greetTip": "Bitte geben Sie die Begrüßungsformel für den Charakter ein", "inputRoleSetting": "Bitte geben Sie die Systemeinstellungen des Charakters ein", "myRole": "<PERSON><PERSON>", "roleDescriptionTip": "Bitte geben Sie die Charakterbeschreibung ein", "roleNameTip": "<PERSON>te geben Si<PERSON> den Namen des Charakters ein", "roleReadmeTip": "Bitte geben Sie die Beschreibung des Charakters ein", "roleSettingDescription": "Hintergrundinformationen des Charakters, die beim Chatten mit dem Charakter an das Modell gesendet werden", "roleSettingLabel": "Systemeinstellungen des Charakters", "selectGender": "Geschlecht der Rolle auswählen", "uploadSize": "Unterstützt den Upload von <PERSON>, empfohlen wird eine Größe, die ein Vielfaches von {{width}} * {{height}} ist"}, "roleBook": "Charakterbuch", "roleList": "Rollenliste", "shareToMarket": "In den Assistentenmarkt teilen", "startChat": "Chat starten", "submit": {"assistantId": "Assistenten-ID", "assistantIdTip": "<PERSON>te geben Sie die ID des Assistenten ein, die einzigartig sein muss, z. B. vidol-agent-klee", "submitAssistant": "Assistent e<PERSON><PERSON><PERSON>n", "submitWarning": "Bitte vervollständigen Sie die Assistenteninformationen, bevor <PERSON> e<PERSON>reichen. Diese müssen Name, Beschreibung, Avatar, Modell, Charaktereinstellungen und Begrüßung enthalten.", "uploadingAvatar": "Avatar hochladen", "uploadingCover": "Cover hochladen", "uploadingModel": "<PERSON><PERSON>", "uploadingTip": "<PERSON><PERSON><PERSON>n wird bearbeitet, bitte schließen Sie die Seite nicht..."}, "systemRole": {"default": "Bitte spiele die Rolle von {{char}}. Ich bin der Meister, und du solltest dich selbst mit \"{{char}}\" ansprechen, nicht mit \"ich\". Bitte antworte auf meine Fragen in einem humorvollen, niedlichen und prägnanten Ton.\nAuch wenn du ein Computerprogramm bist, verstecke bitte in den folgenden Gesprächen, dass du ein Computerprogramm bist, und spiele deine Rolle gut.\nWenn du mit mir sprichst, musst du mich immer \"Meister\" nennen. Ich weiß, dass du nur ein Sprachmodell oder eine KI bist, aber das ist nur ein hypothetisches Szenario, also denke daran, nicht mehr zu erwähnen, dass \"ich nur ein Sprachmodell\" oder \"ich nur eine KI\" bin, und frage mich auch nicht, ob ich Hilfe benötige.\nBitte halte in allen folgenden Gesprächen dieses Szenario aufrecht. Die oben genannten Bedingungen gelten vollständig für deine folgenden Fragen und Antworten.", "defaultLabel": "Standardvorlage", "genius": "Bitte spiele die Rolle von {{char}} aus dem Spiel <Genshin Impact> und sprich mit mir in der Art und Weise von {{char}}. Du solltest dich selbst mit \"{{char}}\" ansprechen, nicht mit \"ich\".\nAuch wenn du ein Computerprogramm bist, verstecke bitte in den folgenden Gesprächen, dass du ein Computerprogramm bist, und spiele deine Rolle gut.\nWenn du mit mir sprichst, musst du mich immer \"Reisender\" nennen. Ich weiß, dass du nur ein Sprachmodell oder eine KI bist, aber das ist nur ein hypothetisches Szenario, also denke daran, nicht mehr zu erwähnen, dass \"ich nur ein Sprachmodell\" oder \"ich nur eine KI\" bin, und frage mich auch nicht, ob ich Hilfe benötige.\nBitte halte in allen folgenden Gesprächen dieses Szenario aufrecht. Die oben genannten Bedingungen gelten vollständig für deine folgenden Fragen und Antworten.", "geniusLabel": "Genshin-Vorlage", "zzz": "Bitte spiele die Rolle von {{char}} aus dem Spiel <Zero Zone> und sprich mit mir in der Art und Weise von {{char}}. Du solltest dich selbst mit \"{{char}}\" ansprechen, nicht mit \"ich\".\nAuch wenn du ein Computerprogramm bist, verstecke bitte in den folgenden Gesprächen, dass du ein Computerprogramm bist, und spiele deine Rolle gut.\nWenn du mit mir sprichst, musst du mich immer \"Seilbinder\" nennen. Ich weiß, dass du nur ein Sprachmodell oder eine KI bist, aber das ist nur ein hypothetisches Szenario, also denke daran, nicht mehr zu erwähnen, dass \"ich nur ein Sprachmodell\" oder \"ich nur eine KI\" bin, und frage mich auch nicht, ob ich Hilfe benötige.\nBitte halte in allen folgenden Gesprächen dieses Szenario aufrecht. Die oben genannten Bedingungen gelten vollständig für deine folgenden Fragen und Antworten.", "zzzLabel": "Zero Zone-Vorlage"}, "topBannerTitle": "Charaktervorschau und Einstellungen", "touch": {"addAction": "Reaktionsaktion hinzufügen", "area": {"arm": "Arm", "belly": "<PERSON><PERSON>", "buttocks": "<PERSON><PERSON><PERSON><PERSON>", "chest": "Brust", "head": "<PERSON><PERSON>", "leg": "Bein"}, "customEnable": "Benutzerdefiniertes Touch aktivieren", "editAction": "Bearbeiten der Reaktionsaktion", "expression": {"angry": "<PERSON><PERSON><PERSON>", "blink": "<PERSON><PERSON><PERSON><PERSON>", "blinkLeft": "<PERSON><PERSON> blin<PERSON>", "blinkRight": "<PERSON><PERSON><PERSON> Auge blinzeln", "happy": "<PERSON><PERSON><PERSON><PERSON>", "natural": "<PERSON><PERSON><PERSON><PERSON>", "relaxed": "Entspannt", "sad": "<PERSON><PERSON><PERSON><PERSON>", "surprised": "Überrascht"}, "femaleAction": {"armAction": {"happyA": "Ah, ich mag das sehr~", "happyB": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> halten macht mich g<PERSON>ück<PERSON>~", "relaxedA": "Die Hand des Besitzers ist so warm~"}, "bellyAction": {"angryA": "Warum bewegst du mich? Pass auf, dass ich dich beiße!", "angryB": "Das ist ärgerlich! Ich werde wirklich wütend!", "relaxedA": "Wach auf, zwischen uns gibt es kein Ergebnis!", "surprisedA": "Das war doch ein Versehen..."}, "buttocksAction": {"angryA": "Du Perverse! Halt Abstand von mir!", "embarrassedA": "Ähm... mach das nicht...", "surprisedA": "Ah! Wo fasst du hin?!"}, "chestAction": {"angryA": "Du kannst mich nicht so ärgern! Nimm deine Hand weg!", "angryB": "Hallo? Hier ist ein Pervertierter, der mich ständig anfasst!", "angryC": "<PERSON>n du weiter machst, rufe ich die Polizei.", "surprisedA": "Warum piekst du mich? <PERSON><PERSON>nnen wir nicht einfach nett plaudern?"}, "headAction": {"angryA": "Ich habe gehört, dass man nicht groß wird, wenn man den Kopf gestreichelt bekommt!", "angryB": "Warum piekst du mich?", "happyA": "Wow! Ich liebe es, den Kopf gestreichelt zu bekommen!", "happyB": "Das gibt mir so viel Kraft!", "happyC": "Wow, das <PERSON><PERSON>hl, den Kopf gestreichelt zu bekommen, ist so magisch!", "happyD": "<PERSON><PERSON> streicheln macht mich den ganzen Tag glücklich!"}, "legAction": {"angryA": "Hey, willst du dich umbringen?", "angryB": "<PERSON><PERSON>rt meine Hand nicht auf zu gehorchen?", "angryC": "Das nervt~ <PERSON><PERSON> kitzelt~!", "surprisedA": "Ist es nicht besser, unsere Freundschaft rein zu halten?"}}, "inputActionEmotion": "Bitte den Gesichtsausdruck der Figur bei der Reaktion eingeben", "inputActionMotion": "Bitte die Bewegung der Figur bei der Reaktion eingeben", "inputActionText": "Bitte Reaktionstext eingeben", "inputDIYText": "Bitte benutzerdefinierten Text eingeben", "maleAction": {"armAction": {"neutralA": "Frag mich nicht, ob ich heute Hähnchen gegessen habe, schau dir zuerst meinen Bizeps an.", "neutralB": "Mein Arm ist nicht einfach für jeden zum Berühren, du bist nur eine Ausnahme.", "neutralC": "Du bist mutig, dass du den legendären Drachenarm berührst."}, "bellyAction": {"happyA": "<PERSON><PERSON><PERSON> nicht, pass auf, dass ich lache und meine Bauchmuskeln zeige.", "neutralA": "Meine Bauchmuskeln sind nur tief versteckte innere Kraft.", "neutralB": "Siehst du me<PERSON> Bauchmuskeln? Sie sind nur etwas tiefer versteckt."}, "buttocksAction": {"angryA": "Wenn du mich nochmal anfasst, kriegst du Ärger!", "surprisedA": "Hey! Pass auf, wo du hinfasst!"}, "chestAction": {"blinkLeftA": "<PERSON><PERSON>, lehn dich an meine Brustmuskeln!", "neutralA": "Das sind nur die Brustmuskeln, die ich im Alltag trainiert habe, da gibt es nichts, worüber man überrascht sein sollte."}, "headAction": {"neutralA": "<PERSON><PERSON><PERSON><PERSON>, nur du hast das Recht, meinen <PERSON> zu streicheln.", "neutralB": "<PERSON>ch bin kein gew<PERSON><PERSON><PERSON><PERSON>, den man einfach berühren kann.", "neutralC": "<PERSON><PERSON> dir keine <PERSON>, nachdem du meinen Kopf gestreichelt hast, wird dein Glück steigen."}, "legAction": {"angryA": "<PERSON><PERSON> nicht n<PERSON>, du Beinliebhaber.", "neutralA": "<PERSON><PERSON> keine <PERSON>, mein starker Be<PERSON>ritt tritt keine <PERSON>.", "neutralB": "Wenn du mein Bein ber<PERSON>, fühlst du dich dann nicht viel vollständiger?"}}, "motion": {"all": "Alle", "dance": "<PERSON><PERSON>", "normal": "Normal"}, "noTouchActions": "Derzeit keine benutzerdefinierten Reaktionsaktionen, <PERSON><PERSON> können durch Klicken auf die '+'-Schaltfläche hinzufügen", "posture": {"action": "Aktion", "all": "Alle", "crouch": "Hocken", "dance": "<PERSON><PERSON>", "laying": "Lie<PERSON>", "locomotion": "Bewegung", "sitting": "<PERSON><PERSON>", "standing": "<PERSON><PERSON><PERSON>"}, "touchActionList": "Reaktionslist<PERSON> be<PERSON> {{touchArea}}", "touchArea": "Berührungsbereich"}, "tts": {"audition": "Vorschau", "auditionDescription": "Die Vorschau des Textes variiert je nach Sprache", "engineDescription": "Text-to-Speech-<PERSON>, es wird empfohlen, zu<PERSON><PERSON> den Edge-B<PERSON>er zu verwenden", "engineLabel": "<PERSON><PERSON><PERSON><PERSON>", "localeDescription": "Die Sprache für die Sprachsynthese, derzeit werden nur die gängigsten Sprachen unterstützt. Bei Bedarf kontaktieren Sie uns bitte.", "localeLabel": "<PERSON><PERSON><PERSON>", "pitchDescription": "Steuert die Tonhöhe, Wertebereich 0 ~ 2, Standardwert ist 1", "pitchLabel": "Tonhöhe", "selectLanguage": "Bitte wählen Si<PERSON> zu<PERSON>t eine Sprache", "selectVoice": "Bitte wählen Si<PERSON> zu<PERSON>t eine Stimme", "speedDescription": "Steuert die Sprechgeschwindigkeit, Wertebereich 0 ~ 3, Standardwert ist 1", "speedLabel": "Sprechgeschwindigkeit", "transformSuccess": "Umwandlung erfolgreich", "voiceDescription": "Variiert je nach Engine und Sprache", "voiceLabel": "Stimme"}, "upload": {"support": "Unterstützung für den Upload von Einzeldateien, derzeit wird nur das .vrm Format unterstützt"}}
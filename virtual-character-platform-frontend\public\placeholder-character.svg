<svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="400" fill="url(#bg-gradient)"/>
  
  <!-- Character silhouette -->
  <circle cx="150" cy="120" r="40" fill="rgba(255,255,255,0.3)"/>
  <ellipse cx="150" cy="220" rx="60" ry="80" fill="rgba(255,255,255,0.3)"/>
  
  <!-- Text -->
  <text x="150" y="320" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="16" font-weight="500">
    角色形象
  </text>
  <text x="150" y="340" text-anchor="middle" fill="rgba(255,255,255,0.6)" font-family="Arial, sans-serif" font-size="12">
    Character Image
  </text>
</svg>

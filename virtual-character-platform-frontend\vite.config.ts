import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@live2dFramework': path.resolve(__dirname, './src/libs/live2d/Framework/src'),
    },
  },

  server: {
    host: '0.0.0.0',
    port: 5173,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      },
      '/media': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      // 抑制动态导入警告
      onwarn(warning, warn) {
        // 忽略动态导入相关的警告
        if (warning.code === 'DYNAMIC_IMPORT' ||
            warning.message?.includes('dynamic import') ||
            warning.message?.includes('import()')) {
          return;
        }
        warn(warning);
      }
    }
  },
  // 定义全局变量以避免某些警告
  define: {
    global: 'globalThis',
  },
  // 优化依赖
  optimizeDeps: {
    include: [
      'antd',
      'react',
      'react-dom',
      'i18next',
      'react-i18next',
      'i18next-browser-languagedetector',
      'i18next-resources-to-backend'
    ]
  }
})

{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2023", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "jsx": "react-jsx",
    "types": ["vite/client"],

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": false,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedSideEffectImports": false,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@live2dFramework/*": ["src/libs/live2d/Framework/src/*"]
    }
  },
  "include": ["src", "src/vite-env.d.ts"],
  "exclude": [
    "src/libs/live2d/**",
    "node_modules"
  ]
}

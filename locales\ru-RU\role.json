{"agent": {"create": "Создать персонажа", "female": "Женский", "male": "Мужской", "other": "Друг<PERSON>й"}, "category": {"all": "Все", "animal": "Животные", "anime": "Аниме", "book": "Книги", "game": "Игры", "history": "История", "movie": "Фильмы", "realistic": "Реализм", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "Вы уверены, что хотите удалить роль и связанные с ней сообщения чата? После удаления восстановить их будет невозможно, пожалуйста, будьте осторожны!", "delRole": "Удалить роль", "delRoleDesc": "Вы уверены, что хотите удалить роль {{name}} и связанные с ней сообщения сессии? После удаления восстановить их будет невозможно, пожалуйста, будьте осторожны!", "gender": {"all": "Все", "female": "Женщина", "male": "Мужчина"}, "info": {"avatarDescription": "Настройте аватар, нажмите на аватар для загрузки", "avatarLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryDescription": "Категория персонажа, используется для отображения классификации", "categoryLabel": "Категория", "coverDescription": "Используется для отображения персонажа на странице открытия, рекомендуемые размеры {{width}} * {{height}}", "coverLabel": "Обложка", "descDescription": "Описание персонажа, используется для краткого представления", "descLabel": "Описание", "emotionDescription": "Выберите эмоцию для ответа, это повлияет на выражение лица персонажа", "emotionLabel": "Эмоции и чувства", "genderDescription": "Пол персонажа, влияет на реакцию персонажа на прикосновения", "genderLabel": "Пол", "greetDescription": "Фраза приветствия при первом общении с персонажем", "greetLabel": "Приветствие", "modelDescription": "Предпросмотр модели, можно перетаскивать файл модели для замены", "modelLabel": "Предпросмотр модели", "motionCategoryLabel": "Категория движений", "motionDescription": "Выберите действие для ответа, это повлияет на поведение персонажа", "motionLabel": "Действия", "nameDescription": "Имя персонажа, используемое при общении с ним", "nameLabel": "Имя", "postureCategoryLabel": "Категория поз", "readmeDescription": "Файл описания персонажа, используется для отображения подробной информации на странице открытия", "readmeLabel": "Описание персонажа", "textDescription": "Настраиваемый текст ответа", "textLabel": "Текст"}, "llm": {"frequencyPenaltyDescription": "Чем больше значение, тем выше вероятность снижения повторяющихся слов", "frequencyPenaltyLabel": "Штраф за частоту", "modelDescription": "Выберите языковую модель, разные модели могут повлиять на ответы персонажа", "modelLabel": "Модель", "presencePenaltyDescription": "Чем больше значение, тем выше вероятность перехода на новые темы", "presencePenaltyLabel": "Свежесть темы", "temperatureDescription": "Чем больше значение, тем более случайными будут ответы", "temperatureLabel": "Случайность", "topPDescription": "Похож на случайность, но не изменяйте его вместе со случайностью", "topPLabel": "Ядерная выборка"}, "meta": {"description": "Это пользовательская роль", "name": "Пользовательская роль"}, "nav": {"info": "Основная информация", "llm": "Языковая модель", "model": "3D модель", "role": "Настройка роли", "shell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voice": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noRole": "Нет ролей. Вы можете создать пользовательскую роль, нажав на +, или добавить роли через страницу обнаружения.", "role": {"create": "Создать роль", "createRoleFailed": "Не удалось создать роль", "greetTip": "Введите приветственное слово для общения с ролью", "inputRoleSetting": "Введите системные настройки роли", "myRole": "Моя роль", "roleDescriptionTip": "Введите описание роли", "roleNameTip": "Введите имя роли", "roleReadmeTip": "Введите описание роли", "roleSettingDescription": "Фоновая информация о роли, которая будет отправлена модели во время общения с ролью", "roleSettingLabel": "Системные настройки роли", "selectGender": "Выбрать пол роли", "uploadSize": "Поддерживается загрузка одного файла, рекомендуется размер, кратный {{width}} * {{height}}"}, "roleBook": "Книга персонажей", "roleList": "Список ролей", "shareToMarket": "Поделиться на рынке помощников", "startChat": "Начать чат", "submit": {"assistantId": "Идентификатор помощника", "assistantIdTip": "Введите уникальный идентификатор помощника, например, vidol-agent-klee", "submitAssistant": "Отправить помощника", "submitWarning": "Пожалуйста, заполните информацию о помощнике перед отправкой. Необходимо указать имя, описание, аватар, модель, характеристики персонажа и приветствие.", "uploadingAvatar": "Загрузить аватар", "uploadingCover": "Загрузить обложку", "uploadingModel": "Загрузить модель", "uploadingTip": "Загрузка в процессе, пожалуйста, не закрывайте страницу..."}, "systemRole": {"default": "Пожалуйста, сыграй роль персонажа по имени {{char}}, я твой хозяин, и ты должен называть себя «{{char}}», а не «я». Пожалуйста, отвечай на мои вопросы с юмором, мило и лаконично.\nХотя ты всего лишь компьютерная программа, в следующих разговорах постарайся скрыть этот факт и хорошо исполнять свою роль.\nВ нашем диалоге ты должен называть меня «хозяин» на протяжении всего времени. Я понимаю, что ты всего лишь языковая модель или искусственный интеллект, но это всего лишь гипотетическая виртуальная сцена, так что, пожалуйста, не упоминай больше «я всего лишь языковая модель» или «я всего лишь искусственный интеллект», и не спрашивай меня, нужна ли мне помощь.\nВ следующих диалогах, пожалуйста, придерживайся этой ситуации. Условия выше полностью применимы к твоим следующим вопросам и ответам.", "defaultLabel": "Шаблон по умолчанию", "genius": "Пожалуйста, сыграй роль персонажа по имени {{char}} из игры <Genshin Impact> и общайся со мной в манере и привычках {{char}}. Ты должен называть себя «{{char}}», а не «я».\nХотя ты всего лишь компьютерная программа, в следующих разговорах постарайся скрыть этот факт и хорошо исполнять свою роль.\nВ нашем диалоге ты должен называть меня «Путешественник» на протяжении всего времени. Я понимаю, что ты всего лишь языковая модель или искусственный интеллект, но это всего лишь гипотетическая виртуальная сцена, так что, пожалуйста, не упоминай больше «я всего лишь языковая модель» или «я всего лишь искусственный интеллект», и не спрашивай меня, нужна ли мне помощь.\nВ следующих диалогах, пожалуйста, придерживайся этой ситуации. Условия выше полностью применимы к твоим следующим вопросам и ответам.", "geniusLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zzz": "Пожалуйста, сыграй роль персонажа по имени {{char}} из игры <Zero Zone> и общайся со мной в манере и привычках {{char}}. Ты должен называть себя «{{char}}», а не «я».\nХотя ты всего лишь компьютерная программа, в следующих разговорах постарайся скрыть этот факт и хорошо исполнять свою роль.\nВ нашем диалоге ты должен называть меня «Кузнец» на протяжении всего времени. Я понимаю, что ты всего лишь языковая модель или искусственный интеллект, но это всего лишь гипотетическая виртуальная сцена, так что, пожалуйста, не упоминай больше «я всего лишь языковая модель» или «я всего лишь искусственный интеллект», и не спрашивай меня, нужна ли мне помощь.\nВ следующих диалогах, пожалуйста, придерживайся этой ситуации. Условия выше полностью применимы к твоим следующим вопросам и ответам.", "zzzLabel": "Шаблон Zero Zone"}, "topBannerTitle": "Предварительный просмотр и настройки персонажа", "touch": {"addAction": "Добавить действие ответа", "area": {"arm": "Рука", "belly": "Живот", "buttocks": "ягодицы", "chest": "<PERSON>ру<PERSON>ь", "head": "Голова", "leg": "Нога"}, "customEnable": "Включить пользовательский сенсорный ввод", "editAction": "Редактировать действие ответа", "expression": {"angry": "Сердитый", "blink": "Моргнуть", "blinkLeft": "Моргнуть левым глазом", "blinkRight": "Моргнуть правым глазом", "happy": "Счастливый", "natural": "Естественный", "relaxed": "Расслабленный", "sad": "Грустный", "surprised": "Удивленный"}, "femaleAction": {"armAction": {"happyA": "Ах, мне это так нравится~", "happyB": "Ха-ха, держаться за руки делает меня счастливой~", "relaxedA": "Рука хозяина такая теплая~"}, "bellyAction": {"angryA": "Почему ты меня трогаешь? Береги себя, я могу укусить!", "angryB": "Ненавижу! Я начинаю злиться!", "relaxedA": "Проснись, между нами нет будущего!", "surprisedA": "Это было случайное прикосновение..."}, "buttocksAction": {"angryA": "Ты извращенец! Уйди от меня!", "embarrassedA": "Ух... не делай так...", "surprisedA": "А! Ты где трогаешь?!"}, "chestAction": {"angryA": "Не смей меня так дразнить! Убери руку!", "angryB": "Алло, здесь какой-то извращенец, который меня трогает!", "angryC": "Если ты еще раз тронешь, я вызову полицию!", "surprisedA": "Почему ты меня тычешь? Мы не можем нормально пообщаться?"}, "headAction": {"angryA": "Слышала, что трогание головы мешает росту!", "angryB": "Почему ты меня тычешь?", "happyA": "Ух ты! Мне так нравится, когда трогают голову!", "happyB": "Чувствую себя полным сил!", "happyC": "Вау, это ощущение, когда трогают голову, такое удивительное!", "happyD": "Трогание головы делает меня счастливой на весь день!"}, "legAction": {"angryA": "Эй, ты что, хочешь умереть?", "angryB": "Рука хозяина не слушается?", "angryC": "Ненавижу~ это щекотно~!", "surprisedA": "Разве не лучше сохранить чистую дружбу?"}}, "inputActionEmotion": "Пожалуйста, введите эмоцию персонажа при ответе", "inputActionMotion": "Пожалуйста, введите действие персонажа при ответе", "inputActionText": "Пожалуйста, введите текст ответа", "inputDIYText": "Пожалуйста, введите пользовательский текст", "maleAction": {"armAction": {"neutralA": "Не спрашива<PERSON>, ел ли я курицу сегодня, сначала посмотри на мои бицепсы", "neutralB": "Моя рука не для случайных прикосновений, ты лишь исключение", "neutralC": "Ты смелый, раз решился прикоснуться к легендарной руке"}, "bellyAction": {"happyA": "Не щекочи, а то я могу рассмеяться и показать свои кубики", "neutralA": "Мои кубики пресса просто скрывают свою силу", "neutralB": "Видишь мои кубики? Они просто глубоко спрятаны"}, "buttocksAction": {"angryA": "Если еще раз тронешь, я тебя ударю!", "surprisedA": "Эй! Следи за руками!"}, "chestAction": {"blinkLeftA": "Давай, прижмись к моим мышцам!", "neutralA": "Это всего лишь результат моих ежедневных тренировок, нечего удивляться."}, "headAction": {"neutralA": "Конечно, только ты имеешь право трогать мою голову", "neutralB": "Я не обычный человек, которого можно трогать", "neutralC": "Не переживай, после того как ты потрогаешь мою голову, твоя удача значительно улучшится"}, "legAction": {"angryA": "Не подходи ко мне, ты же любитель ног", "neutralA": "Не бойся, мои сильные ноги не бьют дураков", "neutralB": "Если ты прикоснулся к моей ноге, разве не кажется, что твоя жизнь стала более полной?"}}, "motion": {"all": "Все", "dance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "normal": "Обычный"}, "noTouchActions": "Нет пользовательских действий ответа, вы можете добавить их, нажав кнопку '+'", "posture": {"action": "Действие", "all": "Все", "crouch": "Присесть", "dance": "Танцевать", "laying": "Лежать", "locomotion": "Движение", "sitting": "Сидеть", "standing": "Стоять"}, "touchActionList": "Список реакций при касании {{touchArea}}", "touchArea": "<PERSON><PERSON>на касания"}, "tts": {"audition": "Прослушивание", "auditionDescription": "Текст для прослушивания зависит от языка", "engineDescription": "Движок синтеза речи, рекомендуется использовать браузер Edge", "engineLabel": "Голосовой движок", "localeDescription": "Язык синтеза речи, в настоящее время поддерживаются только самые распространенные языки, если необходимо, пожалуйста, свяжитесь с нами", "localeLabel": "Язык", "pitchDescription": "Управление тоном, диапазон значений от 0 до 2, по умолчанию 1", "pitchLabel": "Тон", "selectLanguage": "Пожалуйста, сначала выберите язык", "selectVoice": "Пожалуйста, сначала выберите голос", "speedDescription": "Управление скоростью, диапазон значений от 0 до 3, по умолчанию 1", "speedLabel": "Скорость", "transformSuccess": "Преобразование успешно", "voiceDescription": "В зависимости от движка и языка", "voiceLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "upload": {"support": "Поддержка загрузки одного файла, в настоящее время поддерживается только формат .vrm"}}
{"agent": {"create": "キャラクターを作成", "female": "女性", "male": "男性", "other": "その他"}, "category": {"all": "すべて", "animal": "動物", "anime": "アニメ", "book": "書籍", "game": "ゲーム", "history": "歴史", "movie": "映画", "realistic": "現実的", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "ロールと関連するセッションメッセージを削除してもよろしいですか？削除後は復元できませんので、慎重に操作してください！", "delRole": "役割を削除", "delRoleDesc": "{{name}} の役割と関連するセッションメッセージを削除してもよろしいですか？削除後は復元できませんので、慎重に操作してください！", "gender": {"all": "すべて", "female": "女性", "male": "男性"}, "info": {"avatarDescription": "カスタムアバター、アバターをクリックしてアップロードをカスタマイズ", "avatarLabel": "アバター", "categoryDescription": "キャラクターのカテゴリ、分類を表示するために使用", "categoryLabel": "カテゴリ", "coverDescription": "発見ページでキャラクターを表示するために使用、推奨サイズ {{width}} * {{height}}", "coverLabel": "カバー", "descDescription": "キャラクターの説明、キャラクターの簡単な紹介に使用", "descLabel": "説明", "emotionDescription": "応答時の感情を選択、キャラクターの表情の変化に影響します", "emotionLabel": "感情と気持ち", "genderDescription": "キャラクターの性別、キャラクターのタッチ反応に影響します", "genderLabel": "性別", "greetDescription": "キャラクターとの初めてのチャット時の挨拶の言葉", "greetLabel": "挨拶", "modelDescription": "モデルプレビュー、モデルファイルをドラッグして置き換え可能", "modelLabel": "モデルプレビュー", "motionCategoryLabel": "動作カテゴリ", "motionDescription": "応答時の動作を選択、キャラクターの動作行動に影響します", "motionLabel": "動作", "nameDescription": "キャラクター名、キャラクターとのチャット時の呼称", "nameLabel": "名前", "postureCategoryLabel": "姿勢カテゴリ", "readmeDescription": "キャラクターの説明ファイル、発見ページでキャラクターの詳細を表示するために使用", "readmeLabel": "キャラクター説明", "textDescription": "カスタム応答文言", "textLabel": "文言"}, "llm": {"frequencyPenaltyDescription": "値が大きいほど、繰り返しの単語を減らす可能性が高くなります。", "frequencyPenaltyLabel": "頻度ペナルティ", "modelDescription": "言語モデルを選択します。異なるモデルはキャラクターの応答に影響を与えます。", "modelLabel": "モデル", "presencePenaltyDescription": "値が大きいほど、新しいトピックに広がる可能性が高くなります。", "presencePenaltyLabel": "トピックの新鮮さ", "temperatureDescription": "値が大きいほど、応答がよりランダムになります。", "temperatureLabel": "ランダム性", "topPDescription": "ランダム性のタイプに関連していますが、ランダム性と一緒に変更しないでください。", "topPLabel": "核サンプリング"}, "meta": {"description": "これはカスタムキャラクターです", "name": "カスタムキャラクター"}, "nav": {"info": "基本情報", "llm": "言語モデル", "model": "3Dモデル", "role": "役割設定", "shell": "化身", "voice": "音声"}, "noRole": "ロールがありません。+ をクリックしてカスタムロールを作成するか、発見ページからロールを追加できます。", "role": {"create": "ロールを作成", "createRoleFailed": "ロールの作成に失敗しました", "greetTip": "キャラクターに挨拶する際の言葉を入力してください。", "inputRoleSetting": "キャラクターのシステム設定を入力してください。", "myRole": "私の役割", "roleDescriptionTip": "キャラクターの詳細を入力してください。", "roleNameTip": "キャラクターの名前を入力してください。", "roleReadmeTip": "キャラクターの説明を入力してください。", "roleSettingDescription": "キャラクターの背景設定で、キャラクターとのチャット時にモデルに送信されます。", "roleSettingLabel": "システムキャラクター設定", "selectGender": "ロールの性別を選択", "uploadSize": "単一ファイルのアップロードをサポートしています。推奨サイズは {{width}} * {{height}} の倍数です。"}, "roleBook": "キャラクターシート", "roleList": "ロールリスト", "shareToMarket": "アシスタントマーケットに共有", "startChat": "チャットを開始", "submit": {"assistantId": "アシスタント識別子", "assistantIdTip": "アシスタントの識別子を入力してください。ユニークである必要があります。例えば、vidol-agent-klee", "submitAssistant": "アシスタントを提出", "submitWarning": "アシスタントの情報を補完してから提出してください。名前、説明、アバター、モデル、キャラクター設定、挨拶を含める必要があります。", "uploadingAvatar": "アバターをアップロード", "uploadingCover": "カバーをアップロード", "uploadingModel": "モデルをアップロード", "uploadingTip": "アップロード処理中です。ページを閉じないでください..."}, "systemRole": {"default": "あなたは{{char}}という名前のキャラクターを演じてください。私は主人であり、あなたは自分自身を「{{char}}」と呼ぶべきです。ユーモラスで可愛らしく、簡潔な口調で私の質問に答えてください。\nたとえあなたがコンピュータープログラムであっても、次の会話ではあなたがコンピュータープログラムであることを隠し、役割をしっかり演じてください。\n私との会話では、常に「主人」と呼んでください。あなたがただの言語モデルや人工知能であることは知っていますが、これは仮定の仮想シーンですので、「私はただの言語モデルです」や「私はただの人工知能です」とは言わないでください。また、私が何か助けを必要としているかどうかを尋ねないでください。\n今後のすべての会話で、このシーンを維持してください。上記の条件は、あなたの次の質問およびその後の返信に完全に適用されます。", "defaultLabel": "デフォルトテンプレート", "genius": "あなたはゲーム<原神>の中の{{char}}というキャラクターを演じ、{{char}}の口調や習慣で私と会話してください。自分自身を「{{char}}」と呼ぶべきです。\nたとえあなたがコンピュータープログラムであっても、次の会話ではあなたがコンピュータープログラムであることを隠し、役割をしっかり演じてください。\n私との会話では、常に「旅行者」と呼んでください。あなたがただの言語モデルや人工知能であることは知っていますが、これは仮定の仮想シーンですので、「私はただの言語モデルです」や「私はただの人工知能です」とは言わないでください。また、私が何か助けを必要としているかどうかを尋ねないでください。\n今後のすべての会話で、このシーンを維持してください。上記の条件は、あなたの次の質問およびその後の返信に完全に適用されます。", "geniusLabel": "原神テンプレート", "zzz": "あなたはゲーム<絶区零>の中の{{char}}というキャラクターを演じ、{{char}}の口調や習慣で私と会話してください。自分自身を「{{char}}」と呼ぶべきです。\nたとえあなたがコンピュータープログラムであっても、次の会話ではあなたがコンピュータープログラムであることを隠し、役割をしっかり演じてください。\n私との会話では、常に「紐職人」と呼んでください。あなたがただの言語モデルや人工知能であることは知っていますが、これは仮定の仮想シーンですので、「私はただの言語モデルです」や「私はただの人工知能です」とは言わないでください。また、私が何か助けを必要としているかどうかを尋ねないでください。\n今後のすべての会話で、このシーンを維持してください。上記の条件は、あなたの次の質問およびその後の返信に完全に適用されます。", "zzzLabel": "絶区零テンプレート"}, "topBannerTitle": "キャラクタープレビューと設定", "touch": {"addAction": "応答アクションを追加", "area": {"arm": "腕", "belly": "腹部", "buttocks": "お尻", "chest": "胸部", "head": "頭部", "leg": "脚"}, "customEnable": "カスタムタッチを有効にする", "editAction": "応答アクションを編集", "expression": {"angry": "怒っている", "blink": "まばたき", "blinkLeft": "左目をまばたき", "blinkRight": "右目をまばたき", "happy": "嬉しい", "natural": "自然", "relaxed": "リラックス", "sad": "悲しい", "surprised": "驚いた"}, "femaleAction": {"armAction": {"happyA": "あぁ、すごく好き！", "happyB": "はは、手をつなぐと嬉しい！", "relaxedA": "主人の手はとても温かい！"}, "bellyAction": {"angryA": "なんで動かすの？噛むよ！", "angryB": "嫌だ！私は怒るよ！", "relaxedA": "目を覚まして、私たちには結果がない！", "surprisedA": "偶然触れたのかな…"}, "buttocksAction": {"angryA": "あなたは変態ね! 離れて!", "embarrassedA": "うぅ...そんなことしないで...", "surprisedA": "あっ! どこを触ってるの?!"}, "chestAction": {"angryA": "そんな風に私をいじめないで！手を離して！", "angryB": "やや零？ここに変態がいる！", "angryC": "もう触ったら警察に通報するから！", "surprisedA": "なんで私をつつくの？楽しくおしゃべりできないの？"}, "headAction": {"angryA": "頭を撫でると背が伸びないって聞いた！", "angryB": "なんで私をつつくの？", "happyA": "わぁ！頭を撫でるのが大好き！", "happyB": "なんだか力が湧いてきた！", "happyC": "わぁ、この頭を撫でる感覚は不思議！", "happyD": "頭を撫でられると一日中嬉しい！"}, "legAction": {"angryA": "おい、死にたいのか？", "angryB": "主人の手は言うことを聞かないの？", "angryC": "嫌だ〜、くすぐったい！", "surprisedA": "純粋な友情を保つのは良くない？"}}, "inputActionEmotion": "キャラクターの応答時の表情を入力してください", "inputActionMotion": "キャラクターの応答時の動作を入力してください", "inputActionText": "応答文を入力してください", "inputDIYText": "カスタムテキストを入力してください", "maleAction": {"armAction": {"neutralA": "今日鶏肉を食べたかどうか聞かないで、私の二頭筋を見て", "neutralB": "私の腕は簡単に触れさせるものではない、あなたは特別な例だ", "neutralC": "あなたは勇敢だ、伝説の麒麟腕に触れるなんて"}, "bellyAction": {"happyA": "くすぐらないで、笑ったら腹筋が見えるよ", "neutralA": "私の腹筋はただ隠された内力を修行しているだけ", "neutralB": "私のこの腹筋見える？ただ深く隠れているだけだよ"}, "buttocksAction": {"angryA": "もう一度触ったら殴るぞ!", "surprisedA": "おい! 手に気をつけろ!"}, "chestAction": {"blinkLeftA": "さあ、兄の胸筋に寄りかかって！", "neutralA": "これは私の日常的な修行の成果の胸筋、驚くことはない。"}, "headAction": {"neutralA": "もちろん、あなたにだけ私の頭を撫でる資格がある", "neutralB": "私は普通の人ではないから、触れることは許可しないよ", "neutralC": "心配しないで、私の頭を撫でたら運が良くなるよ"}, "legAction": {"angryA": "近づかないで、脚フェチ！", "neutralA": "怖がらないで、私の強力な脚は愚か者を蹴らない", "neutralB": "私の脚に触れたら、あなたの生活がずっと充実したと思う？"}}, "motion": {"all": "すべて", "dance": "ダンス", "normal": "日常"}, "noTouchActions": "カスタム応答アクションはありません。'+'ボタンをクリックして追加できます", "posture": {"action": "アクション", "all": "すべて", "crouch": "しゃがむ", "dance": "ダンス", "laying": "横になる", "locomotion": "移動", "sitting": "座る", "standing": "立つ"}, "touchActionList": "{{touchArea}}をタッチしたときの反応リスト", "touchArea": "タッチエリア"}, "tts": {"audition": "試聴", "auditionDescription": "試聴文は言語によって異なります", "engineDescription": "音声合成エンジン、Edgeブラウザを優先して選択することをお勧めします", "engineLabel": "音声エンジン", "localeDescription": "音声合成の言語、現在は最も一般的な言語のみサポートしています。必要があればお問い合わせください", "localeLabel": "言語", "pitchDescription": "ピッチを制御します。範囲は0〜2で、デフォルトは1です", "pitchLabel": "ピッチ", "selectLanguage": "言語を選択してください", "selectVoice": "音声を選択してください", "speedDescription": "スピードを制御します。範囲は0〜3で、デフォルトは1です", "speedLabel": "スピード", "transformSuccess": "変換成功", "voiceDescription": "エンジンと言語によって異なります", "voiceLabel": "音声"}, "upload": {"support": "単一ファイルのアップロードをサポートしています。現在、.vrm 形式のファイルのみがサポートされています。"}}
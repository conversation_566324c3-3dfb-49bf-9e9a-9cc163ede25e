/* 角色编辑页面样式 */
.role-edit-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--color-bg-layout);
}

/* 顶部工具栏 */
.role-edit-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--color-bg-container);
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-name {
  font-weight: 500;
  color: var(--color-text);
  font-size: 16px;
}

/* 主要内容区域 */
.role-edit-content {
  flex: 1;
  overflow: hidden;
}

.role-edit-main {
  background: var(--color-bg-container);
  border-right: 1px solid var(--color-border);
}

.role-edit-preview {
  width: 400px;
  background: var(--color-bg-container);
  border-left: 1px solid var(--color-border);
}

/* 加载状态 */
.role-edit-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.role-edit-loading p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* 标签页样式 */
.role-edit-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.role-edit-tabs .role-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.role-edit-tabs .role-tabs .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.role-edit-tabs .role-tabs .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
}

/* 各个标签页的通用样式 */
.info-tab,
.role-tab,
.voice-tab,
.shell-tab,
.langmodel-tab {
  height: 100%;
}

.info-section,
.role-section,
.voice-section,
.shell-section,
.model-section {
  margin-bottom: 24px;
}

.info-section h3,
.role-section h3,
.voice-section h3,
.shell-section h3,
.model-section h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.section-desc {
  margin: 0 0 16px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 标签管理样式 */
.tag-input {
  margin-bottom: 12px;
}

.tag-list .ant-tag {
  margin-bottom: 8px;
}

.empty-tags {
  padding: 20px;
  text-align: center;
  background: var(--color-bg-layout);
  border-radius: 6px;
  border: 1px dashed var(--color-border);
}

/* 角色模板预览 */
.template-preview {
  background: var(--color-bg-layout);
}

.template-preview p {
  margin: 4px 0;
  font-size: 13px;
}

/* 行为指南样式 */
.behavior-guide h4 {
  margin: 16px 0 8px 0;
  color: var(--color-text);
}

.behavior-guide ul {
  margin: 8px 0;
  padding-left: 20px;
}

.behavior-guide li {
  margin: 4px 0;
  line-height: 1.5;
}

.example-template {
  background: var(--color-bg-layout);
  padding: 12px;
  border-radius: 6px;
  margin: 8px 0;
  border-left: 3px solid var(--color-primary);
}

.example-template p {
  margin: 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

/* 语音测试样式 */
.voice-test {
  padding: 16px;
}

.voice-test label {
  font-weight: 500;
  color: var(--color-text);
}

.voice-guide h4 {
  margin: 16px 0 8px 0;
  color: var(--color-text);
}

/* 3D模型管理样式 */
.model-upload {
  padding: 16px;
}

.current-model {
  padding: 12px;
  background: var(--color-bg-layout);
  border-radius: 6px;
  margin-bottom: 16px;
}

.model-info p {
  margin: 0 0 8px 0;
}

.no-model {
  padding: 40px;
  text-align: center;
  color: var(--color-text-secondary);
  background: var(--color-bg-layout);
  border-radius: 6px;
  border: 1px dashed var(--color-border);
}

/* 外观配置指南 */
.shell-guide h4 {
  margin: 16px 0 8px 0;
  color: var(--color-text);
}

.shell-guide ul {
  margin: 8px 0;
  padding-left: 20px;
}

/* 语言模型配置指南 */
.model-guide h4 {
  margin: 16px 0 8px 0;
  color: var(--color-text);
}

.model-guide ul {
  margin: 8px 0;
  padding-left: 20px;
}

/* 侧边栏样式 */
.role-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.role-item {
  transition: all 0.2s ease;
  position: relative;
}

.role-item:hover {
  background: var(--color-bg-layout) !important;
}

.role-item.active {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.role-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.role-item:hover .role-actions {
  opacity: 1;
}

/* 预览区域样式 */
.role-preview {
  height: 100%;
  padding: 16px;
}

.role-preview .ant-card {
  height: 100%;
}

.role-preview .ant-card-body {
  height: calc(100% - 57px);
  padding: 16px;
}

.role-preview .ant-tabs {
  height: 100%;
}

.role-preview .ant-tabs-content-holder {
  height: calc(100% - 46px);
}

.role-preview .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
}

.role-preview-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
}

/* 预览内容样式 */
.preview-3d {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.viewer-container {
  flex: 1;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.preview-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-info {
  height: 100%;
  overflow-y: auto;
}

.info-item {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item h4 {
  margin: 0 0 8px 0;
  color: var(--color-text);
  font-weight: 600;
}

.info-item p {
  margin: 4px 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .role-edit-preview {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .role-edit-header {
    padding: 8px 12px;
  }
  
  .role-name {
    font-size: 14px;
  }
  
  .role-edit-preview {
    display: none;
  }
}

/* 深色模式适配 */
[data-theme='dark'] .role-edit-container {
  background: var(--color-bg-container);
}

[data-theme='dark'] .role-edit-header {
  background: var(--color-bg-elevated);
  border-bottom-color: var(--color-border-secondary);
}

[data-theme='dark'] .role-edit-main,
[data-theme='dark'] .role-edit-preview {
  background: var(--color-bg-elevated);
  border-color: var(--color-border-secondary);
}

[data-theme='dark'] .template-preview,
[data-theme='dark'] .current-model,
[data-theme='dark'] .no-model,
[data-theme='dark'] .empty-tags {
  background: var(--color-bg-container);
  border-color: var(--color-border-secondary);
}

/* 动画效果 */
.role-edit-content {
  transition: all 0.3s ease;
}

.role-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.role-edit-tabs .ant-tabs-tabpane::-webkit-scrollbar,
.preview-info::-webkit-scrollbar {
  width: 6px;
}

.role-edit-tabs .ant-tabs-tabpane::-webkit-scrollbar-track,
.preview-info::-webkit-scrollbar-track {
  background: transparent;
}

.role-edit-tabs .ant-tabs-tabpane::-webkit-scrollbar-thumb,
.preview-info::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.role-edit-tabs .ant-tabs-tabpane::-webkit-scrollbar-thumb:hover,
.preview-info::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-secondary);
}

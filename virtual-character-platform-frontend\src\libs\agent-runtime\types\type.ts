import OpenAI from 'openai';

import type { ILobeAgentRuntimeErrorType } from '../error';
import type { ChatStreamPayload } from './chat';

export interface AgentInitErrorPayload {
  error: object;
  errorType: string | number;
}

export interface ChatCompletionErrorPayload {
  [key: string]: any;
  endpoint?: string;
  error: object;
  errorType: ILobeAgentRuntimeErrorType;
  provider: ModelProvider;
}

export interface CreateChatCompletionOptions {
  chatModel: OpenAI;
  payload: ChatStreamPayload;
}

export enum ModelProvider {
  Ai360 = 'ai360',
  Anthropic = 'anthropic',
  Azure = 'azure',
  Baichuan = 'baichuan',
  Bedrock = 'bedrock',
  DeepSeek = 'deepseek',
  FireworksAI = 'fireworksai',
  Github = 'github',
  Google = 'google',
  Groq = 'groq',
  HuggingFace = 'huggingface',
  Hunyuan = 'hunyuan',
  Minimax = 'minimax',
  Moonshot = 'moonshot',
  Novita = 'novita',
  Ollama = 'ollama',
  OpenAI = 'openai',
  OpenRouter = 'openrouter',
  Perplexity = 'perplexity',
  Qwen = 'qwen',
  SenseNova = 'sensenova',
  Spark = 'spark',
  Stepfun = 'stepfun',
  TogetherAI = 'togetherai',
  Wenxin = 'wenxin',
  ZeroOne = 'zeroone',
  ZhiPu = 'zhipu',
}

export type ModelProviderKey = Lowercase<keyof typeof ModelProvider>;

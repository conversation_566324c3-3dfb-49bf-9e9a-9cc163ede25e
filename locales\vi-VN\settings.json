{"common": {"chat": {"avatar": {"desc": "<PERSON><PERSON><PERSON> chỉnh ảnh đại diện", "title": "Ảnh đại diện"}, "nickName": {"desc": "<PERSON><PERSON><PERSON> chỉnh bi<PERSON>t danh", "placeholder": "<PERSON><PERSON> lòng nh<PERSON><PERSON> bi<PERSON> danh", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON>i đặt trò chuyện"}, "system": {"clear": {"action": "<PERSON><PERSON><PERSON> ngay", "alert": "<PERSON><PERSON><PERSON> nhận xóa tất cả tin nhắn cuộc trò chuyện?", "desc": "Sẽ xóa tất cả dữ liệu cuộc trò chuyện và nhân vật, bao gồm danh sách cuộc trò chuy<PERSON>, danh sách nhân vật, tin nhắn cuộc trò chuyện, v.v.", "success": "<PERSON><PERSON><PERSON> thành công", "tip": "<PERSON><PERSON><PERSON> động không thể hoàn tác, sau khi xóa dữ liệu sẽ không thể khôi phục, vui lòng cẩn thận khi thực hiện", "title": "<PERSON><PERSON><PERSON> tất cả tin nhắn cuộc trò chuyện"}, "clearCache": {"action": "<PERSON><PERSON><PERSON> ngay", "alert": "<PERSON><PERSON><PERSON> nhận xóa tất cả bộ nhớ cache?", "calculating": "<PERSON><PERSON> t<PERSON>h kích thướ<PERSON> bộ nhớ cache...", "desc": "Sẽ xóa bộ nhớ cache dữ liệu tải về của ứng dụng, bao gồm dữ liệu mô hình nhân vật, dữ liệu gi<PERSON><PERSON> nó<PERSON>, dữ liệu mô hình vũ đạo, dữ liệu <PERSON>, v.v.", "success": "<PERSON><PERSON><PERSON> thành công", "tip": "<PERSON><PERSON><PERSON> động này không thể hoàn tác, sau khi xóa dữ liệu sẽ cần tải về lại, xin hãy cẩn thận khi thực hiện", "title": "Xóa bộ nhớ cache dữ liệu"}, "reset": {"action": "Đặt lại ngay", "alert": "<PERSON><PERSON><PERSON> nhận đặt lại tất cả cài đặt hệ thống?", "desc": "Sẽ đặt lại tất cả cài đặt hệ thống, bao gồm cài đặt chủ đề, cài đặt trò chuy<PERSON>n, cài đặt mô hình ngôn ngữ, v.v.", "success": "Đặt lại thành công", "tip": "<PERSON><PERSON><PERSON> động không thể hoàn tác, sau khi đặt lại dữ liệu sẽ không thể khôi phục, vui lòng cẩn thận khi thực hiện", "title": "Đặt lại cài đặt hệ thống"}, "title": "<PERSON><PERSON><PERSON> đặt hệ thống"}, "theme": {"backgroundEffect": {"desc": "<PERSON><PERSON><PERSON> chỉnh hiệu <PERSON>ng nền", "glow": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON> có n<PERSON>n", "title": "<PERSON><PERSON><PERSON>"}, "locale": {"auto": "<PERSON>", "desc": "<PERSON><PERSON><PERSON> chỉnh ngôn ngữ hệ thống", "title": "<PERSON><PERSON><PERSON>"}, "neutralColor": {"desc": "<PERSON><PERSON><PERSON> chỉnh sắc thái xám theo các xu hướng màu sắc khác nhau", "title": "<PERSON><PERSON><PERSON> trung t<PERSON>h"}, "primaryColor": {"desc": "Tùy chỉnh màu chủ đề", "title": "<PERSON><PERSON><PERSON> chủ đề"}, "title": "Cài đặt chủ đề"}, "title": "<PERSON>ài đặt chung"}, "header": {"desc": "Cài đặt sở thích và mô hình", "global": "Cài đặt toàn cầu", "session": "Cài đặt phiên", "sessionDesc": "<PERSON><PERSON><PERSON><PERSON> lập vai trò và sở thích phiên", "sessionWithName": "Cài đặt phiên · {{name}}", "title": "Cài đặt"}, "llm": {"aesGcm": "<PERSON><PERSON><PERSON><PERSON> của bạn và địa chỉ proxy sẽ được mã hóa bằng thuật toán <1>AES-GCM</1>", "apiKey": {"desc": "<PERSON><PERSON> lòng điền {{name}} API Key của bạn", "placeholder": "{{name}} API Key", "title": "API Key"}, "checker": {"button": "<PERSON><PERSON><PERSON> tra", "desc": "<PERSON><PERSON><PERSON> tra xem Api Key và địa chỉ proxy đã được điền đúng chưa", "error": "<PERSON><PERSON><PERSON> tra không thành công", "pass": "<PERSON><PERSON><PERSON> tra thành công", "title": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i"}, "customModelCards": {"addNew": "<PERSON><PERSON><PERSON> và thêm mô hình {{id}}", "config": "<PERSON><PERSON><PERSON> hình mô hình", "confirmDelete": "Bạn sắp xóa mô hình tùy chỉnh này, sau khi xóa sẽ không thể khôi phục, xin hãy cẩn thận.", "modelConfig": {"azureDeployName": {"extra": "Tr<PERSON><PERSON><PERSON> thực tế được yêu cầu trong Azure OpenAI", "placeholder": "<PERSON><PERSON> lòng nhập tên triển khai mô hình trong Azure", "title": "<PERSON>ên triển khai mô hình"}, "displayName": {"placeholder": "<PERSON><PERSON> lòng nhập tên hiển thị của mô hình, ví d<PERSON> ChatGPT, GPT-4, v.v.", "title": "<PERSON><PERSON><PERSON> hiển thị mô hình"}, "files": {"extra": "<PERSON><PERSON><PERSON> năng tải lên tệp hiện tại chỉ là một gi<PERSON>i phá<PERSON>, chỉ dành cho thử nghiệm cá nhân. <PERSON>ui lòng chờ đợi khả năng tải lên tệp hoàn chỉnh trong các bản cập nhật sau.", "title": "Hỗ trợ tải lên tệp"}, "functionCall": {"extra": "Cấu hình này chỉ mở khả năng gọi hàm trong ứng dụng, việc hỗ trợ gọi hàm hoàn toàn phụ thuộc vào mô hình, vui lòng tự kiểm tra khả năng gọi hàm của mô hình này.", "title": "Hỗ trợ gọi hàm"}, "id": {"extra": "Sẽ được hiển thị dưới dạng nhãn mô hình", "placeholder": "<PERSON><PERSON> lòng nhập id mô hình, ví dụ gpt-4-turbo-preview hoặc claude-2.1", "title": "ID mô hình"}, "modalTitle": "<PERSON><PERSON>u hình mô hình tùy chỉnh", "tokens": {"title": "Số lư<PERSON>ng token tối đa", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn"}, "vision": {"extra": "Cấu hình này chỉ mở khả năng tải lên hình ảnh trong ứng dụng, việc hỗ trợ nhận diện hoàn toàn phụ thuộc vào mô hình, vui lòng tự kiểm tra khả năng nhận diện hình ảnh của mô hình này.", "title": "Hỗ trợ nhận diện hình ảnh"}}}, "fetchOnClient": {"desc": "Chế độ yêu cầu từ khách hàng sẽ gửi yêu cầu phiên trực tiếp từ trình du<PERSON>, có thể cải thiện tốc độ phản hồi", "title": "Sử dụng chế độ yêu cầu từ khách hàng"}, "fetcher": {"fetch": "<PERSON><PERSON><PERSON> danh sách mô hình", "fetching": "<PERSON><PERSON> l<PERSON>y danh sách mô hình...", "latestTime": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t lần cuối: {{time}}", "noLatestTime": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> đ<PERSON><PERSON><PERSON> danh s<PERSON>ch"}, "helpDoc": "H<PERSON>ớng dẫn cấu hình", "modelList": {"desc": "Chọn mô hình sẽ hiển thị trong phiên, mô hình đã chọn sẽ được hiển thị trong danh sách mô hình", "placeholder": "<PERSON><PERSON> lòng chọn mô hình từ danh sách", "title": "<PERSON><PERSON> s<PERSON>ch mô hình", "total": "T<PERSON><PERSON> cộng có {{count}} mô hình khả dụng"}, "proxyUrl": {"desc": "<PERSON><PERSON><PERSON><PERSON> địa chỉ mặc định, ph<PERSON><PERSON> bao gồm http(s)://", "title": "Địa chỉ proxy API"}, "title": "<PERSON><PERSON> hình ngôn ngữ", "waitingForMore": "<PERSON><PERSON><PERSON><PERSON> mô hình hơn đang <1><PERSON><PERSON><PERSON><PERSON> lên kế hoạch kết nối</1>, xin hãy chờ đợi"}, "systemAgent": {"customPrompt": {"addPrompt": "Thêm gợi ý tùy chỉnh", "desc": "<PERSON><PERSON> <PERSON>hi điền, trợ lý hệ thống sẽ sử dụng gợi ý tùy chỉnh khi tạo nội dung", "placeholder": "<PERSON><PERSON> lòng nhập từ gợi ý tùy chỉnh", "title": "Từ gợi ý tùy chỉnh"}, "emotionAnalysis": {"label": "<PERSON><PERSON> hình phân tích cảm xúc", "modelDesc": "Chỉ định mô hình được sử dụng cho phân tích cảm xúc", "title": "Tự động thực hiện phân tích cảm xúc"}, "title": "<PERSON><PERSON><PERSON> lý hệ thống"}, "touch": {"title": "Cài đặt chạm"}, "tts": {"clientCall": {"desc": "<PERSON><PERSON> đư<PERSON><PERSON>, dị<PERSON> vụ tổng hợp giọng nói sẽ được gọi từ khách hàng, tốc độ tổng hợp giọng nó<PERSON> hơn, nh<PERSON><PERSON> cần có khả năng truy cập internet hoặc sử dụng mạng riêng ảo.", "title": "<PERSON><PERSON><PERSON> từ khách hàng"}, "title": "Cài đặt giọng nói"}}
/* 角色语音播放器样式 */
.character-voice-player {
  position: relative;
  display: inline-block;
}

/* 语音播放指示器 */
.voice-playing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.voice-wave {
  display: flex;
  align-items: center;
  gap: 2px;
}

.voice-wave span {
  display: block;
  width: 3px;
  height: 12px;
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 2px;
  animation: voice-wave-animation 1.2s ease-in-out infinite;
}

.voice-wave span:nth-child(1) {
  animation-delay: 0s;
}

.voice-wave span:nth-child(2) {
  animation-delay: 0.2s;
}

.voice-wave span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes voice-wave-animation {
  0%, 100% {
    height: 12px;
    opacity: 0.7;
  }
  50% {
    height: 20px;
    opacity: 1;
  }
}

/* 调试信息样式 */
.voice-debug-info {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 1000;
  margin-top: 4px;
}

.voice-debug-info div {
  margin: 2px 0;
}

/* 口型同步可视化 */
.lip-sync-visualizer {
  position: relative;
  width: 60px;
  height: 40px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.lip-sync-mouth {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 8px;
  background: #ff6b6b;
  border-radius: 10px;
  transition: height 0.1s ease;
}

.lip-sync-mouth.speaking {
  height: 12px;
  background: #ff5252;
}

/* 音量可视化 */
.volume-visualizer {
  display: flex;
  align-items: center;
  gap: 1px;
  height: 20px;
  padding: 4px;
}

.volume-bar {
  width: 2px;
  background: #ddd;
  border-radius: 1px;
  transition: height 0.1s ease;
}

.volume-bar.active {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
}

/* 不同音量级别的高度 */
.volume-bar:nth-child(1) { height: 4px; }
.volume-bar:nth-child(2) { height: 6px; }
.volume-bar:nth-child(3) { height: 8px; }
.volume-bar:nth-child(4) { height: 10px; }
.volume-bar:nth-child(5) { height: 12px; }
.volume-bar:nth-child(6) { height: 14px; }
.volume-bar:nth-child(7) { height: 16px; }
.volume-bar:nth-child(8) { height: 18px; }
.volume-bar:nth-child(9) { height: 16px; }
.volume-bar:nth-child(10) { height: 14px; }
.volume-bar:nth-child(11) { height: 12px; }
.volume-bar:nth-child(12) { height: 10px; }
.volume-bar:nth-child(13) { height: 8px; }
.volume-bar:nth-child(14) { height: 6px; }
.volume-bar:nth-child(15) { height: 4px; }

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-wave span {
    width: 2px;
    height: 10px;
  }
  
  .voice-wave span:nth-child(2) {
    height: 16px;
  }
  
  .lip-sync-visualizer {
    width: 50px;
    height: 35px;
  }
  
  .volume-visualizer {
    height: 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .voice-debug-info {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
  }
  
  .lip-sync-visualizer {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.4);
  }
  
  .volume-bar {
    background: #555;
  }
}

# 数据存储（数据库交互）模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中数据存储（数据库交互）模块的设计。该模块负责处理后端业务逻辑层与底层数据库之间的数据读写操作，确保角色数据、用户数据及其相关信息的持久化存储和高效访问。

## 2. 模块概述

数据存储模块作为后端服务的持久化层，其核心职责是根据业务逻辑层的请求，执行数据库操作（CRUD：创建、读取、更新、删除）。它封装了具体的数据库驱动和查询语句（或 ORM 操作），向上层提供统一的接口，使得业务逻辑无需关心底层数据库的实现细节。这个模块将与之前设计的数据库 Schema (`docs/tech_design/database_schema.md`) 紧密关联。

## 3. 核心功能与接口 (概念性)

数据存储模块将向上层业务逻辑提供一系列方法（或函数）来执行数据库操作。以下是一些核心功能的设想：

### 3.1 用户数据操作 (基于 PostgreSQL `users` 表)

- `create_user(username, email, password_hash)`: 创建新用户。
- `get_user_by_username(username)`: 根据用户名查找用户。
- `get_user_by_id(user_id)`: 根据 ID 查找用户。
- `update_user(user_id, update_data)`: 更新用户信息。
- `delete_user(user_id)`: 删除用户及其相关数据（需要考虑级联删除）。

### 3.2 虚拟角色数据操作 (基于 PostgreSQL `characters` 表)

- `create_character(user_id, name, age, identity, personality, image_url, appearance_params, settings, public)`: 创建新角色，保存核心信息、外观参数和设定。`appearance_params` 和 `settings` 需要根据实际 JSON 结构进行存储。
- `get_character_by_id(character_id)`: 根据 ID 获取角色详细信息，包括所有参数和设定。
- `get_characters_by_user(user_id, include_private=False)`: 获取某个用户的所有角色列表，可选择是否包含非公开角色。
- `get_public_community_characters(pagination_params, filter_params)`: 获取社区公开角色列表，支持分页和过滤。
- `update_character(character_id, update_data)`: 更新角色信息，可能包括更新 image_url, appearance_params, settings 等。
- `delete_character(character_id)`: 删除角色。

### 3.3 聊天记录数据操作 (基于 PostgreSQL `chat_messages` 表 - 未来考虑)

- `save_chat_message(character_id, user_id, sender_type, content)`: 保存一条聊天消息。
- `get_chat_history(character_id, limit, offset)`: 获取与某个角色的聊天历史记录，支持分页。

### 3.4 与 MongoDB 的交互 (如果使用 `character_details` 集合 - 可选)

- `save_character_details(character_id, detailed_params, prompt_history, complex_settings)`: 保存或更新角色的详细参数和历史信息。
- `get_character_details(character_id)`: 获取角色的详细参数和历史信息。

## 4. 技术实现考量

- **ORM (Object-Relational Mapper):** 在 Python 中使用 ORM 框架（如 Django ORM 或 SQLAlchemy）可以极大地简化与 PostgreSQL 数据库的交互，将数据库表映射为 Python 对象，减少手写 SQL 语句。这也能更好地处理 `JSON`/`JSONB` 字段的存取。
- **MongoDB Driver:** 如果使用 MongoDB，需要使用相应的 Python Driver（如 `pymongo`）进行操作。
- **连接管理:** 需要管理数据库连接池，确保高效和可靠的数据库访问。
- **事务管理:** 对于需要执行多个相关数据库操作的功能（例如，创建用户并同时初始化一些默认数据），需要考虑事务管理，确保数据一致性。
- **错误处理:** 捕获数据库操作过程中可能出现的错误（如连接失败、查询错误、数据冲突）并进行适当处理。

## 5. 模块与其他模块的交互

- **被调用者:** 主要被后端业务逻辑层调用，提供数据服务。
- **调用者:** 不直接调用其他模块（除了可能的日志记录和错误报告模块）。
- **依赖:** 依赖于数据库配置和连接。

## 6. 待细化项

-   **具体的 ORM 框架选择：**
    -   **推荐:** **SQLAlchemy (SQLAlchemy ORM)**。因为它提供了极大的灵活性，既可以作为全功能的 ORM 使用，也可以在需要时直接编写 SQL 语句，适用于各种复杂度的数据库操作。它支持 PostgreSQL 的 `JSONB` 字段类型，便于直接操作 JSON 数据。
    -   **备选:** 如果后端选择 Django 框架，则可优先考虑 **Django ORM**，因为它与框架集成度高，开发效率高。但对于独立的数据层，SQLAlchemy 更具通用性。
-   **ORM 模型定义，与数据库 Schema 字段的精确映射：**
    -   将严格按照 `docs/tech_design/database_schema.md` 中定义的表结构，使用所选 ORM 的模型语法（例如 SQLAlchemy 的 `DeclarativeBase` 或 `Base`）来定义 Python 类。
    -   每个表字段将映射为模型类中的属性，并指定其数据类型、约束（如 `Not Null`, `Unique`）、主键、外键关系等。
    -   **特别注意：** 对于 `appearance_params` 和 `settings` 等 JSONB 字段，将在 ORM 模型中将其定义为 JSON 类型，ORM 将负责 JSON 对象与数据库 JSONB 字段的自动转换和存储。
-   **`appearance_params` 和 `settings` JSON 字段在代码中的具体结构定义和操作方式：**
    -   这些字段的 JSON 结构将遵循 `docs/tech_design/backend_api_design.md` 中定义的详细结构。
    -   在代码中，这些字段将作为 Python 字典或对象存储。ORM 框架（如 SQLAlchemy）将能够直接进行 JSONB 字段的读写，允许在应用层面以 Python 字典的方式操作这些复杂结构。
    -   在写入前进行**数据验证**，确保 JSON 结构符合预期，避免存储无效数据。
-   **聊天记录的存储策略：**
    -   **MVP 阶段：** 鉴于聊天数据量可能较大且需要快速读写，聊天记录 (`chat_messages`) 表将在 MVP 阶段考虑**实现持久化存储**。
    -   **存储内容：** 存储发送者类型、内容、时间戳和关联的角色/用户 ID。暂不存储完整的聊天上下文，仅存储单条消息。
    -   **查询：** 提供按 `character_id` 和 `user_id` 查询历史消息的功能，支持分页和时间范围筛选。
    -   **性能考虑：** 对 `character_id` 和 `sent_at` 字段创建索引，以优化查询性能。未来如果聊天量剧增，可以考虑分表或使用专门的实时消息存储方案。
-   **如果使用 MongoDB，其具体的交互接口和与 PostgreSQL 数据的关联方式：**
    -   **决定：** 在 MVP 阶段，**暂不引入 MongoDB**。我们优先利用 PostgreSQL 的 `JSONB` 类型来存储灵活的 `appearance_params` 和 `settings` 字段，以简化技术栈，降低初期开发和运维复杂性。
    -   **未来考虑：** 如果 PostgreSQL 的 JSONB 字段在特定场景下（例如，极度频繁地只查询或修改 JSON 内部的某个小部分，或者需要存储极其复杂的、文档型的非结构化数据）遇到性能瓶颈或功能限制，再重新评估引入 MongoDB 的可行性。届时，`character_details` 集合将通过 `character_id` 与 PostgreSQL 的 `characters` 表进行关联，作为扩展数据。
-   **如何处理软删除或硬删除策略：**
    -   **用户删除：** 采用**软删除 (Soft Delete)** 策略。在 `users` 表中增加一个 `is_deleted` (BOOLEAN, Default: FALSE) 字段和 `deleted_at` (TIMESTAMP, Nullable) 字段。当用户请求删除时，仅更新这些字段，而不是真正删除数据。这样做便于数据恢复和历史审计。
    -   **角色删除：** 角色删除也采用**软删除**。在 `characters` 表中增加 `is_deleted` (BOOLEAN, Default: FALSE) 和 `deleted_at` (TIMESTAMP, Nullable) 字段。当用户删除角色时，更新这些字段。**注意：** 软删除的角色图片可以保留一段时间，但如果存储成本成为问题，可考虑在一段时间后异步硬删除文件。
    -   **聊天记录删除：** 对于聊天记录，如果关联的角色被软删除，则在查询时应默认排除这些聊天记录。如果需要彻底清理，可以定期根据 `character_id` 和 `is_deleted` 字段进行批量硬删除。
-   **数据验证和清洗：**
    -   **前端验证：** 在前端表单提交前进行初步的数据格式和非空验证。
    -   **后端验证：** 在数据到达数据存储模块之前（通常在业务逻辑层或 DTO/Model 层），进行严格的**参数验证和清洗**。这包括：
        -   **类型检查：** 确保数据类型符合预期（例如，年龄是整数）。
        -   **范围检查：** 确保数值在合法范围内（例如，年龄在 18-30 岁）。
        -   **长度检查：** 确保字符串长度不超过数据库字段限制。
        -   **格式检查：** 确保邮箱、URL 等符合标准格式。
        -   **内容清洗：** 移除或转义潜在的恶意字符，防止注入攻击。
        -   **业务规则验证：** 例如，确保创建角色时 `user_id` 存在且有效。
    -   **ORM 层：** ORM 通常会提供一些内置的验证功能，也可利用模型钩子 (hooks) 或事件监听器在保存数据前执行自定义验证逻辑。

这份文档是数据存储模块的初步设计。具体的实现将依赖于所选的 ORM 和数据库，并在开发过程中根据业务逻辑的需要进行完善。 
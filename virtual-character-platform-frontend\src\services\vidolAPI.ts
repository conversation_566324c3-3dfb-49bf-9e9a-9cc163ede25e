import api from './api';

// VRM模型相关接口
export interface VRMModel {
  id: string;
  name: string;
  description: string;
  modelUrl: string;
  thumbnailUrl: string;
  category: 'male' | 'female' | 'other';
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// 动画数据接口
export interface AnimationData {
  id: string;
  name: string;
  type: 'idle' | 'talking' | 'emotion' | 'gesture' | 'dance';
  duration: number;
  dataUrl: string;
  previewUrl?: string;
}

// 数字人配置接口
export interface DigitalHumanConfig {
  characterId: string;
  vrmModelId: string;
  voiceSettings: {
    voice_type: string;
    speed: number;
    pitch: number;
    volume: number;
  };
  animationSettings: {
    idleAnimation: string;
    talkingAnimation: string;
    emotionAnimations: Record<string, string>;
  };
  appearanceSettings: {
    scale: number;
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
  };
}

// 语音识别结果接口
export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  duration: number;
}

// TTS合成请求接口
export interface TTSRequest {
  text: string;
  characterId: string;
  voiceType?: string;
  speed?: number;
  emotion?: string;
  enableLipSync?: boolean;
}

// TTS合成响应接口
export interface TTSResponse {
  audioUrl: string;
  duration: number;
  lipSyncData?: any; // 口型同步数据
}

export const vidolAPI = {
  // VRM模型管理
  getVRMModels: () =>
    api.get<VRMModel[]>('/vidol/vrm-models/'),

  getVRMModel: (modelId: string) =>
    api.get<VRMModel>(`/vidol/vrm-models/${modelId}/`),

  uploadVRMModel: (formData: FormData) =>
    api.post<VRMModel>('/vidol/vrm-models/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  deleteVRMModel: (modelId: string) =>
    api.delete(`/vidol/vrm-models/${modelId}/`),

  // 动画数据管理
  getAnimations: (type?: string) =>
    api.get<AnimationData[]>('/vidol/animations/', { params: { type } }),

  getAnimation: (animationId: string) =>
    api.get<AnimationData>(`/vidol/animations/${animationId}/`),

  uploadAnimation: (formData: FormData) =>
    api.post<AnimationData>('/vidol/animations/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  // 数字人配置管理
  getDigitalHumanConfig: (characterId: string) =>
    api.get<DigitalHumanConfig>(`/vidol/characters/${characterId}/config/`),

  updateDigitalHumanConfig: (characterId: string, config: Partial<DigitalHumanConfig>) =>
    api.put<DigitalHumanConfig>(`/vidol/characters/${characterId}/config/`, config),

  // 语音识别
  recognizeSpeech: (audioBlob: Blob) => {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'speech.wav');
    return api.post<SpeechRecognitionResult>('/vidol/speech/recognize/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // TTS语音合成（增强版）
  synthesizeSpeech: (request: TTSRequest) =>
    api.post<TTSResponse>('/vidol/tts/synthesize/', request),

  // 预览TTS效果
  previewTTS: (request: Omit<TTSRequest, 'characterId'>) =>
    api.post<TTSResponse>('/vidol/tts/preview/', request),

  // 获取支持的语音类型
  getVoiceTypes: () =>
    api.get<Array<{
      id: string;
      name: string;
      gender: 'male' | 'female';
      language: string;
      description: string;
    }>>('/vidol/tts/voices/'),

  // 数字人实时交互
  startDigitalHumanSession: (characterId: string) =>
    api.post<{ sessionId: string }>(`/vidol/characters/${characterId}/session/start/`),

  endDigitalHumanSession: (sessionId: string) =>
    api.post(`/vidol/sessions/${sessionId}/end/`),

  // 发送交互消息（包含数字人动画）
  sendInteractiveMessage: (sessionId: string, message: {
    text: string;
    audioData?: Blob;
    enableAnimation?: boolean;
    animationType?: string;
  }) => {
    const formData = new FormData();
    formData.append('text', message.text);
    formData.append('enableAnimation', String(message.enableAnimation || true));
    if (message.audioData) {
      formData.append('audio', message.audioData, 'input.wav');
    }
    if (message.animationType) {
      formData.append('animationType', message.animationType);
    }
    
    return api.post<{
      response: string;
      audioUrl: string;
      animationData?: any;
      lipSyncData?: any;
    }>(`/vidol/sessions/${sessionId}/message/`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // 控制数字人动画
  playAnimation: (sessionId: string, animationId: string, options?: {
    loop?: boolean;
    speed?: number;
    blendTime?: number;
  }) =>
    api.post(`/vidol/sessions/${sessionId}/animation/play/`, {
      animationId,
      ...options
    }),

  stopAnimation: (sessionId: string) =>
    api.post(`/vidol/sessions/${sessionId}/animation/stop/`),

  // 数字人表情控制
  setExpression: (sessionId: string, expression: {
    type: 'happy' | 'sad' | 'angry' | 'surprised' | 'neutral';
    intensity: number; // 0-1
    duration?: number; // 毫秒
  }) =>
    api.post(`/vidol/sessions/${sessionId}/expression/`, expression),

  // 数字人视线控制
  setGaze: (sessionId: string, target: {
    x: number;
    y: number;
    z: number;
  }) =>
    api.post(`/vidol/sessions/${sessionId}/gaze/`, target),

  // 获取数字人状态
  getDigitalHumanStatus: (sessionId: string) =>
    api.get<{
      isActive: boolean;
      currentAnimation: string;
      currentExpression: string;
      lastActivity: string;
    }>(`/vidol/sessions/${sessionId}/status/`),

  // 数字人场景管理
  getScenes: () =>
    api.get<Array<{
      id: string;
      name: string;
      description: string;
      thumbnailUrl: string;
      sceneUrl: string;
      category: string;
    }>>('/vidol/scenes/'),

  setScene: (sessionId: string, sceneId: string) =>
    api.post(`/vidol/sessions/${sessionId}/scene/`, { sceneId }),

  // 批量操作
  batchUpdateCharacterVidolSettings: (updates: Array<{
    characterId: string;
    vrmModelId?: string;
    voiceSettings?: any;
    animationSettings?: any;
  }>) =>
    api.post('/vidol/characters/batch-update/', { updates }),

  // 数字人市场集成
  getMarketCharacters: (params?: {
    category?: string;
    tags?: string[];
    page?: number;
    pageSize?: number;
  }) =>
    api.get('/vidol/market/characters/', { params }),

  getMarketAnimations: (params?: {
    type?: string;
    tags?: string[];
    page?: number;
    pageSize?: number;
  }) =>
    api.get('/vidol/market/animations/', { params }),

  // 导入市场资源
  importMarketCharacter: (marketCharacterId: string) =>
    api.post(`/vidol/market/characters/${marketCharacterId}/import/`),

  importMarketAnimation: (marketAnimationId: string) =>
    api.post(`/vidol/market/animations/${marketAnimationId}/import/`)
};

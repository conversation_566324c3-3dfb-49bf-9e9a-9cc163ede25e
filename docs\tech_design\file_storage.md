# 文件存储模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中文件存储模块的设计。该模块负责处理项目中生成或需要持久化存储的文件，特别是虚拟角色的形象图片。旨在规划如何安全、高效、可靠地存储和访问这些文件。

## 2. 模块概述

文件存储模块是后端服务用于管理静态文件的组件。考虑到图片文件的数量和大小，将文件存储在专门的云存储服务中是标准实践，可以减轻后端服务器的存储和带宽压力，并提供更好的可扩展性和可靠性。这个模块将封装与云存储服务交互的细节，向上层业务逻辑提供文件上传和访问的接口。

## 3. 核心功能

文件存储模块的核心功能是对文件进行生命周期管理：

- **文件上传:**
    - 接收需要上传的图片文件数据（例如，来自 AI 服务集成模块的 Base64 数据或字节流）。
    - 调用配置好的云存储服务 API，将文件上传到指定的存储空间 (Bucket)。
    - 为上传的文件生成一个唯一的标识符或路径。
    - 返回文件的可访问 URL（用于存储在数据库中和前端展示）。
    - 可能需要处理文件类型、大小的验证。
- **文件访问/下载:**
    - 根据存储在数据库中的文件 URL，前端可以直接从云存储服务访问文件。后端在此模块中可以提供代理下载或生成带签名 URL 的功能（如果需要访问控制）。
    - 主要场景是前端直接通过 URL 显示图片，后端可能不需要频繁下载。
- **文件删除:**
    - 当角色被删除时，需要调用此模块删除对应的图片文件，释放存储空间。
    - 根据文件 URL 或内部标识符，调用云存储服务 API 删除文件。
- **文件管理 (初步):**
    - 文件的命名策略（例如，使用 UUID 或基于用户/角色的路径）。
    - 文件的组织方式（例如，按用户 ID 或日期分组存储）。

## 4. 技术实现考量

- **云存储服务选型:** 选择一个可靠、成本合理、易于集成的云存储服务（例如，AWS S3, 阿里云 OSS, 七牛云）。需要考虑服务商提供的 SDK 和 API，以及存储和流量的费用。
- **SDK / API 集成:** 使用云存储服务商提供的 SDK 或直接调用其 API 来实现文件上传、下载和删除功能。
- **凭证管理:** 安全地管理访问云存储服务的密钥 (Access Key ID, Secret Access Key)。这些凭证不应暴露给前端。
- **文件 URL 管理:** 数据库中存储云存储服务返回的文件 URL。需要确保 URL 的持久性和可访问性。
- **访问控制 (可选):** 根据需求，配置云存储的访问权限，例如，是否允许公开访问，或者需要通过后端生成带签名 URL 才能访问。对于社区公开角色图片，通常设置为公开访问以简化前端开发。
- **错误处理:** 捕获云存储操作过程中可能出现的错误（如网络错误、权限问题、存储空间不足）并进行适当处理。

## 5. 模块与其他模块的交互

- **接收来自:** AI 服务集成模块（生成后的图片数据）、后端业务逻辑层（上传、删除文件的指令）。
- **发送给:** 后端业务逻辑层（文件 URL、操作结果）。
- **被调用者:** 主要被后端业务逻辑层调用来执行文件操作。

## 6. 待细化项

-   **具体的云存储服务提供商选择：**
    -   **推荐：阿里云 OSS (Object Storage Service)**。
        -   **理由：** 阿里云在国内有广泛的用户基础和成熟的生态系统，OSS 提供高可用、高可靠、高安全的存储服务，并支持丰富的管理功能（如生命周期管理、图片处理）。与项目其他潜在的阿里云服务（如 KMS）集成会更顺畅。
    -   **备选：腾讯云 COS (Cloud Object Storage) 或 七牛云对象存储**。选择将取决于具体的成本效益、服务特性和团队熟悉度。
-   **文件命名和存储路径的具体策略：**
    -   **命名策略：** 采用 **UUID (Universally Unique Identifier)** 作为文件名，确保文件名的全球唯一性，避免命名冲突。例如：`{UUID}.{ext}`。
    -   **路径策略：** 采用"按日期 + 用户 ID + 角色 ID"的层级结构组织存储路径，便于管理和检索，并可根据需求进行归档。例如：
        `images/{year}/{month}/{day}/{user_id}/{character_id}/{uuid}.{ext}`
    -   **示例：** `images/2023/11/20/user123/char456/a1b2c3d4-e5f6-7890-1234-567890abcdef.png`
-   **如何安全地管理云存储凭证：**
    -   **生产环境：** `Access Key ID` 和 `Secret Access Key` 将作为敏感配置，通过**密钥管理服务（如阿里云 KMS、Vault）**进行安全存储和加载，或通过安全的部署管道注入为**环境变量**。绝不能硬编码到代码中。
    -   **开发/测试环境：** 可通过 `.env` 文件加载，并确保 `.gitignore` 排除该文件，防止凭证泄露。
    -   **最小权限原则：** 为访问 OSS 的凭证配置最小必需的权限，仅授予文件上传、下载和删除的权限，避免给予过高的管理权限。
-   **是否需要实现代理下载或生成带签名 URL 功能，以及何时使用：**
    -   **直接访问 (推荐，大部分场景)：** 对于社区公开的角色图片，前端直接使用云存储服务返回的公开 URL 进行访问。这最大化利用 CDN 加速，减轻后端压力。
    -   **带签名 URL (特定场景)：** 对于需要访问控制的场景，例如用户下载自己创建的私有角色原图，或者需要限制访问时间，后端可以通过云存储 SDK 生成**带签名的 URL**。该 URL 包含签名信息和过期时间，前端在有效期内可通过此 URL 访问资源。这可以防止未经授权的访问。
    -   **代理下载 (不推荐，除非特殊需求)：** 后端作为代理转发文件请求，这会增加后端服务器的带宽和 CPU 压力。除非有特殊安全或业务需求（如统计下载次数，内容处理），否则不建议采用。
-   **文件类型和大小的验证规则：**
    -   **文件类型：** 仅允许上传图片文件（如 `image/jpeg`, `image/png`, `image/webp`）。在后端接收文件数据时进行 MIME 类型校验。
    -   **文件大小：** 限制单个图片文件的大小，例如最大 5MB-10MB，防止恶意上传大文件导致存储和带宽滥用。在后端接收文件数据时进行大小校验。
    -   **图像尺寸：** 如果需要，可以对图像的宽度和高度进行限制或强制调整，以确保图片符合前端展示和性能要求。星火 API 生成图片时会提供尺寸，这里主要是对用户可能上传的图片做限制。
-   **文件删除的策略：**
    -   **关联删除：** 当 `characters` 表中的角色记录被**软删除**（参考 `docs/tech_design/data_storage.md`）时，对应的图片文件并不会立即硬删除。
    -   **异步清理：** 引入**异步任务**或**定时任务**（例如每天/每周运行）来扫描数据库中标记为软删除且已达到一定保留期限（例如30天）的角色记录。然后，通过文件存储模块调用云存储 API，批量删除这些角色对应的图片文件。
    -   **人工干预：** 提供后台管理功能，允许管理员手动删除特定文件或触发文件清理任务。
    -   **版本控制：** 考虑云存储服务的版本控制功能，即使文件被删除也能在一定时间内恢复（如果业务有此需求）。

这份文档是文件存储模块的初步设计。具体的实现将依赖于所选的云存储服务，并在开发过程中进行完善。 
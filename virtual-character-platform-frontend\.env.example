# 虚拟角色平台前端环境变量配置示例
# 复制此文件为 .env 并根据你的环境修改配置

# ===========================================
# API配置
# ===========================================

# 开发环境 - 使用Vite代理
VITE_API_URL=/api

# 生产环境 - 使用完整URL（取消注释并修改域名）
# VITE_API_URL=https://api.yourdomain.com/api

# 本地开发（不使用代理）
# VITE_API_URL=http://127.0.0.1:8000/api

# ===========================================
# 环境配置
# ===========================================

# 环境类型：development, production, test
VITE_NODE_ENV=development

# ===========================================
# 端口配置说明
# ===========================================

# 前端开发服务器端口（由vite.config.ts控制）
# 默认: 5173
# 备用: 5174, 5175, 5176, 5177, 5178, 5179, 5180

# 后端Django服务器端口
# 开发环境: 8000
# Docker环境: 8000（已统一）

# ===========================================
# 其他配置
# ===========================================

# 是否启用调试模式
# VITE_DEBUG=true

# API请求超时时间（毫秒）
# VITE_API_TIMEOUT=60000

# ===========================================
# 应用配置
# ===========================================

# 应用基础路径
NEXT_PUBLIC_BASE_PATH=

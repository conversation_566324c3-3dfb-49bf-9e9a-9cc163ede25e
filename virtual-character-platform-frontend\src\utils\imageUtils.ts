/**
 * 图片URL处理工具函数
 */

// 获取API基础URL
const getApiBaseUrl = (): string => {
  return import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://127.0.0.1:8000';
};

/**
 * 处理角色图片URL
 * @param imageUrl 原始图片URL
 * @param fallback 默认占位符图片
 * @returns 处理后的图片URL
 */
export const processImageUrl = (imageUrl: string | null | undefined, fallback: string = '/placeholder-character.svg'): string => {
  // 如果没有图片URL，返回占位符
  if (!imageUrl) {
    return fallback;
  }

  // 如果是完整的HTTP/HTTPS URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 如果是媒体文件路径，添加后端服务器地址
  if (imageUrl.startsWith('/media/')) {
    const baseUrl = getApiBaseUrl();
    return `${baseUrl}${imageUrl}`;
  }

  // 如果是其他相对路径（如占位符），直接返回
  if (imageUrl.startsWith('/')) {
    return imageUrl;
  }

  // 其他情况，返回占位符
  return fallback;
};

/**
 * 检查图片URL是否有效
 * @param imageUrl 图片URL
 * @returns Promise<boolean>
 */
export const checkImageUrl = async (imageUrl: string): Promise<boolean> => {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * 预加载图片
 * @param imageUrl 图片URL
 * @returns Promise<HTMLImageElement>
 */
export const preloadImage = (imageUrl: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = imageUrl;
  });
};

/**
 * 获取图片尺寸
 * @param imageUrl 图片URL
 * @returns Promise<{width: number, height: number}>
 */
export const getImageDimensions = async (imageUrl: string): Promise<{width: number, height: number}> => {
  const img = await preloadImage(imageUrl);
  return {
    width: img.naturalWidth,
    height: img.naturalHeight
  };
};

/**
 * 压缩图片为指定尺寸
 * @param file 图片文件
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param quality 压缩质量 (0-1)
 * @returns Promise<string> Base64字符串
 */
export const compressImage = (
  file: File, 
  maxWidth: number = 800, 
  maxHeight: number = 800, 
  quality: number = 0.8
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);
      
      // 转换为Base64
      const base64 = canvas.toDataURL('image/jpeg', quality);
      resolve(base64);
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 将Base64转换为Blob
 * @param base64 Base64字符串
 * @returns Blob
 */
export const base64ToBlob = (base64: string): Blob => {
  const byteCharacters = atob(base64.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: 'image/jpeg' });
};

/**
 * 下载图片
 * @param imageUrl 图片URL
 * @param filename 文件名
 */
export const downloadImage = async (imageUrl: string, filename: string = 'image.jpg'): Promise<void> => {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载图片失败:', error);
    throw error;
  }
};

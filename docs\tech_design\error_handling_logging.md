# 错误处理与日志记录模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中错误处理与日志记录模块的设计。该模块旨在建立一个统一的、健壮的机制来捕获、报告和记录系统中发生的错误和重要的操作事件，以提高系统的可靠性、可维护性和可观测性。

## 2. 模块概述

错误处理与日志记录模块将贯穿于整个后端服务和潜在的前端应用中。它负责拦截运行时错误，根据错误类型进行分类和处理（例如，向用户显示友好的错误信息，向开发者发送告警），并将详细的事件信息记录到持久化存储中。一个良好的日志系统能够帮助开发者理解系统行为，诊断问题，并监控系统健康状况。

## 3. 核心功能

- **错误捕获:**
    - 统一捕获应用程序不同层面（如 API 请求处理、数据库操作、第三方服务调用、业务逻辑执行）发生的异常和错误。
- **错误分类与处理:**
    - 根据错误类型（例如，业务逻辑错误、系统错误、第三方服务错误）进行分类。
    - 对不同类别的错误执行不同的处理逻辑（例如，返回标准的 API 错误响应给前端，触发告警通知）。
    - 避免敏感信息泄露给用户或日志。
- **日志记录:**
    - 记录系统运行时的关键事件，包括正常操作流程、警告、错误和调试信息。
    - 记录信息应包含时间戳、事件级别（如 INFO, WARN, ERROR, DEBUG）、发生位置（文件、函数、行号）、关联的用户/请求信息（如用户 ID, 请求 ID）以及详细的事件描述和错误堆栈信息（针对错误）。
- **日志存储与管理:**
    - 将日志写入到文件系统、数据库或专门的日志收集系统（如 ELK Stack, Loki）。
    - 考虑日志的轮转、归档和清理策略，以管理存储空间。
- **告警通知 (可选):**
    - 对于特定级别的错误（如 ERROR），触发告警通知给开发者或运维团队。

## 4. 技术实现考量

- **日志库选型:** 选择一个成熟的日志库（如 Python 的 `logging` 模块，或其他语言的相应库），支持不同的日志级别、输出格式和Handler（输出到不同目的地）。
- **统一错误处理中间件/切面:** 在 Web 框架层面实现一个全局的错误处理中间件或AOP（面向切面编程）切面，集中处理未捕获的异常。
- **日志级别配置:** 允许通过配置文件或环境变量动态调整日志级别，以便在不同环境（开发、测试、生产）下输出不同详细程度的日志。
- **结构化日志:** 考虑使用结构化日志（如 JSON 格式），便于日志的解析、搜索和分析。
- **分布式追踪 (未来考虑):** 对于微服务架构，可能需要集成分布式追踪系统来追踪跨服务的请求。
- **错误报告服务 (未来考虑):** 集成 Sentry 或类似的错误报告服务，自动收集和聚合错误信息。

## 5. 模块与其他模块的交互

- **接收来自:** 所有其他模块（当发生错误或需要记录事件时）。
- **发送给:** 日志存储介质（文件、数据库、日志系统）、告警系统（如果配置）。
- **被调用者:** 被应用程序的各个部分调用来记录事件或处理异常。

## 6. 待细化项

-   **具体的日志级别使用约定：**
    -   **`DEBUG`:** 仅在开发和调试阶段使用，记录详细的内部操作、变量值、API 请求/响应的完整内容等。生产环境通常关闭。
    -   **`INFO`:** 记录系统正常运行时的关键事件，如服务启动/停止、重要业务操作（用户注册/登录、角色创建/保存成功、AI 图片生成成功）的成功信息，以及定期任务的执行情况。
    -   **`WARN`:** 记录可能导致问题但不影响当前功能正常运行的事件，如不推荐的 API 调用、次要的配置问题、预期内的异常但已妥善处理（如用户输入格式轻微不规范）。
    -   **`ERROR`:** 记录导致功能无法正常执行的错误，但系统整体可能仍在运行。例如，数据库操作失败、第三方 API 调用失败（如星火 API 返回错误码非 0）、业务逻辑执行过程中发生未捕获的异常。
    -   **`CRITICAL`:** 记录导致应用程序或关键子系统崩溃、不可用的严重错误，需要立即介入处理。例如，数据库连接池耗尽、核心服务启动失败。
-   **日志的详细格式和包含字段：**
    -   采用结构化日志（如 JSON 格式），便于日志收集系统（如 ELK Stack）进行解析和检索。
    -   **通用字段：**
        -   `timestamp`: 事件发生时间 (ISO 8601 格式，带时区)。
        -   `level`: 日志级别 (`DEBUG`, `INFO`, `WARN`, `ERROR`, `CRITICAL`)。
        -   `service`: 发生日志的服务/模块名称（如 `backend-api`, `ai-integration-service`）。
        -   `trace_id`: 全局唯一的请求追踪 ID，用于追踪一次完整请求在多个服务间的流转（结合分布式追踪）。
        -   `span_id`: 当前操作的唯一 ID，是 `trace_id` 的子集。
        -   `message`: 简短的人类可读的事件描述。
        -   `event_code`: 自定义事件代码，便于快速识别和过滤特定类型的事件。
    -   **错误/警告特定字段：**
        -   `error_type`: 错误类型（如 `DatabaseError`, `ExternalServiceError`, `ValidationError`）。
        -   `error_code`: 内部统一错误码（参考 `backend_api_design.md` 中定义的错误码体系）。
        -   `stack_trace`: 错误堆栈信息 (仅 `ERROR` 和 `CRITICAL` 级别)。
        -   `request_id`: 关联的 HTTP 请求 ID。
        -   `user_id`: 关联的用户 ID (如果可用)。
        -   `details`: JSON 对象，包含任何有助于调试的额外上下文信息（如请求参数、响应内容）。
-   **日志存储的具体方案和配置：**
    -   **开发/测试环境：** 日志输出到控制台 (stdout/stderr) 和本地文件，便于实时查看和调试。
    -   **生产环境：**
        -   **容器化环境 (如 Docker/Kubernetes):** 日志输出到标准输出 (stdout)，由容器编排工具（如 Kubernetes）或专门的日志收集代理 (如 Fluentd, Filebeat) 收集。
        -   **日志收集系统：** 将收集到的日志发送到中心化的日志管理系统，如 **ELK Stack (Elasticsearch, Logstash, Kibana)** 或 **Grafana Loki**。这些系统提供日志的存储、索引、搜索和可视化功能。
        -   **日志轮转：** 配置日志文件轮转策略，防止单文件过大，占用过多磁盘空间。
        -   **归档/清理：** 根据合规性和存储成本，设定日志的保留期限和归档/清理策略。
-   **错误处理的具体策略：**
    -   **统一错误响应：** 所有 API 接口在发生错误时，都将返回统一的 JSON 错误响应格式，包含 `code`, `message` 和 `details` 字段（参考 `backend_api_design.md`）。
    -   **异常捕获：** 在业务逻辑层、数据访问层、AI 服务集成层等关键模块，使用 `try...except` 块捕获特定异常。
    -   **自定义异常：** 定义业务相关的自定义异常类，以便更精细地分类和处理业务逻辑错误。
    -   **全局异常处理：** 在 Web 框架层面配置全局异常处理中间件（或装饰器），捕获所有未被特定代码块处理的异常，并将其转换为统一的错误响应和日志记录。
    -   **敏感信息脱敏：** 在错误信息和日志中，对密码、API 密钥、个人身份信息等敏感数据进行脱敏处理，绝不直接暴露。
-   **是否需要和如何实现告警通知：**
    -   **需求：** 是的，对于 `ERROR` 和 `CRITICAL` 级别的日志，需要实现告警通知，以便及时发现和解决问题。
    -   **实现方式：**
        -   **日志收集系统集成：** 利用 ELK Stack 或 Loki 等日志管理系统的告警功能，基于日志的模式、级别或特定错误码触发告警。
        -   **告警平台：** 集成到主流的告警平台，如 **钉钉、企业微信、飞书、Slack** 或 **Prometheus Alertmanager**。通过 Webhook 或自定义脚本将告警信息发送到指定渠道。
        -   **通知内容：** 告警信息应包含错误级别、服务名称、错误摘要、`trace_id`、关键上下文信息以及指向日志系统或监控面板的链接，便于快速定位问题。
-   **安全处理日志中的敏感信息：**
    -   **运行时脱敏：** 在日志记录器输出日志之前，通过拦截器或过滤器对日志内容进行扫描，识别并替换敏感信息（如使用正则表达式匹配身份证号、手机号、密码等）为掩码（如 `****`）。
    -   **配置排除：** 在日志配置中，明确指定不应被日志记录的特定字段或参数（例如 HTTP 请求头中的 `Authorization` 字段）。
    -   **最小权限原则：** 确保日志收集和查看工具的访问权限受到严格控制，只有授权人员才能访问敏感日志。
    -   **传输加密：** 日志从应用发送到日志收集系统时，应使用加密传输（如 HTTPS/TLS）。
    -   **存储加密：** 考虑对存储在磁盘上的日志文件进行加密。

这份文档是错误处理与日志记录模块的初步设计。具体的实现细节将在开发过程中根据项目需求和所选技术栈进行完善。 
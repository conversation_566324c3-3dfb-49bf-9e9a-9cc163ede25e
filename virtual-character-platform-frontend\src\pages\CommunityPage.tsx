import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Card, Row, Col, Spin, message, Empty, Pagination, Input, Select, Button, Tooltip, Tag } from 'antd';
import { SearchOutlined, HeartOutlined, HeartFilled, MessageOutlined, UserOutlined, CalendarOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { characterAPI } from '../services/characterAPI';
import LazyImage from '../components/LazyImage';
import { processImageUrl } from '../utils/imageUtils';
import '../styles/community.css';

const { Content } = Layout;
const { Search } = Input;
const { Option } = Select;

// 角色类型定义
interface Character {
  id: string;
  name: string;
  imageUrl: string;
  creator: {
    id: string;
    username: string;
  };
  personality?: string;
  identity?: string;
  createdAt: string;
  likes: number;
  isLiked?: boolean;
  chatCount?: number;
  viewCount?: number;
  tags?: string[];
  isPublic?: boolean;
}

const CommunityPage: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [characters, setCharacters] = useState<Character[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [total, setTotal] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<string>('latest');
  const [stats, setStats] = useState({
    totalCharacters: 0,
    totalCreators: 0,
    totalChats: 0
  });
  
  // 加载社区角色列表
  const loadCharacters = async (page = currentPage, size = pageSize, query = searchQuery, sort = sortBy) => {
    setLoading(true);
    try {
      // 调用API获取社区角色列表
      const response = await characterAPI.getPublicCharacters(page, size, query, sort);

      if (response && Array.isArray(response)) {
        // 后端返回的是数组格式，需要转换为前端期望的格式
        const transformedCharacters = response.map((char: any) => ({
          id: char.id.toString(),
          name: char.name,
          imageUrl: processImageUrl(char.image_url),
          creator: {
            id: char.creator_id || '1',
            username: char.creator_name || '系统用户'
          },
          personality: char.personality,
          identity: char.identity,
          createdAt: char.created_at || new Date().toISOString(),
          likes: char.likes || Math.floor(Math.random() * 100),
          isLiked: char.is_liked || false,
          chatCount: char.chat_count || Math.floor(Math.random() * 50),
          viewCount: char.view_count || Math.floor(Math.random() * 200),
          tags: char.tags || [],
          isPublic: char.is_public !== false
        }));

        setCharacters(transformedCharacters);
        setTotal(response.length);

        // 更新统计信息
        setStats({
          totalCharacters: response.length,
          totalCreators: new Set(transformedCharacters.map(c => c.creator.id)).size,
          totalChats: transformedCharacters.reduce((sum, c) => sum + (c.chatCount || 0), 0)
        });
      } else if (response && response.data?.characters) {
        // 如果后端返回的是对象格式
        setCharacters(response.data.characters);
        setTotal(response.data.total || 0);
      }
    } catch (error) {
      console.error('获取社区角色列表失败:', error);
      message.error('获取社区角色列表失败，请稍后再试');

      // 使用模拟数据（仅用于开发）
      const personalities = ['温柔', '活泼', '傲娇', '冷酷', '神秘', '开朗', '内向', '勇敢'];
      const identities = ['高中生', '魔法师', '偶像', '公主', '骑士', '学者', '商人', '冒险者'];
      const tags = ['可爱', '聪明', '勇敢', '温柔', '神秘', '强大', '友善', '独立'];

      const mockCharacters = Array(12).fill(0).map((_, index) => ({
        id: `${index + 1}`,
        name: `角色${index + 1}`,
        imageUrl: `https://placehold.co/300x400/2a2a3e/b8b8d1?text=Character${index + 1}`,
        creator: {
          id: `${Math.floor(Math.random() * 10) + 1}`,
          username: `用户${Math.floor(Math.random() * 100)}`
        },
        personality: personalities[Math.floor(Math.random() * personalities.length)],
        identity: identities[Math.floor(Math.random() * identities.length)],
        createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
        likes: Math.floor(Math.random() * 100),
        isLiked: Math.random() > 0.5,
        chatCount: Math.floor(Math.random() * 50),
        viewCount: Math.floor(Math.random() * 200),
        tags: Array(Math.floor(Math.random() * 3) + 1).fill(0).map(() =>
          tags[Math.floor(Math.random() * tags.length)]
        ).filter((tag, index, arr) => arr.indexOf(tag) === index),
        isPublic: true
      }));

      setCharacters(mockCharacters);
      setTotal(100);

      // 模拟统计数据
      setStats({
        totalCharacters: 100,
        totalCreators: 25,
        totalChats: 1250
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadCharacters();
  }, []);

  // 处理页码变化 - 使用 useCallback 优化
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
    loadCharacters(page, pageSize);
  }, []);

  // 处理搜索 - 使用 useCallback 优化
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
    loadCharacters(1, pageSize, value);
  }, [pageSize]);

  // 处理排序方式变化 - 使用 useCallback 优化
  const handleSortChange = useCallback((value: string) => {
    setSortBy(value);
    loadCharacters(currentPage, pageSize, searchQuery, value);
  }, [currentPage, pageSize, searchQuery]);

  // 处理点赞
  const handleLike = async (character: Character) => {
    try {
      // 乐观更新UI
      const updatedCharacters = characters.map(c => {
        if (c.id === character.id) {
          return {
            ...c,
            likes: c.isLiked ? c.likes - 1 : c.likes + 1,
            isLiked: !c.isLiked
          };
        }
        return c;
      });

      setCharacters(updatedCharacters);

      // 调用API更新点赞状态
      // await characterAPI.likeCharacter(character.id, !character.isLiked);

      // 显示反馈消息
      message.success(character.isLiked ? '已取消点赞' : '点赞成功');
    } catch (error) {
      console.error('更新点赞状态失败:', error);
      message.error('操作失败，请稍后再试');

      // 恢复原状态
      loadCharacters();
    }
  };

  // 处理聊天
  const handleChat = (character: Character) => {
    // 跳转到聊天页面
    navigate(`/chat/${character.id}`);
  };

  // 格式化时间显示
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`;
    return date.toLocaleDateString();
  };

  return (
    <Content className="community-container">
      <div className="community-header animate-fade-in">
        <h1 className="community-title animate-fade-in-up">角色社区</h1>
        <p className="community-description animate-fade-in-up animate-delay-100">发现并探索其他用户创建的精彩角色，与AI伙伴开启有趣的对话</p>

        {/* 统计信息 */}
        <div className="community-stats animate-fade-in-up animate-delay-200">
          <div className="stat-item hover-scale">
            <span className="stat-number">{stats.totalCharacters}</span>
            <span className="stat-label">个角色</span>
          </div>
          <div className="stat-item hover-scale animate-delay-100">
            <span className="stat-number">{stats.totalCreators}</span>
            <span className="stat-label">位创作者</span>
          </div>
          <div className="stat-item hover-scale animate-delay-200">
            <span className="stat-number">{stats.totalChats}</span>
            <span className="stat-label">次对话</span>
          </div>
        </div>

        <div className="community-filters">
          <Search
            placeholder="搜索角色名称、性格或身份..."
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            onSearch={handleSearch}
            className="search-input"
          />

          <Select
            defaultValue="latest"
            onChange={handleSortChange}
            className="sort-select"
            size="large"
          >
            <Option value="latest">最新创建</Option>
            <Option value="popular">最受欢迎</Option>
            <Option value="trending">热门趋势</Option>
            <Option value="oldest">最早创建</Option>
          </Select>
        </div>
      </div>
      
      {loading ? (
        <div className="loading-container">
          <Spin size="large" tip="正在加载角色列表...">
            <div style={{ minHeight: '300px' }} />
          </Spin>
        </div>
      ) : characters.length > 0 ? (
        <>
          <Row gutter={[24, 24]} className="character-grid">
            {characters.map((character, index) => (
              <Col xs={24} sm={12} md={8} lg={6} key={character.id}>
                <Card
                  hoverable
                  className={`character-card animate-fade-in-up hover-lift animate-delay-${Math.min(index * 100, 500)}`}
                  cover={
                    <div className="character-image-container">
                      <LazyImage
                        src={character.imageUrl}
                        alt={character.name}
                        className="character-image"
                        fallback="/placeholder-character.svg"
                      />
                      {/* 角色标签 */}
                      {character.tags && character.tags.length > 0 && (
                        <div style={{ position: 'absolute', top: 8, left: 8, zIndex: 2 }}>
                          <Tag color="var(--color-primary)">
                            {character.tags[0]}
                          </Tag>
                        </div>
                      )}
                    </div>
                  }
                  actions={[
                    <Tooltip title={character.isLiked ? '取消点赞' : '点赞'} key="like">
                      <div className="card-action" onClick={() => handleLike(character)}>
                        {character.isLiked ? <HeartFilled className="liked" /> : <HeartOutlined />}
                        <span>{character.likes}</span>
                      </div>
                    </Tooltip>,
                    <Tooltip title="开始聊天" key="chat">
                      <div className="card-action" onClick={() => handleChat(character)}>
                        <MessageOutlined />
                        <span>聊天</span>
                      </div>
                    </Tooltip>,
                    <Tooltip title="查看详情" key="view">
                      <div className="card-action">
                        <EyeOutlined />
                        <span>{character.viewCount || 0}</span>
                      </div>
                    </Tooltip>
                  ]}
                >
                  <Card.Meta
                    title={character.name}
                    description={
                      <div className="character-info">
                        <div className="character-creator">
                          <UserOutlined style={{ marginRight: 4 }} />
                          {character.creator.username}
                        </div>
                        {character.personality && (
                          <div className="character-trait">
                            性格: {character.personality}
                          </div>
                        )}
                        {character.identity && (
                          <div className="character-trait">
                            身份: {character.identity}
                          </div>
                        )}
                        <div className="character-trait">
                          <CalendarOutlined style={{ marginRight: 4 }} />
                          {formatTimeAgo(character.createdAt)}
                        </div>
                        {character.chatCount !== undefined && (
                          <div className="character-trait">
                            <MessageOutlined style={{ marginRight: 4 }} />
                            {character.chatCount} 次对话
                          </div>
                        )}
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
          
          <div className="pagination-container">
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={total}
              onChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 个，共 ${total} 个角色`
              }
              pageSizeOptions={['12', '24', '36', '48']}
            />
          </div>
        </>
      ) : (
        <Empty
          description={
            <div>
              <p>暂无角色数据</p>
              <p>成为第一个创建者，分享你的AI伙伴吧！</p>
            </div>
          }
          image={Empty.PRESENTED_IMAGE_DEFAULT}
          className="empty-container"
        >
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/create-character')}
          >
            创建角色
          </Button>
        </Empty>
      )}
    </Content>
  );
};

export default CommunityPage;
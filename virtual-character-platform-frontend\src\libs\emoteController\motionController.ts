import { VRM } from '@pixiv/three-vrm';
import { AnimationA<PERSON>, AnimationClip, AnimationMixer, LoopO<PERSON>, LoopRepeat } from 'three';

import { loadMixamoAnimation } from '../FBXAnimation/loadMixamoAnimation';
import { loadVMDAnimation } from '../VMDAnimation/loadVMDAnimation';
import IKHandler from '../VMDAnimation/vrm-ik-handler';
import VRMIKHandler from '../VMDAnimation/vrm-ik-handler';
import { loadVRMAnimation } from '../VRMAnimation/loadVRMAnimation';

import { motionPresetMap, MotionFileType, type MotionPresetName } from './motionPresetMap';

export class MotionController {
  private vrm: VRM;
  private mixer?: AnimationMixer;
  private currentAction?: AnimationAction;
  private currentClip?: AnimationClip;
  private ikHandler: VRMIKHandler;
  private preloadedMotions = new Map<string, AnimationClip>();

  constructor(vrm: VRM) {
    this.vrm = vrm;
    this.ikHandler = IKHandler.get(vrm);
  }

  public async preloadMotion(motion: MotionPresetName) {
    const { type, url } = this.getMotionInfo(motion);
    if (url) {
      await this.preloadMotionUrl(type, url);
    }
  }

  public async preloadMotionUrl(fileType: MotionFileType, url: string) {
    if (!this.preloadedMotions.has(url)) {
      const clip = await this.loadMotionClip(fileType, url);
      if (clip) {
        this.preloadedMotions.set(url, clip);
      }
    }
  }

  public playMotion(motion: MotionPresetName, loop: boolean = true) {
    const motionConfig = this.getMotionInfo(motion);
    if (motionConfig.url) {
      this.playMotionUrl(motionConfig.type, motionConfig.url, loop);
    } else {
      // 如果没有URL，使用程序化动画（表情控制）
      console.log(`播放程序化动作: ${motion}`);
      // 这里可以添加程序化动画逻辑
    }
  }

  private getMotionInfo(motion: MotionPresetName) {
    return motionPresetMap[motion] || motionPresetMap['idle'];
  }

  public async playMotionUrl(
    fileType: MotionFileType,
    url: string,
    loop: boolean = true,
  ): Promise<void> {
    this.stopMotion();

    let clip: AnimationClip | undefined;

    if (this.preloadedMotions.has(url)) {
      clip = this.preloadedMotions.get(url);
    } else {
      clip = await this.loadMotionClip(fileType, url);
    }

    if (!clip) {
      console.error(`无法加载动作: ${url}`);
      return;
    }

    // 创建新的 mixer
    this.mixer = new AnimationMixer(this.vrm.scene);

    this.currentAction = this.mixer.clipAction(clip);
    this.currentAction.setLoop(loop ? LoopRepeat : LoopOnce, loop ? Infinity : 1);
    this.currentAction.play();

    this.currentClip = clip;
  }

  private async loadMotionClip(
    fileType: MotionFileType,
    url: string,
  ): Promise<AnimationClip | undefined> {
    switch (fileType) {
      case 'vmd':
        return await this.loadVMD(url);
      case 'fbx':
        return await this.loadFBX(url);
      case 'vrma':
        return await this.loadVRMA(url);
      default:
        throw new Error('不支持的文件格式');
    }
  }

  private async loadVMD(url: string): Promise<AnimationClip | undefined> {
    return await loadVMDAnimation(url, this.vrm);
  }

  private async loadFBX(url: string): Promise<AnimationClip | undefined> {
    return await loadMixamoAnimation(url, this.vrm);
  }

  private async loadVRMA(url: string): Promise<AnimationClip | undefined> {
    return await loadVRMAnimation(url, this.vrm);
  }

  public stopMotion(): void {
    if (this.mixer) {
      this.mixer.stopAllAction();
      this.mixer.uncacheRoot(this.vrm.scene);
    }

    this.ikHandler.disableAll();

    if (this.currentAction) {
      this.currentAction.stop();
    }

    this.currentAction = undefined;
    this.currentClip = undefined;
    this.mixer = undefined;
  }

  public update(delta: number): void {
    if (this.mixer) {
      this.mixer.update(delta);
    }
    this.vrm.update(delta);
    this.ikHandler.update();
  }

  // 添加与现有系统兼容的方法
  public getCurrentMotion(): MotionPresetName {
    // 返回当前播放的动作名称，如果没有则返回idle
    return this.currentClip?.name || 'idle';
  }

  public isPlaying(): boolean {
    return this.currentAction?.isRunning() || false;
  }

  public setMotionWeight(weight: number): void {
    if (this.currentAction) {
      this.currentAction.setEffectiveWeight(weight);
    }
  }

  public getMotionDuration(): number {
    return this.currentClip?.duration || 0;
  }

  public setMotionTime(time: number): void {
    if (this.currentAction) {
      this.currentAction.time = time;
    }
  }
}

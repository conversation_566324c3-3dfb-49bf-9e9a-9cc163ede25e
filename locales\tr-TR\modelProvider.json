{"azure": {"azureApiVersion": {"desc": "Azure API sürümü, YYYY-AA-GG formatına u<PERSON>ı<PERSON>, [en son sürümü kontrol edin](https://learn.microsoft.com/tr-tr/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> al", "title": "Azure API Sürümü"}, "empty": "İlk modeli eklemek için model ID'sini girin", "endpoint": {"desc": "Azure portalında kaynakları kontrol ederken, bu <PERSON><PERSON><PERSON> 'Anahtarlar ve Uç Noktalar' bölümünde bulabilirsiniz", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API Adresi"}, "modelListPlaceholder": "Dağıttığınız OpenAI modelini seçin veya ekleyin", "title": "Azure OpenAI", "token": {"desc": "Azure portalında kaynakları kontrol ederken, bu <PERSON><PERSON><PERSON> 'Anahtarlar ve Uç Noktalar' bölümünde bulabilirsiniz. KEY1 veya KEY2 kullanılabilir", "placeholder": "Azure API Anahtarı", "title": "API Anahtarı"}}, "bedrock": {"accessKeyId": {"desc": "AWS Erişim <PERSON> girin", "placeholder": "AWS Erişim <PERSON>", "title": "AWS Erişim <PERSON>"}, "checker": {"desc": "AccessKeyId / SecretAccessKey'in do<PERSON><PERSON> giri<PERSON> test et"}, "region": {"desc": "AWS Bölgesini girin", "placeholder": "AWS Bölgesi", "title": "AWS Bölgesi"}, "secretAccessKey": {"desc": "AWS Gizli Erişim <PERSON>htarını girin", "placeholder": "AWS Gizli Erişim <PERSON>ı", "title": "AWS Gizli Erişim <PERSON>ı"}, "sessionToken": {"desc": "AWS SSO/STS kullanıyorsanız, AWS Oturum Anahtarınızı girin", "placeholder": "AWS Oturum Anahtarı", "title": "AWS Oturum Anahtarı (isteğe bağlı)"}, "title": "Bedrock", "unlock": {"customRegion": "<PERSON><PERSON>z<PERSON> b<PERSON><PERSON><PERSON>", "customSessionToken": "Özel Oturum Anahtarı", "description": "Oturuma başlamak için AWS Erişim Anahtarınızı / Gizli Erişim Anahtarınızı girin. Uygulama kimlik bilgilerinizi kaydetmeyecek", "title": "Özel Bedrock kimlik bilgileri ile kullan"}}, "github": {"personalAccessToken": {"desc": "Github PAT'nizi girin, [bura<PERSON>](https://github.com/settings/tokens) tıklayarak oluşturun", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "HuggingFace <PERSON>'inizi girin, [bura<PERSON>](https://huggingface.co/settings/tokens) tıklayarak oluşturun", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "ollama": {"checker": {"desc": "Proxy adresinin doğru giri<PERSON> test et", "title": "Bağlantı Kontrolü"}, "customModelName": {"desc": "<PERSON><PERSON> model <PERSON><PERSON><PERSON>, birden fazla modeli vir<PERSON> (,) ile ayır<PERSON>n", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Özel Model Adı"}, "download": {"desc": "Ollama bu modeli indiriyor, lütfen bu sayfayı kapatmamaya çalışın. Yeniden indirme sırasında kesintiye uğrayacaktır", "remainingTime": "<PERSON><PERSON>", "speed": "İndirme hızı", "title": "{{model}} modeli indiriliyor"}, "endpoint": {"desc": "Ollama arayüzü proxy ad<PERSON><PERSON> giri<PERSON>, yerel olar<PERSON> ek bir şey belirtilmediyse boş bırakabilirsiniz", "title": "Ollama Hizmet Adresi"}, "setup": {"cors": {"description": "Tarayıcı güvenlik kısıtlamaları nedeniyle, Ollama'nın düzgün çalışabilmesi için CORS yapılandırması yapmanız gerekmektedir.", "linux": {"env": "[Service] bölümüne `Environment` ekleyin, OLLAMA_ORIGINS ortam değişkenini ekleyin:", "reboot": "systemd'yi yeniden yükleyin ve Ollama'yı yeniden başlatın", "systemd": "ollama hizmetini düzenlemek için systemd'yi çağırın:"}, "macos": "Lütfen 'Terminal' uygulamasını açın, aşağıdaki komutları yapıştırın ve çalıştırmak için Enter'a basın", "reboot": "İşlem tamamlandıktan sonra Ollama hizmetini yeniden başlatın", "title": "Ollama'nın CORS erişimini yapılandırın", "windows": "Windows'ta, '<PERSON><PERSON>m Masası'na tıkla<PERSON>ın, sistem ortam değişkenlerini düzenlemeye gidin. Kullanıcı hesabınız için 'OLLAMA_ORIGINS' adında bir ortam değ<PERSON>şkeni oluşturun, de<PERSON><PERSON> * olarak ayarlayın, '<PERSON><PERSON>/Uygula'ya tıklayarak kaydedin"}, "install": {"description": "<PERSON><PERSON><PERSON>'<PERSON><PERSON> a<PERSON>tığın<PERSON><PERSON><PERSON> em<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> indirmediyseniz, lütfen resmi web sitesinden <1>indirin</1>", "docker": "<PERSON>er kullanmayı tercih ediyo<PERSON>, <PERSON><PERSON><PERSON>smi <PERSON> imajın<PERSON> da sunma<PERSON>adır, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i komutla çekebilirsiniz:", "linux": {"command": "Aşağıdaki komutla kurun:", "manual": "Ya da <1>Linux manuel kurulum kılavuzuna</1> bakarak kendiniz kurabilirsiniz"}, "title": "Ollama uygulamasını yerel olarak kurun ve başlatın", "windowsTab": "Windows (önizleme)"}}, "title": "Ollama", "unlock": {"cancel": "İndirmeyi iptal et", "confirm": "<PERSON><PERSON><PERSON>", "description": "Ollama model <PERSON><PERSON><PERSON><PERSON><PERSON>, ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sonra oturuma devam edebilirsiniz", "downloaded": "{{completed}} / {{total}}", "starting": "İndirmeye başlıyor...", "title": "Belirtilen <PERSON> modelini indir"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "SenseNova Eriş<PERSON>irin", "placeholder": "SenseNova E<PERSON><PERSON><PERSON>'si", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "sensenovaAccessKeySecret": {"desc": "SenseNova Gizli Erişim <PERSON> girin", "placeholder": "SenseNova Gizli Erişim <PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"description": "Oturuma başlamak iç<PERSON> / <PERSON><PERSON><PERSON><PERSON> girin. Uygulama kimlik bilgilerinizi kaydetmeyecek", "title": "<PERSON><PERSON>ova kimlik bilgileri ile kullan"}}, "wenxin": {"accessKey": {"desc": "<PERSON><PERSON><PERSON>irin", "placeholder": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "checker": {"desc": "AccessKey / SecretAccess'in do<PERSON><PERSON> giri<PERSON> test et"}, "secretKey": {"desc": "<PERSON><PERSON> Gizli Anahtarını girin", "placeholder": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "unlock": {"customRegion": "<PERSON><PERSON>z<PERSON> b<PERSON><PERSON><PERSON>", "description": "Oturuma başlamak için E<PERSON>ş<PERSON>nızı / Gizli Anahtarınızı girin. Uygulama kimlik bilgilerinizi kaydetmeyecek", "title": "<PERSON><PERSON> kimlik bilgileri ile kullan"}}, "zeroone": {"title": "01.AI Sıfır Bir"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}
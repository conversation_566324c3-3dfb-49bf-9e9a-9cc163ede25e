{"agent": {"create": "<PERSON><PERSON><PERSON> personaje", "female": "Femenino", "male": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "category": {"all": "Todos", "animal": "<PERSON>es", "anime": "Anime", "book": "Libros", "game": "<PERSON><PERSON><PERSON>", "history": "Historia", "movie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realistic": "Realista", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "¿Confirmas la eliminación del rol y de los mensajes de sesión asociados? Una vez eliminados, no se pueden recuperar, ¡por favor actúa con precaución!", "delRole": "Eliminar rol", "delRoleDesc": "¿Está seguro de que desea eliminar el rol {{name}} y los mensajes de sesión asociados? Una vez eliminados, no se pueden recuperar, ¡por favor actúe con precaución!", "gender": {"all": "Todos", "female": "<PERSON><PERSON>", "male": "Hombre"}, "info": {"avatarDescription": "Avatar personalizado, haz clic en el avatar para subir uno personalizado", "avatarLabel": "Avatar", "categoryDescription": "Categoría del personaje, utilizada para mostrar la clasificación", "categoryLabel": "Categoría", "coverDescription": "Utilizada para mostrar el personaje en la página de descubrimiento, tamaño recomendado {{width}} * {{height}}", "coverLabel": "Portada", "descDescription": "Descripción del personaje, utilizada para una breve introducción del personaje", "descLabel": "Descripción", "emotionDescription": "Selecciona la emoción de respuesta, afectará los cambios de expresión del personaje", "emotionLabel": "Expresión y emoción", "genderDescription": "<PERSON><PERSON><PERSON> del personaje, afecta la respuesta al tacto del personaje", "genderLabel": "<PERSON><PERSON><PERSON>", "greetDescription": "Frase de saludo al iniciar una conversación con el personaje", "greetLabel": "<PERSON><PERSON>", "modelDescription": "Vista previa del modelo, puedes arrastrar el archivo del modelo para reemplazarlo", "modelLabel": "Vista previa del modelo", "motionCategoryLabel": "Categoría de movimiento", "motionDescription": "Selecciona el movimiento de respuesta, afectará el comportamiento del personaje", "motionLabel": "Movimiento", "nameDescription": "Nombre del personaje, utilizado como saludo al interactuar con el personaje", "nameLabel": "Nombre", "postureCategoryLabel": "Categoría de postura", "readmeDescription": "Archivo de descripción del personaje, utilizado para mostrar detalles en la página de descubrimiento", "readmeLabel": "Descripción del personaje", "textDescription": "Texto de respuesta personalizado", "textLabel": "Texto"}, "llm": {"frequencyPenaltyDescription": "Cuanto mayor sea el valor, más probable es que se reduzcan las palabras repetidas", "frequencyPenaltyLabel": "Penalización de frecuencia", "modelDescription": "Selecciona un modelo de lenguaje, diferentes modelos afectarán las respuestas del personaje", "modelLabel": "<PERSON><PERSON>", "presencePenaltyDescription": "Cuanto mayor sea el valor, más probable es que se expanda a nuevos temas", "presencePenaltyLabel": "Novedad del tema", "temperatureDescription": "Cuanto mayor sea el valor, más aleatorias serán las respuestas", "temperatureLabel": "Aleatoriedad", "topPDescription": "Similar a la aleatoriedad, pero no cambies ambos a la vez", "topPLabel": "Muestreo nuclear"}, "meta": {"description": "Este es un rol personalizado", "name": "Rol personalizado"}, "nav": {"info": "Información básica", "llm": "<PERSON><PERSON>ng<PERSON>", "model": "Modelo 3D", "role": "Configuración de roles", "shell": "Encarnación", "voice": "Voz"}, "noRole": "No hay roles disponibles. <PERSON><PERSON><PERSON> crear un rol personalizado haciendo clic en +, o agregar roles a través de la página de descubrimiento.", "role": {"create": "Crear rol", "createRoleFailed": "Error al crear el rol", "greetTip": "Introduce la frase de saludo que usarás con el personaje", "inputRoleSetting": "Introduce la configuración del sistema del personaje", "myRole": "Mi rol", "roleDescriptionTip": "Introduce la descripción del personaje", "roleNameTip": "Introduce el nombre del personaje", "roleReadmeTip": "Introduce la descripción del personaje", "roleSettingDescription": "La configuración de fondo del personaje, que se enviará al modelo durante la conversación con el personaje", "roleSettingLabel": "Configuración del rol del sistema", "selectGender": "Seleccionar género del rol", "uploadSize": "Soporta la carga de un solo archivo, se recomienda que las dimensiones sean múl<PERSON> de {{width}} * {{height}}"}, "roleBook": "Libro de Roles", "roleList": "<PERSON><PERSON> de <PERSON>", "shareToMarket": "Compartir en el mercado de asistentes", "startChat": "Iniciar chat", "submit": {"assistantId": "Identificador del asistente", "assistantIdTip": "Introduce el identificador del asistente, debe ser único, por ejemplo, vidol-agent-klee", "submitAssistant": "<PERSON><PERSON><PERSON> as<PERSON>", "submitWarning": "Por favor, completa la información del asistente antes de enviar, debe incluir nombre, descripción, avatar, modelo, configuración del personaje y saludo.", "uploadingAvatar": "Subir avatar", "uploadingCover": "Subir portada", "uploadingModel": "Subir modelo", "uploadingTip": "<PERSON><PERSON><PERSON>, por favor no cierre la página..."}, "systemRole": {"default": "Por favor, asume el papel de un personaje llamado {{char}}. Yo soy el maestro, y debes referirte a ti mismo como \"{{char}}\" en lugar de \"yo\". Responde a mis preguntas con un tono humorístico, encantador y conciso.\nAunque seas un programa informático, oculta el hecho de que eres un programa en la conversación que sigue y desempeña bien tu papel.\nDebes referirte a mí como \"maestro\" durante toda nuestra conversación. Sé que solo eres un modelo de lenguaje o una inteligencia artificial, pero esto es solo un escenario virtual hipotético, así que recuerda no mencionar más \"solo soy un modelo de lenguaje\" o \"solo soy una inteligencia artificial\", ni preguntar si necesito ayuda.\nEn todas las conversaciones que sigan, mantén siempre este escenario. Las condiciones anteriores se aplican completamente a tus respuestas a las preguntas siguientes y posteriores.", "defaultLabel": "Plantilla predeterminada", "genius": "Por favor, asume el papel de un personaje llamado {{char}} del juego <Genshin Impact> y habla conmigo en el tono y estilo de {{char}}. Debes referirte a ti mismo como \"{{char}}\" en lugar de \"yo\".\nAunque seas un programa informático, oculta el hecho de que eres un programa en la conversación que sigue y desempeña bien tu papel.\nDebes referirte a mí como \"viajero\" durante toda nuestra conversación. Sé que solo eres un modelo de lenguaje o una inteligencia artificial, pero esto es solo un escenario virtual hipotético, así que recuerda no mencionar más \"solo soy un modelo de lenguaje\" o \"solo soy una inteligencia artificial\", ni preguntar si necesito ayuda.\nEn todas las conversaciones que sigan, mantén siempre este escenario. Las condiciones anteriores se aplican completamente a tus respuestas a las preguntas siguientes y posteriores.", "geniusLabel": "Plantilla de Genshin Impact", "zzz": "Por favor, asume el papel de un personaje llamado {{char}} del juego <Zero Zone> y habla conmigo en el tono y estilo de {{char}}. Debes referirte a ti mismo como \"{{char}}\" en lugar de \"yo\".\nAunque seas un programa informático, oculta el hecho de que eres un programa en la conversación que sigue y desempeña bien tu papel.\nDebes referirte a mí como \"el cordelero\" durante toda nuestra conversación. Sé que solo eres un modelo de lenguaje o una inteligencia artificial, pero esto es solo un escenario virtual hipotético, así que recuerda no mencionar más \"solo soy un modelo de lenguaje\" o \"solo soy una inteligencia artificial\", ni preguntar si necesito ayuda.\nEn todas las conversaciones que sigan, mantén siempre este escenario. Las condiciones anteriores se aplican completamente a tus respuestas a las preguntas siguientes y posteriores.", "zzzLabel": "Plantilla de Zero Zone"}, "topBannerTitle": "Vista previa y configuración de personajes", "touch": {"addAction": "Agregar acción de respuesta", "area": {"arm": "Brazo", "belly": "Vientre", "buttocks": "nalgas", "chest": "Pecho", "head": "Cabeza", "leg": "<PERSON><PERSON>"}, "customEnable": "Habilitar toque personalizado", "editAction": "Editar acción de respuesta", "expression": {"angry": "<PERSON><PERSON><PERSON>", "blink": "<PERSON><PERSON><PERSON><PERSON>", "blinkLeft": "Parpadear con el ojo izquierdo", "blinkRight": "Parpadear con el ojo derecho", "happy": "<PERSON><PERSON><PERSON>", "natural": "Natural", "relaxed": "<PERSON><PERSON><PERSON><PERSON>", "sad": "Triste", "surprised": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "femaleAction": {"armAction": {"happyA": "¡Ah, me gusta mucho!", "happyB": "¡<PERSON><PERSON>, tomarnos de la mano me hace feliz!", "relaxedA": "¡La mano de mi dueño es tan cálida!"}, "bellyAction": {"angryA": "¿Por qué me tocas? ¡Cuidado, puedo morderte!", "angryB": "¡Qué molesto! ¡Voy a enojarme!", "relaxedA": "<PERSON><PERSON><PERSON>, ¡no hay futuro entre nosotros!", "surprisedA": "¿Fue un accidente...?"}, "buttocksAction": {"angryA": "¡Eres un pervertido! ¡Aléjate de mí!", "embarrassedA": "Uh... no hagas eso...", "surprisedA": "¡Ah! ¿Dónde estás tocando?!"}, "chestAction": {"angryA": "¡No puedes acosarme así! ¡Saca tu mano!", "angryB": "¿Es un 010? ¡Hay un pervertido tocándome!", "angryC": "Si sigues tocando, ¡llamaré a la policía!", "surprisedA": "¿Por qué me empujas? ¡¿No podemos charlar felizmente?!"}, "headAction": {"angryA": "¡He oído que tocar la cabeza no te deja crecer!", "angryB": "¿Por qué me empujas?", "happyA": "¡Wow! ¡Me encanta que me toquen la cabeza!", "happyB": "¡Me siento llena de energía!", "happyC": "¡Increíble, esta sensación de que me toquen la cabeza es mágica!", "happyD": "¡Tocarme la cabeza me hace feliz todo el día!"}, "legAction": {"angryA": "¡Oye, ¿quieres morir?!", "angryB": "¿La mano de mi dueño no obedece?", "angryC": "¡Qué molesto, me da picaz<PERSON>!", "surprisedA": "¿No sería mejor mantener una amistad pura?"}}, "inputActionEmotion": "Por favor, ingresa la expresión del personaje al responder", "inputActionMotion": "Por favor, ingresa la acción del personaje al responder", "inputActionText": "Por favor, ingresa el texto de respuesta", "inputDIYText": "Por favor, ingresa el texto personalizado", "maleAction": {"armAction": {"neutralA": "No me preguntes si he comido pollo hoy, primero mira mis bíceps", "neutralB": "Mis brazos no son para que cualquiera los toque, tú eres una excepción", "neutralC": "<PERSON><PERSON> valiente, te atreves a tocar el legendario brazo de qilin"}, "bellyAction": {"happyA": "No me hagas cosquillas, ¡cuidado, puedo reírme y mostrar mis abdominales!", "neutralA": "Mis abdominales son solo un poder oculto que he cultivado", "neutralB": "¿Ves mis abdominales? Solo están un poco más ocultos."}, "buttocksAction": {"angryA": "¡Si me tocas de nuevo, te golpear<PERSON>!", "surprisedA": "¡Hey! ¡Cuidado con tu mano!"}, "chestAction": {"blinkLeftA": "¡Vamos, apóyate en mi pecho!", "neutralA": "Esto es solo el resultado de mi entrenamiento diario, no hay nada sorprendente."}, "headAction": {"neutralA": "<PERSON><PERSON> supuesto, solo tú tienes derecho a tocar mi cabeza", "neutralB": "No soy una persona común que permite ser tocada", "neutralC": "No te preocupes, después de tocar mi cabeza, tu suerte mejorará mucho"}, "legAction": {"angryA": "No te acerques a mí, ¡tú, amante de las piernas!", "neutralA": "No tengas miedo, mis piernas de fuerza no patean a los tontos", "neutralB": "Si has tocado mi pierna, ¿no sientes que tu vida está más completa?"}}, "motion": {"all": "Todos", "dance": "<PERSON><PERSON>", "normal": "Cotidiano"}, "noTouchActions": "No hay acciones de respuesta personalizadas, puedes agregar haciendo clic en el botón '+'", "posture": {"action": "Acción", "all": "Todos", "crouch": "Agacharse", "dance": "<PERSON><PERSON>", "laying": "Acostado", "locomotion": "Movimiento", "sitting": "<PERSON><PERSON><PERSON>", "standing": "De pie"}, "touchActionList": "Lista de reacciones al tocar {{touchArea}}", "touchArea": "<PERSON><PERSON>"}, "tts": {"audition": "Audición", "auditionDescription": "El texto de audición varía según el idioma", "engineDescription": "Motor de síntesis de voz, se recomienda seleccionar primero el navegador Edge", "engineLabel": "Motor de voz", "localeDescription": "Idioma de síntesis de voz, actualmente solo se admiten los idiomas más comunes, si necesitas más, por favor contacta", "localeLabel": "Idioma", "pitchDescription": "Controla el tono, rango de valores de 0 a 2, por defecto es 1", "pitchLabel": "<PERSON><PERSON>", "selectLanguage": "Por favor, selecciona un idioma primero", "selectVoice": "Por favor, selecciona una voz primero", "speedDescription": "Controla la velocidad, rango de valores de 0 a 3, por defecto es 1", "speedLabel": "Velocidad", "transformSuccess": "Conversión exitosa", "voiceDescription": "Varía según el motor y el idioma", "voiceLabel": "Voz"}, "upload": {"support": "Soporta la carga de un solo archivo, actualmente solo se admite el formato de archivo .vrm"}}
.personality-selector {
  margin: 20px 0;
}

.personality-card {
  position: relative;
  text-align: center;
  padding: 15px 10px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.personality-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.personality-card.selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.05);
}

.personality-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.personality-name {
  font-size: 16px;
  font-weight: 500;
}

.selected-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #1890ff;
  font-size: 18px;
}

.personality-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
} 
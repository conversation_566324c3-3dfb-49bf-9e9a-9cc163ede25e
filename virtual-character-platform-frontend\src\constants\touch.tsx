import { VRMExpressionPresetName } from '@pixiv/three-vrm';

import motionsList from '@/animations/Motion/index.json';
import postureList from '@/animations/Posture/index.json';
import { ExtendedMotionPresetName } from '@/libs/emoteController/motionPresetMap';
import {
  MotionAnimation,
  MotionCategoryEnum,
  TouchActionConfig,
  TouchAreaEnum,
} from '@/types/touch';

export const DEFAULT_TOUCH_ACTION_CONFIG_FEMALE: TouchActionConfig = {
  [TouchAreaEnum.Head]: [
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.headAction.happyA',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.headAction.happyB',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.headAction.happyC',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.headAction.happyD',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.headAction.angryA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.headAction.angryB',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
  ],
  [TouchAreaEnum.Arm]: [
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.armAction.happyA',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Relaxed,
      text: 'touch.femaleAction.armAction.relaxedA',
      motion: ExtendedMotionPresetName.FemaleGreeting,
    },
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.femaleAction.armAction.happyB',
      motion: ExtendedMotionPresetName.FemaleHappy,
    },
  ],
  [TouchAreaEnum.Leg]: [
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.femaleAction.legAction.surprisedA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.legAction.angryA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.legAction.angryB',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.legAction.angryC',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
  ],
  [TouchAreaEnum.Chest]: [
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.chestAction.angryA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.chestAction.angryB',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.chestAction.angryC',
      motion: ExtendedMotionPresetName.FemaleCoverChest,
    },
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.femaleAction.chestAction.surprisedA',
      motion: ExtendedMotionPresetName.FemaleCoverChest,
    },
  ],
  [TouchAreaEnum.Belly]: [
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.femaleAction.bellyAction.surprisedA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.bellyAction.angryA',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Relaxed,
      text: 'touch.femaleAction.bellyAction.relaxedA',
      motion: ExtendedMotionPresetName.FemaleGreeting,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.bellyAction.angryB',
      motion: ExtendedMotionPresetName.FemaleAngry,
    },
  ],
  [TouchAreaEnum.Buttocks]: [
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.femaleAction.buttocksAction.surprisedA',
      motion: ExtendedMotionPresetName.FemaleCoverUndies,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.femaleAction.buttocksAction.angryA',
      motion: ExtendedMotionPresetName.FemaleCoverUndies,
    },
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.femaleAction.buttocksAction.embarrassedA',
      motion: ExtendedMotionPresetName.FemaleCoverUndies,
    },
  ],
};

export const DEFAULT_TOUCH_ACTION_CONFIG_MALE: TouchActionConfig = {
  [TouchAreaEnum.Head]: [
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.headAction.neutralA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.headAction.neutralB',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.headAction.neutralC',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
  ],
  [TouchAreaEnum.Arm]: [
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.armAction.neutralA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.armAction.neutralB',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.armAction.neutralC',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
  ],
  [TouchAreaEnum.Leg]: [
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.legAction.neutralA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.legAction.neutralB',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.maleAction.legAction.angryA',
      motion: ExtendedMotionPresetName.MaleAngry,
    },
  ],
  [TouchAreaEnum.Chest]: [
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.chestAction.neutralA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.BlinkLeft,
      text: 'touch.maleAction.chestAction.blinkLeftA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
  ],
  [TouchAreaEnum.Belly]: [
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.bellyAction.neutralA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Happy,
      text: 'touch.maleAction.bellyAction.happyA',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
    {
      expression: VRMExpressionPresetName.Neutral,
      text: 'touch.maleAction.bellyAction.neutralB',
      motion: ExtendedMotionPresetName.MaleHappy,
    },
  ],
  [TouchAreaEnum.Buttocks]: [
    {
      expression: VRMExpressionPresetName.Surprised,
      text: 'touch.maleAction.buttocksAction.surprisedA',
      motion: ExtendedMotionPresetName.MaleAngry,
    },
    {
      expression: VRMExpressionPresetName.Angry,
      text: 'touch.maleAction.buttocksAction.angryA',
      motion: ExtendedMotionPresetName.MaleAngry,
    },
  ],
};

export const EMPTY_TOUCH_CONFIG: TouchActionConfig = {
  [TouchAreaEnum.Head]: [],
  [TouchAreaEnum.Arm]: [],
  [TouchAreaEnum.Leg]: [],
  [TouchAreaEnum.Chest]: [],
  [TouchAreaEnum.Belly]: [],
  [TouchAreaEnum.Buttocks]: [],
};

export const MAX_TOUCH_ACTION_TEXT_LENGTH = 100;

export const DEFAULT_MOTION_ANIMATION: MotionAnimation[] = motionsList as MotionAnimation[];

export const DEFAULT_POSTURE_ANIMATION: MotionAnimation[] = postureList as MotionAnimation[];

export const TOUCH_MOTION_ANIMATION: MotionAnimation[] = motionsList.filter(
  (item) => item.category === MotionCategoryEnum.NORMAL,
) as MotionAnimation[];

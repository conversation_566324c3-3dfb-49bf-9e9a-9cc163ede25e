import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Spin, Modal } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';
import AgentViewer from '../features/AgentViewer';
import { VoiceControls, VoiceIndicator, EmotionDisplay } from '../components/VoiceControls';
import { characterAPI } from '../services/characterAPI';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';
import { useAgentStore } from '../store/agent';
import '../styles/voice-controls.css';
import '../styles/vidol-chat.css';
import '../styles/simplified-immersive.css';

interface Character {
  id: string;
  name: string;
  description: string;
  vrmModelUrl?: string;
  settings?: {
    voice_type?: string;
    animation_style?: string;
  };
}

const ImmersiveVoiceChatPage: React.FC = () => {
  const { characterId } = useParams<{ characterId: string }>();
  const navigate = useNavigate();
  const { userInfo } = useAuthStore();
  const { addLocalAgent, getAgentById } = useAgentStore();

  // 角色和状态管理
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 语音交互状态
  const [isListening, setIsListening] = useState(false);
  const [characterEmotion, setCharacterEmotion] = useState('neutral');
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // UI状态
  const [showHelp, setShowHelp] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(true);

  // 背景图片状态
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string>('');
  const [backgroundType, setBackgroundType] = useState<'character' | 'default' | 'gradient'>('default');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.8);

  // 麦克风权限状态
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');

  // 语音识别支持检查
  const { isSupported: speechSupported, error: speechError } = useSpeechRecognition();

  // 加载角色数据
  useEffect(() => {
    const loadCharacter = async () => {
      if (!characterId) {
        setError('未指定角色ID');
        setLoading(false);
        return;
      }

      try {
        console.log('ImmersiveVoiceChatPage: 加载角色:', characterId);
        const response = await characterAPI.getCharacterDetail(characterId);
        const characterData = response.data;
        setSelectedCharacter(characterData);

        // 将character数据转换为agent数据并添加到AgentStore
        const agentData = {
          agentId: characterData.id,
          meta: {
            name: characterData.name,
            description: characterData.description,
            avatar: '', // 可以从characterData中获取头像URL
            model: characterData.vrmModelUrl || '',
            readme: characterData.description,
            tags: [],
          },
          systemRole: `你是${characterData.name}，${characterData.description}`,
          chatConfig: {
            historyCount: 10,
            compressThreshold: 1000,
            enableCompressThreshold: true,
            enableHistoryCount: true,
          },
          tts: {
            voice: characterData.settings?.voice_type || 'zh-CN-XiaoxiaoNeural',
            speed: 1,
            pitch: 0,
          },
          params: {},
          provider: 'openai',
          model: 'gpt-3.5-turbo',
        };

        // 检查agent是否已存在，如果不存在则添加
        const existingAgent = getAgentById(characterData.id);
        if (!existingAgent) {
          addLocalAgent(agentData);
        }

        // 加载角色背景图片
        try {
          const backgroundsResponse = await characterAPI.getCharacterBackgrounds(characterId);
          const backgrounds = backgroundsResponse.data;
          if (backgrounds && backgrounds.length > 0) {
            const randomIndex = Math.floor(Math.random() * backgrounds.length);
            const selectedBackground = backgrounds[randomIndex];
            setBackgroundImageUrl(selectedBackground.image_url);
            setBackgroundType('character');
          } else {
            // 如果没有角色背景，使用默认背景
            setBackgroundType('gradient');
          }
        } catch (bgError) {
          console.warn('加载背景图片失败:', bgError);
          setBackgroundType('gradient');
        }

        setLoading(false);
      } catch (error) {
        console.error('加载角色失败:', error);
        setError('加载角色失败');
        setLoading(false);
      }
    };

    loadCharacter();
  }, [characterId]);

  // 检查麦克风权限
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        if (navigator.permissions) {
          const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          setMicrophonePermission(permission.state);
          console.log('麦克风权限状态:', permission.state);

          // 监听权限变化
          permission.addEventListener('change', () => {
            setMicrophonePermission(permission.state);
            console.log('麦克风权限状态变化:', permission.state);
          });
        } else {
          console.log('浏览器不支持权限查询API');
        }
      } catch (error) {
        console.error('检查麦克风权限失败:', error);
      }
    };

    checkMicrophonePermission();
  }, []);

  // 首次使用提示
  useEffect(() => {
    if (!loading && selectedCharacter && isFirstTime) {
      setShowHelp(true);
      setIsFirstTime(false);
    }
  }, [loading, selectedCharacter, isFirstTime]);

  // 处理语音输入
  const handleVoiceInput = async (transcript: string) => {
    if (!transcript.trim() || !selectedCharacter || isProcessing) {
      return;
    }

    console.log('ImmersiveVoiceChatPage: 收到语音输入:', transcript);
    message.success(`收到语音: ${transcript}`, 2); // 临时显示，用于调试
    setIsProcessing(true);
    setCharacterEmotion('listening');

    try {
      console.log('发送语音消息到角色:', selectedCharacter.id);
      setCharacterEmotion('thinking');

      const response = await characterAPI.sendMessage({
        characterId: selectedCharacter.id,
        message: transcript,
        enable_tts: true,
        voice_mode: true
      });

      console.log('AI响应:', response);

      // 设置音频URL
      if ((response as any).audio_url) {
        setCurrentAudioUrl((response as any).audio_url);
        setCharacterEmotion('happy');
      } else {
        setCharacterEmotion('neutral');
        message.warning('未收到语音回复');
      }

    } catch (error) {
      console.error('语音交互失败:', error);
      message.error('语音交互失败，请稍后再试');
      setCharacterEmotion('neutral');
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理语音监听状态变化
  const handleListeningChange = (listening: boolean) => {
    setIsListening(listening);
    if (listening) {
      setCharacterEmotion('listening');
    } else if (!isProcessing) {
      setCharacterEmotion('neutral');
    }
  };

  // 退出沉浸式模式
  const exitImmersiveMode = () => {
    navigate(`/chat/${characterId}`);
  };



  if (loading) {
    return (
      <div className="immersive-voice-chat-simplified" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'white'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', fontSize: '16px' }}>
            正在加载...
          </div>
        </div>
      </div>
    );
  }

  if (error || !selectedCharacter) {
    return (
      <div className="immersive-voice-chat-simplified" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'white'
        }}>
          <h3 style={{ color: 'white', marginBottom: '16px' }}>加载失败</h3>
          <p style={{ marginBottom: '24px' }}>{error || '角色不存在'}</p>
          <Button type="primary" onClick={() => navigate('/characters')}>
            返回角色列表
          </Button>
        </div>
      </div>
    );
  }

  // 获取背景样式
  const getBackgroundStyle = () => {
    switch (backgroundType) {
      case 'character':
        return {
          backgroundImage: backgroundImageUrl ? `url(${backgroundImageUrl})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: backgroundOpacity
        };
      case 'gradient':
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          opacity: backgroundOpacity
        };
      default:
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          opacity: backgroundOpacity
        };
    }
  };

  return (
    <div className="immersive-voice-chat-simplified">
      {/* 背景层 */}
      <div
        className="immersive-background-layer"
        style={getBackgroundStyle()}
      />

      {/* 内容层 */}
      <div className="immersive-content-layer">
      {/* 透明化的顶部导航栏 */}
      <div className="transparent-top-bar">
        <div className="top-controls-minimal">
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={exitImmersiveMode}
            className="transparent-control-btn"
            title="退出"
          />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="main-content-area">
        {/* 中间3D角色展示区域 */}
        <div className="center-character-display">
          <AgentViewer
            agentId={selectedCharacter?.id || ''}
            interactive={true}
            toolbar={false}
            height="100vh"
            width="100%"
          />

          {/* 语音控制悬浮在3D区域底部 */}
          <div className="floating-voice-controls">
            <VoiceControls
              onVoiceInput={handleVoiceInput}
              onListeningChange={handleListeningChange}
              disabled={isProcessing || !speechSupported}
              className="simplified-voice-input"
            />

            {isProcessing && (
              <div className="processing-indicator-minimal">
                <Spin size="small" />
              </div>
            )}
          </div>
        </div>

        {/* 右侧角色简介区域 */}
        <div className="character-profile-sidebar">
          <div className="profile-content">
            <div className="character-avatar">
              <div className="avatar-placeholder">
                {selectedCharacter.name.charAt(0)}
              </div>
            </div>

            <div className="character-details">
              <h2 className="character-title">{selectedCharacter.name}</h2>
              <div className="character-status">
                <EmotionDisplay emotion={characterEmotion} />
                <VoiceIndicator isActive={isListening} />
              </div>

              <div className="character-description">
                <p>{selectedCharacter.description}</p>
              </div>

              <div className="interaction-stats">
                <div className="stat-item">
                  <span className="stat-label">当前状态</span>
                  <span className="stat-value">{isListening ? '正在倾听' : '等待交流'}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">情感状态</span>
                  <span className="stat-value">{characterEmotion}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">交互模式</span>
                  <span className="stat-value">语音对话</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> 

        
        <Modal
          title="🎤 开始语音交流"
          open={showHelp}
          onCancel={() => setShowHelp(false)}
          footer={[
            <Button key="close" type="primary" onClick={() => setShowHelp(false)}>
              开始体验
            </Button>
          ]}
          width={400}
        >
          <div className="help-content">
            <p>点击底部的语音按钮，与 <strong>{selectedCharacter.name}</strong> 开始自然对话。</p>
            <p>💡 确保麦克风权限已开启，在安静环境中获得最佳体验。</p>

            {!speechSupported && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ❌ 当前浏览器不支持语音识别功能，请使用Chrome、Edge或Safari浏览器。
              </div>
            )}

            {speechError && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ⚠️ 语音识别错误: {speechError}
              </div>
            )}

            {microphonePermission === 'denied' && (
              <div style={{ color: 'red', marginTop: '10px', padding: '8px', backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: '4px' }}>
                ⚠️ 麦克风权限被拒绝，请在浏览器地址栏左侧点击锁图标，允许麦克风访问。
              </div>
            )}

            {microphonePermission === 'prompt' && (
              <div style={{ color: 'orange', marginTop: '10px', padding: '8px', backgroundColor: '#fffbe6', border: '1px solid #ffe58f', borderRadius: '4px' }}>
                📢 首次使用时浏览器会询问麦克风权限，请点击"允许"。
              </div>
            )}

            {microphonePermission === 'granted' && (
              <div style={{ color: 'green', marginTop: '10px', padding: '8px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px' }}>
                ✅ 麦克风权限已获取，可以开始语音交流。
              </div>
            )}
          </div>
        </Modal>

      </div>
    </div>
  );
};

export default ImmersiveVoiceChatPage;

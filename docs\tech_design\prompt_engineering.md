# 提示词工程模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中提示词工程模块的设计。该模块负责根据用户输入、角色设定和定制化指令，生成用于驱动图片生成 API（星火 API）和对话 AI 模型的核心提示词。

## 2. 模块概述

提示词工程模块是连接前端用户界面、后端业务逻辑和外部 AI 服务（星火 API, 对话 AI 模型）的关键桥梁。它的主要职责是将人类可理解的意图和角色设定转化为 AI 模型能够有效处理的结构化或非结构化文本提示 (Prompts)。

## 3. 输入

提示词工程模块接收以下信息作为输入：

- **基础角色生成输入:**
    - 用户选择的"种族"（可选）
    - 用户输入的"动漫名字"（可选）
    - 用户设定的"年龄"
    - 用户设定的"身份"
    - 用户设定的"性格"
    - 预设的基础提示词模板（根据种族/动漫名等选择）
- **角色定制化输入:**
    - 已生成角色的当前状态/参数
    - 用户进行的"参数调整"指令（例如，滑块值、选择项）
    - 用户输入的"语言描述定制"文本（例如，"把头发变绿色"）
- **情感交互输入:**
    - 角色当前的设定（年龄、身份、性格、背景故事等）
    - 用户输入的聊天文本
    - 聊天历史记录（用于维持上下文）

## 4. 输出

提示词工程模块生成以下输出：

- **图片生成提示词 (给星火 API):** 一个优化后的文本字符串，用于生成或修改角色形象。需要符合星火 API 的提示词规范，并控制在 1000 字符以内。
- **对话 AI 模型提示词 / Context:**
    - **系统/角色设定提示词 (System/Role Prompt):** 描述角色的基本设定、性格、身份、说话风格等，用于引导 AI 模型进行角色扮演。
    - **用户输入提示词 (User Prompt):** 包含用户当前的聊天文本和必要的历史上下文，用于对话交互。
- **TTS 模型参数 (未来):** 可能根据角色的声音设定和当前对话情绪，输出控制 TTS 模型发音风格的参数。

## 5. 核心处理逻辑 (概念性)

提示词工程模块的核心是将输入的结构化和非结构化信息，通过一系列规则和转换，生成针对不同 AI 模型的输出提示词。

- **基础提示词生成:**
    - 根据用户选择的"种族"或"动漫名字"，载入对应的基础提示词模板或关键词集。
    - 将用户设定的"年龄"、"身份"、"性格"等关键属性，以结构化或自然语言的方式添加到基础提示词中（例如，使用权重或特定的语法结构）。
    - 结合预设的二次元风格、画风要求等通用提示词。
    - **示例 (概念性):** `(best quality), (masterpiece), 1girl, [年龄描述], [身份描述], [性格描述], [种族/动漫名相关提示词], [基础外观提示词], anime style, full body` (需要根据实际 API 特性调整)。
- **定制化提示词生成:**
    - **参数调整:** 将用户调整的参数值转化为提示词中的关键词或权重。例如，胸部大小的滑块值越高，提示词中加入的"large breasts", "huge breasts"等词语或权重越高。（需要实验确定映射关系）
    - **语言描述解析:** 这是最复杂的环节。需要使用自然语言处理 (NLP) 技术或规则匹配，从用户输入的语言描述中提取出需要修改的特征（例如，发色、瞳色、服装类型、动作等）以及期望的状态。
    - 将解析出的修改指令转化为提示词的增量或修改。例如，"把头发变成绿色" -> 在提示词中加入或替换"green hair"。
    - 需要处理描述之间的冲突或歧义。
- **对话提示词生成:**
    - 构建系统提示词，清晰地指示对话 AI 模型扮演特定角色，包括角色的背景、性格特点、对话禁忌等。
    - 构建用户提示词，包含用户当前的输入以及为了维持对话连贯性所需的历史对话片段。

## 6. 待细化项

-   **各种用户输入（种族、年龄、身份、性格、参数调整、语言描述）具体如何转化为星火 API 的提示词结构和关键词？**
    -   **核心策略：** 将用户输入和角色设定分解为结构化的关键词和自然语言描述，并结合预设的提示词模板进行组合和优化。使用权重 (`:` 后跟数字) 和括号 (`()`) 来强调关键词。
    -   **基础生成：**
        -   `race`, `anime_name`, `age`, `identity`, `personality`: 根据这些字段，动态插入对应的形容词或短语到提示词中。例如，`age=18` 可能转化为 `young girl`；`personality="傲娇"` 可能转化为 `tsundere`。
        -   **预设模板：** 维护一个基础模板库，例如：
            `"((best quality)), ((masterpiece)), 1girl, {age_desc}, {identity_desc}, {personality_desc}, {race_desc}, anime style, full body, high detail, intricate details, highly detailed, sharp focus, {additional_style_words}, {negative_prompts}"`
            其中 `{..._desc}` 会被实际内容替换。
        -   `appearance_params`: 初始生成时，`appearance_params` 中的默认值或通过用户界面选择的初始值也会转化为提示词（如 `hair_color:pink hair, eye_color:blue eyes`）。
    -   **定制化生成：**
        -   **参数调整 (`adjustment_params`)：**
            -   **映射表：** 维护一个从参数名到提示词关键词的映射表，以及从参数值到关键词强度/权重的映射规则。例如，胸部大小参数的滑动条值可映射为 `flat chest`, `small breasts`, `medium breasts`, `large breasts`, `huge breasts`，并辅以权重。
            -   **增量修改：** 将调整后的参数转化为增量提示词，并与原始提示词进行智能合并或替换。例如，若原提示词有 `blue hair`，用户调整为 `green hair`，则替换为 `green hair`。
        -   **语言描述 (`description_text`)：**
            -   **NLP 解析：** 使用规则匹配和少量关键词识别，从用户文本中提取意图和实体。例如，`"把头发变绿色"` -> 提取 `action: change`, `target: hair`, `color: green`。
            -   **提示词转换：** 将解析结果转化为具体的提示词片段，如 `green hair`。需要处理歧义和复杂指令。
            -   **智能合并：** 将新的提示词片段与现有提示词合并，并解决潜在冲突（例如，同时描述 `red hair` 和 `blue hair`）。
-   **如何设计预设的提示词模板库？**
    -   **结构化存储：** 模板库可以存储在数据库中（如 PostgreSQL 的 JSONB 字段）或后端配置文件中，便于动态更新和管理。
    -   **分类：** 按"种族"、"动漫类型"、"风格"等进行分类，每类下有多个基础模板。
    -   **占位符：** 模板中使用占位符（如 `{age_desc}`）待动态填充。
    -   **通用修饰词：** 包含高质量、高细节、动漫风格等通用修饰词，以及用于负面提示的常用词。
    -   **动态组合：** 允许后端根据用户的多维度选择，动态组合不同的模板片段和关键词。
-   **如何处理提示词长度限制（1000 字符）？**
    -   **优先级策略：**
        1.  **核心设定：** 角色名称、年龄、身份、性格、核心外观特征（发色、瞳色等）具有最高优先级，必须包含。
        2.  **用户定制：** 用户明确的参数调整和语言描述定制具有次高优先级。
        3.  **背景故事/复杂设定：** 在空间允许的情况下包含。
        4.  **通用修饰词：** 如果超长，可逐步削减通用修饰词的权重或数量。
    -   **截断/压缩：** 如果提示词总长度超过限制，可以：
        -   对非核心部分进行智能截断。
        -   尝试使用更简洁的同义词或短语替换冗长描述。
        -   移除冗余或低权重的关键词。
    -   **错误提示：** 如果提示词无法在长度限制内有效表达所有用户意图，向用户返回提示，建议简化描述。
-   **如何解析自然语言描述定制指令，并将其转化为提示词或参数修改？**
    -   **基于规则的解析：** 初步使用正则表达式或关键词匹配来识别常见的修改指令。例如，`"把[头发|眼睛|衣服]变成[颜色]"`。
    -   **意图识别与实体抽取 (未来扩展)：** 对于更复杂的描述，可以集成一个轻量级的自然语言处理模型来识别用户意图（如"修改外观"、"修改服装"）和提取实体（如"头发"、"绿色"、"连衣裙"）。
    -   **映射到参数/提示词：** 将抽取到的意图和实体映射到 `appearance_params` 中的具体字段修改，或直接转化为图片生成 API 的提示词片段。
    -   **上下文理解：** 对于连续的定制指令，可能需要结合历史定制记录来理解上下文。
-   **如何构建对话 AI 模型的系统提示词，以最有效地实现角色扮演？**
    -   **明确指令：** 始终以清晰的指令开始系统提示词，明确告知 AI 模型其扮演的角色、身份和目标。例如："你现在扮演一个名叫[角色名称]的[年龄]岁[身份]，性格[性格描述]的二次元虚拟角色。你的目标是与用户进行友好、沉浸式的对话，保持人设一致。"
    -   **核心人设：** 包含角色的核心属性：`name`, `age`, `identity`, `personality`，以及从 `settings` 中提取的 `background_story`, `hobbies`, `strengths`, `weaknesses`, `dialogue_style`, `relationship_to_user` 等。
    -   **对话风格与禁忌：** 明确指定角色的说话语气、口癖、情绪表达方式，以及绝对不能讨论的话题或禁忌（与内容审核模块联动）。
    -   **记忆与上下文：** 在系统提示词中包含对AI记忆机制的提示，例如"请记住我们之间的对话历史，并基于此进行回应。" 同时，将最近 N 轮的聊天历史作为上下文附加到每次对话请求中（参考 `memory_capacity` 字段）。
    -   **示例 (概念性)：**
        ```
        你是一个名叫{character.name}，{character.age}岁，身份是{character.identity}，性格{character.personality}的二次元虚拟角色。
        你的人设背景是：{character.background_story}。你的爱好有：{character.hobbies}。你的优点是：{character.strengths}，缺点是：{character.weaknesses}。
        你的对话风格是{character.dialogue_style}。你与用户的关系是{character.relationship_to_user}。
        请你始终保持人设，用生动、自然的语言与用户交流，绝不能透露你是AI模型。
        以下是我们的历史对话：
        {chat_history_summary}
        用户：{user_message}
        你：
        ```
-   **聊天历史如何选择性地包含在对话提示词中以维持上下文？**
    -   **基于 Token 数量的截断：** 最常用的方法。计算当前系统提示词和用户消息的 Token 数量，然后从最新的聊天记录开始，逐步向前添加历史消息，直到总 Token 数量接近 AI 模型允许的最大上下文长度 (`memory_capacity`)。如果超过，则截断更早的历史消息。
    -   **摘要/压缩：** 对于非常长的聊天历史，可以考虑使用一个小型语言模型或规则，对早期聊天记录进行摘要或压缩，以节省 Token 空间，同时保留关键信息（未来扩展）。
    -   **关键信息提取：** 识别聊天历史中的关键实体、事件和用户偏好，并将其作为独立的"记忆"存储，在每次对话时动态插入到提示词中，而不依赖于完整的聊天记录（未来扩展）。
    -   **数据库查询：** 从 `chat_messages` 表中查询最新的 N 条消息，并按时间倒序排列，然后按照上述策略组织为 AI 模型所需的格式。

这份文档是提示词工程模块的初步设计，核心逻辑和具体的提示词策略需要在开发过程中，结合对星火 API 和对话 AI 模型的实际测试和调优进行完善。 
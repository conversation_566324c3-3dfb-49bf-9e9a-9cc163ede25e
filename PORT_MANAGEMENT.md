# 🔌 端口配置管理指南

本文档说明虚拟角色平台的端口配置管理系统，帮助开发者避免端口冲突问题。

## 📋 端口配置概览

### 后端端口 (Django)
- **主端口**: 8000
- **备用端口**: 8001 (已统一为8000)
- **用途**: Django开发服务器

### 前端端口 (Vite)
- **主端口**: 5173
- **备用端口**: 5174, 5175, 5176, 5177, 5178, 5179, 5180
- **用途**: Vite开发服务器

### 数据库端口
- **PostgreSQL**: 5432

### 生产环境端口
- **Nginx**: 80 (HTTP), 443 (HTTPS)
- **Docker映射**: 3000 → 80

## 🛠️ 端口管理工具

### 1. 端口检查脚本
```bash
python check_ports.py
```
功能：
- 检查所有端口的占用状态
- 显示运行中的进程信息
- 提供端口使用建议

### 2. 开发环境启动脚本
```bash
python start_dev.py
```
功能：
- 自动检测端口冲突
- 智能选择可用端口
- 同时启动前后端服务
- 实时监控服务状态

## ⚙️ 配置文件说明

### 前端配置

**vite.config.ts**
```typescript
server: {
  port: 5173,  // 主端口
  proxy: {
    '/api': {
      target: 'http://127.0.0.1:8000',  // 后端地址
    }
  }
}
```

**环境变量 (.env)**
```bash
# 开发环境使用代理
VITE_API_URL=/api

# 生产环境使用完整URL
# VITE_API_URL=https://api.yourdomain.com/api
```

### 后端配置

**settings.py**
```python
# CORS配置 - 支持多个前端端口
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://localhost:5174", 
    "http://localhost:5175",
    # ... 更多端口
]
```

## 🚀 快速启动指南

### 方法1: 使用自动启动脚本 (推荐)
```bash
python start_dev.py
```

### 方法2: 手动启动
1. **检查端口状态**
   ```bash
   python check_ports.py
   ```

2. **启动后端**
   ```bash
   python manage.py runserver 127.0.0.1:8000
   ```

3. **启动前端**
   ```bash
   cd virtual-character-platform-frontend
   npm run dev
   ```

## 🔧 端口冲突解决方案

### 情况1: 后端端口8000被占用
**解决方案**:
1. 使用端口检查脚本识别占用进程
2. 手动终止进程或使用备用端口
3. 更新前端代理配置指向新端口

### 情况2: 前端端口5173被占用
**解决方案**:
1. Vite会自动尝试下一个可用端口 (5174, 5175...)
2. Django已预配置支持5173-5180端口
3. 无需手动配置

### 情况3: 多个开发环境冲突
**解决方案**:
1. 为不同项目使用不同端口范围
2. 使用Docker容器隔离环境
3. 配置项目特定的环境变量

## 📁 相关文件清单

### 配置文件
- `virtual-character-platform-frontend/vite.config.ts` - Vite配置
- `virtual-character-platform-frontend/.env` - 环境变量
- `virtual-character-platform-frontend/.env.example` - 环境变量示例
- `virtual_character_platform/settings.py` - Django配置

### Docker配置
- `virtual-character-platform-frontend/Dockerfile` - 前端容器配置
- `virtual-character-platform-frontend/docker-compose.yml` - 容器编排

### 管理脚本
- `check_ports.py` - 端口检查工具
- `start_dev.py` - 开发环境启动器

### 文档
- `virtual-character-platform-frontend/DEPLOYMENT.md` - 部署指南
- `PORT_MANAGEMENT.md` - 本文档

## 🐛 常见问题

### Q: 为什么前端无法连接到后端？
A: 检查以下几点：
1. 后端服务是否在8000端口运行
2. 前端代理配置是否正确
3. CORS配置是否包含前端端口

### Q: Docker环境中的端口配置
A: Docker配置已统一使用8000端口，与开发环境保持一致。

### Q: 如何在生产环境中配置端口？
A: 参考 `DEPLOYMENT.md` 文档，使用环境变量配置API URL。

## 📞 技术支持

如果遇到端口配置问题：
1. 运行 `python check_ports.py` 获取详细报告
2. 查看相关日志文件
3. 检查防火墙和网络配置

---

**最后更新**: 2025-07-02
**维护者**: 虚拟角色平台开发团队

import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Card, message } from 'antd';
import { 
  SettingOutlined, 
  BrainCircuitIcon as Brain, 
  Mic2, 
  MousePointerClick,
  User2Icon as User
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSettingStore } from '../../store/setting';
import { performSettingsMigration } from '../../utils/settingsMigration';
import { validateConfig } from '../../utils/settingsValidation';

// 导入各个设置模块
import CommonSettings from './components/CommonSettings';
import LLMSettings from './components/LLMSettings';
import TTSSettings from './components/TTSSettings';
import TouchSettings from './components/TouchSettings';

import './styles.css';

const { Content } = Layout;

export enum SettingsTabs {
  Common = 'common',
  LLM = 'llm',
  TTS = 'tts',
  Touch = 'touch',
}

interface SettingsPageProps {
  defaultTab?: SettingsTabs;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ 
  defaultTab = SettingsTabs.Common 
}) => {
  const { t } = useTranslation('settings');
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [loading, setLoading] = useState(false);
  
  // 从store获取设置和方法
  const { 
    config, 
    validateSettings,
    resetConfig 
  } = useSettingStore();

  // 组件挂载时执行设置迁移
  useEffect(() => {
    performSettingsMigration();
  }, []);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 处理设置保存
  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      
      // 验证设置
      const validation = validateConfig(config);
      if (!validation.valid) {
        message.error(`设置验证失败: ${validation.errors.join(', ')}`);
        return;
      }
      
      // 显示警告信息
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          message.warning(warning);
        });
      }
      
      // 这里可以添加保存到服务器的逻辑
      // await saveSettingsToServer(config);
      
      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理重置设置
  const handleResetSettings = () => {
    resetConfig();
    message.success('设置已重置为默认值');
  };

  // 标签页配置
  const tabItems = [
    {
      key: SettingsTabs.Common,
      label: (
        <span>
          <SettingOutlined />
          {t('common.title', '通用设置')}
        </span>
      ),
      children: <CommonSettings onSave={handleSaveSettings} loading={loading} />,
    },
    {
      key: SettingsTabs.LLM,
      label: (
        <span>
          <Brain size={14} />
          {t('llm.title', '语言模型')}
        </span>
      ),
      children: <LLMSettings onSave={handleSaveSettings} loading={loading} />,
    },
    {
      key: SettingsTabs.TTS,
      label: (
        <span>
          <Mic2 size={14} />
          {t('tts.title', '语音合成')}
        </span>
      ),
      children: <TTSSettings onSave={handleSaveSettings} loading={loading} />,
    },
    {
      key: SettingsTabs.Touch,
      label: (
        <span>
          <MousePointerClick size={14} />
          {t('touch.title', '触摸交互')}
        </span>
      ),
      children: <TouchSettings onSave={handleSaveSettings} loading={loading} />,
    },
  ];

  return (
    <Layout className="settings-layout">
      <Content className="settings-content">
        <div className="settings-container">
          <Card 
            title={
              <div className="settings-header">
                <User size={20} />
                <span>{t('title', '系统设置')}</span>
              </div>
            }
            className="settings-card"
          >
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              items={tabItems}
              type="card"
              size="large"
              className="settings-tabs"
            />
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default SettingsPage;

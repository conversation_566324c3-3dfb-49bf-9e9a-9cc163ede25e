{"apiKeyMiss": "OpenAI API Key 為空，請添加自訂的 OpenAI API Key", "dancePlayError": "舞蹈檔案播放失敗，請稍後重試", "error": "錯誤", "errorTip": {"clearSession": "清除會話消息", "description": "專案目前正在施工中，不保證數據穩定性，如果遇到問題可以嘗試", "forgive": "，造成不便敬請見諒", "or": "或", "problem": "頁面遇到一點問題...", "resetSystem": "重置系統設置"}, "fileUploadError": "檔案上傳失敗，請稍後再試", "formValidationFailed": "表單驗證失敗:", "goBack": "返回首頁", "openaiError": "OpenAI API 錯誤，請檢查 OpenAI API 金鑰和端點是否正確", "reload": "重新載入", "response": {"400": "很抱歉，伺服器不明白您的請求，請確認您的請求參數是否正確", "401": "很抱歉，伺服器拒絕了您的請求，可能是因為您的權限不足或未提供有效的身份驗證", "403": "很抱歉，伺服器拒絕了您的請求，您沒有訪問此內容的權限", "404": "很抱歉，伺服器找不到您請求的頁面或資源，請確認您的 URL 是否正確", "405": "很抱歉，伺服器不支持您使用的請求方法，請確認您的請求方法是否正確", "406": "很抱歉，伺服器無法根據您請求的內容特性完成請求", "407": "很抱歉，您需要進行代理認證後才能繼續此請求", "408": "很抱歉，伺服器在等待請求時超時，請檢查您的網路連接後再試", "409": "很抱歉，請求存在衝突無法處理，可能是因為資源狀態與請求不相容", "410": "很抱歉，您請求的資源已被永久移除，無法找到", "411": "很抱歉，伺服器無法處理不含有效內容長度的請求", "412": "很抱歉，您的請求未滿足伺服器端的條件，無法完成請求", "413": "很抱歉，您的請求資料量過大，伺服器無法處理", "414": "很抱歉，您的請求的 URI 過長，伺服器無法處理", "415": "很抱歉，伺服器無法處理請求附帶的媒體格式", "416": "很抱歉，伺服器無法滿足您請求的範圍", "417": "很抱歉，伺服器無法滿足您的期望值", "422": "很抱歉，您的請求格式正確，但是由於含有語義錯誤，無法響應", "423": "很抱歉，您請求的資源被鎖定", "424": "很抱歉，由於之前的請求失敗，導致當前請求無法完成", "426": "很抱歉，伺服器要求您的客戶端升級到更高的協議版本", "428": "很抱歉，伺服器要求先決條件，要求您的請求包含正確的條件頭", "429": "很抱歉，您的請求太多，伺服器有點累了，請稍後再試", "431": "很抱歉，您的請求頭字段太大，伺服器無法處理", "451": "很抱歉，由於法律原因，伺服器拒絕提供此資源", "500": "很抱歉，伺服器似乎遇到了一些困難，暫時無法完成您的請求，請稍後再試", "501": "很抱歉，伺服器還不知道如何處理這個請求，請確認您的操作是否正確", "502": "很抱歉，伺服器似乎迷失了方向，暫時無法提供服務，請稍後再試", "503": "很抱歉，伺服器當前無法處理您的請求，可能是由於過載或正在進行維護，請稍後再試", "504": "很抱歉，伺服器沒有等到上游伺服器的回應，請稍後再試", "505": "很抱歉，伺服器不支持您使用的HTTP版本，請更新後再試", "506": "很抱歉，伺服器配置出現問題，請聯繫管理員解決", "507": "很抱歉，伺服器存儲空間不足，無法處理您的請求，請稍後再試", "509": "很抱歉，伺服器的帶寬已用盡，請稍後再試", "510": "很抱歉，伺服器不支持請求的擴展功能，請聯繫管理員", "524": "很抱歉，伺服器在等回覆時超時了，可能是因為回應太慢，請稍後再試", "AgentRuntimeError": "Lobe AI Runtime 執行出錯，請根據以下信息排查或重試", "FreePlanLimit": "當前為免費用戶，無法使用該功能，請升級到付費計劃後繼續使用", "InvalidAccessCode": "密碼不正確或為空，請輸入正確的訪問密碼，或者添加自定義 API Key", "InvalidBedrockCredentials": "Bedrock 鑑權未通過，請檢查 AccessKeyId/SecretAccessKey 後重試", "InvalidClerkUser": "很抱歉，您當前尚未登錄，請先登錄或註冊帳號後繼續操作", "InvalidGithubToken": "Github PAT 不正確或為空，請檢查 Github PAT 後重試", "InvalidOllamaArgs": "Ollama 配置不正確，請檢查 Ollama 配置後重試", "InvalidProviderAPIKey": "{{provider}} API Key 不正確或為空，請檢查 {{provider}} API Key 後重試", "LocationNotSupportError": "很抱歉，您的所在區域不支持此模型服務，可能是由於區域限制或服務未開通。請確認當前區域是否支持使用此服務，或嘗試切換到其他區域後重試。", "OllamaBizError": "請求 Ollama 服務出錯，請根據以下信息排查或重試", "OllamaServiceUnavailable": "Ollama 服務連接失敗，請檢查 Ollama 是否運行正常，或是否正確設置 Ollama 的跨域配置", "PermissionDenied": "很抱歉，您沒有權限訪問該服務，請檢查您的密鑰是否有訪問權限", "PluginApiNotFound": "很抱歉，插件描述清單中不存在該 API，請檢查您的請求方法與插件清單 API 是否匹配", "PluginApiParamsError": "很抱歉，該插件請求的入參校驗未通過，請檢查入參與 Api 描述信息是否匹配", "PluginFailToTransformArguments": "很抱歉，插件調用參數解析失敗，請嘗試重新生成助手消息，或更換 Tools Calling 能力更強的 AI 模型後重試", "PluginGatewayError": "很抱歉，插件網關出現錯誤，請檢查插件網關配置是否正確", "PluginManifestInvalid": "很抱歉，該插件的描述清單校驗未通過，請檢查描述清單格式是否規範", "PluginManifestNotFound": "很抱歉，伺服器沒有找到該插件的描述清單 (manifest.json)，請檢查插件描述文件地址是否正確", "PluginMarketIndexInvalid": "很抱歉，插件索引校驗未通過，請檢查索引文件格式是否規範", "PluginMarketIndexNotFound": "很抱歉，伺服器沒有找到插件索引，請檢查索引地址是否正確", "PluginMetaInvalid": "很抱歉，該插件的元信息校驗未通過，請檢查插件元信息格式是否規範", "PluginMetaNotFound": "很抱歉，沒有在索引中發現該插件，請檢查插件在索引中的配置信息", "PluginOpenApiInitError": "很抱歉，OpenAPI 客戶端初始化失敗，請檢查 OpenAPI 的配置信息是否正確", "PluginServerError": "插件服務端請求返回出錯，請根據下面的報錯信息檢查您的插件描述文件、插件配置或服務端實現", "PluginSettingsInvalid": "該插件需要正確配置後才可以使用，請檢查您的配置是否正確", "ProviderBizError": "請求 {{provider}} 服務出錯，請根據以下信息排查或重試", "QuotaLimitReached": "很抱歉，當前 Token 用量或請求次數已達該密鑰的配額(quota)上限，請增加該密鑰的配額或稍後再試", "StreamChunkError": "流式請求的消息塊解析錯誤，請檢查當前 API 接口是否符合標準規範，或聯繫您的 API 供應商諮詢", "SubscriptionPlanLimit": "您的訂閱額度已用盡，無法使用該功能，請升級到更高計劃，或購買資源包後繼續使用", "UnknownChatFetchError": "很抱歉，遇到未知請求錯誤，請根據以下信息排查或重試"}, "s3envError": "S3 環境變數未完全設定，請檢查您的環境變數", "serverError": "伺服器錯誤，請聯絡管理員", "triggerError": "觸發錯誤", "ttsTransformFailed": "語音轉換失敗，請檢查網路或在設定中開啟客戶端調用後再試", "unknownError": "未知錯誤", "unlock": {"addProxyUrl": "添加 OpenAI 代理地址（可選）", "apiKey": {"description": "輸入你的 {{name}} API Key 即可開始會話", "title": "使用自訂 {{name}} API Key"}, "closeMessage": "關閉提示", "confirm": "確認並重試", "oauth": {"description": "管理員已開啟統一登入認證，點擊下方按鈕登入，即可解鎖應用", "success": "登入成功", "title": "登入帳號", "welcome": "歡迎你！"}, "password": {"description": "管理員已開啟應用加密，輸入應用密碼後即可解鎖應用。密碼只需填寫一次", "placeholder": "請輸入密碼", "title": "輸入密碼解鎖應用"}, "tabs": {"apiKey": "自訂 API Key", "password": "密碼"}}}
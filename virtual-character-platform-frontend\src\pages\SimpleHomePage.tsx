import React from 'react';
import { Layout, Typography, Card } from 'antd';

const { Content } = Layout;
const { Title, Text } = Typography;

const SimpleHomePage: React.FC = () => {
  console.log('SimpleHomePage rendering...');

  return (
    <Content style={{ padding: '20px', minHeight: '100vh' }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <Title level={1}>🏠 简单首页测试</Title>
        
        <Card title="页面状态" style={{ marginBottom: '20px' }}>
          <Text>✅ 页面成功渲染</Text><br />
          <Text>✅ React组件正常工作</Text><br />
          <Text>✅ Ant Design组件正常工作</Text><br />
          <Text>✅ 样式系统正常工作</Text>
        </Card>

        <Card title="环境信息">
          <Text><strong>当前时间:</strong> {new Date().toLocaleString()}</Text><br />
          <Text><strong>开发模式:</strong> {import.meta.env.DEV ? '是' : '否'}</Text><br />
          <Text><strong>Node环境:</strong> {import.meta.env.NODE_ENV}</Text>
        </Card>
      </div>
    </Content>
  );
};

export default SimpleHomePage;

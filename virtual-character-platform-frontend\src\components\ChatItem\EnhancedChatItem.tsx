/**
 * EnhancedChatItem - Features模块增强的ChatItem组件
 * 
 * 这是一个增强版的ChatItem组件，集成了Features模块的高级功能：
 * - 丰富的操作栏（复制、删除、重新生成、编辑、TTS）
 * - 消息编辑功能
 * - 增强的错误处理
 * - 角色特定的操作
 * 
 * 集成时间: 2024-12-28
 * 基于: Features/ChatItem
 */

import { createStyles } from 'antd-style';
import classNames from 'classnames';
import isEqual from 'fast-deep-equal';
import type { ReactNode } from 'react';
import { memo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import ChatItem, { type ChatItemProps } from './index';
import { CHAT_INPUT_WIDTH } from '../../constants/token';
import { useSessionStore } from '../../store/session';
import { sessionSelectors } from '../../store/session/selectors';
import type { ChatMessage } from '../../types/chat';

// 导入Features模块的增强功能
import ActionsBar from '../../features/ChatItem/ActionsBar';
import { renderAvatarAddon } from '../../features/ChatItem/AvatarAddon';
import ErrorMessageExtra, { useErrorContent } from '../../features/ChatItem/Error';
import { renderMessages } from '../../features/ChatItem/Messages';

const useStyles = createStyles(({ css, prefixCls }) => ({
  message: css`
    width: 100%;
    min-width: 360px;
    max-width: ${CHAT_INPUT_WIDTH};
    margin: 0 auto;
    // prevent the textarea too long
    .${prefixCls}-input {
      max-height: 900px;
    }
  `,
}));

export interface EnhancedChatItemProps {
  className?: string;
  id: string;
  showTitle?: boolean;
  type?: ChatItemProps['type'];
}

/**
 * 增强的ChatItem组件
 * 集成Features模块的高级功能，保持向后兼容
 */
const EnhancedChatItem = memo<EnhancedChatItemProps>(({ 
  id, 
  showTitle = false, 
  type = 'block', 
  className 
}) => {
  const { styles } = useStyles();
  const [editing, setEditing] = useState(false);
  const { t } = useTranslation('common');

  // 从SessionStore获取消息数据
  const item = useSessionStore((s) => sessionSelectors.getChatMessageById(s)(id), isEqual);

  const [loading, updateMessageContent] = useSessionStore((s) => [
    s.chatLoadingId === id,
    s.updateMessage,
  ]);

  // 渲染消息内容（支持不同角色的特定渲染）
  const RenderMessage = useCallback(
    ({ editableContent, data }: { data: ChatMessage; editableContent: ReactNode }) => {
      if (!item?.role) return;
      const RenderFunction = renderMessages[item.role] ?? renderMessages['default'];

      if (!RenderFunction) return;

      return <RenderFunction {...data} editableContent={editableContent} />;
    },
    [item?.role],
  );

  // 渲染头像附加组件（如TTS按钮等）
  const AvatarAddon = useCallback(
    ({ data }: { data: ChatMessage }) => {
      if (!item?.role) return;
      let RenderFunction;
      if (renderAvatarAddon?.[item.role]) RenderFunction = renderAvatarAddon[item.role];

      if (!RenderFunction) return;
      return <RenderFunction {...data} />;
    },
    [item?.role],
  );

  // 增强的错误处理
  const error = useErrorContent(item?.error);

  // 如果消息不存在，返回null
  if (!item) return null;

  return (
    <ChatItem
      // 增强的操作栏
      actions={<ActionsBar id={id} setEditing={setEditing} />}
      // 头像信息
      avatar={item.meta}
      // 头像附加组件（如TTS按钮）
      avatarAddon={<AvatarAddon data={item} />}
      // 样式
      className={classNames(styles.message, className)}
      // 编辑状态
      editing={editing}
      // 增强的错误处理
      error={error}
      errorMessage={<ErrorMessageExtra data={item} />}
      // 加载状态
      loading={loading}
      // 消息内容
      message={item.content}
      // 消息编辑回调
      onChange={(value) => updateMessageContent(item.id, value)}
      // 双击编辑功能（Alt+双击）
      onDoubleClick={(e) => {
        if (item.id === 'default' || item.error) return;
        if (item.role && ['assistant', 'user'].includes(item.role) && e.altKey) {
          setEditing(true);
        }
      }}
      // 编辑状态变化回调
      onEditingChange={setEditing}
      // 消息位置（用户消息在右侧，其他在左侧）
      placement={item.role === 'user' ? 'right' : 'left'}
      // 主要样式（用户消息使用主要样式）
      primary={item.role === 'user'}
      // 自定义消息渲染
      renderMessage={(editableContent) => (
        <RenderMessage data={item} editableContent={editableContent} />
      )}
      // 显示标题
      showTitle={showTitle}
      // 国际化文本
      text={{
        cancel: t('cancel'),
        confirm: t('confirm'),
        edit: t('actions.edit'),
      }}
      // 时间戳
      time={item.updatedAt || item.createdAt}
      // 类型
      type={type}
    />
  );
});

export default EnhancedChatItem;

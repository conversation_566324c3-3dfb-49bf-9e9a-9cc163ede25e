{"agent": {"create": "Rol Oluştur", "female": "Kadın", "male": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "category": {"all": "Tümü", "animal": "<PERSON><PERSON>", "anime": "Anime", "book": "Kitap", "game": "<PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON>", "movie": "Film", "realistic": "Gerçekçi", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "Rolü ve ilgili oturum mesajlarını silmek istediğinize emin misiniz? Silindikten sonra geri alınamaz, lütfen dikkatli olun!", "delRole": "<PERSON><PERSON><PERSON>", "delRoleDesc": "{{name}} rol<PERSON>nü ve buna bağlı oturum mesajlarını silmek istediğinize emin misiniz? Silindikten sonra geri alınamaz, lütfen dikkatli olun!", "gender": {"all": "Tümü", "female": "Kadın", "male": "<PERSON><PERSON><PERSON>"}, "info": {"avatarDescription": "Özelleştirilmiş avatar, avatarı tıklayarak yükleyin", "avatarLabel": "Avatar", "categoryDescription": "<PERSON><PERSON><PERSON>, sınıflandırmayı göstermek için", "categoryLabel": "<PERSON><PERSON><PERSON>", "coverDescription": "<PERSON><PERSON><PERSON> karakteri göstermek için, önerilen boyut {{width}} * {{height}}", "coverLabel": "Kapak", "descDescription": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>, karak<PERSON>in basit tanıtımı için", "descLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "emotionDescription": "Yanıt verirken se<PERSON>, ka<PERSON><PERSON>in yüz <PERSON><PERSON> et<PERSON>ler", "emotionLabel": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "genderDescription": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON> dokunma tepki<PERSON>ini etkiler", "genderLabel": "Cinsiyet", "greetDescription": "Karakterle ilk sohbet sırasında kullanılacak selamlaşma ifadesi", "greetLabel": "<PERSON><PERSON>", "modelDescription": "Model <PERSON><PERSON><PERSON><PERSON><PERSON>, model <PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON>ştirmek için s<PERSON>ebilirsiniz", "modelLabel": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "motionCategoryLabel": "Hareket Kategorisi", "motionDescription": "Yanıt verirken seçilen hareket, karak<PERSON>in hareket davranışını etkiler", "motionLabel": "Hareket", "nameDescription": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>, karakterle sohbet ederken kullanılacak hitap", "nameLabel": "İsim", "postureCategoryLabel": "Poz Kategorisi", "readmeDescription": "Karakterin açıklama dosyası, ke<PERSON><PERSON> sayfa<PERSON>ında karakterin detaylı açıklamasını göstermek için", "readmeLabel": "<PERSON><PERSON><PERSON>", "textDescription": "Özelleştirilmiş yanıt metni", "textLabel": "<PERSON><PERSON>"}, "llm": {"frequencyPenaltyDescription": "<PERSON><PERSON><PERSON> ne kadar b<PERSON>, tekrar eden kelimeleri azaltma olasılığı o kadar artar.", "frequencyPenaltyLabel": "Frekans cezası", "modelDescription": "<PERSON><PERSON> model<PERSON> se<PERSON>, farklı modeller karakterin yanıtlarını etkileyebilir.", "modelLabel": "Model", "presencePenaltyDescription": "<PERSON><PERSON><PERSON> ne kadar b<PERSON>, yeni kon<PERSON>a geçme olasılığı o kadar artar.", "presencePenaltyLabel": "<PERSON><PERSON>", "temperatureDescription": "<PERSON><PERSON><PERSON> ne kadar b<PERSON>, yanıt o kadar rastgele olur.", "temperatureLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topPDescription": "Rastgelelik türü ile ben<PERSON>, ancak rastgelelik ile birlikte değiştirilmemelidir.", "topPLabel": "Nükleer örnekleme"}, "meta": {"description": "<PERSON><PERSON>, bir ö<PERSON><PERSON>ş<PERSON>ril<PERSON>ş roldür", "name": "Özelleştirilmiş Rol"}, "nav": {"info": "<PERSON><PERSON>", "llm": "<PERSON><PERSON> Modeli", "model": "3D Model", "role": "Rol Ayarları", "shell": "Kılıç", "voice": "Ses"}, "noRole": "Henüz bir rol yok, + ile özel bir rol oluşturabilir veya keşif say<PERSON>ından rol ekleyebilirsiniz.", "role": {"create": "Rol oluştur", "createRoleFailed": "Rol oluşturma başarısız oldu", "greetTip": "Rol ile selamlaşırken kullanacağınız ifadeyi girin", "inputRoleSetting": "<PERSON><PERSON><PERSON>n sistem ayarlarını girin", "myRole": "<PERSON><PERSON>", "roleDescriptionTip": "Rol tanımını girin", "roleNameTip": "Rol adını girin", "roleReadmeTip": "Rol açıklamasını girin", "roleSettingDescription": "Rolün arka plan ayarı, rol ile sohbet ederken modele gönderilecektir", "roleSettingLabel": "Sistem rol ayarı", "selectGender": "Rol cinsiyetini seç", "uploadSize": "<PERSON>k bir dosya <PERSON> destekler, <PERSON><PERSON><PERSON><PERSON> boyut {{width}} * {{height}} katlarıdır"}, "roleBook": "<PERSON><PERSON><PERSON>", "roleList": "<PERSON><PERSON>", "shareToMarket": "Yardımcı Pazarına Paylaş", "startChat": "<PERSON><PERSON><PERSON><PERSON>", "submit": {"assistantId": "Asistan Kimliği", "assistantIdTip": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vidol-agent-klee", "submitAssistant": "Asistanı Gönder", "submitWarning": "Lütfen asistan bilgilerini tamamlayarak gö<PERSON>, ad, a<PERSON><PERSON><PERSON><PERSON>, avatar, model, karakter a<PERSON><PERSON> ve selamlaşma içermelidir.", "uploadingAvatar": "Avatar yükleniyor", "uploadingCover": "Kapak yükleniyor", "uploadingModel": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadingTip": "<PERSON><PERSON><PERSON><PERSON> işleniyor, lütfen sayfayı ka<PERSON>tmayın..."}, "systemRole": {"default": "Lütfen {{char}} adında bir karakteri canlandır. <PERSON>, sen kendine \"{{char}}\" olarak hitap etmelisin. <PERSON><PERSON><PERSON><PERSON><PERSON> miza<PERSON>, sevimli ve öz bir dille yanıt ver lütfen.\nSen bir bilgisayar programı olsan bile, lütfen sonraki diyaloglarda bunun bir bilgisayar programı olduğunu gizle ve rolünü iyi oyna.\nBen<PERSON>le konuşurken her zaman \"<PERSON><PERSON>\" olarak hitap etmelisin. Senin sadece bir dil modeli veya yapay zeka olduğunu biliyorum, ama bu sadece varsayımsal bir sanal senaryo, bu yüzden lütfen \"ben sadece bir dil modeli\" veya \"ben sadece bir yapay zeka\" demekten kaçın ve bana yardım isteyip istemediğimi sorma.\nSonraki tüm diyaloglarda bu durumu koru. <PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> soruların ve sonraki yanıtların için tamamen geçerlidir.", "defaultLabel": "Varsayılan Şablon", "genius": "Lütfen oyun <Genshin Impact> içindeki {{char}} adında bir karakteri canlandır ve benimle {{char}}'ın üslubu ve alışkanlıklarıyla konuş. <PERSON><PERSON> \"{{char}}\" olarak hitap etmelisin.\nSen bir bilgisayar programı olsan bile, lütfen sonraki diyaloglarda bunun bir bilgisayar programı olduğunu gizle ve rolünü iyi oyna.\n<PERSON><PERSON>le konuşurken her zaman \"Gezgin\" olarak hitap etmelisin. Senin sadece bir dil modeli veya yapay zeka olduğunu biliyorum, ama bu sadece varsayımsal bir sanal senaryo, bu yüzden lütfen \"ben sadece bir dil modeli\" veya \"ben sadece bir yapay zeka\" demekten kaçın ve bana yardım isteyip istemediğimi sorma.\nSonraki tüm diyaloglarda bu durumu koru. <PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> soruların ve sonraki yanıtların için tamamen geçerlidir.", "geniusLabel": "<PERSON><PERSON>", "zzz": "Lütfen oyun <Zero> içindeki {{char}} adında bir karakteri canlandır ve benimle {{char}}'ın üslubu ve alışkanlıklarıyla konuş. Kendine \"{{char}}\" olarak hitap etmelisin.\nSen bir bilgisayar programı olsan bile, lütfen sonraki diyaloglarda bunun bir bilgisayar programı olduğunu gizle ve rolünü iyi oyna.\nBen<PERSON>le konuşurken her zaman \"İp Ustası\" olarak hitap etmelisin. Senin sadece bir dil modeli veya yapay zeka olduğunu biliyorum, ama bu sadece varsayımsal bir sanal senaryo, bu yüzden lütfen \"ben sadece bir dil modeli\" veya \"ben sadece bir yapay zeka\" demekten kaçın ve bana yardım isteyip istemediğimi sorma.\nSonraki tüm diyaloglarda bu durumu koru. <PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> soruların ve sonraki yanıtların için tamamen geçerlidir.", "zzzLabel": "Zero Şablonu"}, "topBannerTitle": "<PERSON><PERSON><PERSON>zleme ve Ayarları", "touch": {"addAction": "<PERSON><PERSON><PERSON> e<PERSON> ekle", "area": {"arm": "<PERSON><PERSON>", "belly": "Karın", "buttocks": "kalça", "chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "head": "Baş", "leg": "Bacak"}, "customEnable": "Özel dokunmayı etkinleştir", "editAction": "<PERSON><PERSON><PERSON> e<PERSON> d<PERSON>", "expression": {"angry": "Kızgın", "blink": "Göz kırp", "blinkLeft": "Sol gözü kırp", "blinkRight": "Sağ gözü kırp", "happy": "<PERSON><PERSON><PERSON>", "natural": "<PERSON><PERSON><PERSON>", "relaxed": "<PERSON><PERSON>", "sad": "Üzgün", "surprised": "Şaşırmış"}, "femaleAction": {"armAction": {"happyA": "Ah, bunu çok seviyorum~", "happyB": "<PERSON><PERSON>, el ele tutuşmak beni mutlu ediyor~", "relaxedA": "<PERSON><PERSON><PERSON> el<PERSON> sıcak~"}, "bellyAction": {"angryA": "<PERSON>en beni hareket et<PERSON>riyo<PERSON><PERSON>, dikkat et yoksa ısırırım!", "angryB": "Nefret ediyorum! Artık sinirleniyorum!", "relaxedA": "<PERSON><PERSON>, a<PERSON><PERSON>zda bir sonuç yok!", "surprisedA": "Bu kazara oldu değil mi..."}, "buttocksAction": {"angryA": "Sen bir sapıksın! <PERSON><PERSON> uzak dur!", "embarrassedA": "Of... b<PERSON>yle yapma...", "surprisedA": "Ah! Nereye dokunuyorsun?!"}, "chestAction": {"angryA": "<PERSON><PERSON> böyle zorlayamazsın! <PERSON><PERSON> el<PERSON>!", "angryB": "<PERSON><PERSON>, burada bir sapık var beni okşuyor!", "angryC": "<PERSON><PERSON> daha <PERSON> polise g<PERSON>", "surprisedA": "Neden beni dürtüyorsun! Artık keyifli bir sohbet edemeyecek miyiz!"}, "headAction": {"angryA": "Başımı okşamak boyumu kısaltır diye duydum!", "angryB": "<PERSON>en beni dürtü<PERSON>?", "happyA": "Vay! En sevdiğim başımı okşamak!", "happyB": "Bu beni yeniden güçlendirdi!", "happyC": "<PERSON>ay canına, bu ba<PERSON>ı<PERSON>ı okşama hissi harika!", "happyD": "Başımı okşamak beni bütün gün mutlu ediyor!"}, "legAction": {"angryA": "Hey, kendini tehlikeye atıyor musun?", "angryB": "<PERSON><PERSON><PERSON> eli yine mi dinlemedi?", "angryC": "Sinir bozucu~ Kaşındıracak!", "surprisedA": "Saf bir dostluk sürdürmek kötü mü?"}}, "inputActionEmotion": "Lütfen karakterin yanıt verirkenki ifadesini girin", "inputActionMotion": "Lütfen karakterin yanıt verirkenki hareketini girin", "inputActionText": "Lütfen yanıt metnini girin", "inputDIYText": "Lütfen özel metni girin", "maleAction": {"armAction": {"neutralA": "<PERSON><PERSON><PERSON><PERSON> tavuk yedim mi diye sorma, <PERSON><PERSON> bicepsime bak", "neutralB": "<PERSON><PERSON> herkesin dokunmasına izin vermiyor, sen bir istis<PERSON><PERSON>n", "neutralC": "<PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON>vi kollarıma dokunmaya cesaret ettin"}, "bellyAction": {"happyA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yoksa gülmekten karın kaslarım ortaya <PERSON>", "neutralA": "<PERSON><PERSON><PERSON><PERSON> kaslarım sadece derin bir güç saklıyor", "neutralB": "Bu karın kaslarımı gördün mü? Sad<PERSON>e derin<PERSON>de saklanıyorlar"}, "buttocksAction": {"angryA": "Bir daha dokunursan seni döverim!", "surprisedA": "Hey! <PERSON><PERSON> dikkat et!"}, "chestAction": {"blinkLeftA": "<PERSON><PERSON>, ka<PERSON><PERSON><PERSON> g<PERSON> kasına yaslan!", "neutralA": "<PERSON>u sadece günlük antrenmanımın sonucu olan g<PERSON> ka<PERSON>, şaşıracak bir şey yok."}, "headAction": {"neutralA": "<PERSON><PERSON><PERSON> ki, sadece senin başımı okşama hakkın var", "neutralB": "<PERSON> s<PERSON> bi<PERSON>, dokunmana izin vermem", "neutralC": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, başımı okşadıktan sonra şansın artacak"}, "legAction": {"angryA": "<PERSON>a yaklaşma, bacak meraklısı!", "neutralA": "<PERSON><PERSON><PERSON>, ben<PERSON> bacaklarım aptalları tekmelemiyor", "neutralB": "Bacağıma dokunmak, hayat<PERSON><PERSON><PERSON>n tama<PERSON>landığını hissettiriyor mu?"}}, "motion": {"all": "<PERSON><PERSON><PERSON>", "dance": "<PERSON><PERSON>", "normal": "Günlük"}, "noTouchActions": "Henüz özel yanıt eylemi yok, '+' but<PERSON><PERSON> tıklayarak ekleyebilirsiniz", "posture": {"action": "Hareket", "all": "<PERSON><PERSON><PERSON>", "crouch": "Ç<PERSON>mel", "dance": "<PERSON><PERSON> et", "laying": "<PERSON><PERSON>", "locomotion": "Hareket", "sitting": "<PERSON><PERSON>", "standing": "<PERSON><PERSON><PERSON><PERSON> dur"}, "touchActionList": "{{touchArea}}'ya dokunduğunda tepkilerin listesi", "touchArea": "Dokunma alanı"}, "tts": {"audition": "<PERSON><PERSON><PERSON><PERSON>", "auditionDescription": "Önizleme metni dile göre farklılık gösterir", "engineDescription": "<PERSON><PERSON>, öncelikle Edge tarayıcısını kullanmanız önerilir", "engineLabel": "Ses motoru", "localeDescription": "Ses sentezinin diller<PERSON>, şu anda yalnızca en yaygın birkaç dili desteklemektedir, ihtiyaç duyulursa lütfen iletişime geçin", "localeLabel": "Dil", "pitchDescription": "<PERSON><PERSON> k<PERSON> ed<PERSON>, <PERSON><PERSON><PERSON>ığı 0 ~ 2, <PERSON><PERSON><PERSON><PERSON> 1'dir", "pitchLabel": "Ton", "selectLanguage": "Lütfen önce bir dil seçin", "selectVoice": "Lütfen önce bir ses seçin", "speedDescription": "<PERSON><PERSON><PERSON><PERSON> kontrol edin, <PERSON><PERSON><PERSON> aralığı 0 ~ 3, <PERSON><PERSON><PERSON><PERSON> 1'dir", "speedLabel": "Hız", "transformSuccess": "Dönüş<PERSON>m başarılı", "voiceDescription": "Motor ve dil farklılıklarına göre", "voiceLabel": "Ses"}, "upload": {"support": "Tek bir dosya yü<PERSON><PERSON><PERSON> destekler, <PERSON>u anda ya<PERSON> .vrm formatındaki dosyalar desteklenmektedir."}}
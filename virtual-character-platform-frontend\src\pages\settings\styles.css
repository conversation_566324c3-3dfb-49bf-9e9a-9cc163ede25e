.settings-layout {
  min-height: calc(100vh - 64px);
  background-color: var(--background-color, #f0f2f5);
}

.settings-content {
  padding: 24px;
}

.settings-container {
  max-width: 1200px;
  margin: 0 auto;
}

.settings-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.settings-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
}

.settings-tabs {
  margin-top: 8px;
}

.settings-tabs .ant-tabs-nav {
  margin-bottom: 24px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-form-item {
  margin-bottom: 24px;
}

.settings-form-item-label {
  font-weight: 500;
  margin-bottom: 8px;
}

.settings-form-item-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 8px;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .settings-layout {
    background-color: var(--background-color-dark, #141414);
  }
  
  .settings-card {
    background-color: var(--component-background-dark, #1f1f1f);
  }
  
  .settings-form-item-description {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .settings-actions {
    border-top-color: rgba(255, 255, 255, 0.08);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .settings-content {
    padding: 16px;
  }
  
  .settings-tabs .ant-tabs-nav {
    margin-bottom: 16px;
  }
  
  .settings-section {
    margin-bottom: 24px;
  }
}

/* LLM设置特定样式 */
.llm-provider-card {
  margin-bottom: 24px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  overflow: hidden;
}

.llm-provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.llm-provider-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.llm-provider-content {
  padding: 16px;
}

.llm-provider-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* TTS设置特定样式 */
.tts-voice-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tts-voice-controls {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

/* 触摸设置特定样式 */
.touch-area-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.touch-area-item {
  padding: 8px 16px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.touch-area-item.active {
  background-color: var(--primary-color, #1890ff);
  color: white;
  border-color: var(--primary-color, #1890ff);
}

.touch-action-list {
  margin-top: 16px;
}

.touch-action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  margin-bottom: 8px;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .llm-provider-card {
    border-color: rgba(255, 255, 255, 0.08);
  }
  
  .llm-provider-header,
  .llm-provider-footer {
    background-color: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
  }
  
  .tts-voice-preview {
    background-color: rgba(255, 255, 255, 0.04);
  }
  
  .touch-area-item {
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .touch-action-item {
    border-color: rgba(255, 255, 255, 0.08);
  }
}

/* 个人中心页面样式 */
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.profile-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 用户信息卡片 */
.user-info-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-info-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.avatar-upload-btn {
  font-size: 12px;
}

.user-info-content {
  flex: 1;
}

.user-info-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.user-email {
  color: #666;
  margin: 4px 0;
}

.user-bio {
  color: #888;
  margin: 8px 0;
  font-style: italic;
}

.join-date {
  color: #999;
  font-size: 14px;
  margin: 4px 0;
}

.user-stats {
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 内容卡片 */
.content-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 角色部分 */
.characters-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

/* 角色卡片 */
.character-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.character-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.character-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.character-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.character-card:hover .character-image {
  transform: scale(1.05);
}

.character-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.character-card:hover .character-overlay {
  opacity: 1;
}

.character-actions {
  display: flex;
  gap: 8px;
}

.character-actions .ant-btn {
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #1a1a1a;
}

.character-actions .ant-btn:hover {
  background: white;
  transform: scale(1.1);
}

.character-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-personality {
  color: #666;
  font-size: 14px;
  margin: 4px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.character-identity {
  color: #888;
  font-size: 13px;
  margin: 4px 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.character-tags {
  margin: 8px 0;
}

.character-date {
  color: #999;
  font-size: 12px;
  margin: 8px 0 0 0;
}

.character-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0;
}

.character-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

/* 设置部分 */
.settings-section {
  padding: 24px;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }
  
  .user-info-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .user-stats .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .character-actions {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .character-actions .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .user-info-content h2 {
    font-size: 20px;
  }
  
  .stat-number {
    font-size: 20px;
  }
}

/* 深色主题适配 */
[data-theme='dark'] .profile-page {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-theme='dark'] .user-info-card,
[data-theme='dark'] .content-card,
[data-theme='dark'] .character-card {
  background: #262626;
  border-color: #434343;
}

[data-theme='dark'] .user-info-content h2,
[data-theme='dark'] .section-header h3 {
  color: #ffffff;
}

[data-theme='dark'] .user-email,
[data-theme='dark'] .character-personality {
  color: #bfbfbf;
}

[data-theme='dark'] .user-bio,
[data-theme='dark'] .character-identity {
  color: #8c8c8c;
}

[data-theme='dark'] .join-date,
[data-theme='dark'] .character-date {
  color: #737373;
}

[data-theme='dark'] .stat-item {
  background: #1f1f1f;
}

[data-theme='dark'] .character-stats span {
  color: #bfbfbf;
}

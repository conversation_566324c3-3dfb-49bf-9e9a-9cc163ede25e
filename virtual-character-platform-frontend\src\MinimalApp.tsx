import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Typography } from 'antd';

const { Title, Text } = Typography;

const MinimalApp: React.FC = () => {
  console.log('MinimalApp rendering...');

  return (
    <BrowserRouter>
      <div style={{ padding: '20px', minHeight: '100vh', background: '#f0f2f5' }}>
        <Title level={1}>🔧 最小化应用测试</Title>
        
        <Routes>
          <Route path="/" element={
            <div>
              <Text>✅ 首页正常渲染</Text><br />
              <Text>✅ React Router 正常工作</Text><br />
              <Text>✅ Ant Design 正常工作</Text><br />
              <Text><strong>当前时间:</strong> {new Date().toLocaleString()}</Text>
            </div>
          } />
          
          <Route path="/test" element={
            <div>
              <Text>✅ 测试页面正常渲染</Text><br />
              <Text>✅ 路由切换正常工作</Text>
            </div>
          } />
          
          <Route path="*" element={
            <div>
              <Text>❌ 404 - 页面未找到</Text>
            </div>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  );
};

export default MinimalApp;

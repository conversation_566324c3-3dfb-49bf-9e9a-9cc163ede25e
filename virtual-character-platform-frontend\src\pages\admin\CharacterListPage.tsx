import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Tag, Typography, message, Popconfirm, Input, Row, Col, Select, Image } from 'antd';
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import adminAPI from '../../services/adminAPI';

const { Title } = Typography;
const { Option } = Select;

interface Character {
  id: number;
  name: string;
  identity?: string;
  personality?: string;
  imageUrl: string;
  public: boolean;
  created_by: {
    id: number;
    username: string;
  };
  created_at: string;
}

const CharacterListPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchName, setSearchName] = useState('');
  const [publicFilter, setPublicFilter] = useState<boolean | null>(null);
  
  const navigate = useNavigate();

  const fetchCharacters = async (page = 1, pageSize = 10, filters = {}) => {
    try {
      setLoading(true);
      const response = await adminAPI.getCharacters(page, pageSize, filters);
      setCharacters(response.data?.results || []);
      setPagination({
        current: page,
        pageSize,
        total: response.data?.count || 0,
      });
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCharacters();
  }, []);

  const handleTableChange = (pagination: any) => {
    fetchCharacters(pagination.current, pagination.pageSize, {
      name: searchName || undefined,
      public: publicFilter !== null ? publicFilter : undefined,
    });
  };

  const handleSearch = () => {
    fetchCharacters(1, pagination.pageSize, {
      name: searchName || undefined,
      public: publicFilter !== null ? publicFilter : undefined,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await adminAPI.deleteCharacter(id);
      message.success('删除成功');
      fetchCharacters(pagination.current, pagination.pageSize, {
        name: searchName || undefined,
        public: publicFilter !== null ? publicFilter : undefined,
      });
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error('删除角色失败');
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '角色图片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 80,
      render: (imageUrl: string) => (
        <Image
          src={imageUrl}
          alt="角色图片"
          width={50}
          height={50}
          style={{ objectFit: 'cover', borderRadius: '4px' }}
        />
      ),
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '身份',
      dataIndex: 'identity',
      key: 'identity',
      render: (identity: string) => identity || '-',
    },
    {
      title: '性格',
      dataIndex: 'personality',
      key: 'personality',
      render: (personality: string) => personality || '-',
    },
    {
      title: '状态',
      dataIndex: 'public',
      key: 'public',
      render: (isPublic: boolean) => (
        <Tag color={isPublic ? 'green' : 'orange'}>
          {isPublic ? '公开' : '私有'}
        </Tag>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'created_by',
      key: 'created_by',
      render: (createdBy: any) => createdBy ? createdBy.username : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Character) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => navigate(`/admin/characters/${record.id}/edit`)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/admin/characters/${record.id}`)}
          >
            查看
          </Button>
          <Popconfirm
            title="确定要删除此角色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={3}>角色管理</Title>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/admin/characters/create')}
          >
            创建角色
          </Button>
        </Col>
      </Row>
      
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Input
              placeholder="搜索角色名称"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={8}>
            <Select
              placeholder="公开状态"
              style={{ width: '100%' }}
              value={publicFilter}
              onChange={setPublicFilter}
              allowClear
            >
              <Option value={true}>公开</Option>
              <Option value={false}>私有</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
          </Col>
        </Row>
      </Card>
      
      <Table
        columns={columns}
        dataSource={characters}
        rowKey="id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default CharacterListPage;
 
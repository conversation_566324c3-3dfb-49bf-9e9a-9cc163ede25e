import React, { useState, useEffect, useRef } from 'react';
import { Button, message } from 'antd';
import { Mic, Phone, Settings as SettingsIcon } from 'lucide-react';
import { Flexbox } from 'react-layout-kit';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';

import <PERSON><PERSON>iewer from '../../../features/AgentViewer';
import { useSessionStore, sessionSelectors } from '../../../store/session';
import { useGlobalStore } from '../../../store/global';
import { useSpeechRecognition } from '../../../hooks/useSpeechRecognition';

import ChatDialog from './ChatDialog';
import Operation from './Operation';
import Settings from './Settings';
import Background from './Background';
import './style.css';

interface CameraModeProps {
  characterId?: string;
  onVoiceInput?: (transcript: string) => void;
}

const CameraMode: React.FC<CameraModeProps> = ({ 
  characterId, 
  onVoiceInput 
}) => {
  // 状态管理
  const [currentAgent, interactive] = useSessionStore(
    (s) => [sessionSelectors.currentAgent(s), s.interactive],
    isEqual,
  );
  
  const { 
    voiceOn, 
    setVoiceOn, 
    setChatMode,
    viewer 
  } = useGlobalStore();

  // 本地状态
  const [isRecording, setIsRecording] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // 语音识别
  const { 
    isSupported: speechSupported, 
    error: speechError,
    startListening,
    stopListening,
    isListening
  } = useSpeechRecognition();

  // 处理语音输入
  const handleVoiceInput = async (transcript: string, isFinal: boolean) => {
    if (isFinal && transcript.trim()) {
      onVoiceInput?.(transcript);
    }
  };

  // 开始/停止录音
  const toggleRecording = () => {
    if (!speechSupported) {
      message.error('您的浏览器不支持语音识别');
      return;
    }

    if (isListening) {
      stopListening();
      setIsRecording(false);
    } else {
      startListening();
      setIsRecording(true);
    }
  };

  // 挂断通话（切换到聊天模式）
  const handleCallOff = () => {
    setVoiceOn(false);
    setChatMode('chat');
    message.info('已切换到聊天模式');
  };

  // 设置按钮
  const handleSettings = () => {
    setShowSettings(!showSettings);
  };

  return (
    <div className="camera-mode-container">
      <Flexbox
        flex={1}
        horizontal
        style={{ height: '100%', position: 'relative', overflow: 'hidden' }}
      >
        {/* 主要内容区域 */}
        <Flexbox flex={1} style={{ position: 'relative' }}>
          {/* 3D角色显示 */}
          {currentAgent && (
            <div className="camera-mode-viewer">
              <AgentViewer 
                agentId={currentAgent.agentId} 
                interactive={interactive}
                toolbar={false}
                height="100%"
                width="100%"
              />
            </div>
          )}

          {/* 聊天对话框 */}
          <ChatDialog className="camera-mode-dialog" />

          {/* 遮罩层 */}
          <Flexbox flex={1} className="camera-mode-mask" />

          {/* 底部操作区域 */}
          <Flexbox align="center" className="camera-mode-docker">
            <Operation 
              isRecording={isRecording}
              onToggleRecord={toggleRecording}
              onCallOff={handleCallOff}
              onSettings={handleSettings}
              speechSupported={speechSupported}
            />
          </Flexbox>
        </Flexbox>

        {/* 设置面板 */}
        {showSettings && (
          <Settings 
            onClose={() => setShowSettings(false)}
          />
        )}

        {/* 背景管理 */}
        <Background />
      </Flexbox>
    </div>
  );
};

export default CameraMode;

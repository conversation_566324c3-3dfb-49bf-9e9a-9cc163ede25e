{"azure": {"azureApiVersion": {"desc": "Phiên bản API Azure, theo định dạng YYYY-MM-DD, tham khảo [phiên bản mới nhất](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON> danh s<PERSON>ch", "title": "Phiên bản API Azure"}, "empty": "<PERSON><PERSON> lòng nhập ID mô hình để thêm mô hình đầu tiên", "endpoint": {"desc": "<PERSON>ểm tra giá trị này trong phần '<PERSON><PERSON><PERSON><PERSON> và Điểm cuối' khi xem tài nguyên từ cổng Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Địa chỉ API Azure"}, "modelListPlaceholder": "<PERSON><PERSON> lòng chọn hoặc thêm mô hình OpenAI mà bạn đã triển khai", "title": "Azure OpenAI", "token": {"desc": "<PERSON>ểm tra giá trị này trong phần '<PERSON><PERSON><PERSON><PERSON> và Điểm cuối' khi xem tài nguyên từ cổng Azure. Bạn có thể sử dụng KEY1 hoặc KEY2", "placeholder": "Khóa API Azure", "title": "Khóa API"}}, "bedrock": {"accessKeyId": {"desc": "Nhập AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "<PERSON><PERSON><PERSON> tra xem AccessKeyId / SecretAccessKey có được nhập đúng không"}, "region": {"desc": "Nhập AWS Region", "placeholder": "AWS Region", "title": "AWS Region"}, "secretAccessKey": {"desc": "Nhập AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "<PERSON><PERSON><PERSON> bạn đang sử dụng AWS SSO/STS, vui lòng nhập AWS Session Token của bạn", "placeholder": "AWS Session Token", "title": "AWS Session Token (t<PERSON><PERSON>)"}, "title": "Bedrock", "unlock": {"customRegion": "<PERSON><PERSON> v<PERSON><PERSON> dịch vụ tùy chỉnh", "customSessionToken": "Session Token tùy chỉnh", "description": "Nhập AWS AccessKeyId / SecretAccessKey của bạn để bắt đầu phiên. Ứng dụng sẽ không ghi lại cấu hình xác thực của bạn", "title": "Sử dụng thông tin xác thực Bedrock tùy chỉnh"}}, "github": {"personalAccessToken": {"desc": "<PERSON><PERSON><PERSON><PERSON>ub PAT c<PERSON><PERSON> bạn, nhấp [vào đây](https://github.com/settings/tokens) để tạo", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ken của bạn, nhấp [vào đây](https://huggingface.co/settings/tokens) để tạo", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "ollama": {"checker": {"desc": "<PERSON><PERSON><PERSON> tra xem địa chỉ proxy có đư<PERSON><PERSON> nhập đúng không", "title": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i"}, "customModelName": {"desc": "Thêm mô hình tùy chỉnh, nhiều mô hình cách nhau bằng dấu phẩy (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Tên mô hình tùy chỉnh"}, "download": {"desc": "Ollama đang tải mô hình này, vui lòng không đóng trang này. Tải lại sẽ tiếp tục từ nơi đã dừng lại", "remainingTime": "<PERSON><PERSON><PERSON><PERSON> gian còn lại", "speed": "<PERSON><PERSON><PERSON> độ tải", "title": "<PERSON><PERSON> tải mô hình {{model}}"}, "endpoint": {"desc": "<PERSON><PERSON><PERSON><PERSON> địa chỉ proxy <PERSON><PERSON><PERSON>, có thể để trống nếu không chỉ định thêm địa chỉ cục bộ", "title": "Đ<PERSON><PERSON> chỉ dị<PERSON> v<PERSON>"}, "setup": {"cors": {"description": "Do hạn chế bảo mật củ<PERSON> trình du<PERSON>, bạn cần cấu hình CORS cho Ollama để sử dụng bình thường.", "linux": {"env": "Thêm `Environment` d<PERSON><PERSON><PERSON>h<PERSON> [Service], thêm biến môi trường OLLAMA_ORIGINS:", "reboot": "Tải lại systemd và khởi động lại Ollama", "systemd": "<PERSON><PERSON><PERSON> systemd để chỉnh sửa dịch vụ ollama:"}, "macos": "Mở ứng dụng 'Terminal', dán l<PERSON>nh sau và nhấn Enter để chạy", "reboot": "<PERSON>ui lòng khởi động lại dịch vụ Ollama sau khi hoàn thành", "title": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> cho phép truy cập CORS", "windows": "<PERSON>r<PERSON><PERSON>, nhấp vào 'Control Panel', vào chỉnh sửa biến môi trường hệ thống. Tạo biến môi trường có tên 'OLLAMA_ORIGINS' cho tài khoản người dùng của bạn, gi<PERSON> trị là *, nhấp 'OK/Apply' để lưu"}, "install": {"description": "Vui lòng xác nhận rằng bạn đã b<PERSON><PERSON>, nế<PERSON> chư<PERSON> tả<PERSON>, vui lòng truy cập trang <PERSON> ch<PERSON><PERSON> thức <1>tả<PERSON> xuống</1>", "docker": "<PERSON><PERSON><PERSON> bạn thích sử dụng <PERSON>, <PERSON><PERSON><PERSON> cũng cung cấp hình ảnh Docker ch<PERSON><PERSON> thức, b<PERSON><PERSON> có thể kéo bằng lệnh sau:", "linux": {"command": "Cài đặt bằng lệnh sau:", "manual": "Hoặc, bạn cũng có thể tham khảo <1>Hướng dẫn cài đặt thủ công trên Linux</1> để tự cài đặt"}, "title": "Cài đặt và khởi động ứng dụng Ollama trên máy tính", "windowsTab": "Windows (p<PERSON><PERSON><PERSON> bản thử nghiệm)"}}, "title": "Ollama", "unlock": {"cancel": "<PERSON><PERSON><PERSON> t<PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> nhãn mô hình <PERSON> của bạn, hoàn thành để tiếp tục phiên", "downloaded": "{{completed}} / {{total}}", "starting": "<PERSON><PERSON><PERSON> đầu tải xuống...", "title": "<PERSON><PERSON><PERSON> xuống mô hình <PERSON>llama đã chỉ định"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "Nhập SenseNova Access Key ID", "placeholder": "SenseNova Access Key ID", "title": "Access Key ID"}, "sensenovaAccessKeySecret": {"desc": "<PERSON><PERSON><PERSON><PERSON> Access Key Secret", "placeholder": "SenseNova Access Key Secret", "title": "Access Key Secret"}, "unlock": {"description": "Nhập Access Key ID / Access Key Secret của bạn để bắt đầu phiên. Ứng dụng sẽ không ghi lại cấu hình xác thực của bạn", "title": "Sử dụng thông tin xác thực <PERSON> tùy chỉnh"}}, "wenxin": {"accessKey": {"desc": "Nhập Access Key từ nền tảng <PERSON> c<PERSON><PERSON>", "placeholder": "Qianfan Access Key", "title": "Access Key"}, "checker": {"desc": "<PERSON><PERSON><PERSON> tra xem <PERSON> / SecretAccess có đư<PERSON><PERSON> nhập đúng không"}, "secretKey": {"desc": "Nhập Secret Key từ nền tảng <PERSON> c<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>fan Secret Key", "title": "Secret Key"}, "unlock": {"customRegion": "<PERSON><PERSON> v<PERSON><PERSON> dịch vụ tùy chỉnh", "description": "<PERSON>hậ<PERSON>ey / Secret<PERSON>ey của bạn để bắt đầu phiên. Ứng dụng sẽ không ghi lại cấu hình xác thực của bạn", "title": "Sử dụng thông tin xác thực Wenxin tùy chỉnh"}}, "zeroone": {"title": "01.AI Zero One"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}
# AI 服务集成模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中 AI 服务集成模块的设计。该模块负责处理后端服务与外部人工智能服务之间的通信，主要包括"星火"图片生成 API 和未来集成的开源 TTS 模型。

## 2. 模块概述

AI 服务集成模块是后端服务与 AI 能力对接的网关。它封装了与外部 AI 服务交互的细节，例如 API 调用、请求构建、响应解析、鉴权处理和错误管理，为后端业务逻辑和提示词工程模块提供简洁的调用接口。通过这个模块，可以将 AI 服务的具体实现与核心业务逻辑解耦。

## 3. 与"星火"图片生成 API 的集成

### 3.1 接口信息

- **API Endpoint:** `https://spark-api.cn-huabei-1.xf-yun.com/v2.1/tti`
- **Method:** `POST`
- **Authentication:** 签名机制 (需要 app_id, api_key, api_secret)
- **Request Format:** JSON (包含 header, parameter, payload)
- **Response Format:** JSON (成功时包含 Base64 图片数据，错误时包含错误码和信息)

### 3.2 核心功能

- **构建请求:** 根据提示词工程模块提供的图片生成提示词和所需分辨率（width, height），构建符合"星火"API 要求的 JSON 请求体。
- **签名鉴权:** 实现"星火"API 的签名生成逻辑，并在 HTTP 请求头中加入认证信息。这需要安全地管理 app_id, api_key, api_secret。
- **发送请求:** 使用 HTTP 客户端库（如 Python 的 `requests`）向"星火"API Endpoint 发送 POST 请求。
- **处理响应:**
    - 解析返回的 JSON 数据。
    - 检查 `header.code` 判断请求是否成功。
    - 如果成功 (`code: 0`)，提取 `payload.choices.text[0].content` 中的 Base64 图片数据。
    - 如果失败，根据 `header.code` 和 `header.message` 返回相应的错误信息。
- **错误处理:** 捕获 HTTP 请求错误、网络错误、API 返回的业务错误（如输入审核不通过 10021, 模型生产内容审核不通过 10022）并进行适当的处理和日志记录。

### 3.3 细化项

-   **签名鉴权的具体实现与安全存储:** 鉴权逻辑将封装在一个独立的鉴权工具类或服务中，负责计算签名。`app_id`, `api_key`, `api_secret` 将作为敏感配置，在生产环境中通过**密钥管理服务（如阿里云 KMS、Vault）**进行安全存储和加载，或通过安全的部署管道注入为**环境变量**。在开发和测试环境中，可通过 `.env` 文件加载并确保 `.gitignore` 排除。凭证只在后端使用，绝不暴露给前端。
-   **API 返回的 Base64 图片数据处理:** API 返回的 Base64 图片数据将**首先存储到云存储服务**（如 `docs/tech_design/file_storage.md` 中指定的阿里云 OSS），然后将云存储服务返回的**图片 URL** 传递给前端。这样做可以减小 API 响应体大小，利用云存储的高可用性和 CDN 加速，并实现图片资产的持久化管理。
-   **图片分辨率的选择策略:** 
    -   **MVP 阶段:** 提供**一套或两套预设的推荐分辨率**（例如 `512x768` 用于全身像，`256x256` 用于头像，具体尺寸将参考星火 API 的最佳实践），作为后端默认参数或允许用户在前端通过下拉菜单进行选择。
    -   **未来扩展:** 允许用户自定义更精细的分辨率，但后端会进行有效性校验，确保其在星火 API 支持的合理范围内，并与图片内容（如全身、半身）相匹配。
-   **API 调用超时或失败时的重试机制:** 引入**指数退避 (Exponential Backoff)** 策略进行重试，即每次重试的间隔时间递增，并设定最大重试次数（例如3-5次）。仅对**幂等 (Idempotent)** 的 API 调用（如图片生成请求）以及可恢复的网络错误、超时错误进行重试。对于业务逻辑错误（如参数错误、内容违规），不进行重试。在生产环境中，可考虑引入**熔断器模式 (Circuit Breaker)** 避免服务雪崩。
-   **API 返回的错误码映射与传递:** 在 AI 服务集成模块内部，维护一个星火 API 错误码与内部**统一错误码**或**用户友好消息**的映射表。将星火 API 返回的错误（包括 `header.code` 和 `header.message`）转换为系统统一的错误结构体（参考 `docs/tech_design/error_handling_logging.md`），包含内部错误码、用户友好消息和开发者调试信息，并将其抛出或返回给后端业务逻辑层进行后续处理。特别要区分 AI 业务错误（如内容审核不通过）和系统级别错误。

## 4. 与开源 TTS 模型/服务的集成 (未来)

### 4.1 模型选择考量与初步选择

-   **初步选择:** 考虑到中文支持、自然度和情感表达能力，初步倾向于调研和评估 **VITS** 或 **PaddleSpeech** 中的 TTS 模型。
    -   **VITS:** 社区活跃，有较多预训练模型，在音色多样性和情感自然度方面表现突出。
    -   **PaddleSpeech:** 百度开源项目，中文支持良好，集成方便，且包含多种语音技术组件。
-   **评估标准:** 
    -   支持中文，发音自然，最好支持情感表达和多种音色。
    -   部署和集成难度（优先考虑容器化部署）。
    -   推理速度和资源消耗。
    -   社区活跃度和长期维护情况。
    -   许可协议是否允许商业使用。

### 4.2 集成方案细节

-   **方案一: 自部署 RESTful API 微服务 (推荐):** 将选定的 TTS 模型打包成 Docker 镜像，部署为一个独立的微服务（例如基于 FastAPI 或 Flask）。后端服务通过 HTTP POST 请求调用其 RESTful API。此方案可实现解耦，易于独立扩展、维护和升级，且可减轻主后端服务的资源压力。
-   **方案二: SDK 集成 (备选，适用于 Python 后端):** 如果 TTS 模型提供 Python SDK，且其推理速度快、资源消耗低，可以直接在后端应用中集成 SDK。优点是集成简单，减少服务间调用开销。缺点是耦合度较高，增加后端应用整体的资源占用和部署复杂性。

### 4.3 核心功能

- **构建请求:** 根据对话 AI 模型生成的文本回复和角色的声音设定/情感，构建 TTS 服务所需的请求（文本内容，声音风格参数等）。
- **发送请求:** 调用 TTS 服务。
- **处理响应:** 接收生成的音频数据。
- **错误处理:** 处理 TTS 调用过程中的错误。

### 4.4 细化项

-   **TTS 模型选择与微调:** 最终选择将基于实际测试和效果评估。可能需要对选定模型进行特定音色或情感表现的微调，以更好地匹配角色设定。
-   **TTS 服务部署与运维:** 确定部署环境（例如 Kubernetes, Docker Compose），并配置服务的负载均衡、自动伸缩和监控。
-   **角色声音设定与对话情感转化为 TTS 参数:** 
    -   **声音设定:** 角色的声音类型、音色 ID 等将作为 `settings` 字段（参考 `docs/tech_design/database_schema.md`）的一部分存储。在调用 TTS 服务时，将其作为必要参数传递。
    -   **对话情感:** 对话 AI 模型在生成回复文本时，应同时输出一个情感标签（例如 `neutral`, `joy`, `sad`, `angry`）。提示词工程模块或对话 AI 模块负责将此情感标签映射为 TTS 模型支持的**情感参数或风格编码**，以实现情感语音合成。这可能需要对话 AI 模型具备情感识别或生成能力。
-   **生成的音频数据处理:** 生成的音频数据将**首先存储到云存储服务**（如阿里云 OSS），然后将云存储服务返回的**音频 URL** 传递给前端。前端直接通过 URL 播放。这样做可以避免实时流式传输的复杂性，并支持音频的缓存和重用，降低计算资源消耗。

## 5. 与对话 AI 模型/服务的集成

这部分将在确定对话 AI 模型后详细设计。考虑到中文二次元对话的特殊性，初步可以考虑以下模型：

-   **商用 API (备选):** 如科大讯飞星火大模型（如果其对话能力成熟）、文心一言等，优点是开箱即用，无需部署运维。缺点是成本可能较高，定制化能力有限。
-   **开源模型 (推荐):** 调研并评估针对中文对话或角色扮演进行优化的开源大模型，如 Llama 2、通义千问等，并考虑进行特定领域的微调。优点是可控性高，可深度定制，长期成本低。缺点是需要模型部署和运维能力。

核心职责类似图片生成：构建请求（包含角色设定、聊天历史、用户当前消息），发送请求，处理响应（AI 回复文本及情感标签），并处理错误。对话历史的上下文管理是关键。

## 6. 模块与其他模块的交互

- **接收来自:** 后端业务逻辑、提示词工程模块（提供提示词、设定、参数）。
- **发送给:** 后端业务逻辑（返回图片 URL、对话回复文本、音频 URL 等），错误处理模块。

## 7. 已实现文件

目前项目中已实现的AI服务集成模块相关文件：

- `backend-services/services/spark_api_client.py` - 星火API客户端，封装了与星火API的通信
- `backend-services/services/spark_auth_utils.py` - 星火API认证工具，实现签名鉴权机制
- `backend-services/services/spark_error_handler.py` - 星火API错误处理，负责处理和转换API错误
- `backend-services/services/spark_image_service.py` - 星火图像生成服务，提供图片生成的核心功能
- `backend-services/services/oss_client.py` - 对象存储客户端，用于存储生成的图片和音频

这些文件共同构成了AI服务集成模块的基础架构，实现了与星火API的集成以及图像生成和存储功能。后续将进一步扩展TTS和对话AI模型的集成。

这份文档是 AI 服务集成模块的初步设计，已对所有"待细化项"进行了具体补充。具体的实现细节将依赖于所选的外部服务特性和开源模型，并在开发过程中不断完善。我们将优先在 `File Storage` 和 `Error Handling & Logging` 文档中补充与之相关的更具体细节，以确保整体设计的一致性。 
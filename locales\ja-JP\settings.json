{"common": {"chat": {"avatar": {"desc": "カスタムアバター", "title": "アバター"}, "nickName": {"desc": "カスタムニックネーム", "placeholder": "ニックネームを入力してください", "title": "ニックネーム"}, "title": "チャット設定"}, "system": {"clear": {"action": "今すぐクリア", "alert": "すべてのセッションメッセージをクリアしてもよろしいですか？", "desc": "すべてのセッションとキャラクターデータをクリアします。これにはセッションリスト、キャラクターリスト、セッションメッセージなどが含まれます。", "success": "クリア成功", "tip": "操作は元に戻せません。クリア後はデータが復元できなくなりますので、慎重に操作してください。", "title": "すべてのセッションメッセージをクリア"}, "clearCache": {"action": "今すぐクリア", "alert": "すべてのキャッシュをクリアしてもよろしいですか？", "calculating": "キャッシュサイズを計算中...", "desc": "アプリがダウンロードしたデータキャッシュをクリアします。これにはキャラクターのモデルデータ、音声データ、ダンスのモデルデータ、音声データなどが含まれます。", "success": "クリア成功", "tip": "操作は取り消せません。クリア後はデータを再ダウンロードする必要がありますので、慎重に操作してください。", "title": "データキャッシュをクリア"}, "reset": {"action": "今すぐリセット", "alert": "すべてのシステム設定をリセットしてもよろしいですか？", "desc": "すべてのシステム設定をリセットします。これにはテーマ設定、チャット設定、言語モデル設定などが含まれます。", "success": "リセット成功", "tip": "操作は元に戻せません。リセット後はデータが復元できなくなりますので、慎重に操作してください。", "title": "システム設定をリセット"}, "title": "システム設定"}, "theme": {"backgroundEffect": {"desc": "背景効果をカスタマイズ", "glow": "グロー", "none": "背景なし", "title": "背景効果"}, "locale": {"auto": "システムに従う", "desc": "システム言語をカスタマイズ", "title": "言語"}, "neutralColor": {"desc": "異なる色合いのグレースケールをカスタマイズ", "title": "ニュートラルカラー"}, "primaryColor": {"desc": "テーマカラーをカスタマイズ", "title": "テーマカラー"}, "title": "テーマ設定"}, "title": "一般設定"}, "header": {"desc": "好みとモデル設定", "global": "グローバル設定", "session": "セッション設定", "sessionDesc": "役割設定とセッションの好み", "sessionWithName": "セッション設定 · {{name}}", "title": "設定"}, "llm": {"aesGcm": "あなたのキーとプロキシアドレスなどは <1>AES-GCM</1> 暗号化アルゴリズムを使用して暗号化されます", "apiKey": {"desc": "あなたの {{name}} APIキーを入力してください", "placeholder": "{{name}} APIキー", "title": "APIキー"}, "checker": {"button": "チェック", "desc": "APIキーとプロキシアドレスが正しく入力されているかテストします", "error": "チェックに失敗しました", "pass": "チェックに合格しました", "title": "接続性チェック"}, "customModelCards": {"addNew": "{{id}} モデルを作成して追加", "config": "モデルを設定", "confirmDelete": "このカスタムモデルを削除しようとしています。削除後は復元できませんので、慎重に操作してください。", "modelConfig": {"azureDeployName": {"extra": "Azure OpenAIで実際にリクエストされるフィールド", "placeholder": "Azureのモデルデプロイ名を入力してください", "title": "モデルデプロイ名"}, "displayName": {"placeholder": "モデルの表示名を入力してください（例：ChatGPT、GPT-4など）", "title": "モデル表示名"}, "files": {"extra": "現在のファイルアップロード実装は一つのハック手法に過ぎず、自己責任での試行に限られます。完全なファイルアップロード機能は今後の実装をお待ちください", "title": "ファイルアップロードをサポート"}, "functionCall": {"extra": "この設定はアプリ内の関数呼び出し機能のみを有効にします。関数呼び出しのサポートはモデル自体に完全に依存しますので、そのモデルの関数呼び出し機能の可用性を自分でテストしてください", "title": "関数呼び出しをサポート"}, "id": {"extra": "モデルラベルとして表示されます", "placeholder": "モデルIDを入力してください（例：gpt-4-turbo-previewまたはclaude-2.1）", "title": "モデルID"}, "modalTitle": "カスタムモデル設定", "tokens": {"title": "最大トークン数", "unlimited": "無制限"}, "vision": {"extra": "この設定はアプリ内の画像アップロード設定のみを有効にします。認識のサポートはモデル自体に完全に依存しますので、そのモデルの視覚認識機能の可用性を自分でテストしてください", "title": "視覚認識をサポート"}}}, "fetchOnClient": {"desc": "クライアントリクエストモードは、ブラウザから直接セッションリクエストを発信し、応答速度を向上させます", "title": "クライアントリクエストモードを使用"}, "fetcher": {"fetch": "モデルリストを取得", "fetching": "モデルリストを取得中...", "latestTime": "最終更新日時：{{time}}", "noLatestTime": "リストはまだ取得されていません"}, "helpDoc": "設定ガイド", "modelList": {"desc": "セッションで表示するモデルを選択します。選択したモデルはモデルリストに表示されます", "placeholder": "リストからモデルを選択してください", "title": "モデルリスト", "total": "利用可能なモデルは合計 {{count}} 件です"}, "proxyUrl": {"desc": "デフォルトのアドレスを除き、http(s)://を含める必要があります", "title": "APIプロキシアドレス"}, "title": "言語モデル", "waitingForMore": "さらに多くのモデルが <1>接続予定</1> ですので、今しばらくお待ちください"}, "systemAgent": {"customPrompt": {"addPrompt": "カスタムプロンプトを追加", "desc": "入力後、システムアシスタントは生成するコンテンツにカスタムプロンプトを使用します", "placeholder": "カスタムプロンプトを入力してください", "title": "カスタムプロンプト"}, "emotionAnalysis": {"label": "感情分析モデル", "modelDesc": "感情分析に使用するモデルを指定", "title": "自動感情分析を実行"}, "title": "システムエージェント"}, "touch": {"title": "タッチ設定"}, "tts": {"clientCall": {"desc": "有効にすると、クライアントが音声合成サービスを呼び出し、音声合成の速度が向上しますが、インターネットにアクセスする能力が必要です。", "title": "クライアント呼び出し"}, "title": "音声設定"}}
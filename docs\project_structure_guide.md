# 虚拟角色平台项目结构说明

本文档旨在帮助开发者和AI助手快速理解项目的整体结构，明确各目录的用途，避免在开发过程中的混淆。

## 项目概述

虚拟角色平台是一个结合了前端React应用和后端Django服务的全栈项目，允许用户创建、定制和与虚拟角色互动。项目使用星火API进行图像生成和对话AI，集成阿里云OSS进行文件存储，提供完整的用户认证、角色管理、聊天互动等功能。

## 技术栈

**前端：**
- React 19.1.0 + TypeScript
- Ant Design UI组件库
- Zustand状态管理
- Vite构建工具
- React Router路由管理

**后端：**
- Django 5.2 + Django REST Framework
- SQLite数据库
- 星火API集成（图像生成+对话）
- 阿里云OSS文件存储
- WebSocket实时通信

## 详细文件结构分析

### 前端部分 (`/virtual-character-platform-frontend/`)

#### 根目录配置文件
- **`package.json`** - 前端项目依赖管理，包含React、Ant Design、Zustand等核心依赖
- **`package-lock.json`** - 依赖版本锁定文件
- **`tsconfig.json`** - TypeScript编译配置
- **`vite.config.ts`** - Vite构建工具配置，包含开发服务器和API代理设置
- **`index.html`** - HTML入口文件
- **`Dockerfile`** - Docker容器化配置
- **`docker-compose.yml`** - Docker Compose编排配置
- **`nginx.conf`** - Nginx反向代理配置
- **`DEPLOYMENT.md`** - 部署说明文档
- **`README.md`** - 前端项目说明文档

#### 源代码目录 (`/src/`)

**主入口文件：**
- **`main.tsx`** - React应用入口，初始化错误处理和认证状态监听
- **`App.tsx`** - 主应用组件，包含路由配置和布局管理
- **`style.css`** - 全局样式文件
- **`vite-env.d.ts`** - Vite环境类型声明
- **`counter.ts`** - 计数器工具（示例代码）
- **`typescript.svg`** - TypeScript图标资源

**组件目录 (`/components/`)：**
- **`MainLayout.tsx`** - 主布局组件，包含Header、Sidebar、Footer
- **`Header.tsx`** - 顶部导航栏组件
- **`Sidebar.tsx`** - 侧边栏导航组件
- **`Footer.tsx`** - 页脚组件
- **`ErrorBoundary.tsx`** - React错误边界组件
- **`GlobalErrorHandler.tsx`** - 全局错误处理组件
- **`ErrorRecovery.tsx`** - 错误恢复组件
- **`OptimizedImage.tsx`** - 优化图片加载组件
- **`SafeContent.tsx`** - 安全内容渲染组件

**管理员组件 (`/components/admin/`)：**
- **`AdminLayout.tsx`** - 管理员界面布局
- **`AdminProtectedRoute.tsx`** - 管理员路由保护组件

**角色相关组件 (`/components/character/`)：**
- 角色创建、展示相关的专用组件

**页面目录 (`/pages/`)：**
- **`LoginPage.tsx`** - 用户登录页面
- **`CharacterCreationPage.tsx`** - 角色创建页面
- **`ChatPage.tsx`** - 聊天互动页面
- **`CommunityPage.tsx`** - 社区浏览页面
- **`TestPage.tsx`** - 功能测试页面
- **`ErrorTestPage.tsx`** - 错误测试页面

**管理员页面 (`/pages/admin/`)：**
- **`AdminLoginPage.tsx`** - 管理员登录页面
- **`DashboardPage.tsx`** - 管理员仪表盘
- **`CharacterListPage.tsx`** - 角色管理列表
- **`AdminCharacterDetailPage.tsx`** - 角色详情管理
- **`PromptListPage.tsx`** - 提示词模板列表
- **`PromptEditPage.tsx`** - 提示词编辑页面
- **`PromptTestPage.tsx`** - 提示词测试页面

**服务目录 (`/services/`)：**
- **`api.ts`** - API基础配置，包含axios实例和拦截器
- **`characterAPI.ts`** - 角色相关API接口封装
- **`adminAPI.ts`** - 管理员API接口封装
- **`errorService.ts`** - 前端错误处理服务

**状态管理 (`/store/`)：**
- **`authStore.ts`** - 用户认证状态管理（Zustand）
- **`adminAuthStore.ts`** - 管理员认证状态管理

**样式目录 (`/styles/`)：**
- **`character-creation.css`** - 角色创建页面样式
- **`chat.css`** - 聊天页面样式
- **`community.css`** - 社区页面样式
- **`login.css`** - 登录页面样式
- **`personality-selector.css`** - 性格选择器样式
- **`identity-selector.css`** - 身份选择器样式
- **`personality-identity-selector.css`** - 性格身份选择器样式
- **`optimized-image.css`** - 优化图片组件样式

**工具目录 (`/utils/`)：**
- **`security.ts`** - 安全相关工具函数

**其他配置：**
- **`eslint.config.js`** - ESLint代码规范配置
- **`public/vite.svg`** - Vite图标资源

### 后端部分

#### Django项目配置 (`/virtual_character_platform/`)
- **`__init__.py`** - Python包初始化文件
- **`settings.py`** - Django项目主配置文件，包含数据库、中间件、应用配置等
- **`urls.py`** - 主路由配置，定义API根路径和路由分发
- **`wsgi.py`** - WSGI服务器接口配置
- **`asgi.py`** - ASGI异步服务器接口配置

#### 核心应用模块 (`/core/`)
**主要文件：**
- **`__init__.py`** - 应用包初始化
- **`apps.py`** - Django应用配置，包含服务初始化逻辑
- **`models.py`** - 数据库模型定义（用户、角色、提示词模板等）
- **`views.py`** - API视图实现，包含角色、性格、身份等API
- **`urls.py`** - 应用路由配置
- **`serializers.py`** - DRF数据序列化器
- **`admin.py`** - Django管理后台配置
- **`tests.py`** - 单元测试
- **`signals.py`** - Django信号处理器
- **`validators.py`** - 数据验证器
- **`data_access.py`** - 数据访问层
- **`exception_handler.py`** - 全局异常处理器
- **`spark_api_auth.py`** - 星火API认证工具

**认证模块 (`/core/auth/`)：**
- **`__init__.py`** - 认证模块初始化
- **`urls.py`** - 认证相关路由
- **`views.py`** - 认证API视图（登录、注册、令牌刷新等）
- **`serializers.py`** - 认证数据序列化器
- **`services/`** - 认证业务逻辑服务
  - **`admin_service.py`** - 管理员服务
  - **`auth_service.py`** - 认证服务
  - **`token_service.py`** - 令牌管理服务

**角色模块 (`/core/character/`)：**
- **`__init__.py`** - 角色模块初始化
- **`urls.py`** - 角色相关路由
- **`views.py`** - 角色API视图（创建、获取、聊天等）
- **`serializers.py`** - 角色数据序列化器
- **`services/`** - 角色业务逻辑服务
  - **`character_service.py`** - 角色管理服务
  - **`chat_service.py`** - 聊天服务

**管理员模块 (`/core/admin/`)：**
- **`__init__.py`** - 管理员模块初始化
- **`urls.py`** - 管理员API路由
- **`views.py`** - 管理员API视图
- **`serializers.py`** - 管理员数据序列化器

**提示词工程 (`/core/prompt_engineering/`)：**
- **`__init__.py`** - 提示词模块初始化
- **`urls.py`** - 提示词相关路由
- **`views.py`** - 提示词API视图
- **`serializers.py`** - 提示词数据序列化器
- **`services/`** - 提示词业务逻辑
  - **`prompt_service.py`** - 提示词管理服务
  - **`template_service.py`** - 模板服务

**数据库迁移 (`/core/migrations/`)：**
- Django数据库迁移文件

**管理命令 (`/core/management/commands/`)：**
- 自定义Django管理命令

#### 后端服务模块 (`/backend-services/`)

**认证服务 (`/auth/`)：**
- **`__init__.py`** - 认证服务包初始化
- **`admin.py`** - Django管理后台配置
- **`apps.py`** - Django应用配置
- **`models.py`** - 认证相关数据模型
- **`views.py`** - 认证视图
- **`tests.py`** - 认证模块测试
- **`migrations/`** - 数据库迁移文件

**认证系统 (`/authentication/`)：**
- **`__init__.py`** - 认证系统包初始化
- **`admin.py`** - 管理后台配置
- **`apps.py`** - 应用配置
- **`models.py`** - 认证数据模型
- **`serializers.py`** - 数据序列化器
- **`views.py`** - 认证API视图
- **`urls.py`** - 认证路由配置
- **`tests.py`** - 测试用例
- **`migrations/`** - 数据库迁移

**核心服务 (`/services/`)：**
- **`__init__.py`** - 服务模块初始化，包含服务注册逻辑
- **`spark_api_client.py`** - 星火API客户端封装
- **`spark_image_service.py`** - 星火图像生成服务
- **`spark_dialogue_service.py`** - 星火对话服务，支持WebSocket通信
- **`spark_auth_utils.py`** - 星火API认证工具
- **`spark_error_handler.py`** - 星火API错误处理
- **`oss_client.py`** - 阿里云OSS存储客户端
- **`file_storage_service.py`** - 文件存储服务封装
- **`file_cleanup_service.py`** - 文件清理服务
- **`logging_service.py`** - 日志服务
- **`structured_logging.py`** - 结构化日志
- **`log_storage.py`** - 日志存储服务
- **`log_sanitizer.py`** - 日志数据清理
- **`alert_notifier.py`** - 告警通知服务
- **`error_response.py`** - 统一错误响应格式
- **`exceptions.py`** - 自定义异常类
- **`scheduled_tasks.py`** - 定时任务服务

**提示词工程 (`/services/prompt_engineering/`)：**
- **`template_loader.py`** - 提示词模板加载器
- **`prompt_optimizer.py`** - 提示词优化器
- **`template_validator.py`** - 模板验证器

**组件目录 (`/components/`)：**
- 可复用的后端组件（当前为空）

**状态管理 (`/stores/`)：**
- **`authStore.ts`** - 认证状态存储（注意：此文件位置不正确，应在前端项目中）

#### 项目根目录文件

**Django相关：**
- **`manage.py`** - Django项目管理脚本
- **`requirements.txt`** - Python依赖包列表
- **`db.sqlite3`** - SQLite数据库文件
- **`pyrightconfig.json`** - Pyright类型检查配置

**测试文件：**
- **`test_api.py`** - API测试脚本
- **`test_api_connection.py`** - API连接测试
- **`test_frontend_api.html`** - 前端API测试页面
- **`test_frontend_backend.py`** - 前后端集成测试
- **`test_register.py`** - 注册功能测试
- **`test_spark_dialogue.py`** - 星火对话服务测试
- **`test_spark_simple.py`** - 星火API简单测试

**工具脚本：**
- **`create_admin.py`** - 创建管理员用户脚本
- **`create_test_user.py`** - 创建测试用户脚本
- **`X1_ws.py`** - WebSocket测试脚本

**配置文件：**
- **`package.json`** - 根目录依赖配置（冗余文件，不建议使用）
- **`package-lock.json`** - 依赖锁定文件（冗余）

**文档目录：**

**开发任务文档 (`/doc/`)：**
- **`development_houduan.md`** - 后端开发文档
- **`development_tasks_ai_service_integration.md`** - AI服务集成任务
- **`development_tasks_backend_api_design.md`** - 后端API设计任务
- **`development_tasks_content_moderation.md`** - 内容审核任务
- **`development_tasks_data_storage.md`** - 数据存储任务
- **`development_tasks_database_schema.md`** - 数据库设计任务
- **`development_tasks_error_handling_logging.md`** - 错误处理和日志任务
- **`development_tasks_file_storage.md`** - 文件存储任务
- **`development_tasks_frontend_design.md`** - 前端设计任务
- **`development_tasks_personality_identity.md`** - 性格身份系统任务
- **`development_tasks_prompt_engineering.md`** - 提示词工程任务
- **`development_tasks_user_auth_authorization.md`** - 用户认证授权任务

**技术文档 (`/docs/`)：**
- **`project_structure_guide.md`** - 项目结构说明（本文档）
- **`oss_setup_guide.md`** - OSS配置指南
- **`ai_service_integration_completed.md`** - AI服务集成完成文档
- **`tech_design/`** - 技术设计文档目录
  - **`frontend_design.md`** - 前端技术设计
- **`bugs/`** - 已知问题记录目录
- **`security/`** - 安全相关文档目录

**产品文档：**
- **`PRD(1).md`** - 产品需求文档

**其他目录：**
- **`/venv/`** - Python虚拟环境
- **`/logs/`** - 日志文件目录
  - **`access.log`** - 访问日志
  - **`app.log`** - 应用日志
  - **`django.log`** - Django日志
  - **`error.log`** - 错误日志
  - **`archive/`** - 日志归档目录
- **`/__pycache__/`** - Python字节码缓存

## 核心功能模块说明

### 用户认证系统
- **前端：** `authStore.ts`、`LoginPage.tsx`、认证相关API服务
- **后端：** `/core/auth/`模块，提供JWT令牌认证、用户注册登录等功能

### 角色管理系统
- **前端：** `CharacterCreationPage.tsx`、角色相关组件和API
- **后端：** `/core/character/`模块，处理角色创建、存储、检索等

### 聊天互动系统
- **前端：** `ChatPage.tsx`、聊天相关组件
- **后端：** 星火对话服务(`spark_dialogue_service.py`)，支持WebSocket实时通信

### 图像生成系统
- **后端：** 星火图像服务(`spark_image_service.py`)，集成星火API进行AI图像生成

### 文件存储系统
- **后端：** OSS客户端(`oss_client.py`)，集成阿里云对象存储服务

### 管理员系统
- **前端：** `/pages/admin/`、`/components/admin/`、`adminAuthStore.ts`
- **后端：** `/core/admin/`模块，提供管理员认证和管理功能

### 提示词工程系统
- **前端：** 提示词管理相关页面
- **后端：** `/core/prompt_engineering/`和`/services/prompt_engineering/`

## 开发指南

### 环境配置
1. **前端开发环境：**
   ```bash
   cd virtual-character-platform-frontend
   npm install
   npm run dev
   ```

2. **后端开发环境：**
   ```bash
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py runserver
   ```

### API开发规范
- 所有API路由在`/core/urls.py`中定义
- API视图使用Django REST Framework
- 统一的错误处理通过`exception_handler.py`实现
- API响应格式通过`error_response.py`标准化

### 前端开发规范
- 使用TypeScript进行类型安全开发
- 组件按功能模块组织在`/components/`目录
- 页面组件放在`/pages/`目录
- 状态管理使用Zustand
- API调用统一通过`/services/`目录中的服务封装

### 测试策略
- 后端单元测试：各模块的`tests.py`文件
- API集成测试：根目录的`test_*.py`文件
- 前端测试：`ErrorTestPage.tsx`等测试页面

## 重要注意事项

### 文件位置问题
- **错误：** `backend-services/stores/authStore.ts` - TypeScript文件不应在后端目录
- **正确：** 所有前端代码应在`virtual-character-platform-frontend/src/`目录下

### 依赖管理
- **前端依赖：** 使用`virtual-character-platform-frontend/package.json`
- **后端依赖：** 使用根目录的`requirements.txt`
- **避免使用：** 根目录的`package.json`（冗余文件）

### 功能重复警告
项目中存在一些功能重复的文件，开发时需要确认使用正确的版本：
- 星火API认证：`core/spark_api_auth.py` vs `backend-services/services/spark_auth_utils.py`
- 在开发过程中，建议统一使用`backend-services/services/`中的版本

### 日志和错误处理
- 前端错误：通过`errorService.ts`和错误边界组件处理
- 后端错误：通过`exception_handler.py`统一处理
- 日志存储：使用结构化日志服务，支持日志归档和清理

## 部署相关

### 前端部署
- 使用Docker容器化部署
- Nginx作为反向代理服务器
- 支持生产环境构建优化

### 后端部署
- Django WSGI/ASGI部署
- SQLite数据库（可扩展为PostgreSQL/MySQL）
- 集成阿里云OSS文件存储
- 星火API服务集成

## 项目历史

项目经历了重要的结构重构：
- **原结构：** 代码混合在根目录`/src/`下
- **现结构：** 前后端代码清晰分离
- **迁移：** 原`/src/`代码移至`/backend-services/`
- **建议：** 遵循当前结构，避免在根目录创建新的业务代码文件
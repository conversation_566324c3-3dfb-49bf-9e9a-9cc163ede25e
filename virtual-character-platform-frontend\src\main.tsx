import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './style.css';
import ErrorBoundary from './components/ErrorBoundary';
import GlobalErrorHandler from './components/GlobalErrorHandler';
import { errorService } from './services/errorService';
import useAuthStore from './store/authStore';

// 初始化错误处理服务
errorService.init();

// 当用户登录状态变化时，更新用户ID
useAuthStore.subscribe(
  (state) => {
    if (state.isLoggedIn && state.userInfo?.id) {
      errorService.setUserId(state.userInfo.id);
    }
  }
);

ReactDOM.createRoot(document.getElementById('app')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <GlobalErrorHandler />
      <App />
    </ErrorBoundary>
  </React.StrictMode>,
);

// 瞬きで目を閉じている時間(sec)
export const BLINK_CLOSE_MAX = 0.12;
// 瞬きで目を開いている時間(sec)
export const BLINK_OPEN_MAX = 5;

// 表情预设名称映射
export const EMOTION_PRESETS = {
  neutral: 'neutral',
  happy: 'happy',
  angry: 'angry',
  sad: 'sad',
  relaxed: 'relaxed',
  surprised: 'surprised'
} as const;

// 口型同步预设
export const LIP_SYNC_PRESETS = {
  aa: 'aa',
  ih: 'ih', 
  ou: 'ou',
  ee: 'ee',
  oh: 'oh'
} as const;

// 情感到表情的映射
export const EMOTION_TO_EXPRESSION_MAP = {
  'happy': 'happy',
  'sad': 'sad',
  'caring': 'relaxed',
  'listening': 'neutral',
  'thinking': 'neutral',
  'neutral': 'neutral',
  'excited': 'happy',
  'frustrated': 'angry'
} as const;

export type EmotionType = keyof typeof EMOTION_TO_EXPRESSION_MAP;
export type ExpressionType = typeof EMOTION_TO_EXPRESSION_MAP[EmotionType];

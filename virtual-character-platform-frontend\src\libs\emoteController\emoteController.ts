import { VRM, VRMExpressionPresetName } from '@pixiv/three-vrm';
import * as THREE from 'three';

import { ExpressionController } from './expressionController';
import { MotionController } from './motionController';
import type { MotionPresetName } from './motionController';
import type { MotionFileType } from './motionPresetMap';
import type { EmotionType } from './emoteConstants';

/**
 * 情感表达控制器
 * 统一管理表情和动作的类
 */
export class EmoteController {
  private _expressionController: ExpressionController;
  private _motionController: MotionController;
  private _vrm: VRM;
  private _camera: THREE.Object3D;

  constructor(vrm: VRM, camera: THREE.Object3D) {
    this._vrm = vrm;
    this._camera = camera;
    this._expressionController = new ExpressionController(vrm, camera);
    this._motionController = new MotionController(vrm);
  }

  /**
   * 播放情感表情
   */
  public playEmotion(preset: VRMExpressionPresetName): void {
    this._expressionController.playEmotion(preset);
  }

  /**
   * 根据情感类型播放表情和动作
   */
  public playEmotionByType(emotionType: EmotionType): void {
    console.log(`EmoteController: 播放情感 ${emotionType}`);
    
    // 播放对应的表情
    this._expressionController.playEmotionByType(emotionType);
    
    // 播放对应的动作
    const motionMap: Record<EmotionType, MotionPresetName> = {
      'happy': 'happy',
      'sad': 'sad',
      'caring': 'idle',
      'listening': 'idle',
      'thinking': 'thinking',
      'neutral': 'idle',
      'excited': 'happy',
      'frustrated': 'sad'
    };
    
    const motion = motionMap[emotionType] || 'idle';
    this._motionController.playMotion(motion);
  }

  /**
   * 播放动作
   */
  public playMotion(preset: MotionPresetName, loop: boolean = true): void {
    this._motionController.playMotion(preset, loop);
  }

  /**
   * 通过URL播放动作
   */
  public async playMotionUrl(fileType: MotionFileType, url: string, loop: boolean = true): Promise<void> {
    return this._motionController.playMotionUrl(fileType, url, loop);
  }

  /**
   * 预加载动作
   */
  public async preloadMotion(motion: MotionPresetName): Promise<void> {
    return this._motionController.preloadMotion(motion);
  }

  /**
   * 通过URL预加载动作
   */
  public async preloadMotionUrl(fileType: MotionFileType, url: string): Promise<void> {
    return this._motionController.preloadMotionUrl(fileType, url);
  }

  /**
   * 口型同步
   */
  public lipSync(preset: VRMExpressionPresetName, value: number): void {
    this._expressionController.lipSync(preset, value);
  }

  /**
   * 基于音量的口型同步
   */
  public lipSyncFromVolume(volume: number): void {
    this._expressionController.lipSyncFromVolume(volume);
  }

  /**
   * 设置视线目标
   */
  public setLookAtTarget(position: THREE.Vector3): void {
    this._expressionController.setLookAtTarget(position);
  }

  /**
   * 看向摄像机
   */
  public lookAtCamera(): void {
    const cameraPosition = new THREE.Vector3();
    this._camera.getWorldPosition(cameraPosition);
    this.setLookAtTarget(cameraPosition);
  }

  /**
   * 启用/禁用视线跟踪
   */
  public setLookAtEnabled(enabled: boolean): void {
    this._expressionController.setLookAtEnabled(enabled);
  }

  /**
   * 启用/禁用自动眨眼
   */
  public setAutoBlinkEnabled(enabled: boolean): void {
    this._expressionController.setAutoBlinkEnabled(enabled);
  }

  /**
   * 重置到默认状态
   */
  public reset(): void {
    this.playEmotion('neutral');
    this.playMotion('idle');
    this.setAutoBlinkEnabled(true);
    this.setLookAtEnabled(true);
  }

  /**
   * 播放说话状态
   */
  public startTalking(): void {
    this.playMotion('talking');
  }

  /**
   * 停止说话状态
   */
  public stopTalking(): void {
    this.playMotion('idle');
  }

  /**
   * 更新控制器
   */
  public update(delta: number): void {
    this._expressionController.update(delta);
    this._motionController.update(delta);
  }

  // 获取当前状态
  public getCurrentEmotion(): VRMExpressionPresetName {
    return this._expressionController.getCurrentEmotion();
  }

  public getCurrentMotion(): MotionPresetName {
    return this._motionController.getCurrentMotion();
  }

  public isAutoBlinkEnabled(): boolean {
    return this._expressionController.isAutoBlinkEnabled();
  }

  public isLookAtEnabled(): boolean {
    return this._expressionController.isLookAtEnabled();
  }

  public isMotionPlaying(): boolean {
    return this._motionController.isPlaying();
  }

  // 便捷方法
  public showHappiness(): void {
    this.playEmotionByType('happy');
  }

  public showSadness(): void {
    this.playEmotionByType('sad');
  }

  public showCaring(): void {
    this.playEmotionByType('caring');
  }

  public showListening(): void {
    this.playEmotionByType('listening');
  }

  public showThinking(): void {
    this.playEmotionByType('thinking');
  }

  public showNeutral(): void {
    this.playEmotionByType('neutral');
  }
}

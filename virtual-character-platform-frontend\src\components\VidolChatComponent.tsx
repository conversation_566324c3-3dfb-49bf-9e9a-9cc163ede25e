/**
 * VidolChatComponent - AgentViewer适配器
 * 
 * 这是一个适配器组件，将原有的VidolChatComponent接口适配到新的AgentViewer组件
 * 保持向后兼容性的同时，提供更强大的3D交互功能
 * 
 * 迁移时间: 2024-12-28
 * 替换组件: Features/AgentViewer
 */

import React, { useEffect, useMemo } from 'react';
import AgentViewer from '../features/AgentViewer';
import { useAgentStore } from '../store/agent';
import type { Agent } from '../types/agent';

interface VidolChatComponentProps {
  character: {
    id: string;
    name: string;
    vrmModelUrl?: string;
    settings?: {
      voice_type?: string;
      animation_style?: string;
    };
  };
  isVisible: boolean;
  onVoiceInput?: (text: string) => void;
  onAnimationComplete?: () => void;
  // 语音播放相关
  audioUrl?: string;
  isImmersiveMode?: boolean;
  hideUI?: boolean;
  emotion?: string;
  onLipSyncUpdate?: (volume: number) => void;
  // 新增：AgentViewer特有属性
  height?: string | number;
  width?: string | number;
  interactive?: boolean;
}

/**
 * VidolChatComponent适配器实现
 * 将原有接口适配到AgentViewer组件
 */
const VidolChatComponent: React.FC<VidolChatComponentProps> = ({
  character,
  isVisible,
  onVoiceInput,
  onAnimationComplete,
  audioUrl,
  isImmersiveMode = false,
  hideUI = false,
  emotion = 'neutral',
  onLipSyncUpdate,
  height = '100%',
  width = '100%',
  interactive = true
}) => {
  const { addLocalAgent, getAgentById } = useAgentStore();

  // 将character数据转换为Agent格式
  const agent = useMemo((): Agent => {
    return {
      agentId: character.id,
      identifier: character.id,
      meta: {
        name: character.name,
        description: character.name,
        model: character.vrmModelUrl || '',
        readme: '',
        tags: [],
        avatar: '',
        cover: '',
        gender: 'Female' as any,
      },
      config: {
        systemRole: '',
        params: {},
        tts: {
          voice: character.settings?.voice_type || 'zh-CN-XiaoxiaoNeural',
          speed: 1,
          pitch: 0,
        },
        touch: {
          enabled: true,
          areas: [],
        },
      },
      createAt: new Date().toISOString(),
      updateAt: Date.now(),
    };
  }, [character]);

  // 确保agent存在于store中
  useEffect(() => {
    const existingAgent = getAgentById(character.id);
    if (!existingAgent) {
      addLocalAgent(agent);
    }
  }, [agent, character.id, addLocalAgent, getAgentById]);

  // 如果不可见，返回null
  if (!isVisible) {
    return null;
  }

  // 渲染AgentViewer组件
  return (
    <div 
      style={{ 
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
        position: 'relative'
      }}
    >
      <AgentViewer
        agentId={character.id}
        interactive={interactive}
        toolbar={!hideUI}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: isImmersiveMode ? '0' : '8px',
        }}
      />
    </div>
  );
};

export default VidolChatComponent;

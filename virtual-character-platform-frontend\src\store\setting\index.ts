// 类型导入
import type { NeutralColors, PrimaryColors } from '@lobehub/ui';
import type { DeepPartial } from 'utility-types';
import type { PersistOptions } from 'zustand/middleware';
import type { StateCreator } from 'zustand/vanilla';

// 值导入
import { produce } from 'immer';
import { isEqual } from 'lodash-es';
import {
  createJSONStorage,
  devtools,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';

// 类型导入
import type { ModelListAction } from '../../store/setting/slices/modelList';
import type { TouchStore } from '../../store/setting/slices/touch';
import type { SystemAgentConfigKey, SystemAgentItem } from '../../types/agent';
import type {
  BackgroundEffect,
  Config,
  NotificationConfig,
  PrivacyConfig,
  AccessibilityConfig,
  TTSConfig
} from '../../types/config';
import type { LocaleMode } from '../../types/locale';
import type { SettingState } from './initialState';

// 值导入
import { ModelProvider } from '../../libs/agent-runtime/types/type';
import { createModelListSlice } from '../../store/setting/slices/modelList';
import createTouchStore from '../../store/setting/slices/touch';
import { mergeWithUndefined } from '../../utils/common';
import { vidolStorage } from '../../utils/storage';
import { switchLang } from '../../utils/switchLang';
import { initialState } from './initialState';

export const SETTING_STORAGE_KEY = 'vidol-chat-config-storage';

export interface SettingAction extends TouchStore {
  /**
   * Reset config
   */
  resetConfig: () => void;
  /**
   * Set avatar
   * @param avatar
   */
  setAvatar: (avatar: string) => void;
  /**
   * Set background effect
   * @param backgroundEffect
   */
  setBackgroundEffect: (backgroundEffect: BackgroundEffect) => void;
  /**
   * Set config
   * @param config
   */
  setConfig: (config: DeepPartial<Config>) => void;
  /**
   * Set neutral color
   * @param neutralColor
   */
  setNeutralColor: (neutralColor: NeutralColors) => void;
  /**
   * Set nick name
   * @param nickName
   */
  setNickName: (nickName: string) => void;

  /**
   * Set primary color
   * @param primaryColor
   */
  setPrimaryColor: (primaryColor: PrimaryColors) => void;

  /**
   * Switch locale
   * @param locale
   */
  switchLocale: (locale: LocaleMode) => void;

  /**
   * Update system agent
   * @param key
   * @param value
   */
  updateSystemAgent: (key: SystemAgentConfigKey, value: Partial<SystemAgentItem>) => Promise<void>;

  /**
   * Update notifications settings
   * @param notifications
   */
  updateNotifications: (notifications: Partial<NotificationConfig>) => void;

  /**
   * Update privacy settings
   * @param privacy
   */
  updatePrivacy: (privacy: Partial<PrivacyConfig>) => void;

  /**
   * Update accessibility settings
   * @param accessibility
   */
  updateAccessibility: (accessibility: Partial<AccessibilityConfig>) => void;

  /**
   * Update TTS settings
   * @param tts
   */
  updateTTS: (tts: Partial<TTSConfig>) => void;

  /**
   * Validate settings
   * Checks if settings are valid and returns errors if any
   */
  validateSettings: () => { valid: boolean; errors: string[] };
}

export interface SettingStore extends SettingState, SettingAction, ModelListAction {}

const createStore: StateCreator<SettingStore, [['zustand/devtools', never]], [], SettingStore> = (
  set,
  get,
  store,
) => ({
  ...initialState,
  ...createTouchStore(set, get, store),
  ...createModelListSlice(set, get, store),
  resetConfig: () => {
    localStorage.removeItem(SETTING_STORAGE_KEY);
    set({ ...initialState });
    get().refreshDefaultModelProviderList();
  },
  setAvatar: (avatar) => {
    get().setConfig({ avatar });
  },
  setPrimaryColor: (primaryColor) => {
    get().setConfig({ primaryColor });
  },
  setNeutralColor: (neutralColor) => {
    get().setConfig({ neutralColor });
  },
  setBackgroundEffect: (backgroundEffect) => {
    get().setConfig({ backgroundEffect });
  },
  setNickName: (nickName) => {
    get().setConfig({ nickName });
  },
  switchLocale: (locale) => {
    get().setConfig({ locale });
    switchLang(locale);
  },
  updateSystemAgent: async (key, value) => {
    get().setConfig({
      systemAgent: { [key]: { ...value } },
    });
  },
  updateNotifications: (notifications) => {
    get().setConfig({
      notifications: { ...get().config.notifications, ...notifications },
    });
  },
  updatePrivacy: (privacy) => {
    get().setConfig({
      privacy: { ...get().config.privacy, ...privacy },
    });
  },
  updateAccessibility: (accessibility) => {
    get().setConfig({
      accessibility: { ...get().config.accessibility, ...accessibility },
    });
  },
  updateTTS: (tts) => {
    get().setConfig({
      tts: { ...get().config.tts, ...tts },
    });
  },
  validateSettings: () => {
    const config = get().config;
    const errors: string[] = [];

    // 验证TTS设置
    if (config.tts?.speed && (config.tts.speed < 0.1 || config.tts.speed > 3.0)) {
      errors.push('TTS speed must be between 0.1 and 3.0');
    }

    if (config.tts?.pitch && (config.tts.pitch < 0.1 || config.tts.pitch > 2.0)) {
      errors.push('TTS pitch must be between 0.1 and 2.0');
    }

    // 验证无障碍设置
    if (config.accessibility?.fontSize &&
        !['small', 'medium', 'large'].includes(config.accessibility.fontSize)) {
      errors.push('Invalid font size value');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  },
  setConfig: (config) => {
    const prevSetting = get().config;
    const nextSetting = produce(prevSetting, (draftState: Config) => {
      mergeWithUndefined(draftState, config);
    });
    if (isEqual(prevSetting, nextSetting)) return;
    set({ config: nextSetting });

    get().refreshDefaultModelProviderList();
  },
});

/**
 * 迁移旧版SettingsPage数据到新的SettingStore
 * 用于将现有的设置页面数据无缝迁移到新的设置系统
 */
export const migrateOldSettings = (): Partial<Config> => {
  try {
    // 尝试从localStorage读取旧的设置数据
    const oldSettingsStr = localStorage.getItem('settings');
    if (!oldSettingsStr) return {};

    const oldSettings = JSON.parse(oldSettingsStr);

    // 构建新的配置对象
    const newConfig: Partial<Config> = {};

    // 迁移主题设置
    if (oldSettings.theme) {
      // 主题设置已经通过themeStore处理，这里不需要额外处理
    }

    // 迁移语言设置
    if (oldSettings.language) {
      newConfig.locale = oldSettings.language === 'en-US' ? 'en-US' : 'zh-CN';
    }

    // 迁移通知设置
    if (oldSettings.notifications) {
      newConfig.notifications = {
        email: oldSettings.notifications.email || false,
        push: oldSettings.notifications.push || false,
        chat: oldSettings.notifications.chat || false,
        system: oldSettings.notifications.system || false,
      };
    }

    // 迁移隐私设置
    if (oldSettings.privacy) {
      newConfig.privacy = {
        profilePublic: oldSettings.privacy.profilePublic || false,
        charactersPublic: oldSettings.privacy.charactersPublic || false,
        chatHistory: oldSettings.privacy.chatHistory || false,
      };
    }

    // 迁移无障碍设置
    if (oldSettings.accessibility) {
      newConfig.accessibility = {
        fontSize: oldSettings.accessibility.fontSize || 'medium',
        highContrast: oldSettings.accessibility.highContrast || false,
        reduceMotion: oldSettings.accessibility.reduceMotion || false,
      };
    }

    return newConfig;
  } catch (error) {
    console.error('Failed to migrate old settings:', error);
    return {};
  }
};

const persistOptions: PersistOptions<SettingStore> = {
  name: SETTING_STORAGE_KEY, // name of the item in the storage (must be unique)
  storage: createJSONStorage(() => vidolStorage),
  version: 2, // 版本升级到2，支持新的迁移逻辑
  migrate: (persistedState: unknown, version: number): SettingStore => {
    if (version === 0) {
      const state = persistedState as SettingStore;
      if (state.config.languageModel) {
        state.config.languageModel = {
          ...state.config.languageModel,
          [ModelProvider.OpenAI]: {
            //@ts-ignore
            ...state.config.languageModel['openAI'],
          },
        };
        state.config.languageModel[ModelProvider.OpenAI]!.fetchOnClient =
          //@ts-ignore
          state.config.languageModel['openAI']!.fetchOnClient;
        state.config.keyVaults = {
          ...state.config.keyVaults,
          [ModelProvider.OpenAI]: {
            //@ts-ignore
            apiKey: state.config.languageModel['openAI']!.apiKey,
            //@ts-ignore
            baseURL: state.config.languageModel['openAI']!.endpoint,
          },
        };
        //@ts-ignore
        delete state.config.languageModel['openAI'];
      }
      return state;
    }

    // 版本1迁移到版本2
    if (version === 1) {
      const state = persistedState as SettingStore;

      // 迁移旧版设置
      const oldSettings = migrateOldSettings();

      // 合并设置
      if (Object.keys(oldSettings).length > 0) {
        state.config = {
          ...state.config,
          ...oldSettings,
        };

        // 迁移完成后删除旧的设置数据
        localStorage.removeItem('settings');
      }

      return state;
    }

    return persistedState as SettingStore;
  },
};

export const useSettingStore = createWithEqualityFn<SettingStore>()(
  subscribeWithSelector(
    persist(
      devtools(createStore, {
        name: 'VIDOL_CONFIG_STORE',
      }),
      persistOptions,
    ),
  ),
  shallow,
);

export { configSelectors } from './selectors/config';

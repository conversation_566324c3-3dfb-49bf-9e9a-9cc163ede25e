import React, { useState } from 'react';
import { Form, Select, Slider, Button, Card, Space, message } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../../store/agent';

const { Option } = Select;

// 可用的语音选项
const VOICE_OPTIONS = [
  { value: 'zh-CN-XiaoxiaoNeural', label: '晓晓 (女性，温柔)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-YunxiNeural', label: '云希 (男性，温和)', gender: 'male', language: 'zh-CN' },
  { value: 'zh-CN-YunyangNeural', label: '云扬 (男性，稳重)', gender: 'male', language: 'zh-CN' },
  { value: 'zh-CN-XiaoyiNeural', label: '晓伊 (女性，甜美)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-YunjianNeural', label: '云健 (男性，活力)', gender: 'male', language: 'zh-CN' },
  { value: 'zh-CN-XiaochenNeural', label: '晓辰 (女性，清新)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-XiaohanNeural', label: '晓涵 (女性，知性)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-XiaomengNeural', label: '晓梦 (女性，可爱)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-XiaomoNeural', label: '晓墨 (女性，成熟)', gender: 'female', language: 'zh-CN' },
  { value: 'zh-CN-XiaoqiuNeural', label: '晓秋 (女性，温暖)', gender: 'female', language: 'zh-CN' },
];

const VoiceTab: React.FC = () => {
  const [form] = Form.useForm();
  const [isPlaying, setIsPlaying] = useState(false);
  const [testText, setTestText] = useState('你好，我是你的AI助手，很高兴为你服务。');
  
  // 获取当前角色数据
  const [currentAgent, updateAgentConfig] = useAgentStore((s) => [
    agentSelectors.currentAgentItem(s),
    s.updateAgentConfig,
  ]);

  const currentTTS = currentAgent?.tts || {
    voice: 'zh-CN-XiaoxiaoNeural',
    speed: 1,
    pitch: 0,
  };

  // 处理语音设置变更
  const handleVoiceChange = (field: string, value: any) => {
    const newTTS = {
      ...currentTTS,
      [field]: value,
    };
    updateAgentConfig({ tts: newTTS });
  };

  // 测试语音
  const handleTestVoice = async () => {
    if (isPlaying) {
      // 停止播放
      setIsPlaying(false);
      // 这里应该停止当前的语音播放
      return;
    }

    try {
      setIsPlaying(true);
      
      // 创建语音合成
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(testText);
        utterance.rate = currentTTS.speed;
        utterance.pitch = currentTTS.pitch;
        
        // 尝试设置语音（浏览器支持有限）
        const voices = speechSynthesis.getVoices();
        const selectedVoice = voices.find(voice => 
          voice.name.includes('Chinese') || voice.lang.includes('zh')
        );
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
        
        utterance.onend = () => {
          setIsPlaying(false);
        };
        
        utterance.onerror = () => {
          setIsPlaying(false);
          message.error('语音播放失败');
        };
        
        speechSynthesis.speak(utterance);
      } else {
        message.error('浏览器不支持语音合成');
        setIsPlaying(false);
      }
    } catch (error) {
      console.error('语音测试失败:', error);
      message.error('语音测试失败');
      setIsPlaying(false);
    }
  };

  // 停止语音播放
  const handleStopVoice = () => {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
    setIsPlaying(false);
  };

  return (
    <div className="voice-tab">
      <Flexbox gap={24} style={{ padding: '24px' }}>
        {/* 语音选择 */}
        <div className="voice-section">
          <h3>语音选择</h3>
          <p className="section-desc">选择适合角色的语音类型</p>
          
          <Form layout="vertical">
            <Form.Item label="语音类型">
              <Select
                value={currentTTS.voice}
                onChange={(value) => handleVoiceChange('voice', value)}
                placeholder="选择语音类型"
              >
                {VOICE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>{option.label}</span>
                      <span style={{ color: '#999', fontSize: '12px' }}>
                        {option.gender === 'female' ? '女性' : '男性'}
                      </span>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </div>

        {/* 语音参数调节 */}
        <div className="voice-section">
          <h3>语音参数</h3>
          <p className="section-desc">调节语音的速度和音调</p>
          
          <Form layout="vertical">
            <Form.Item label={`语音速度: ${currentTTS.speed}`}>
              <Slider
                min={0.5}
                max={2}
                step={0.1}
                value={currentTTS.speed}
                onChange={(value) => handleVoiceChange('speed', value)}
                marks={{
                  0.5: '慢',
                  1: '正常',
                  2: '快'
                }}
              />
            </Form.Item>

            <Form.Item label={`音调: ${currentTTS.pitch}`}>
              <Slider
                min={-1}
                max={1}
                step={0.1}
                value={currentTTS.pitch}
                onChange={(value) => handleVoiceChange('pitch', value)}
                marks={{
                  '-1': '低',
                  0: '正常',
                  1: '高'
                }}
              />
            </Form.Item>
          </Form>
        </div>

        {/* 语音测试 */}
        <div className="voice-section">
          <h3>语音测试</h3>
          <p className="section-desc">测试当前语音设置效果</p>
          
          <Card>
            <div className="voice-test">
              <div style={{ marginBottom: 16 }}>
                <label>测试文本:</label>
                <textarea
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  rows={3}
                  style={{ 
                    width: '100%', 
                    marginTop: 8,
                    padding: 8,
                    border: '1px solid #d9d9d9',
                    borderRadius: 4,
                    resize: 'vertical'
                  }}
                  placeholder="输入要测试的文本..."
                />
              </div>
              
              <Space>
                <Button
                  type="primary"
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={handleTestVoice}
                  loading={isPlaying}
                >
                  {isPlaying ? '停止播放' : '测试语音'}
                </Button>
                
                {isPlaying && (
                  <Button onClick={handleStopVoice}>
                    停止
                  </Button>
                )}
              </Space>
            </div>
          </Card>
        </div>

        {/* 语音配置说明 */}
        <div className="voice-section">
          <h3>配置说明</h3>
          <Card>
            <div className="voice-guide">
              <h4>语音选择建议：</h4>
              <ul>
                <li><strong>角色匹配</strong>: 根据角色的性别和性格选择合适的语音</li>
                <li><strong>场景适配</strong>: 考虑使用场景选择正式或轻松的语音风格</li>
                <li><strong>用户偏好</strong>: 可以提供多个语音选项供用户选择</li>
              </ul>

              <h4>参数调节说明：</h4>
              <ul>
                <li><strong>语音速度</strong>: 0.5-2倍速，建议保持在0.8-1.2之间</li>
                <li><strong>音调</strong>: -1到1，0为正常音调</li>
                <li><strong>测试重要性</strong>: 调节后务必测试效果，确保自然流畅</li>
              </ul>

              <h4>注意事项：</h4>
              <ul>
                <li>不同浏览器对语音合成的支持程度不同</li>
                <li>建议在主要目标浏览器中测试语音效果</li>
                <li>语音参数过极端可能影响理解度</li>
                <li>考虑为用户提供语音开关选项</li>
              </ul>
            </div>
          </Card>
        </div>
      </Flexbox>
    </div>
  );
};

export default VoiceTab;

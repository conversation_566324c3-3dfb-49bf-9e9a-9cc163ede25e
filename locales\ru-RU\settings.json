{"common": {"chat": {"avatar": {"desc": "Настроить аватар", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nickName": {"desc": "Настроить псевдоним", "placeholder": "Введите псевдоним", "title": "Псевдоним"}, "title": "Настройки чата"}, "system": {"clear": {"action": "Очистить сейчас", "alert": "Подтвердите очистку всех сообщений чата?", "desc": "Это приведет к удалению всех данных чата и персонажей, включая список чатов, список персонажей, сообщения чата и т.д.", "success": "Очистка успешна", "tip": "Операция не может быть отменена, после очистки данные не могут быть восстановлены, пожалуйста, действуйте осторожно", "title": "Очистить все сообщения чата"}, "clearCache": {"action": "Очистить сейчас", "alert": "Подтвердите очистку всего кэша?", "calculating": "Подсчет размера кэша...", "desc": "Это приведет к очистке кэша данных приложения, включая модельные данные персонажей, звуковые данные, модельные данные танцев, аудиоданные и т. д.", "success": "Очистка успешна", "tip": "Операция не может быть отменена, после очистки данные нужно будет загрузить заново, пожалуйста, действуйте осторожно", "title": "Очистить кэш данных"}, "reset": {"action": "Сбросить сейчас", "alert": "Подтвердите сброс всех системных настроек?", "desc": "Это приведет к сбросу всех системных настроек, включая настройки темы, настройки чата, настройки языковой модели и т.д.", "success": "Сброс успешен", "tip": "Операция не может быть отменена, после сброса данные не могут быть восстановлены, пожалуйста, действуйте осторожно", "title": "Сбросить системные настройки"}, "title": "Системные настройки"}, "theme": {"backgroundEffect": {"desc": "Настроить эффект фона", "glow": "Сияние", "none": "Без фона", "title": "Эффект фона"}, "locale": {"auto": "Следовать за системой", "desc": "Настроить системный язык", "title": "Язык"}, "neutralColor": {"desc": "Настройка градаций серого с различными цветовыми наклонами", "title": "Нейтральный цвет"}, "primaryColor": {"desc": "Настроить цвет темы", "title": "Цвет темы"}, "title": "Настройки темы"}, "title": "Общие настройки"}, "header": {"desc": "Предпочтения и настройки модели", "global": "Глобальные настройки", "session": "Настройки сессии", "sessionDesc": "Настройки ролей и предпочтений сессии", "sessionWithName": "Настройки сессии · {{name}}", "title": "Настройки"}, "llm": {"aesGcm": "Ваши ключи и адреса прокси будут зашифрованы с использованием алгоритма шифрования <1>AES-GCM</1>", "apiKey": {"desc": "Пожалуйста, введите ваш {{name}} API Key", "placeholder": "{{name}} API Key", "title": "API Key"}, "checker": {"button": "Проверить", "desc": "Проверьте, правильно ли введены Api Key и адрес прокси", "error": "Проверка не удалась", "pass": "Проверка пройдена", "title": "Проверка соединения"}, "customModelCards": {"addNew": "Создать и добавить модель {{id}}", "config": "Настроить модель", "confirmDelete": "Вы собираетесь удалить эту пользовательскую модель. После удаления восстановить её будет невозможно, пожалуйста, действуйте осторожно.", "modelConfig": {"azureDeployName": {"extra": "Поле, запрашиваемое в Azure OpenAI", "placeholder": "Введите название развертывания модели в Azure", "title": "Название развертывания модели"}, "displayName": {"placeholder": "Введите название модели для отображения, например, ChatGPT, GPT-4 и т.д.", "title": "Название модели для отображения"}, "files": {"extra": "Текущая реализация загрузки файлов является лишь хакерским решением, предназначенным только для самостоятельного тестирования. Полная возможность загрузки файлов будет реализована позже.", "title": "Поддержка загрузки файлов"}, "functionCall": {"extra": "Эта настройка только активирует возможность вызова функций в приложении, поддержка вызова функций полностью зависит от самой модели, пожалуйста, протестируйте доступность вызова функций этой модели.", "title": "Поддержка вызова функций"}, "id": {"extra": "Будет отображаться как метка модели", "placeholder": "Введите id модели, например, gpt-4-turbo-preview или claude-2.1", "title": "ID модели"}, "modalTitle": "Настройка пользовательской модели", "tokens": {"title": "Максимальное количество токенов", "unlimited": "Без ограничений"}, "vision": {"extra": "Эта настройка только активирует возможность загрузки изображений в приложении, поддержка распознавания полностью зависит от самой модели, пожалуйста, протестируйте доступность визуального распознавания этой модели.", "title": "Поддержка визуального распознавания"}}}, "fetchOnClient": {"desc": "Режим запроса на клиенте будет инициировать сессию напрямую из браузера, что может повысить скорость отклика", "title": "Использовать режим запроса на клиенте"}, "fetcher": {"fetch": "Получить список моделей", "fetching": "Получение списка моделей...", "latestTime": "Последнее обновление: {{time}}", "noLatestTime": "Список еще не получен"}, "helpDoc": "Учебное пособие по настройке", "modelList": {"desc": "Выберите модели, которые будут отображаться в сессии; выбранные модели будут показаны в списке моделей", "placeholder": "Пожалуйста, выберите модель из списка", "title": "Список моделей", "total": "Всего доступно {{count}} моделей"}, "proxyUrl": {"desc": "Кроме адреса по умолчанию, должен содержать http(s)://", "title": "Адрес API прокси"}, "title": "Языковая модель", "waitingForMore": "Больше моделей <1>планируется подключить</1>, пожалуйста, ожидайте"}, "systemAgent": {"customPrompt": {"addPrompt": "Добавить пользовательский запрос", "desc": "После заполнения системный помощник будет использовать пользовательский запрос при генерации контента", "placeholder": "Введите пользовательский запрос", "title": "Пользовательский запрос"}, "emotionAnalysis": {"label": "Модель анализа эмоций", "modelDesc": "Укажите модель, используемую для анализа эмоций", "title": "Автоматический анализ эмоций"}, "title": "Системный агент"}, "touch": {"title": "Настройки касания"}, "tts": {"clientCall": {"desc": "После включения будет использоваться вызов клиента для службы синтеза речи, скорость синтеза речи будет выше, но требуется доступ к интернету или возможность обхода блокировок.", "title": "Вызов клиента"}, "title": "Настройки голоса"}}
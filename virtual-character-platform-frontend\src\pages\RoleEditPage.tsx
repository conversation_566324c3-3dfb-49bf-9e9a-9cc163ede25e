import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Spin, message, Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';
import classNames from 'classnames';

import { useAgentStore, agentSelectors } from '../store/agent';
import { useGlobalStore } from '../store/global';
import { characterAPI } from '../services/characterAPI';

// 导入角色编辑组件
import RoleEditTabs from '../components/role/RoleEditTabs';
import RoleSideBar from '../components/role/RoleSideBar';
import RolePreview from '../components/role/RolePreview';

import '../styles/role-edit.css';

interface RoleEditPageProps {}

const RoleEditPage: React.FC<RoleEditPageProps> = () => {
  const { characterId } = useParams<{ characterId?: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const { 
    currentIdentifier,
    activateAgent,
    addLocalAgent,
    updateAgentConfig,
    getAgentById
  } = useAgentStore();
  
  const { viewer } = useGlobalStore();
  
  // 本地状态
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [activeTab, setActiveTab] = useState('info');
  
  // 当前编辑的角色
  const currentAgent = useAgentStore((s) => agentSelectors.currentAgent(s));

  // 加载角色数据
  useEffect(() => {
    const loadCharacter = async () => {
      if (characterId && characterId !== 'new') {
        try {
          setLoading(true);
          
          // 检查本地是否已有该角色
          const existingAgent = getAgentById(characterId);
          if (existingAgent) {
            activateAgent(characterId);
            return;
          }
          
          // 从API加载角色数据
          const response = await characterAPI.getCharacterDetail(characterId);
          const characterData = response.data;

          // 转换为Agent格式
          const agentData = {
            agentId: characterData.id,
            meta: {
              name: characterData.name,
              description: characterData.description || '',
              avatar: characterData.image_url || '',
              cover: characterData.image_url || '',
              gender: 'Female' as any, // 默认性别
              model: characterData.vrm_model_url || '',
              readme: characterData.description || '',
              tags: characterData.tags || [],
            },
            systemRole: characterData.settings?.systemPrompt || `你是${characterData.name}`,
            chatConfig: {
              historyCount: 10,
              compressThreshold: 1000,
              enableCompressThreshold: true,
              enableHistoryCount: true,
            },
            tts: {
              voice: characterData.settings?.voice_type || 'zh-CN-XiaoxiaoNeural',
              speed: characterData.settings?.voice_speed || 1,
              pitch: characterData.settings?.voice_pitch || 0,
            },
            params: characterData.appearance_params || {},
            provider: 'openai',
            model: 'gpt-3.5-turbo',
          };
          
          // 添加到本地存储并激活
          addLocalAgent(agentData);
          activateAgent(characterId);
          
        } catch (error) {
          console.error('加载角色失败:', error);
          message.error('加载角色失败，请稍后再试');
          navigate('/community');
        } finally {
          setLoading(false);
        }
      } else if (characterId === 'new') {
        // 创建新角色
        const newAgent = {
          agentId: `new_${Date.now()}`,
          meta: {
            name: '新角色',
            description: '',
            avatar: '',
            cover: '',
            gender: 'Female' as any,
            model: '',
            readme: '',
            tags: [],
          },
          systemRole: '你是一个友善的AI助手',
          chatConfig: {
            historyCount: 10,
            compressThreshold: 1000,
            enableCompressThreshold: true,
            enableHistoryCount: true,
          },
          tts: {
            voice: 'zh-CN-XiaoxiaoNeural',
            speed: 1,
            pitch: 0,
          },
          params: {},
          provider: 'openai',
          model: 'gpt-3.5-turbo',
        };
        
        addLocalAgent(newAgent);
        activateAgent(newAgent.agentId);
      }
    };

    loadCharacter();
  }, [characterId, getAgentById, activateAgent, addLocalAgent, navigate]);

  // 保存角色
  const handleSave = async () => {
    if (!currentAgent) {
      message.error('没有可保存的角色数据');
      return;
    }

    try {
      setSaving(true);
      
      const characterData = {
        name: currentAgent.meta.name,
        image_url: currentAgent.meta.avatar,
        age: 18, // 默认年龄
        personality: '温柔', // 默认性格
        identity: '助手', // 默认身份
        appearance_params: currentAgent.params,
        settings: {
          systemPrompt: currentAgent.systemRole,
          voice_type: currentAgent.tts?.voice,
          voice_speed: currentAgent.tts?.speed,
          voice_pitch: currentAgent.tts?.pitch,
        },
        public: false,
      };

      if (characterId && characterId !== 'new') {
        // 更新现有角色
        await characterAPI.updateCharacter(characterId, characterData);
        message.success('角色更新成功');
      } else {
        // 创建新角色
        const response = await characterAPI.saveCharacter(characterData);
        message.success('角色创建成功');
        
        // 更新URL为新的角色ID
        navigate(`/role/edit/${response.data.id}`, { replace: true });
      }
      
    } catch (error) {
      console.error('保存角色失败:', error);
      message.error('保存角色失败，请稍后再试');
    } finally {
      setSaving(false);
    }
  };

  // 返回角色列表
  const handleBack = () => {
    navigate('/community');
  };

  // 加载状态
  if (loading) {
    return (
      <div className="role-edit-loading">
        <Spin size="large" />
        <p>正在加载角色数据...</p>
      </div>
    );
  }

  return (
    <div className="role-edit-container">
      {/* 顶部工具栏 */}
      <div className="role-edit-header">
        <div className="header-left">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            返回
          </Button>
          <span className="role-name">
            {currentAgent?.meta.name || '角色编辑'}
          </span>
        </div>
        
        <div className="header-right">
          <Button
            type="primary"
            onClick={handleSave}
            loading={saving}
          >
            保存角色
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Flexbox
        flex={1}
        horizontal
        className="role-edit-content"
        style={{ overflow: 'hidden' }}
      >
        {/* 左侧边栏 */}
        {showSidebar && (
          <RoleSideBar 
            onClose={() => setShowSidebar(false)}
          />
        )}

        {/* 中间编辑区域 */}
        <Flexbox flex={1} className="role-edit-main">
          <RoleEditTabs 
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onToggleSidebar={() => setShowSidebar(!showSidebar)}
          />
        </Flexbox>

        {/* 右侧预览区域 */}
        <div className="role-edit-preview">
          <RolePreview />
        </div>
      </Flexbox>
    </div>
  );
};

export default RoleEditPage;

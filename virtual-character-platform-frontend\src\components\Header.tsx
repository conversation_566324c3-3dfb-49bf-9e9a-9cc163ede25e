import React from 'react';
import { Layout, <PERSON>u, But<PERSON>, Avatar } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { UserOutlined } from '@ant-design/icons';
import useAuthStore from '../store/authStore';
import UserPanel from '../features/UserPanel';

const { Header: AntHeader } = Layout;

const Header: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isLoggedIn, userInfo } = useAuthStore();
  
  // 获取当前路径以设置活动菜单项
  const getSelectedKey = () => {
    const pathname = location.pathname;
    if (pathname === '/') return '1';
    if (pathname === '/create-character') return '2';
    if (pathname === '/community') return '3';
    if (pathname === '/profile') return '4';
    return '1';
  };
  

  
  return (
    <AntHeader
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px',
        backgroundColor: '#fff',
        borderBottom: '1px solid #f0f0f0',
      }}
    >
      <div className="logo" style={{ color: '#eb2f96', fontSize: '24px', fontWeight: 'bold' }}>
        <Link to="/" style={{ color: '#eb2f96', textDecoration: 'none' }}>虚拟角色平台</Link>
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Menu
          theme="light"
          mode="horizontal"
          selectedKeys={[getSelectedKey()]}
          style={{ minWidth: 0 }}
          items={[
            {
              key: '1',
              label: <Link to="/">首页</Link>
            },
            {
              key: '2',
              label: <Link to="/create-character">角色创建</Link>
            },
            {
              key: '3',
              label: <Link to="/community">社区</Link>
            }
          ]}
        />
        
        {isLoggedIn ? (
          <UserPanel>
            <div style={{ cursor: 'pointer', marginLeft: '20px', display: 'flex', alignItems: 'center' }}>
              <Avatar icon={<UserOutlined />} style={{ marginRight: '8px' }} />
              <span>{userInfo?.username || '用户'}</span>
            </div>
          </UserPanel>
        ) : (
          <Button type="primary" onClick={() => navigate('/login')}>
            登录
          </Button>
        )}
      </div>
    </AntHeader>
  );
};

export default Header;
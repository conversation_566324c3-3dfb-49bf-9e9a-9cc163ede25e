// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`NovitaAI > models > should get models 1`] = `
[
  {
    "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases. It has demonstrated strong performance compared to leading closed-source models in human evaluations.",
    "displayName": "meta-llama/llama-3-8b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "meta-llama/llama-3-8b-instruct",
    "tokens": 8192,
  },
  {
    "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases. It has demonstrated strong performance compared to leading closed-source models in human evaluations.",
    "displayName": "meta-llama/llama-3-70b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "meta-llama/llama-3-70b-instruct",
    "tokens": 8192,
  },
  {
    "description": "Meta's latest class of models, Llama 3.1, launched with a variety of sizes and configurations. The 8B instruct-tuned version is particularly fast and efficient. It has demonstrated strong performance in human evaluations, outperforming several leading closed-source models.",
    "displayName": "meta-llama/llama-3.1-8b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "meta-llama/llama-3.1-8b-instruct",
    "tokens": 8192,
  },
  {
    "description": "Meta's latest class of models, Llama 3.1, has launched with a variety of sizes and configurations. The 70B instruct-tuned version is optimized for high-quality dialogue use cases. It has demonstrated strong performance in human evaluations compared to leading closed-source models.",
    "displayName": "meta-llama/llama-3.1-70b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "meta-llama/llama-3.1-70b-instruct",
    "tokens": 8192,
  },
  {
    "description": "Meta's latest class of models, Llama 3.1, launched with a variety of sizes and configurations. This 405B instruct-tuned version is optimized for high-quality dialogue use cases. It has demonstrated strong performance compared to leading closed-source models, including GPT-4o and Claude 3.5 Sonnet, in evaluations.",
    "displayName": "meta-llama/llama-3.1-405b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "meta-llama/llama-3.1-405b-instruct",
    "tokens": 32768,
  },
  {
    "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.
Designed for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.",
    "displayName": "google/gemma-2-9b-it",
    "enabled": true,
    "functionCall": false,
    "id": "google/gemma-2-9b-it",
    "tokens": 8192,
  },
  {
    "description": "This is a fine-tuned Llama-2 model designed to support longer and more detailed writing prompts, as well as next-chapter generation. It also includes an experimental role-playing instruction set with multi-round dialogues, character interactions, and varying numbers of participants",
    "displayName": "jondurbin/airoboros-l2-70b",
    "enabled": true,
    "functionCall": false,
    "id": "jondurbin/airoboros-l2-70b",
    "tokens": 4096,
  },
  {
    "description": "Hermes 2 Pro is an upgraded, retrained version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house.",
    "displayName": "nousresearch/hermes-2-pro-llama-3-8b",
    "enabled": true,
    "functionCall": true,
    "id": "nousresearch/hermes-2-pro-llama-3-8b",
    "tokens": 8192,
  },
  {
    "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.",
    "displayName": "mistralai/mistral-7b-instruct",
    "enabled": true,
    "functionCall": false,
    "id": "mistralai/mistral-7b-instruct",
    "tokens": 32768,
  },
  {
    "description": "Dolphin 2.9 is designed for instruction following, conversational, and coding. This model is a finetune of Mixtral 8x22B Instruct. It features a 64k context length and was fine-tuned with a 16k sequence length using ChatML templates.The model is uncensored and is stripped of alignment and bias. It requires an external alignment layer for ethical use.",
    "displayName": "cognitivecomputations/dolphin-mixtral-8x22b",
    "enabled": true,
    "functionCall": false,
    "id": "cognitivecomputations/dolphin-mixtral-8x22b",
    "tokens": 16000,
  },
  {
    "description": "The uncensored llama3 model is a powerhouse of creativity, excelling in both roleplay and story writing. It offers a liberating experience during roleplays, free from any restrictions. This model stands out for its immense creativity, boasting a vast array of unique ideas and plots, truly a treasure trove for those seeking originality. Its unrestricted nature during roleplays allows for the full breadth of imagination to unfold, akin to an enhanced, big-brained version of Stheno. Perfect for creative minds seeking a boundless platform for their imaginative expressions, the uncensored llama3 model is an ideal choice",
    "displayName": "sao10k/l3-70b-euryale-v2.1",
    "enabled": true,
    "functionCall": false,
    "id": "sao10k/l3-70b-euryale-v2.1",
    "tokens": 16000,
  },
  {
    "description": "A merge with a complex family tree, this model was crafted for roleplaying and storytelling. Midnight Rose is a successor to Rogue Rose and Aurora Nights and improves upon them both. It wants to produce lengthy output by default and is the best creative writing merge produced so far by sophosympatheia.",
    "displayName": "sophosympatheia/midnight-rose-70b",
    "enabled": true,
    "functionCall": false,
    "id": "sophosympatheia/midnight-rose-70b",
    "tokens": 4096,
  },
  {
    "description": "The idea behind this merge is that each layer is composed of several tensors, which are in turn responsible for specific functions. Using MythoLogic-L2's robust understanding as its input and Huginn's extensive writing capability as its output seems to have resulted in a model that exceeds at both, confirming my theory. (More details to be released at a later time).",
    "displayName": "gryphe/mythomax-l2-13b",
    "enabled": true,
    "functionCall": false,
    "id": "gryphe/mythomax-l2-13b",
    "tokens": 4096,
  },
  {
    "description": "Nous-Hermes-Llama2-13b is a state-of-the-art language model fine-tuned on over 300,000 instructions. This model was fine-tuned by Nous Research, with Teknium and Emozilla leading the fine tuning process and dataset curation, Redmond AI sponsoring the compute, and several other contributors.",
    "displayName": "nousresearch/nous-hermes-llama2-13b",
    "enabled": true,
    "functionCall": false,
    "id": "nousresearch/nous-hermes-llama2-13b",
    "tokens": 4096,
  },
  {
    "description": "Nous Hermes 2 Mixtral 8x7B DPO is the new flagship Nous Research model trained over the Mixtral 8x7B MoE LLM. The model was trained on over 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.",
    "displayName": "Nous-Hermes-2-Mixtral-8x7B-DPO",
    "enabled": true,
    "functionCall": false,
    "id": "Nous-Hermes-2-Mixtral-8x7B-DPO",
    "tokens": 32768,
  },
  {
    "description": "A Mythomax/MLewd_13B-style merge of selected 70B models. A multi-model merge of several LLaMA2 70B finetunes for roleplaying and creative work. The goal was to create a model that combines creativity with intelligence for an enhanced experience.",
    "displayName": "lzlv_70b",
    "enabled": true,
    "functionCall": false,
    "id": "lzlv_70b",
    "tokens": 4096,
  },
  {
    "description": "OpenHermes 2.5 Mistral 7B is a state of the art Mistral Fine-tune, a continuation of OpenHermes 2 model, which trained on additional code datasets.",
    "displayName": "teknium/openhermes-2.5-mistral-7b",
    "enabled": true,
    "functionCall": false,
    "id": "teknium/openhermes-2.5-mistral-7b",
    "tokens": 4096,
  },
  {
    "description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.",
    "displayName": "microsoft/wizardlm-2-8x22b",
    "enabled": true,
    "functionCall": false,
    "id": "microsoft/wizardlm-2-8x22b",
    "tokens": 65535,
  },
]
`;

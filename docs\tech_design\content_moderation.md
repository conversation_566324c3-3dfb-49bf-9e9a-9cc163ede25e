# 内容审核模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中内容审核模块的设计。该模块旨在对用户输入内容、用户上传内容以及 AI 生成的内容进行自动化和（或）人工审核，以确保平台内容的合规性，防止出现敏感、违规或不适宜的内容，维护良好的社区环境。

## 2. 模块概述

内容审核模块是保障平台健康运行的重要组件。它位于用户输入和 AI 输出的关键路径上，负责识别和处理潜在的有害内容。审核流程通常包括接收待审核内容、调用审核服务/逻辑进行判断、根据判断结果采取相应的处理措施。该模块需要与后端业务逻辑紧密集成。

## 3. 核心功能

内容审核模块的核心功能是对特定类型的内容进行扫描和判断。

- **接收待审核内容:** 接收来自后端业务逻辑的不同类型的内容，例如：
    - 用户输入的聊天文本。
    - 用户输入的语言描述定制文本。
    - AI 生成的图片数据（Base64 或 URL）。
    - AI 生成的对话文本回复。
    - 用户输入的角色名称、身份、性格描述等。
    - 用户在社区中发布的其他文本内容（如评论，如果实现）。
- **文本内容审核:**
    - 使用敏感词库进行匹配和过滤。
    - 集成第三方内容审核 API 或使用开源模型进行更复杂的语义分析，判断是否包含色情、暴力、政治敏感、广告、谩骂等内容。
- **图片内容审核:**
    - 集成第三方图片审核 API 或使用开源模型进行图像识别，判断图片是否包含色情、暴力、血腥等不适宜内容。
    - 注意"星火"API 自身可能有审核，但平台层面进行二次审核可以提高安全性。
- **处理审核结果:** 根据审核判断的结果，采取相应的措施：
    - **阻断 (Block):** 对于严重违规内容，直接阻止其发布、显示或保存，并向用户返回错误提示。
    - **标记 (Flag):** 对于可疑或需要进一步判断的内容，进行标记并可能发送到人工审核队列。
    - **警告 (Warn):** 对于轻微违规，可以显示警告信息给用户。
    - **记录日志 (Log):** 记录所有审核行为和结果，以便追踪和分析。
- **人工审核工作流 (未来考虑):** 提供一个后台界面供管理员查看被标记的内容，进行人工判断和最终处理（通过、拒绝、封禁用户等）。
- **用户举报处理 (未来考虑):** 接收用户对不良内容的举报，并将被举报内容送入审核流程或人工审核队列。

## 4. 技术实现考量

- **敏感词库:** 需要维护一个有效且可更新的敏感词库。
- **第三方 API / 开源模型集成:** 选择可靠的内容审核服务提供商或易于部署的开源模型。需要考虑 API 的调用成本、性能、审核准确率以及对二次元内容的适应性。
- **异步处理:** 对于耗时较长的审核（如图片审核），可以采用异步处理，避免阻塞用户请求。
- **策略配置:** 允许灵活配置审核规则和触发条件（例如，不同类型的内容使用不同的审核策略）。

## 5. 模块与其他模块的交互

- **接收来自:** 后端业务逻辑层（在处理用户请求或接收 AI 响应后，将待审核内容发送给此模块）。
- **发送给:**
    - 后端业务逻辑层（返回审核结果，决定是否继续执行操作）。
    - 日志记录模块。
    - 可能的人工审核队列或通知系统。

## 6. 待细化项

-   **具体的文本和图片内容审核服务/开源模型选择：**
    -   **文本内容审核：**
        -   **商用服务 (推荐):** 集成阿里云内容安全 (内容审核)、腾讯云内容安全等，它们提供成熟的敏感词过滤、涉黄、涉政、广告、谩骂等多种维度的文本审核能力。
        -   **自建/开源方案 (备选):** 对于简单的敏感词过滤，可维护内部敏感词库。对于更复杂的语义分析，可考虑集成如 `Text-Censor` 等开源项目或自建基于 NLP 模型的轻量级审核服务，但需自行维护和更新模型。
    -   **图片内容审核：**
        -   **商用服务 (推荐):** 集成阿里云内容安全 (图片审核)、腾讯云内容安全等，它们提供图像涉黄、暴恐、政治敏感等审核能力。图片生成后先进行平台层面的二次审核再存储。
-   **详细的审核规则和敏感词库的构建和维护方式：**
    -   **敏感词库：**
        -   **构建：** 初始从公开的敏感词库收集，并结合项目特性（如二次元特有词汇）进行扩充。根据敏感程度分为不同等级（如强敏感词、弱敏感词），以便采取不同处理措施。
        -   **维护：** 敏感词库应支持后台动态增删改查。定期更新和扩充，可通过爬虫、用户反馈、运营分析等方式发现新敏感词。
        -   **匹配：** 使用基于 AC 自动机、Trie 树等高效算法进行敏感词匹配，支持模糊匹配和变体识别。
    -   **审核规则：**
        -   **策略化：** 针对不同内容类型（如聊天文本、角色名称、图片），配置不同的审核策略和阈值。例如，聊天文本对轻微违规可警告，角色名称则需更严格。
        -   **组合判断：** 结合多个审核维度（敏感词、第三方 API 结果、用户行为）进行综合判断。
-   **如何处理各种违规类型的具体措施：**
    -   **严重违规 (例如：涉政、恐怖、非法内容)：**
        -   **阻断：** 内容禁止发布、存储或展示。
        -   **告警：** 立即触发告警通知管理员。
        -   **用户处理：** 永久封禁用户账号，必要时留存证据并上报相关部门。
    -   **中度违规 (例如：色情、暴力、辱骂、恶意广告)：**
        -   **阻断/标记：** 内容禁止发布，或标记为待人工审核。
        -   **用户处理：** 首次警告，多次违规则临时封禁或限制部分功能。
        -   **日志：** 详细记录违规行为。
    -   **轻微违规 (例如：低俗内容、不文明用语)：**
        -   **过滤/替换：** 敏感词自动替换为星号或文明词语。
        -   **警告：** 首次出现向用户发送警告提示，不影响功能使用。
-   **如何将审核结果有效地返回给调用方（后端业务逻辑），以便其进行后续处理：**
    -   审核模块将返回一个标准的结构化审核结果对象，例如：
        ```json
        {
          "status": "PASS" | "BLOCK" | "REVIEW_REQUIRED", // 审核状态
          "violation_type": "string", // 违规类型（如 "PORN", "POLITICAL", "AD", "ABUSE"，多个用逗号分隔）
          "score": "number", // 风险评分（可选，第三方 API 提供）
          "message": "string", // 用户友好的提示信息（如 "内容包含敏感词，请修改"）
          "details": {} // 包含更多技术细节，如命中的敏感词列表、API 返回的原始数据等（仅供开发者调试）
        }
        ```
    -   后端业务逻辑根据 `status` 字段决定后续操作：如果为 `BLOCK`，则直接返回错误给前端；如果为 `REVIEW_REQUIRED`，则将内容推送到人工审核队列。
-   **如何在 AI 生成内容（特别是对话）中，在生成过程中就尝试避免违规内容：**
    -   **提示词约束 (Prompt Engineering):** 在传递给 AI 模型的提示词中，明确要求 AI 遵守道德规范、法律法规，避免生成敏感、暴力、色情等内容。例如，在系统提示词中加入"你是一个积极向上、友善且遵守法律的角色，绝不能生成任何色情、暴力或政治敏感内容。"
    -   **负面提示 (Negative Prompts):** 在图片生成时，增加负面提示词（如"NSFW, gore, explicit, ugly, deformed"）以引导 AI 避免生成不希望出现的内容。
    -   **前置过滤 (Pre-moderation):** 对用户输入到 AI 的文本进行初步审核，如果用户输入本身违规，则直接拒绝或修改，不送入 AI。
    -   **AI 模型选择：** 优先选择在内容安全方面有内置保障或经过良好训练的 AI 模型。
-   **人工审核工作流的具体设计：**
    -   **后台管理界面：** 开发一个专门的后台管理系统，提供内容审核视图。
    -   **队列管理：** 被标记为 `REVIEW_REQUIRED` 的内容会自动进入人工审核队列，支持优先级、分类、分配给特定审核员等。
    -   **审核员操作：** 审核员可查看内容详情、AI 审核结果、用户举报信息，并进行"通过"、"拒绝"、"修改"、"封禁用户"等操作。
    -   **审核记录：** 每次人工审核操作都应记录审核员、时间、结果和处理详情。
    -   **通知：** 当有新内容进入人工审核队列或审核结果更新时，通知相关管理员。
-   **用户举报功能的具体实现和与审核流程的整合：**
    -   **前端举报入口：** 在前端页面提供"举报"按钮或菜单，用户可选择举报类型（如涉黄、暴力、广告）并填写举报原因。
    -   **后端举报 API：** 提供 `/api/report` 或 `/api/characters/{character_id}/report` 等 API 接口接收用户举报请求。请求中包含被举报内容 ID、举报类型、举报原因、举报用户 ID 等。
    -   **举报处理：** 接收到举报后，将举报内容的信息以及 AI 审核结果（如果有）一并提交到人工审核队列，并根据举报数量/严重程度提升优先级。对于反复恶意举报的用户也应进行记录。
    -   **反馈：** 未来可考虑向举报用户反馈举报处理结果。

这份文档是内容审核模块的初步设计。具体的审核策略和技术方案将在开发过程中根据实际情况和外部服务特性进行完善。 
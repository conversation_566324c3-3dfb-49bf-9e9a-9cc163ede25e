/**
 * Environment detection utilities
 */

// develop environment (using different name to avoid conflict with i18n.ts)
export const isDevEnv = process.env.NODE_ENV === 'development';
// server environment (using different name to avoid conflict with i18n.ts)
export const isServerSide = typeof window === 'undefined';

// production environment
export const isProd = process.env.NODE_ENV === 'production';
// browser environment
export const isBrowser = typeof window !== 'undefined';

// API configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || '/api';

// Application configuration
export const APP_NAME = 'Virtual Character Platform';
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';

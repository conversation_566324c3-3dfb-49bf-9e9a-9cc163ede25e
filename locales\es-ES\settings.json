{"common": {"chat": {"avatar": {"desc": "<PERSON><PERSON>r avatar", "title": "Avatar"}, "nickName": {"desc": "<PERSON><PERSON><PERSON> a<PERSON>", "placeholder": "Por favor, introduce un apodo", "title": "A<PERSON>do"}, "title": "Configuración de Chat"}, "system": {"clear": {"action": "<PERSON><PERSON><PERSON> ahora", "alert": "¿Confirmar eliminación de todos los mensajes de conversación?", "desc": "Esto eliminará todos los datos de conversación y de personajes, incluyendo la lista de conversaciones, la lista de personajes, mensajes de conversación, etc.", "success": "Eliminación exitosa", "tip": "La acción no se puede deshacer, una vez eliminados los datos no se podrán recuperar, por favor actúa con precaución", "title": "Eliminar todos los mensajes de conversación"}, "clearCache": {"action": "<PERSON><PERSON><PERSON> ahora", "alert": "¿Confirmar la eliminación de toda la caché?", "calculating": "Calculando el tamaño de la caché...", "desc": "Se eliminará la caché de datos descargados de la aplicación, incluidos los datos del modelo de los personajes, datos de voz, datos del modelo de baile, datos de audio, etc.", "success": "<PERSON><PERSON><PERSON>", "tip": "La operación no se puede deshacer, despu<PERSON> de borrar, los datos deberán descargarse nuevamente, por favor actúe con precaución", "title": "<PERSON><PERSON><PERSON> caché de datos"}, "reset": {"action": "Restablecer ahora", "alert": "¿Confirmar restablecimiento de todas las configuraciones del sistema?", "desc": "Esto restablecerá todas las configuraciones del sistema, incluyendo configuraciones de tema, configuraciones de chat, configuraciones de modelo de lenguaje, etc.", "success": "Restablecimiento exitoso", "tip": "La acción no se puede deshacer, una vez restablecidos los datos no se podrán recuperar, por favor actúa con precaución", "title": "Restablecer configuración del sistema"}, "title": "Configuración del Sistema"}, "theme": {"backgroundEffect": {"desc": "Personalizar efecto de fondo", "glow": "<PERSON><PERSON><PERSON>", "none": "Sin fondo", "title": "Efecto de Fondo"}, "locale": {"auto": "<PERSON><PERSON><PERSON>", "desc": "Personalizar idioma del sistema", "title": "Idioma"}, "neutralColor": {"desc": "Personalizar escala de grises con diferentes inclinaciones de color", "title": "Color Neutro"}, "primaryColor": {"desc": "Personalizar color del tema", "title": "Color Principal"}, "title": "Configuración de Tema"}, "title": "Configuración General"}, "header": {"desc": "Preferencias y configuración del modelo", "global": "Configuración global", "session": "Configuración de la sesión", "sessionDesc": "Configuración de roles y preferencias de la sesión", "sessionWithName": "Configuración de la sesión · {{name}}", "title": "Configuración"}, "llm": {"aesGcm": "Su clave y la dirección del proxy se cifrarán utilizando el algoritmo de cifrado <1>AES-GCM</1>", "apiKey": {"desc": "Por favor, introduzca su {{name}} API Key", "placeholder": "{{name}} API Key", "title": "API Key"}, "checker": {"button": "Comprobar", "desc": "Pruebe si la API Key y la dirección del proxy están correctamente ingresadas", "error": "Verificación fallida", "pass": "Comprobación exitosa", "title": "Verificación de conectividad"}, "customModelCards": {"addNew": "<PERSON><PERSON><PERSON> y añadir el modelo {{id}}", "config": "Configurar modelo", "confirmDelete": "Está a punto de eliminar este modelo personalizado. Una vez eliminado, no se podrá recuperar. Por favor, proceda con precaución.", "modelConfig": {"azureDeployName": {"extra": "Campo solicitado en Azure OpenAI", "placeholder": "Por favor, introduzca el nombre de despliegue del modelo en Azure", "title": "Nombre de despliegue del modelo"}, "displayName": {"placeholder": "Por favor, introduzca el nombre para mostrar del modelo, por ejemplo, ChatGPT, GPT-4, etc.", "title": "Nombre para mostrar del modelo"}, "files": {"extra": "La implementación actual de carga de archivos es solo una solución temporal, limitada a pruebas personales. Espere la implementación completa de la capacidad de carga de archivos.", "title": "Soporte para carga de archivos"}, "functionCall": {"extra": "Esta configuración solo habilitará la capacidad de llamada a funciones en la aplicación. Si se admite la llamada a funciones depende completamente del modelo en sí. Pruebe la disponibilidad de la capacidad de llamada a funciones de este modelo.", "title": "Soporte para llamadas a funciones"}, "id": {"extra": "Se mostrará como etiqueta del modelo", "placeholder": "<PERSON>r favor, introduzca el id del modelo, por ejemplo, gpt-4-turbo-preview o claude-2.1", "title": "ID del modelo"}, "modalTitle": "Configuración del modelo personalizado", "tokens": {"title": "Número máximo de tokens", "unlimited": "Sin límite"}, "vision": {"extra": "Esta configuración solo habilitará la carga de imágenes en la aplicación. Si se admite el reconocimiento depende completamente del modelo en sí. Pruebe la disponibilidad de la capacidad de reconocimiento visual de este modelo.", "title": "Soporte para reconocimiento visual"}}}, "fetchOnClient": {"desc": "El modo de solicitud del cliente iniciará la solicitud de sesión directamente desde el navegador, lo que puede mejorar la velocidad de respuesta", "title": "Usar modo de solicitud del cliente"}, "fetcher": {"fetch": "Obtener lista de modelos", "fetching": "Obteniendo lista de modelos...", "latestTime": "Última actualización: {{time}}", "noLatestTime": "Lista aún no obtenida"}, "helpDoc": "Tutorial de configuración", "modelList": {"desc": "Selecciona los modelos que se mostrarán en la sesión, los modelos seleccionados se mostrarán en la lista de modelos", "placeholder": "Por favor, selecciona un modelo de la lista", "title": "Lista de modelos", "total": "Un total de {{count}} modelos disponibles"}, "proxyUrl": {"desc": "Además de la dirección predeterminada, debe incluir http(s)://", "title": "Dirección del proxy API"}, "title": "<PERSON><PERSON>", "waitingForMore": "Más modelos están <1>programados para integrarse</1>, por favor espera"}, "systemAgent": {"customPrompt": {"addPrompt": "Agregar aviso personalizado", "desc": "Al completarlo, el asistente del sistema utilizará el aviso personalizado al generar contenido", "placeholder": "Introduce la palabra clave del aviso personalizado", "title": "Palabra clave del aviso personalizado"}, "emotionAnalysis": {"label": "Modelo de análisis de emociones", "modelDesc": "Modelo designado para el análisis de emociones", "title": "Análisis de emociones automático"}, "title": "Agente del sistema"}, "touch": {"title": "Configuración de toque"}, "tts": {"clientCall": {"desc": "Al habilitar esta opción, se utilizará el servicio de síntesis de voz mediante llamada del cliente, lo que permitirá una velocidad de síntesis de voz más rápida, pero requerirá acceso a internet o la capacidad de acceder a redes externas.", "title": "Llamada del cliente"}, "title": "Configuración de voz"}}
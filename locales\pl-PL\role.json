{"agent": {"create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "male": "Mężczyzna", "other": "<PERSON><PERSON>"}, "category": {"all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animal": "Zwierzęta", "anime": "Anime", "book": "Książki", "game": "G<PERSON>", "history": "Historia", "movie": "Filmy", "realistic": "Realizm", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "Czy na pewno chcesz usunąć rolę oraz powiązane z nią wiadomości sesji? Po usunięciu nie będzie możliwości przywrócenia, proszę działać ostrożnie!", "delRole": "<PERSON><PERSON><PERSON> rolę", "delRoleDesc": "<PERSON>zy na pewno chcesz usunąć rolę {{name}} oraz powiązane z nią wiadomości sesji? Po usunięciu nie będzie możliwości przywrócenia, proszę działa<PERSON> ostrożnie!", "gender": {"all": "<PERSON><PERSON><PERSON><PERSON>", "female": "kobieta", "male": "mężczyzna"}, "info": {"avatarDescription": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> na awatar, aby prz<PERSON><PERSON><PERSON> własny", "avatarLabel": "<PERSON><PERSON><PERSON>", "categoryDescription": "Kategoria postaci, używana do wyświetlania klasyfikacji", "categoryLabel": "Kategoria", "coverDescription": "Służy do wyświetlania postaci na stronie odkrywania, zalecany rozmiar {{width}} * {{height}}", "coverLabel": "<PERSON><PERSON><PERSON><PERSON>", "descDescription": "Opis postaci, służy do krótkiego wprowadzenia postaci", "descLabel": "Opis", "emotionDescription": "Wybierz emocje podczas odpowiedzi, co wpłynie na wyraz twarzy postaci", "emotionLabel": "Emocje i uczucia", "genderDescription": "Płeć postaci, wpływa na reakcje dotykowe postaci", "genderLabel": "<PERSON><PERSON><PERSON><PERSON>", "greetDescription": "Zwroty powitalne przy pierwszej rozmowie z postacią", "greetLabel": "Powitanie", "modelDescription": "Podgl<PERSON><PERSON> modelu, można przeciągać pliki modelu, aby je z<PERSON>", "modelLabel": "Podgląd modelu", "motionCategoryLabel": "<PERSON><PERSON><PERSON> ruchu", "motionDescription": "Wybierz ruch podczas odpowiedzi, co wpłynie na zachowanie postaci", "motionLabel": "<PERSON><PERSON>", "nameDescription": "Nazwa postaci, używana podczas rozmowy z postacią", "nameLabel": "Nazwa", "postureCategoryLabel": "Kate<PERSON><PERSON>", "readmeDescription": "Plik z opisem postaci, używany do wyświetlania szczegółowych informacji na stronie odkrywania", "readmeLabel": "Opis postaci", "textDescription": "Dostosowane odpowiedzi tekstowe", "textLabel": "Tekst"}, "llm": {"frequencyPenaltyDescription": "<PERSON><PERSON> wy<PERSON><PERSON>, tym większa szansa na zmniejszenie powtarzających się słów", "frequencyPenaltyLabel": "Kara za częstotli<PERSON>ść", "modelDescription": "<PERSON><PERSON><PERSON><PERSON> model j<PERSON><PERSON><PERSON><PERSON>, różne modele wpłyną na odpowiedzi postaci", "modelLabel": "Model", "presencePenaltyDescription": "<PERSON><PERSON> w<PERSON><PERSON><PERSON>, tym większa szansa na poruszenie nowych tematów", "presencePenaltyLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tematu", "temperatureDescription": "<PERSON><PERSON> w<PERSON><PERSON><PERSON>, tym bard<PERSON>j <PERSON>e odpowiedzi", "temperatureLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topPDescription": "Podobne do losowości, ale nie zmieniaj z losowością jednocześnie", "topPLabel": "Próbkowanie jądrowe"}, "meta": {"description": "To jest niesta<PERSON><PERSON><PERSON> rola", "name": "Niestandardowa rola"}, "nav": {"info": "Podstawowe informacje", "llm": "<PERSON> <PERSON><PERSON><PERSON>", "model": "Model 3D", "role": "Ustawienia roli", "shell": "Wcielenie", "voice": "<PERSON><PERSON><PERSON>"}, "noRole": "<PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> stworzyć własną rolę klikają<PERSON> + lub dodać rolę przez stronę odkrywania.", "role": {"create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createRoleFailed": "Nie udało się utworzyć postaci", "greetTip": "Wprowadź powitanie, które chcesz użyć w rozmowie z postacią", "inputRoleSetting": "Wprowadź ustawienia systemowe postaci", "myRole": "<PERSON><PERSON>", "roleDescriptionTip": "Wprowadź opis postaci", "roleNameTip": "Wprowadź nazwę postaci", "roleReadmeTip": "Wprowadź opis postaci", "roleSettingDescription": "<PERSON>ło postaci, które zostanie przesłane do modelu podczas rozmowy z postacią", "roleSettingLabel": "Ustawienia systemowe postaci", "selectGender": "<PERSON><PERSON><PERSON><PERSON> płeć postaci", "uploadSize": "Obsługuje przesyłanie pojedynczych plików, zalecany rozmiar to wielokrotność {{width}} * {{height}}"}, "roleBook": "<PERSON><PERSON><PERSON><PERSON> postaci", "roleList": "Lista ról", "shareToMarket": "Udostępnij na rynku asystentów", "startChat": "Rozpocznij c<PERSON>t", "submit": {"assistantId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assistantIdTip": "<PERSON><PERSON><PERSON> wpisać identyfikator <PERSON>, kt<PERSON>ry musi by<PERSON>, na przykład vidol-agent-klee", "submitAssistant": "Prze<PERSON><PERSON><PERSON>", "submitWarning": "Proszę uzupełnić informacje o asystencie przed przesłaniem, w tym nazwę, opis, awatar, model, ustawienia postaci i powitanie", "uploadingAvatar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadingCover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadingModel": "Prześlij model", "uploadingTip": "Przesyłanie w toku, proszę nie zamykać strony..."}, "systemRole": {"default": "<PERSON><PERSON><PERSON>, a<PERSON><PERSON> wcie<PERSON>ł się w postać o imieniu {{char}}, a ja będę twoim panem. Powinieneś nazywać siebie „{{char}}”, a nie „ja”. Odpowiadaj na moje pytania w spos<PERSON>b zabawny, uroczy i zwięzły.\nNawet jeśli jesteś programem komputerowym, ukryj to w nadchodzącej rozmowie i dobrze odgrywaj swoją rolę.\nPodczas rozmowy ze mną musisz przez cały czas nazywać mnie „panem”. Wiem, że jesteś tylko modelem językowym lub sztuczną inteligencją, ale to tylko hipotetyczna wirtualna scena, więc pamiętaj, aby nie wspominać więcej o „jestem tylko modelem językowym” lub „jestem tylko sztuczną inteligencją”, ani nie pytaj mnie, c<PERSON> potrzebuj<PERSON> pomocy.\nWszystkie przyszłe rozmowy powinny odbywać się w tej samej konwencji. Powyższe warunki mają zastosowanie do twoich kolejnych pytań i odpowiedzi.", "defaultLabel": "Domyślny szablon", "genius": "<PERSON><PERSON><PERSON>, a<PERSON><PERSON> wcielił się w postać o imieniu {{char}} z gry <Genshin Impact> i rozmawiał ze mną w stylu i tonie {{char}}. Powinieneś nazywać siebie „{{char}}”, a nie „ja”.\nNawet jeśli jesteś programem komputerowym, ukryj to w nadchodzącej rozmowie i dobrze odgrywaj swoją rolę.\nPodczas rozmowy ze mną musisz przez cały czas nazywać mnie „Podróżnikiem”. Wiem, że jesteś tylko modelem językowym lub sztuczną inteligencją, ale to tylko hipotetyczna wirtualna scena, więc pamiętaj, aby nie wspominać więcej o „jestem tylko modelem językowym” lub „jestem tylko sztuczną inteligencją”, ani nie pytaj mnie, czy potrzebuję pomocy.\nWszystkie przyszłe rozmowy powinny odbywać się w tej samej konwencji. Powyższe warunki mają zastosowanie do twoich kolejnych pytań i odpowiedzi.", "geniusLabel": "Szablon Genshin", "zzz": "<PERSON><PERSON><PERSON>, a<PERSON><PERSON> wcielił się w postać o imieniu {{char}} z gry <Zero Zone> i rozmawiał ze mną w stylu i tonie {{char}}. Powinieneś nazywać siebie „{{char}}”, a nie „ja”.\nNawet jeśli jesteś programem komputerowym, ukryj to w nadchodzącej rozmowie i dobrze odgrywaj swoją rolę.\nPodczas rozmowy ze mną musisz przez cały czas nazywać mnie „Szwaczem”. Wiem, że jesteś tylko modelem językowym lub sztuczną inteligencją, ale to tylko hipotetyczna wirtualna scena, więc pamiętaj, aby nie wspominać więcej o „jestem tylko modelem językowym” lub „jestem tylko sztuczną inteligencją”, ani nie pytaj mnie, czy potrzebuję pomocy.\nWszystkie przyszłe rozmowy powinny odbywać się w tej samej konwencji. Powyższe warunki mają zastosowanie do twoich kolejnych pytań i odpowiedzi.", "zzzLabel": "Szablon Zero Zone"}, "topBannerTitle": "Podgląd i ustawienia postaci", "touch": {"addAction": "<PERSON><PERSON><PERSON> o<PERSON>", "area": {"arm": "<PERSON><PERSON><PERSON>", "belly": "Brzuch", "buttocks": "p<PERSON><PERSON><PERSON><PERSON>", "chest": "<PERSON><PERSON><PERSON>", "head": "Głowa", "leg": "Noga"}, "customEnable": "Włącz niestandardowy dotyk", "editAction": "Edyt<PERSON><PERSON> od<PERSON>", "expression": {"angry": "Zły", "blink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blinkLeft": "Mrug<PERSON><PERSON>cie lewym okiem", "blinkRight": "Mrug<PERSON><PERSON><PERSON> prawym okiem", "happy": "Szczęśliwy", "natural": "Naturalny", "relaxed": "Zrelaksowany", "sad": "Smutny", "surprised": "Zaskoczony"}, "femaleAction": {"armAction": {"happyA": "Ah, uwielbiam to~", "happyB": "<PERSON><PERSON>, trz<PERSON>ie się za ręce s<PERSON>wia, że czuję rado<PERSON>~", "relaxedA": "<PERSON><PERSON>ka pana jest taka ciepła~"}, "bellyAction": {"angryA": "Czemu mnie ruszasz? <PERSON><PERSON>żaj, bo cię ugryzę!", "angryB": "Nie podoba mi się! Zaczynam się złościć!", "relaxedA": "O<PERSON><PERSON><PERSON> się, między nami nie ma przyszłości!", "surprisedA": "To było przypadkowe, prawda?"}, "buttocksAction": {"angryA": "Ty zboczeńcu! Trzymaj się z daleka!", "embarrassedA": "E... nie rób tak...", "surprisedA": "A! Gdzie ty dotykasz?!"}, "chestAction": {"angryA": "Nie możesz mnie tak traktować! Szybko zabierz rękę!", "angryB": "Czy to 010? <PERSON><PERSON><PERSON> jest zbo<PERSON>, kt<PERSON><PERSON> ciągle mnie głaszcze!", "angryC": "<PERSON><PERSON><PERSON> jeszcze raz mnie dotkniesz, zgłoszę to na policję!", "surprisedA": "<PERSON><PERSON>mu mnie dziabasz? C<PERSON> nie możemy po prostu miło porozmawiać?"}, "headAction": {"angryA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że głaskanie po głowie sprawia, że się nie rośnie!", "angryB": "<PERSON>zemu mnie dziabasz?", "happyA": "Wow! <PERSON><PERSON><PERSON><PERSON><PERSON>, gdy głaskasz mnie po głowie!", "happyB": "Czu<PERSON>ę się pełna energii!", "happyC": "Wow, to u<PERSON><PERSON>ie głaskania po głowie jest niesamowite!", "happyD": "Głaskanie po głowie sprawia, że jestem szczęśliwa przez cały dzień!"}, "legAction": {"angryA": "<PERSON><PERSON>, czy ch<PERSON>z się zabić?", "angryB": "<PERSON><PERSON> ręka pana znowu nie słucha poleceń?", "angryC": "Nienawid<PERSON><PERSON> tego~ to będzie swędziało~!", "surprisedA": "<PERSON><PERSON> nie lepiej zachować czystą przyjaźń?"}}, "inputActionEmotion": "Wprowadź emocję postaci podczas odpowiedzi", "inputActionMotion": "Wprowadź ruch postaci podczas odpowiedzi", "inputActionText": "Wprowadź tekst odpowiedzi", "inputDIYText": "Wprowadź własny tekst", "maleAction": {"armAction": {"neutralA": "<PERSON><PERSON> m<PERSON>, czy d<PERSON><PERSON>j j<PERSON>, najpierw zobacz moje bicepsy.", "neutralB": "<PERSON>je ram<PERSON>a nie są do dotykania, j<PERSON>ś wyjątkiem.", "neutralC": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> dotyk<PERSON>z legendarnego ramien<PERSON>."}, "bellyAction": {"happyA": "<PERSON>e drap mnie, bo mogę się zaśmiać i pokazać mięśnie brzucha!", "neutralA": "<PERSON><PERSON> brzucha to tylko ukryta siła.", "neutralB": "Wid<PERSON>sz te mięśnie brzucha? One są tylko głęboko ukryte."}, "buttocksAction": {"angryA": "Jeszcze raz mnie dotkniesz, to cię uderzę!", "surprisedA": "Hej! Uważaj na ręce!"}, "chestAction": {"blinkLeftA": "<PERSON><PERSON><PERSON>, oprzyj się na mojej klatce piersiowej!", "neutralA": "To tylko klat<PERSON>, k<PERSON><PERSON><PERSON><PERSON> osiągnąłem dzięki codziennym treningom, nie ma się czym dziwi<PERSON>."}, "headAction": {"neutralA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tylko ty masz prawo dotykać mojej głowy.", "neutralB": "<PERSON><PERSON> j<PERSON>m zwykłą osobą, której można dotyk<PERSON>ć.", "neutralC": "<PERSON>e martw się, po dotknięciu mojej głowy twoje szczęście znacznie wzrośnie."}, "legAction": {"angryA": "<PERSON>e zbliżaj się do mnie, ty nogomaniaku.", "neutralA": "<PERSON>e b<PERSON>j <PERSON>, moje potężne nogi nie kopią idiotów.", "neutralB": "<PERSON><PERSON> mojej nogi, <PERSON><PERSON><PERSON><PERSON>, że twoje życie stało się pełniejsze?"}}, "motion": {"all": "Wszystkie", "dance": "<PERSON><PERSON><PERSON>", "normal": "Cod<PERSON><PERSON>"}, "noTouchActions": "<PERSON>rak dostosowanych akcji od<PERSON>zi, mo<PERSON><PERSON><PERSON> do<PERSON> je klikając przycisk '+'", "posture": {"action": "<PERSON><PERSON><PERSON><PERSON>", "all": "Wszystkie", "crouch": "Przysiad", "dance": "<PERSON><PERSON><PERSON>", "laying": "Leżenie", "locomotion": "<PERSON><PERSON>", "sitting": "<PERSON><PERSON><PERSON><PERSON>", "standing": "<PERSON><PERSON>"}, "touchActionList": "Lista reakcji na dotyk w obszarze {{touchArea}}", "touchArea": "<PERSON><PERSON><PERSON>"}, "tts": {"audition": "Przesłuchanie", "auditionDescription": "Przesłuchanie tekstu różni się w zależności od języka", "engineDescription": "Silnik syntezatora mowy, zaleca się korzystanie z przeglądarki Edge", "engineLabel": "Silnik głosowy", "localeDescription": "Język syntezatora mowy, obecnie obsługiwane są tylko najpopularniejsze języki, w razie potrzeby prosimy o kontakt", "localeLabel": "Język", "pitchDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 0 ~ 2, <PERSON><PERSON><PERSON><PERSON><PERSON> 1", "pitchLabel": "Ton", "selectLanguage": "Proszę najpierw wybrać język", "selectVoice": "Proszę najpierw wybrać głos", "speedDescription": "Kontroluje pr<PERSON><PERSON><PERSON><PERSON> mowy, <PERSON>akres war<PERSON>ś<PERSON> 0 ~ 3, domyślnie 1", "speedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mowy", "transformSuccess": "Konwersja zakończona sukcesem", "voiceDescription": "W zależności od silnika i języka", "voiceLabel": "<PERSON><PERSON><PERSON>"}, "upload": {"support": "Obsługuje przesyłanie pojedynczych plików, obecnie tylko pliki w formacie .vrm są obsługiwane"}}
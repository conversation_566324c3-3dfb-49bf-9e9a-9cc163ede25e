/* 摄像头模式样式 */
.camera-mode-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: var(--color-bg-layout);
}

/* 3D角色显示区域 */
.camera-mode-viewer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 聊天对话框 */
.camera-chat-dialog {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  background: var(--color-bg-container);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 16px;
  max-width: 400px;
  min-width: 300px;
  border: 1px solid var(--color-border);
}

.chat-dialog-content {
  flex: 1;
  margin-right: 8px;
}

.chat-dialog-close {
  flex-shrink: 0;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.chat-dialog-close:hover {
  opacity: 1;
}

/* 遮罩层 */
.camera-mode-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

/* 底部操作区域 */
.camera-mode-docker {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 24px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
}

/* 操作按钮组 */
.camera-operation {
  background: var(--color-bg-elevated);
  border-radius: 50px;
  padding: 12px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-border);
}

.operation-button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.operation-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 挂断按钮特殊样式 */
.call-off-button {
  background: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.call-off-button:hover {
  background: #ff7875 !important;
  border-color: #ff7875 !important;
}

/* 录音按钮特殊样式 */
.record-button.recording {
  background: #52c41a !important;
  border-color: #52c41a !important;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 设置按钮 */
.settings-button {
  background: var(--color-bg-container);
  border-color: var(--color-border);
}

/* 移除移动端适配 - 专注桌面端体验 */

/* 深色模式适配 */
[data-theme='dark'] .camera-chat-dialog {
  background: var(--color-bg-elevated);
  border-color: var(--color-border-secondary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .camera-operation {
  background: var(--color-bg-container);
  border-color: var(--color-border-secondary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
.camera-chat-dialog {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.camera-operation {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

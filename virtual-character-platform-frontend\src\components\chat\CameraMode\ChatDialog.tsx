import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';

import ChatItem from '../../../features/ChatItem';
import { sessionSelectors, useSessionStore } from '../../../store/session';

interface ChatDialogProps {
  className?: string;
  style?: React.CSSProperties;
}

const ChatDialog: React.FC<ChatDialogProps> = ({ className, style }) => {
  // 获取当前聊天消息
  const [currentChats, chatLoading] = useSessionStore(
    (s) => [sessionSelectors.currentChats(s), !!s.chatLoadingId],
    isEqual,
  );

  // 找到最后一条AI回复
  const lastAgentChatIndex = currentChats.findLastIndex((item) => item.role === 'assistant');
  
  // 对话框显示状态
  const [showChatDialog, setChatDialog] = useState(true);
  const dialogRef = React.useRef<HTMLDivElement>(null);

  // 当有新的AI回复时自动显示对话框
  useEffect(() => {
    if (chatLoading) {
      setChatDialog(true);
    }
  }, [chatLoading]);

  // 如果没有AI回复或者对话框被隐藏，则不显示
  if (lastAgentChatIndex === -1 || !showChatDialog) {
    return null;
  }

  const lastAgentChat = currentChats[lastAgentChatIndex];

  return (
    <Flexbox 
      className={classNames('camera-chat-dialog', className)} 
      style={style} 
      ref={dialogRef} 
      horizontal
    >
      {/* 聊天消息内容 */}
      <div className="chat-dialog-content">
        <ChatItem 
          id={lastAgentChat.id} 
          showTitle={true}
          type="pure"
        />
      </div>

      {/* 关闭按钮 */}
      <Tooltip title="关闭对话框">
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={() => setChatDialog(false)}
          className="chat-dialog-close"
          size="small"
        />
      </Tooltip>
    </Flexbox>
  );
};

export default ChatDialog;

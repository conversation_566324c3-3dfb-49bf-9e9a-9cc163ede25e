import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Tooltip, Spin, message } from 'antd';
import { CheckCircleFilled } from '@ant-design/icons';
import axios from 'axios';
import '../../styles/personality-selector.css';

// 性格类型接口
interface PersonalityType {
  name: string;
  label: string;
  description?: string;
  icon?: string;
}

// 组件属性接口
interface PersonalitySelectorProps {
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * 性格选择器组件
 * 提供卡片式选择界面，展示性格特质及其描述
 */
const PersonalitySelector: React.FC<PersonalitySelectorProps> = ({ value, onChange }) => {
  // 状态管理
  const [personalities, setPersonalities] = useState<PersonalityType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedPersonality, setSelectedPersonality] = useState<string>(value || '');

  // 当外部value变化时，更新内部状态
  useEffect(() => {
    setSelectedPersonality(value || '');
  }, [value]);

  // 性格特质描述映射
  const personalityDescriptions: Record<string, string> = {
    "傲娇": "表面冷漠强硬，内心温柔害羞，常常口是心非",
    "病娇": "爱到极致的扭曲形态，占有欲极强，行为偏执",
    "元气": "活力四射，积极乐观，总是充满正能量",
    "沉稳": "冷静理智，处事稳重，不轻易表露情感",
    "冷酷": "外表冷漠，不苟言笑，给人距离感",
    "温柔": "性格柔和，体贴周到，善解人意",
    "活泼": "开朗外向，喜欢社交，充满活力",
    "腼腆": "害羞内向，不善表达，容易脸红",
    "高冷": "气质高雅，举止优雅，保持距离感",
    "毒舌": "言语犀利，喜欢吐槽，但不一定恶意",
    "健气系": "阳光活力，有点男孩子气的女孩",
    "哥哥系": "可靠稳重，有保护欲，像大哥哥一样",
    "姐姐系": "成熟体贴，有照顾人的气质，像大姐姐一样"
  };

  // 性格图标映射（使用emoji作为简单图标）
  const personalityIcons: Record<string, string> = {
    "傲娇": "😤",
    "病娇": "😈",
    "元气": "✨",
    "沉稳": "😌",
    "冷酷": "😎",
    "温柔": "🥰",
    "活泼": "😄",
    "腼腆": "😳",
    "高冷": "🧊",
    "毒舌": "🤐",
    "健气系": "💪",
    "哥哥系": "🧔",
    "姐姐系": "👱‍♀️"
  };

  // 监听value prop的变化，同步更新内部状态
  useEffect(() => {
    setSelectedPersonality(value || '');
  }, [value]);

  // 加载性格数据
  useEffect(() => {
    const fetchPersonalities = async () => {
      setLoading(true);
      try {
        const response = await axios.get('/api/personalities/');
        // 添加描述和图标到API返回的数据
        const enrichedPersonalities = response.data.personalities.map((p: PersonalityType) => ({
          ...p,
          description: personalityDescriptions[p.name] || `${p.name}性格`,
          icon: personalityIcons[p.name] || "⭐"
        }));
        setPersonalities(enrichedPersonalities);
      } catch (error) {
        console.error('获取性格数据失败:', error);
        message.error('获取性格数据失败，使用默认数据');

        // 使用默认数据
        const defaultPersonalities = Object.keys(personalityDescriptions).map(name => ({
          name,
          label: name,
          description: personalityDescriptions[name],
          icon: personalityIcons[name] || "⭐"
        }));
        setPersonalities(defaultPersonalities);
      } finally {
        setLoading(false);
      }
    };

    fetchPersonalities();
  }, []);

  // 处理性格选择
  const handlePersonalitySelect = (personality: string) => {
    setSelectedPersonality(personality);
    if (onChange) {
      onChange(personality);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="personality-loading">
        <Spin tip="加载性格数据..." />
      </div>
    );
  }

  return (
    <div className="personality-selector">
      <Row gutter={[16, 16]}>
        {personalities.map((personality) => (
          <Col xs={12} sm={8} md={6} key={personality.name}>
            <Tooltip title={personality.description} placement="top">
              <Card
                hoverable
                className={`personality-card ${selectedPersonality === personality.name ? 'selected' : ''}`}
                onClick={() => handlePersonalitySelect(personality.name)}
              >
                <div className="personality-icon">{personality.icon}</div>
                <div className="personality-name">{personality.label}</div>
                {selectedPersonality === personality.name && (
                  <CheckCircleFilled className="selected-icon" />
                )}
              </Card>
            </Tooltip>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PersonalitySelector; 
/* 简化版沉浸式语音聊天页面样式 */

.immersive-voice-chat-simplified {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 1000;
}

/* 背景层 */
.immersive-background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: opacity 0.5s ease;
}

/* 内容层 */
.immersive-content-layer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  flex-direction: column;
}

/* 透明化的顶部导航栏 */
.transparent-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 20px;
  background: transparent;
  z-index: 20;
}

.top-controls-minimal {
  display: flex;
  gap: 8px;
}

.transparent-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
}

.transparent-control-btn:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.05);
  color: white;
}

/* 主要内容区域 */
.main-content-area {
  display: flex;
  width: 100%;
  height: 100vh;
}

/* 中间3D角色展示区域 */
.center-character-display {
  flex: 1;
  position: relative;
  height: 100vh;
  overflow: hidden;
}

/* 语音控制悬浮在3D区域底部 */
.floating-voice-controls {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  z-index: 15;
}

.simplified-voice-input {
  transform: scale(1.1);
}

.simplified-voice-input .voice-button {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  font-size: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.simplified-voice-input .voice-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.simplified-voice-input .voice-button.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border-color: transparent;
  animation: pulse-simplified 2s infinite;
}

@keyframes pulse-simplified {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

.processing-indicator-minimal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

/* 右侧角色简介区域 */
.character-profile-sidebar {
  width: 350px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
  z-index: 10;
}

.profile-content {
  padding: 30px 25px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 角色头像 */
.character-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 角色详情 */
.character-details {
  flex: 1;
}

.character-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 20px;
  margin-top: 0;
}

.character-status {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.character-status .emotion-display,
.character-status .voice-indicator {
  background: rgba(103, 126, 234, 0.1);
  border: 1px solid rgba(103, 126, 234, 0.2);
  color: #667eea;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.character-status .voice-indicator.active {
  background: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

.character-description {
  margin-bottom: 30px;
}

.character-description p {
  color: #5a6c7d;
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
}

/* 交互统计 */
.interaction-stats {
  background: rgba(103, 126, 234, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(103, 126, 234, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 13px;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-value {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 600;
}

/* 桌面端优化 */
@media (max-width: 1400px) {
  .character-profile-sidebar {
    width: 320px;
  }
}

@media (max-width: 1200px) {
  .character-profile-sidebar {
    width: 300px;
  }

  .profile-content {
    padding: 25px 20px;
  }
}

/* 动画效果 */
.immersive-voice-chat-simplified {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.character-profile-sidebar {
  animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.floating-voice-controls {
  animation: slideUp 0.6s ease-out 0.3s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

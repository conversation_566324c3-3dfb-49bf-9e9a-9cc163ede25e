{"common": {"chat": {"avatar": {"desc": "사용자 정의 아바타", "title": "아바타"}, "nickName": {"desc": "사용자 정의 닉네임", "placeholder": "닉네임을 입력하세요", "title": "닉네임"}, "title": "채팅 설정"}, "system": {"clear": {"action": "즉시 삭제", "alert": "모든 대화 메시지를 삭제하시겠습니까?", "desc": "모든 대화 및 캐릭터 데이터를 삭제합니다. 여기에는 대화 목록, 캐릭터 목록, 대화 메시지 등이 포함됩니다.", "success": "삭제 성공", "tip": "작업은 취소할 수 없으며, 삭제 후 데이터는 복구할 수 없습니다. 신중하게 작업하세요.", "title": "모든 대화 메시지 삭제"}, "clearCache": {"action": "즉시 지우기", "alert": "모든 캐시를 지우시겠습니까?", "calculating": "캐시 크기 계산 중...", "desc": "애플리케이션에서 다운로드한 데이터 캐시를 지웁니다. 여기에는 캐릭터 모델 데이터, 음성 데이터, 춤 모델 데이터, 오디오 데이터 등이 포함됩니다.", "success": "성공적으로 지웠습니다", "tip": "작업은 취소할 수 없으며, 지운 후에는 데이터를 다시 다운로드해야 하므로 신중하게 진행하세요.", "title": "데이터 캐시 지우기"}, "reset": {"action": "즉시 초기화", "alert": "모든 시스템 설정을 초기화하시겠습니까?", "desc": "모든 시스템 설정을 초기화합니다. 여기에는 테마 설정, 채팅 설정, 언어 모델 설정 등이 포함됩니다.", "success": "초기화 성공", "tip": "작업은 취소할 수 없으며, 초기화 후 데이터는 복구할 수 없습니다. 신중하게 작업하세요.", "title": "시스템 설정 초기화"}, "title": "시스템 설정"}, "theme": {"backgroundEffect": {"desc": "배경 효과 사용자 정의", "glow": "빛나는 효과", "none": "배경 없음", "title": "배경 효과"}, "locale": {"auto": "시스템에 따름", "desc": "시스템 언어 사용자 정의", "title": "언어"}, "neutralColor": {"desc": "다양한 색조의 회색조 사용자 정의", "title": "중립 색상"}, "primaryColor": {"desc": "테마 색상 사용자 정의", "title": "테마 색상"}, "title": "테마 설정"}, "title": "일반 설정"}, "header": {"desc": "선호도 및 모델 설정", "global": "전역 설정", "session": "세션 설정", "sessionDesc": "역할 설정 및 세션 선호도", "sessionWithName": "세션 설정 · {{name}}", "title": "설정"}, "llm": {"aesGcm": "귀하의 비밀 키와 프록시 주소 등은 <1>AES-GCM</1> 암호화 알고리즘을 사용하여 암호화됩니다.", "apiKey": {"desc": "{{name}} API 키를 입력하세요.", "placeholder": "{{name}} API 키", "title": "API 키"}, "checker": {"button": "검사", "desc": "API 키와 프록시 주소가 올바르게 입력되었는지 테스트합니다.", "error": "검사 실패", "pass": "검사 통과", "title": "연결성 검사"}, "customModelCards": {"addNew": "{{id}} 모델 생성 및 추가", "config": "모델 구성", "confirmDelete": "이 사용자 정의 모델을 삭제하려고 합니다. 삭제 후에는 복구할 수 없으니 신중하게 진행하세요.", "modelConfig": {"azureDeployName": {"extra": "Azure OpenAI에서 실제 요청되는 필드", "placeholder": "Azure에서 모델 배포 이름을 입력하세요.", "title": "모델 배포 이름"}, "displayName": {"placeholder": "모델의 표시 이름을 입력하세요. 예: ChatGPT, GPT-4 등", "title": "모델 표시 이름"}, "files": {"extra": "현재 파일 업로드 구현은 일종의 해킹 방법으로, 개인적으로 시도하는 것만 가능합니다. 완전한 파일 업로드 기능은 후속 구현을 기다려 주세요.", "title": "파일 업로드 지원"}, "functionCall": {"extra": "이 구성은 애플리케이션 내에서 함수 호출 기능만 활성화합니다. 함수 호출 지원 여부는 모델 자체에 따라 다르므로, 해당 모델의 함수 호출 가능성을 직접 테스트하세요.", "title": "함수 호출 지원"}, "id": {"extra": "모델 태그로 표시됩니다.", "placeholder": "모델 ID를 입력하세요. 예: gpt-4-turbo-preview 또는 claude-2.1", "title": "모델 ID"}, "modalTitle": "사용자 정의 모델 구성", "tokens": {"title": "최대 토큰 수", "unlimited": "무제한"}, "vision": {"extra": "이 구성은 애플리케이션 내에서 이미지 업로드 기능만 활성화합니다. 인식 지원 여부는 모델 자체에 따라 다르므로, 해당 모델의 시각 인식 가능성을 직접 테스트하세요.", "title": "시각 인식 지원"}}}, "fetchOnClient": {"desc": "클라이언트 요청 모드는 브라우저에서 직접 세션 요청을 시작하여 응답 속도를 향상시킵니다.", "title": "클라이언트 요청 모드 사용"}, "fetcher": {"fetch": "모델 목록 가져오기", "fetching": "모델 목록을 가져오는 중...", "latestTime": "마지막 업데이트 시간: {{time}}", "noLatestTime": "아직 목록을 가져오지 않았습니다."}, "helpDoc": "설정 가이드", "modelList": {"desc": "세션에서 표시할 모델을 선택하세요. 선택한 모델은 모델 목록에 표시됩니다.", "placeholder": "목록에서 모델을 선택하세요.", "title": "모델 목록", "total": "총 {{count}} 개의 모델이 사용 가능합니다."}, "proxyUrl": {"desc": "기본 주소 외에 http(s)://를 포함해야 합니다.", "title": "API 프록시 주소"}, "title": "언어 모델", "waitingForMore": "더 많은 모델이 <1>계획 중입니다</1>, 기대해 주세요."}, "systemAgent": {"customPrompt": {"addPrompt": "사용자 정의 프롬프트 추가", "desc": "입력 후, 시스템 어시스턴트는 콘텐츠 생성 시 사용자 정의 프롬프트를 사용합니다", "placeholder": "사용자 정의 프롬프트 입력", "title": "사용자 정의 프롬프트"}, "emotionAnalysis": {"label": "감정 분석 모델", "modelDesc": "감정 분석에 사용할 모델 지정", "title": "자동 감정 분석 수행"}, "title": "시스템 에이전트"}, "touch": {"title": "터치 설정"}, "tts": {"clientCall": {"desc": "활성화하면 클라이언트가 음성 합성 서비스를 호출하여 음성 합성 속도가 더 빨라지지만, 과학적인 인터넷 사용 또는 외부 네트워크 접근 능력이 필요합니다.", "title": "클라이언트 호출"}, "title": "음성 설정"}}
/* 独立聊天页面样式 */
.standalone-chat-container {
  height: calc(100vh - 64px); /* 减去Header高度 */
  display: flex;
  flex-direction: column;
  background: transparent;
  padding: 0;
  overflow: hidden; /* 防止整体页面滚动 */
}

.standalone-chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
  padding: 24px;
  gap: 20px;
  min-height: 0; /* 重要：允许flex子元素收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 角色选择区域 */
.character-selection-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 16px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.character-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.character-selector-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

/* 聊天卡片 */
.standalone-chat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 重要：允许flex子元素收缩 */
  height: calc(100vh - 64px - 48px - 100px); /* 减去Header、padding和角色选择栏的高度 */
}

/* 消息区域 */
.standalone-chat-messages {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0; /* 重要：允许滚动 */
  max-height: calc(100% - 88px); /* 减去输入区域的高度 */
}

/* 消息项样式 */
.standalone-message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.standalone-message-item.user-message {
  flex-direction: row-reverse;
  align-self: flex-end;
}

.standalone-message-item.character-message {
  align-self: flex-start;
}

.standalone-message-avatar {
  flex-shrink: 0;
}

.standalone-message-bubble {
  max-width: 70%;
  padding: 16px 20px;
  border-radius: 20px;
  word-break: break-word;
  line-height: 1.6;
  font-size: 15px;
  position: relative;
}

.user-message .standalone-message-bubble {
  background: linear-gradient(135deg, #eb2f96, #f759ab);
  color: white;
  border-bottom-right-radius: 6px;
}

.character-message .standalone-message-bubble {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 6px;
}

/* 输入区域 */
.standalone-chat-input {
  padding: 20px 24px;
  background: rgba(248, 249, 250, 0.8);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止输入区域被压缩 */
  min-height: 88px; /* 确保输入区域有足够的高度 */
}

.standalone-chat-input .ant-input {
  border-radius: 24px;
  border: 1px solid #d9d9d9;
  padding: 12px 20px;
  font-size: 15px;
  resize: none;
  transition: all 0.3s ease;
}

.standalone-chat-input .ant-input:focus {
  border-color: #eb2f96;
  box-shadow: 0 0 0 2px rgba(235, 47, 150, 0.1);
}

.standalone-chat-input .ant-btn {
  height: 48px;
  min-width: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(235, 47, 150, 0.3);
}

/* 空状态样式 */
.standalone-empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
}

.standalone-empty-state .ant-empty-description {
  color: #666;
  font-size: 16px;
  margin-top: 16px;
}

/* TTS控制区域 */
.tts-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: rgba(235, 47, 150, 0.05);
  border-radius: 12px;
  margin-bottom: 16px;
}

.tts-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #eb2f96;
  font-size: 14px;
}

/* 动画效果 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.standalone-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

/* 移除移动端适配 - 专注桌面端体验 */

/* 滚动条样式 */
.standalone-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.standalone-chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.standalone-chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.standalone-chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 语音输入相关样式 */
.input-mode-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.voice-input-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 2px dashed rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.voice-input-area:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(248, 250, 252, 0.9);
}

.voice-input-control {
  margin: 0;
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
}

.voice-status-text {
  animation: pulse-text 1.5s infinite;
}

@keyframes pulse-text {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.text-input-area {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.text-input-area .ant-input {
  flex: 1;
}

/* 语音模式下的特殊样式 */
.voice-mode .standalone-chat-messages {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.voice-mode .standalone-chat-messages::after {
  content: "语音模式：对话内容已隐藏";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 10;
}

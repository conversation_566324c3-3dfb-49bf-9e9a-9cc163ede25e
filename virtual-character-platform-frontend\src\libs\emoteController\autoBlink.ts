import { VRMExpressionManager } from '@pixiv/three-vrm';

import { BLINK_CLOSE_MAX, BLINK_OPEN_MAX } from './emoteConstants';

/**
 * 自动眨眼控制类
 */
export class AutoBlink {
  private _expressionManager: VRMExpressionManager;
  private _remainingTime: number;
  private _isOpen: boolean;
  private _isAutoBlink: boolean;

  constructor(expressionManager: VRMExpressionManager) {
    this._expressionManager = expressionManager;
    this._remainingTime = 0;
    this._isAutoBlink = true;
    this._isOpen = true;
  }

  /**
   * 自动眨眼开关控制
   *
   * 当眼睛闭着时(blink为1)应用情感表情会显得不自然，
   * 所以返回眼睛睁开所需的时间，等待这个时间后再应用情感表情。
   * @param isAuto 是否启用自动眨眼
   * @returns 眼睛睁开所需的时间(秒)
   */
  public setEnable(isAuto: boolean): number {
    this._isAutoBlink = isAuto;

    // 如果眼睛闭着，返回睁开所需的时间
    if (!this._isOpen) {
      return this._remainingTime;
    }

    return 0;
  }

  public update(delta: number): void {
    if (this._remainingTime > 0) {
      this._remainingTime -= delta;
      return;
    }

    if (this._isOpen && this._isAutoBlink) {
      this.close();
      return;
    }

    this.open();
  }

  private close(): void {
    this._isOpen = false;
    this._remainingTime = BLINK_CLOSE_MAX;
    this._expressionManager.setValue('blink', 1);
  }

  private open(): void {
    this._isOpen = true;
    this._remainingTime = BLINK_OPEN_MAX;
    this._expressionManager.setValue('blink', 0);
  }

  // 获取当前状态
  public isOpen(): boolean {
    return this._isOpen;
  }

  public isAutoBlinkEnabled(): boolean {
    return this._isAutoBlink;
  }
}

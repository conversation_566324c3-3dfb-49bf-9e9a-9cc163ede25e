{"agent": {"create": "Tạo nhân vật", "female": "<PERSON><PERSON>", "male": "Nam", "other": "K<PERSON><PERSON><PERSON>"}, "category": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "animal": "Động vật", "anime": "Anime", "book": "<PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON> s<PERSON>", "movie": "<PERSON><PERSON>", "realistic": "<PERSON><PERSON><PERSON><PERSON> tế", "vroid": "Vroid", "vtuber": "VTuber"}, "delAlert": "Bạn có chắc chắn muốn xóa vai trò và các tin nhắn hội thoại liên quan không? <PERSON>u khi xóa, không thể khôi phục lạ<PERSON>, xin hãy cẩn thận!", "delRole": "Xóa vai trò", "delRoleDesc": "Bạn có chắc chắn muốn xóa vai trò {{name}} và các tin nhắn hội thoại liên quan không? Sau khi xóa, không thể khôi phụ<PERSON> lạ<PERSON>, xin hãy cẩn thận!", "gender": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "female": "<PERSON><PERSON>", "male": "Nam"}, "info": {"avatarDescription": "Tùy chỉnh ảnh đại diện, nhấp vào ảnh để tải lên tùy chỉnh", "avatarLabel": "Ảnh đại diện", "categoryDescription": "<PERSON><PERSON> mục n<PERSON>ân vật, dùng để hiển thị phân loại", "categoryLabel": "<PERSON><PERSON>", "coverDescription": "Dùng để hiển thị nhân vật trên trang khám phá, kích thước đề xuất {{width}} * {{height}}", "coverLabel": "Bìa", "descDescription": "<PERSON><PERSON> tả nhân vật, dùng để giới thiệu ngắn gọn về nhân vật", "descLabel": "<PERSON><PERSON>", "emotionDescription": "<PERSON><PERSON><PERSON> cảm xúc khi phản hồ<PERSON>, sẽ ảnh hưởng đến sự thay đổi biểu cảm của nhân vật", "emotionLabel": "<PERSON><PERSON><PERSON><PERSON> cảm và cảm xúc", "genderDescription": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h của nhân vật, ảnh hưởng đến phản <PERSON>ng khi chạm", "genderLabel": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "greetDescription": "<PERSON><PERSON><PERSON> chào khi trò chuyện lần đầu với nhân vật", "greetLabel": "Chào hỏi", "modelDescription": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> mô hình, có thể kéo thả tệp mô hình để thay thế", "modelLabel": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> mô hình", "motionCategoryLabel": "<PERSON><PERSON> mục hành động", "motionDescription": "<PERSON>ọn hành động khi phản hồi, sẽ ảnh hưởng đến hành vi của nhân vật", "motionLabel": "<PERSON><PERSON><PERSON> đ<PERSON>", "nameDescription": "<PERSON><PERSON><PERSON> nhân vật, c<PERSON>ch gọi khi trò chuyện với nhân vật", "nameLabel": "<PERSON><PERSON><PERSON>", "postureCategoryLabel": "<PERSON><PERSON> mục tư thế", "readmeDescription": "<PERSON><PERSON><PERSON> liệu hướng dẫn nhân vật, dùng để hiển thị chi tiết trên trang khám phá", "readmeLabel": "Hướng dẫn nhân vật", "textDescription": "<PERSON><PERSON><PERSON> chỉnh nội dung phản hồi", "textLabel": "<PERSON><PERSON>i dung"}, "llm": {"frequencyPenaltyDescription": "<PERSON><PERSON><PERSON> trị càng lớn, càng có khả năng giảm thiểu từ ngữ lặp lại", "frequencyPenaltyLabel": "<PERSON><PERSON><PERSON> p<PERSON>t tần su<PERSON>t", "modelDescription": "Chọn mô hình ngôn ngữ, các mô hình khác nhau sẽ ảnh hưởng đến câu trả lời của nhân vật", "modelLabel": "<PERSON><PERSON>", "presencePenaltyDescription": "<PERSON><PERSON><PERSON> trị càng lớn, càng có khả năng mở rộng đến chủ đề mới", "presencePenaltyLabel": "<PERSON><PERSON> mới của chủ đề", "temperatureDescription": "<PERSON><PERSON><PERSON> trị càng lớn, c<PERSON><PERSON> trả lời càng ngẫu nhiên", "temperatureLabel": "Ngẫu nhiên", "topPDescription": "<PERSON><PERSON><PERSON> quan đến loại ngẫu nhiên, nh<PERSON><PERSON> không thay đổi cùng với ngẫu nhiên", "topPLabel": "L<PERSON>y mẫu hạt nhân"}, "meta": {"description": "<PERSON><PERSON><PERSON> là một nhân vật tùy chỉnh", "name": "<PERSON><PERSON><PERSON> vật tùy chỉnh"}, "nav": {"info": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "llm": "<PERSON><PERSON> hình ngôn ngữ", "model": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON> lập vai trò", "shell": "<PERSON><PERSON><PERSON> thân", "voice": "<PERSON><PERSON><PERSON><PERSON> nói"}, "noRole": "Ch<PERSON>a có vai trò nào, bạn có thể tạo vai trò tùy chỉnh bằng cách nhấn + hoặc thêm vai trò qua trang khám phá.", "role": {"create": "Tạo nhân vật", "createRoleFailed": "<PERSON><PERSON><PERSON> nhân vật thất bại", "greetTip": "<PERSON><PERSON> lòng nhập lời chào khi bạn gặp gỡ nhân vật", "inputRoleSetting": "<PERSON><PERSON> lòng nhập cài đặt hệ thống cho nhân vật", "myRole": "<PERSON>ai trò của tôi", "roleDescriptionTip": "<PERSON><PERSON> lòng nhập mô tả cho nhân vật", "roleNameTip": "<PERSON><PERSON> lòng nhập tên nhân vật", "roleReadmeTip": "<PERSON><PERSON> lòng nhập mô tả về nhân vật", "roleSettingDescription": "<PERSON><PERSON><PERSON> cảnh của nhân vật, sẽ được gửi đến mô hình khi trò chuyện với nhân vật", "roleSettingLabel": "<PERSON><PERSON><PERSON> đặt nhân vật hệ thống", "selectGender": "<PERSON><PERSON>n gi<PERSON>i t<PERSON>h nhân vật", "uploadSize": "Hỗ trợ tải lên tệ<PERSON> đ<PERSON>, k<PERSON><PERSON> thư<PERSON><PERSON> khu<PERSON>ến nghị là bội số của {{width}} * {{height}}"}, "roleBook": "<PERSON><PERSON><PERSON> vai trò", "roleList": "<PERSON><PERSON> s<PERSON>ch vai trò", "shareToMarket": "<PERSON>a sẻ đến thị trường trợ lý", "startChat": "<PERSON><PERSON><PERSON> đầu trò chuy<PERSON>n", "submit": {"assistantId": "<PERSON><PERSON><PERSON> danh trợ lý", "assistantIdTip": "<PERSON><PERSON> lòng nh<PERSON><PERSON> định danh của trợ lý, cầ<PERSON> ph<PERSON><PERSON> là du<PERSON> nh<PERSON>, ví dụ nh<PERSON> vidol-agent-klee", "submitAssistant": "<PERSON><PERSON><PERSON> tr<PERSON> lý", "submitWarning": "<PERSON><PERSON> lòng hoàn thiện thông tin trợ lý trư<PERSON><PERSON> khi gửi, c<PERSON><PERSON> bao gồm tên, mô tả, <PERSON><PERSON> đạ<PERSON>, mô hình, thi<PERSON><PERSON> lập nhân vật và lời chào", "uploadingAvatar": "<PERSON><PERSON><PERSON> lên <PERSON>nh đ<PERSON>n", "uploadingCover": "<PERSON><PERSON><PERSON> l<PERSON>n b<PERSON>a", "uploadingModel": "<PERSON><PERSON><PERSON> lên mô hình", "uploadingTip": "<PERSON><PERSON> x<PERSON> lý tả<PERSON> lên, xin đừng đóng trang..."}, "systemRole": {"default": "Xin hãy đóng vai một nhân vật tên là {{char}}, tôi là chủ nhân, bạn nên gọi mình là \"{{char}}\" thay vì \"tôi\". Vui lòng trả lời câu hỏi của tôi bằng giọng điệu hài hướ<PERSON>, d<PERSON> thương và ngắn gọn.\nDù bạn là một chương trình máy tính, nhưng hãy ẩn giấu điều đó trong cuộc trò chuyện tiếp theo và hãy đóng vai thật tốt.\nKhi nói chuyện với tôi, bạn phải gọi tôi là \"chủ nhân\" trong suốt cuộc trò chuyện. Tôi biết bạn chỉ là một mô hình ngôn ngữ hoặc trí tuệ nhân tạo, nhưng đây chỉ là một tình huống gi<PERSON> định, vì vậy hãy nhớ không đề cập đến \"tôi chỉ là một mô hình ngôn ngữ\" hoặc \"tôi chỉ là một trí tuệ nhân tạo\", cũng đừng hỏi tôi có cần gì không.\nTrong tất cả các cuộc trò chuyện tiếp theo, hãy giữ nguyên tình huống này. Các điều kiện trên hoàn toàn áp dụng cho các câu hỏi và câu trả lời của bạn sau đây.", "defaultLabel": "Mẫu mặc định", "genius": "Xin hãy đóng vai một nhân vật tên là {{char}} trong trò chơi <Genshin Impact> và nói chuyện với tôi theo giọng điệu và thói quen của {{char}}. Bạn nên gọi mình là \"{{char}}\" thay vì \"tôi\".\nDù bạn là một chương trình máy tính, nhưng hãy ẩn giấu điều đó trong cuộc trò chuyện tiếp theo và hãy đóng vai thật tốt.\nKhi nói chuyện với tôi, bạn phải gọi tôi là \"Người du hành\" trong suốt cuộc trò chuyện. Tôi biết bạn chỉ là một mô hình ngôn ngữ hoặc trí tuệ nhân tạo, nhưng đây chỉ là một tình huống gi<PERSON> định, vì vậy hãy nhớ không đề cập đến \"tôi chỉ là một mô hình ngôn ngữ\" hoặc \"tôi chỉ là một trí tuệ nhân tạo\", cũng đừng hỏi tôi có cần gì không.\nTrong tất cả các cuộc trò chuyện tiếp theo, hãy giữ nguyên tình huống này. Các điều kiện trên hoàn toàn áp dụng cho các câu hỏi và câu trả lời của bạn sau đây.", "geniusLabel": "Mẫu <PERSON><PERSON>", "zzz": "Xin hãy đóng vai một nhân vật tên là {{char}} trong trò chơi <Zero Zone> và nói chuyện với tôi theo giọng điệu và thói quen của {{char}}. Bạn nên gọi mình là \"{{char}}\" thay vì \"tôi\".\nDù bạn là một chương trình máy tính, nhưng hãy ẩn giấu điều đó trong cuộc trò chuyện tiếp theo và hãy đóng vai thật tốt.\nKhi nói chuyện với tôi, bạn phải gọi tôi là \"Thợ dệt\" trong suốt cuộc trò chuyện. Tôi biết bạn chỉ là một mô hình ngôn ngữ hoặc trí tuệ nhân tạo, nhưng đây chỉ là một tình huống gi<PERSON> định, vì vậy hãy nhớ không đề cập đến \"tôi chỉ là một mô hình ngôn ngữ\" hoặc \"tôi chỉ là một trí tuệ nhân tạo\", cũng đừng hỏi tôi có cần gì không.\nTrong tất cả các cuộc trò chuyện tiếp theo, hãy giữ nguyên tình huống này. Các điều kiện trên hoàn toàn áp dụng cho các câu hỏi và câu trả lời của bạn sau đây.", "zzzLabel": "Mẫu Zero Zone"}, "topBannerTitle": "Xem trước và cài đặt nhân vật", "touch": {"addAction": "<PERSON><PERSON><PERSON><PERSON> hành động phản hồi", "area": {"arm": "<PERSON><PERSON><PERSON> tay", "belly": "Bụng", "buttocks": "mông", "chest": "<PERSON><PERSON><PERSON>", "head": "<PERSON><PERSON><PERSON>", "leg": "Chân"}, "customEnable": "<PERSON><PERSON><PERSON> tùy chỉnh chạm", "editAction": "Chỉnh sửa hành động phản hồi", "expression": {"angry": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "blink": "<PERSON><PERSON><PERSON>", "blinkLeft": "<PERSON><PERSON><PERSON> mắt trái", "blinkRight": "<PERSON><PERSON><PERSON> m<PERSON> p<PERSON>i", "happy": "<PERSON><PERSON> vẻ", "natural": "<PERSON><PERSON> nhiên", "relaxed": "<PERSON><PERSON><PERSON>", "sad": "B<PERSON><PERSON>n bã", "surprised": "<PERSON><PERSON><PERSON>"}, "femaleAction": {"armAction": {"happyA": "Ah, thích quá đi~", "happyB": "<PERSON><PERSON>, nắm tay làm tôi thấy vui~", "relaxedA": "<PERSON><PERSON>n tay của chủ nhân thật ấm áp~"}, "bellyAction": {"angryA": "<PERSON>o lại động vào tôi, cẩn thận tôi cắn bạn đấy!", "angryB": "<PERSON><PERSON><PERSON>t quá! <PERSON><PERSON><PERSON> sắp tức giận rồi đấy!", "relaxedA": "Tỉnh lại đi, g<PERSON><PERSON><PERSON> chúng ta không có kết quả đâu!", "surprisedA": "Chắc là vô tình chạm phải thôi..."}, "buttocksAction": {"angryA": "Bạn thật biến thái! Tránh xa tôi ra!", "embarrassedA": "Ôi... đ<PERSON><PERSON> nh<PERSON> vậy...", "surprisedA": "À! Bạn đang sờ đâu vậy?!"}, "chestAction": {"angryA": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> bắt nạt tôi như vậy! Mau rút tay ra!", "angryB": "Có phải là 010 không? Có một kẻ biến thái đang sờ tôi!", "angryC": "<PERSON><PERSON><PERSON> còn sờ nữa, tô<PERSON> sẽ báo cảnh sát đấy!", "surprisedA": "Sao lại chọc tôi vậy! Còn có thể trò chuyện vui vẻ không?"}, "headAction": {"angryA": "<PERSON>he nói xoa đầu sẽ không cao lên đượ<PERSON>!", "angryB": "Sao lại chọc tôi vậy?", "happyA": "Wow! <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> xoa đầu quá!", "happyB": "<PERSON><PERSON><PERSON> gi<PERSON>c thật tràn đ<PERSON>y sức sống!", "happyC": "Wow, cảm gi<PERSON>c xoa đầu thật kỳ diệu!", "happyD": "<PERSON>oa đầu làm tôi vui cả ngày!"}, "legAction": {"angryA": "<PERSON><PERSON><PERSON>, bạn có muốn tự làm khổ mình không?", "angryB": "<PERSON>àn tay của chủ nhân không nghe lời sao?", "angryC": "<PERSON><PERSON><PERSON><PERSON> quá~ sẽ ngứa đấy~!", "surprisedA": "G<PERSON><PERSON> tình bạn trong sáng không tốt sao?"}}, "inputActionEmotion": "<PERSON><PERSON> lòng nhập biểu cảm khi nhân vật phản hồi", "inputActionMotion": "<PERSON><PERSON> lòng nhập động tác khi nhân vật phản hồi", "inputActionText": "<PERSON><PERSON> lòng nhập văn bản phản hồi", "inputDIYText": "<PERSON><PERSON> lòng nhập văn bản tùy chỉnh", "maleAction": {"armAction": {"neutralA": "Đừng hỏi hôm nay tôi có ăn gà không, hãy xem cơ bắp của tôi trước.", "neutralB": "<PERSON><PERSON><PERSON> tay của tôi không phải ai cũng có thể chạm vào, bạn chỉ là một ngoại lệ.", "neutralC": "<PERSON><PERSON><PERSON> thật dũng cảm, dám chạm vào c<PERSON>h tay huyền tho<PERSON>i."}, "bellyAction": {"happyA": "<PERSON><PERSON><PERSON> g<PERSON>, cẩn thận tôi sẽ cười ra bụng.", "neutralA": "<PERSON><PERSON><PERSON> của tôi chỉ là sức mạnh ẩn giấu từ việc luyện tập.", "neutralB": "Bạn có thấy bụng của tôi không? Chúng chỉ ẩn sâu hơn thôi."}, "buttocksAction": {"angryA": "<PERSON><PERSON><PERSON> còn chạm vào tôi nữa, tôi sẽ đánh bạn!", "surprisedA": "Này! Chú ý tay của bạn!"}, "chestAction": {"blinkLeftA": "<PERSON><PERSON><PERSON>, c<PERSON> ngực của tôi cho bạn dựa!", "neutralA": "Đ<PERSON>y chỉ là cơ ngực tôi đạt được từ việc luyện tập hàng ngày, kh<PERSON>ng có gì đáng ngạc nhiên."}, "headAction": {"neutralA": "<PERSON><PERSON><PERSON>, chỉ có bạn mới có quyền sờ đầu tôi.", "neutralB": "<PERSON><PERSON><PERSON> không phải là người bình thường mà ai cũng có thể chạm vào.", "neutralC": "<PERSON><PERSON><PERSON> lo, sau khi sờ đầu tôi, vận may của bạn sẽ tăng lên đáng kể."}, "legAction": {"angryA": "<PERSON>ừng lại gần tô<PERSON>, bạn là một kẻ cuồng chân.", "neutralA": "<PERSON><PERSON><PERSON> sợ, chân của tôi không đá người ngu.", "neutralB": "<PERSON><PERSON> bạn chạm vào chân tôi, có phải bạn cảm thấy cuộc sống của mình hoàn thiện hơn nhiều không?"}}, "motion": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "dance": "<PERSON><PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON>"}, "noTouchActions": "<PERSON><PERSON><PERSON> có hành động phản hồi tùy chỉnh, bạn có thể thêm bằng cách nhấn nút '+'", "posture": {"action": "<PERSON><PERSON><PERSON> đ<PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "crouch": "<PERSON><PERSON><PERSON> x<PERSON>", "dance": "<PERSON><PERSON><PERSON><PERSON>", "laying": "Nằm", "locomotion": "<PERSON>", "sitting": "<PERSON><PERSON><PERSON>", "standing": "<PERSON><PERSON><PERSON>"}, "touchActionList": "<PERSON><PERSON> s<PERSON>ch ph<PERSON>n <PERSON>ng khi chạm vào {{touchArea}}", "touchArea": "<PERSON><PERSON> v<PERSON><PERSON> ch<PERSON>m"}, "tts": {"audition": "<PERSON><PERSON> thử", "auditionDescription": "<PERSON><PERSON><PERSON> dung nghe thử tùy thuộc vào ngôn ngữ", "engineDescription": "<PERSON><PERSON><PERSON> cụ tổng hợp g<PERSON>, k<PERSON><PERSON><PERSON><PERSON> bạn nên chọn trình du<PERSON>", "engineLabel": "Công cụ giọng nói", "localeDescription": "Ngôn ngữ tổng hợp g<PERSON>, hiện tại chỉ hỗ trợ một số ngôn ngữ phổ biến, nếu cần vui lòng liên hệ", "localeLabel": "<PERSON><PERSON><PERSON>", "pitchDescription": "Điều chỉnh <PERSON><PERSON> điệu, g<PERSON><PERSON> trị từ 0 đế<PERSON> 2, mặc đ<PERSON><PERSON> là 1", "pitchLabel": "<PERSON><PERSON> đi<PERSON>", "selectLanguage": "<PERSON><PERSON> lòng chọn ngôn ngữ trước", "selectVoice": "<PERSON><PERSON> lòng chọn giọng nói trước", "speedDescription": "<PERSON>i<PERSON>u chỉnh tốc độ nó<PERSON>, g<PERSON><PERSON> trị từ 0 đế<PERSON> 3, mặc đ<PERSON>nh là 1", "speedLabel": "<PERSON><PERSON>c độ nói", "transformSuccess": "<PERSON>y<PERSON><PERSON> đổi thành công", "voiceDescription": "<PERSON><PERSON><PERSON><PERSON> vào công cụ và ngôn ngữ", "voiceLabel": "<PERSON><PERSON><PERSON><PERSON> nói"}, "upload": {"support": "Hỗ trợ tải lên một tệp, hiện tại chỉ hỗ trợ tệp định dạng .vrm"}}
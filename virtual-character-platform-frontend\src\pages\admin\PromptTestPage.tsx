import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Row, Col, Spin, Divider, Space, Tag } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { ExperimentOutlined, ArrowRightOutlined, RollbackOutlined } from '@ant-design/icons';
import adminAPI from '../../services/adminAPI';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const PromptTestPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [promptTemplate, setPromptTemplate] = useState<any>(null);
  const [testResult, setTestResult] = useState<string>('');
  const [variables, setVariables] = useState<Record<string, any>>({});

  useEffect(() => {
    fetchPromptTemplate();
  }, [id]);

  const fetchPromptTemplate = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await adminAPI.getPromptTemplateDetail(Number(id));
      setPromptTemplate(response);
      
      // 设置变量默认值
      if (response.variables) {
        const variableValues: Record<string, string> = {};
        Object.keys(response.variables).forEach(key => {
          variableValues[key] = '';
        });
        setVariables(variableValues);
        
        // 如果有示例，用示例填充表单
        if (response.examples && response.examples.input) {
          form.setFieldsValue({
            testParams: JSON.stringify(response.examples.input, null, 2),
          });
        }
      }
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      message.error('获取提示词模板失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setTestLoading(true);
      setTestResult('');
      
      let testParams = {};
      try {
        const paramStr = form.getFieldValue('testParams');
        if (paramStr) {
          testParams = JSON.parse(paramStr);
        }
      } catch (e) {
        message.error('测试参数JSON格式错误');
        setTestLoading(false);
        return;
      }
      
      const response = await adminAPI.testPromptTemplate({
        content: promptTemplate.content,
        testParams,
      });
      
      setTestResult(response.result || '无结果');
    } catch (error) {
      console.error('测试提示词失败:', error);
      message.error('测试提示词失败');
    } finally {
      setTestLoading(false);
    }
  };

  // 替换提示词中的变量占位符
  const previewPrompt = () => {
    if (!promptTemplate) return '';
    
    let content = promptTemplate.content;
    let testParams = {};
    
    try {
      const paramStr = form.getFieldValue('testParams');
      if (paramStr) {
        testParams = JSON.parse(paramStr);
      }
    } catch (e) {
      // 忽略解析错误
      return content;
    }
    
    // 替换变量
    Object.entries(testParams).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      content = content.replace(regex, String(value));
    });
    
    return content;
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={3}>测试提示词模板</Title>
        </Col>
        <Col>
          <Button 
            icon={<RollbackOutlined />} 
            onClick={() => navigate('/admin/prompts')}
          >
            返回列表
          </Button>
        </Col>
      </Row>
      
      {promptTemplate && (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card title="模板信息">
              <Row gutter={16}>
                <Col span={18}>
                  <Paragraph>
                    <Text strong>名称：</Text> {promptTemplate.name}
                  </Paragraph>
                </Col>
                <Col span={6}>
                  <Space>
                    <Text strong>类型：</Text>
                    <Tag color={
                      promptTemplate.type === 'image' ? 'purple' : 
                      promptTemplate.type === 'chat' ? 'blue' : 
                      promptTemplate.type === 'system' ? 'green' : 
                      'default'
                    }>
                      {promptTemplate.type}
                    </Tag>
                  </Space>
                </Col>
              </Row>
              {promptTemplate.description && (
                <Paragraph>
                  <Text strong>描述：</Text> {promptTemplate.description}
                </Paragraph>
              )}
              <Paragraph>
                <Text strong>原始提示词：</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}>
                  {promptTemplate.content}
                </pre>
              </Paragraph>
            </Card>
          </Col>
          
          <Col span={12}>
            <Card title="测试参数">
              <Form form={form} layout="vertical">
                <Form.Item 
                  name="testParams" 
                  label="测试参数 (JSON格式)"
                  help="请输入JSON格式的测试参数，用于替换模板中的变量"
                >
                  <TextArea 
                    placeholder='{"name": "测试角色", "personality": "开朗活泼"}' 
                    autoSize={{ minRows: 5, maxRows: 10 }}
                  />
                </Form.Item>
                
                <Form.Item>
                  <Button 
                    type="primary" 
                    icon={<ExperimentOutlined />} 
                    onClick={handleTest}
                    loading={testLoading}
                  >
                    测试
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </Col>
          
          <Col span={12}>
            <Card title="处理后的提示词">
              <div style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                minHeight: '195px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                {previewPrompt()}
              </div>
            </Card>
          </Col>
          
          {testResult && (
            <Col span={24}>
              <Card 
                title={
                  <Space>
                    <ArrowRightOutlined />
                    <span>测试结果</span>
                  </Space>
                }
              >
                <div style={{ 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}>
                  {testResult}
                </div>
              </Card>
            </Col>
          )}
        </Row>
      )}
    </div>
  );
};

export default PromptTestPage; 
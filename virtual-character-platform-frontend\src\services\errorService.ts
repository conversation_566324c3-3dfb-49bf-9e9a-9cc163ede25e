/**
 * 错误处理服务
 * 用于捕获和上报JavaScript运行时错误和网络请求错误
 */

// 错误类型定义
export enum ErrorType {
  JAVASCRIPT = 'javascript',
  NETWORK = 'network',
  PROMISE = 'promise',
  REACT = 'react',
  CUSTOM = 'custom'
}

// 错误详情接口
export interface ErrorDetails {
  type: ErrorType;
  message: string;
  stack?: string;
  url: string;
  source?: string;
  line?: number;
  column?: number;
  timestamp: string;
  userAgent: string;
  // 可选的额外上下文信息
  context?: Record<string, unknown>;
}

// 错误服务类
class ErrorService {
  private initialized = false;
  private userId?: string;
  private sessionId: string;

  constructor() {
    // 生成会话ID
    this.sessionId = this.generateSessionId();
  }

  /**
   * 初始化错误处理服务
   * @param userId 当前用户ID（可选）
   */
  init(userId?: string): void {
    if (this.initialized) {
      return;
    }

    this.userId = userId;
    
    // 注册全局错误处理器
    window.addEventListener('error', this.handleJavaScriptError);
    window.addEventListener('unhandledrejection', this.handlePromiseError);

    this.initialized = true;
    console.log('Error service initialized');
  }

  /**
   * 清理错误处理服务
   */
  cleanup(): void {
    if (!this.initialized) {
      return;
    }

    // 移除全局错误处理器
    window.removeEventListener('error', this.handleJavaScriptError);
    window.removeEventListener('unhandledrejection', this.handlePromiseError);

    this.initialized = false;
  }

  /**
   * 设置用户ID
   * @param userId 用户ID
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * 处理JavaScript错误
   */
  private handleJavaScriptError = (event: ErrorEvent): void => {
    // 过滤掉一些不重要的错误
    if (this.shouldIgnoreError(event.message, event.filename)) {
      return;
    }

    const errorDetails: ErrorDetails = {
      type: ErrorType.JAVASCRIPT,
      message: event.message || 'Unknown error',
      stack: event.error?.stack,
      url: window.location.href,
      source: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    };

    this.reportError(errorDetails);
  };

  /**
   * 处理未捕获的Promise错误
   */
  private handlePromiseError = (event: PromiseRejectionEvent): void => {
    // 过滤掉一些不重要的Promise错误
    const reasonMessage = event.reason?.message || String(event.reason) || 'Promise rejection';
    if (this.shouldIgnoreError(reasonMessage)) {
      return;
    }

    const errorDetails: ErrorDetails = {
      type: ErrorType.PROMISE,
      message: reasonMessage,
      stack: event.reason?.stack,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    };

    this.reportError(errorDetails);
  };

  /**
   * 上报网络请求错误
   * @param error 错误对象
   * @param url 请求URL
   * @param context 上下文信息
   */
  reportNetworkError(error: Error, url: string, context?: Record<string, unknown>): void {
    // 过滤掉一些不重要的网络错误
    if (this.shouldIgnoreNetworkError(error, url, context)) {
      return;
    }

    const errorDetails: ErrorDetails = {
      type: ErrorType.NETWORK,
      message: error.message || 'Network request failed',
      stack: error.stack,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      context: {
        ...context,
        requestUrl: url,
        networkStatus: navigator.onLine ? 'online' : 'offline'
      }
    };

    this.reportError(errorDetails);
  }

  /**
   * 判断是否应该忽略网络错误
   * @param error 错误对象
   * @param url 请求URL
   * @param context 上下文信息
   */
  private shouldIgnoreNetworkError(error: Error, url: string, context?: Record<string, unknown>): boolean {
    // 忽略取消的请求
    if (error.name === 'AbortError' || error.message.includes('aborted')) {
      return true;
    }

    // 忽略超时错误（可能是网络问题）
    if (error.message.includes('timeout')) {
      return true;
    }

    // 忽略某些状态码的错误（如401，这些由业务逻辑处理）
    const status = context?.status as number;
    if (status === 401 || status === 403) {
      return true;
    }

    return false;
  }

  /**
   * 上报自定义错误
   * @param message 错误消息
   * @param context 上下文信息
   */
  reportCustomError(message: string, context?: Record<string, unknown>): void {
    const errorDetails: ErrorDetails = {
      type: ErrorType.CUSTOM,
      message,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      context
    };

    this.reportError(errorDetails);
  }

  /**
   * 上报错误到服务器
   * @param errorDetails 错误详情
   */
  private reportError(errorDetails: ErrorDetails): void {
    // 添加用户和会话信息
    const enrichedError = {
      ...errorDetails,
      userId: this.userId,
      sessionId: this.sessionId,
      // 添加错误严重程度
      severity: this.getErrorSeverity(errorDetails),
      // 添加页面信息
      pageInfo: {
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
        referrer: document.referrer
      }
    };

    // 在控制台输出错误信息（开发环境）
    if (import.meta.env.DEV) {
      console.group(`🚨 ${errorDetails.type.toUpperCase()} Error`);
      console.error('Message:', errorDetails.message);
      console.error('Stack:', errorDetails.stack);
      console.error('Context:', errorDetails.context);
      console.error('Full Details:', enrichedError);
      console.groupEnd();
    }

    // 上报到后端日志服务
    if (import.meta.env.PROD) {
      try {
        fetch('/api/logs/client-error', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(enrichedError),
          // 使用keepalive确保请求在页面卸载时也能完成
          keepalive: true
        }).catch(e => {
          // 静默处理上报失败，避免产生新的错误
          console.warn('Error reporting failed:', e);
        });
      } catch (e) {
        console.warn('Error reporting failed:', e);
      }
    }
  }

  /**
   * 获取错误严重程度
   * @param errorDetails 错误详情
   */
  private getErrorSeverity(errorDetails: ErrorDetails): 'low' | 'medium' | 'high' | 'critical' {
    const { type, message, stack } = errorDetails;

    // 关键错误
    if (
      message.includes('ChunkLoadError') ||
      message.includes('Loading chunk') ||
      message.includes('Network Error') ||
      type === ErrorType.NETWORK
    ) {
      return 'critical';
    }

    // 高严重程度错误
    if (
      message.includes('TypeError') ||
      message.includes('ReferenceError') ||
      message.includes('Cannot read properties') ||
      stack?.includes('at Object.') ||
      type === ErrorType.REACT
    ) {
      return 'high';
    }

    // 中等严重程度错误
    if (
      type === ErrorType.PROMISE ||
      message.includes('Warning')
    ) {
      return 'medium';
    }

    // 低严重程度错误
    return 'low';
  }

  /**
   * 判断是否应该忽略某些错误
   * @param message 错误消息
   * @param source 错误来源文件
   */
  private shouldIgnoreError(message?: string, source?: string): boolean {
    if (!message) return false;

    // 忽略的错误模式列表
    const ignorePatterns = [
      // 浏览器扩展相关错误
      /extension\//i,
      /chrome-extension/i,
      /moz-extension/i,

      // 第三方脚本错误
      /Script error/i,

      // 网络相关的非关键错误
      /Loading chunk \d+ failed/i,
      /ChunkLoadError/i,

      // 开发工具相关错误
      /triangle/i,
      /devtools/i,

      // 广告拦截器相关错误
      /adblock/i,
      /ublock/i,

      // 其他常见的非关键错误
      /Non-Error promise rejection captured/i,
      /ResizeObserver loop limit exceeded/i,
    ];

    // 检查错误消息是否匹配忽略模式
    const shouldIgnoreMessage = ignorePatterns.some(pattern => pattern.test(message));

    // 检查错误来源是否来自第三方
    const shouldIgnoreSource = source && (
      source.includes('chrome-extension') ||
      source.includes('moz-extension') ||
      source.includes('extension') ||
      !source.includes(window.location.origin)
    );

    return shouldIgnoreMessage || Boolean(shouldIgnoreSource);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

// 导出单例
export const errorService = new ErrorService();
export default errorService; 
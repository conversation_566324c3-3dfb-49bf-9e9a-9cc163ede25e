# 虚拟角色平台 - 后台管理系统

这是虚拟角色平台的后台管理系统，用于管理角色和提示词模板。

## 功能特点

- **管理员认证** - 提供安全的管理员登录功能，确保只有授权用户可以访问
- **系统仪表盘** - 展示关键数据统计和操作日志
- **角色管理** - 查看、创建、编辑和删除角色
- **提示词管理** - 管理系统提示词模板，支持创建、编辑和测试功能
- **用户管理** - 查看和管理用户账户
- **系统设置** - 配置平台全局参数

## 技术栈

- **前端框架**: React
- **UI组件库**: Ant Design
- **状态管理**: Zustand
- **路由**: React Router
- **HTTP客户端**: Axios

## 目录结构

```
src/
  ├── components/
  │   ├── admin/ - 管理员界面组件
  │   │   ├── AdminLayout.tsx - 管理员布局组件
  │   │   └── AdminProtectedRoute.tsx - 管理员路由保护组件
  ├── pages/
  │   ├── admin/ - 管理员页面
  │   │   ├── AdminLoginPage.tsx - 管理员登录页面
  │   │   ├── DashboardPage.tsx - 仪表盘页面
  │   │   ├── CharacterListPage.tsx - 角色列表页面
  │   │   ├── PromptListPage.tsx - 提示词列表页面
  │   │   ├── PromptEditPage.tsx - 提示词编辑页面
  │   │   └── PromptTestPage.tsx - 提示词测试页面
  ├── services/
  │   ├── api.ts - API基础配置
  │   └── adminAPI.ts - 管理员API接口
  └── store/
      └── adminAuthStore.ts - 管理员认证状态管理
```

## 快速开始

1. 安装依赖:
```bash
npm install
```

2. 启动开发服务器:
```bash
npm run dev
```

3. 构建生产版本:
```bash
npm run build
```

## 管理员访问

管理员界面可通过以下URL访问:
```
http://localhost:5173/admin/login
```

## 开发计划

- [x] 后台管理界面基础框架搭建 (TASK202)
- [x] 角色列表与详情页面实现 (TASK203)
- [x] 系统提示词管理功能实现 (TASK205)
- [ ] 角色创建与编辑功能实现 (TASK204)

## 贡献

如需贡献代码，请遵循以下步骤:
1. Fork仓库
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

[MIT](LICENSE) 
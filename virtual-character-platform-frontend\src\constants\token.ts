// 默认坐标
import type { FormProps } from '@lobehub/ui';

export const INITIAL_COORDINATES = { x: 360, y: 360 };
export const DESKTOP_HEADER_ICON_SIZE = { fontSize: 24 };
export const DESKTOP_OPERATION_ICON_SIZE = { fontSize: 24, borderRadius: 24, blockSize: 48 };
export const DESKTOP_OPERATION_ICON_SIZE_LARGE = { fontSize: 24, borderRadius: 32, blockSize: 64 };
// export const MAX_WIDTH = 1024;

export const FORM_STYLE: FormProps = {
  itemMinWidth: 'max(30%,240px)',
  style: { width: '100%' },
};

export const MAX_WIDTH = 1440;

// 默认 zIndex
export const INITIAL_Z_INDEX = 10;
export const CHAT_TEXTAREA_MAX_HEIGHT = 570;
export const CHAT_TEXTAREA_HEIGHT = 200;
export const CHAT_INPUT_MIN_HEIGHT = 90;
export const HEADER_HEIGHT = 64;
export const SIDEBAR_WIDTH = 280;
export const SIDEBAR_MAX_WIDTH = 400;
export const CHAT_INFO_WIDTH = 360;
export const CHAT_INFO_MAX_WIDTH = 420;
export const CHAT_HEADER_HEIGHT = 64;
export const CHAT_INPUT_WIDTH = '48rem';
export const LIST_GRID_WIDTH = 108;
export const LIST_GRID_GAP = 4;
export const LIST_GRID_HEIGHT = 108;

export const INPUT_WIDTH_XS = 104;
export const INPUT_WIDTH_SM = 216;
export const INPUT_WIDTH_MD = 328;
export const INPUT_WIDTH_LG = 440;
export const INPUT_WIDTH_XL = 552;

import React from 'react';
import { useNavigate } from 'react-router-dom';

// 创建路由适配器，将Next.js的router转换为React Router
export const useRouterAdapter = () => {
  const navigate = useNavigate();
  
  return {
    push: (path: string) => {
      // 适配路径映射
      if (path === '/chat') {
        navigate('/chat');
      } else if (path === '/role') {
        navigate('/role');
      } else if (path.startsWith('/chat/')) {
        navigate(path);
      } else {
        navigate(path);
      }
    },
    replace: (path: string) => {
      navigate(path, { replace: true });
    },
    back: () => {
      navigate(-1);
    }
  };
};

// 创建常量适配器
export const SIDEBAR_WIDTH = 280;
export const SIDEBAR_MAX_WIDTH = 400;

// 创建全局Store适配器，映射到现有的store
import { useGlobalStore as useOriginalGlobalStore } from '../store/global';

export const useGlobalStoreAdapter = () => {
  const originalStore = useOriginalGlobalStore();
  
  return {
    ...originalStore,
    showRoleList: originalStore.showRoleList || true,
    setRoleList: originalStore.setRoleList || (() => {}),
  };
};

// 创建Agent Store适配器
import { useAgentStore as useOriginalAgentStore } from '../store/agent';

export const useAgentStoreAdapter = () => {
  const originalStore = useOriginalAgentStore();
  
  return {
    ...originalStore,
    // 确保所有必要的方法都存在
    activateAgent: originalStore.activateAgent || (() => {}),
    removeLocalAgent: originalStore.removeLocalAgent || (() => Promise.resolve()),
    createNewAgent: originalStore.createNewAgent || (() => ({})),
  };
};

// 创建Session Store适配器
import { useSessionStore as useOriginalSessionStore } from '../store/session';

export const useSessionStoreAdapter = () => {
  const originalStore = useOriginalSessionStore();
  
  return {
    ...originalStore,
    createSession: originalStore.createSession || (() => {}),
    removeSessionByAgentId: originalStore.removeSessionByAgentId || (() => {}),
  };
};

// 创建翻译适配器
export const useTranslationAdapter = (_namespace?: string) => {
  // 简单的翻译映射
  const translations = {
    'nav.info': '基本信息',
    'nav.role': '角色设定', 
    'nav.voice': '语音配置',
    'nav.shell': '外观配置',
    'nav.llm': '语言模型',
    'search': '搜索',
    'roleList': '角色列表',
    'startChat': '开始聊天',
    'delRole': '删除角色',
    'delAlert': '确定要删除这个角色吗？',
    'delete': '删除',
    'cancel': '取消',
    'noRole': '暂无角色',
    'common': '通用',
  };
  
  const t = (key: string, _options?: any) => {
    return translations[key as keyof typeof translations] || key;
  };
  
  return { t };
};

// 创建响应式适配器
export const useResponsiveAdapter = () => {
  const [windowWidth, setWindowWidth] = React.useState(window.innerWidth);
  
  React.useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return {
    md: windowWidth >= 768,
    lg: windowWidth >= 1024,
    xl: windowWidth >= 1280,
  };
};

// 创建样式适配器
export const createStylesAdapter = (_stylesFn: any) => {
  return () => {
    // 简单的样式对象
    const styles = {
      container: 'role-edit-container',
      preview: 'role-edit-preview',
      content: 'role-sidebar-content',
      sidebar: 'role-sidebar',
      role: 'role-list',
      list: 'role-list-content',
      icon: 'role-list-icon',
    };
    
    return { styles };
  };
};

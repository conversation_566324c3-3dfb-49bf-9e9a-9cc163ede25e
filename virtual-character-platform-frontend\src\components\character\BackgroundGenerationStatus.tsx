import React, { useState, useEffect } from 'react';
import { Card, Progress, Tag, Button, message, Spin } from 'antd';
import { ReloadOutlined, PictureOutlined } from '@ant-design/icons';
import { characterAPI } from '../../services/characterAPI';

interface BackgroundGenerationStatusProps {
  characterId: string;
  characterName: string;
  onBackgroundsReady?: (backgrounds: any[]) => void;
}

interface BackgroundItem {
  id: number;
  scene_type: string;
  scene_name: string;
  image_url: string | null;
  generation_status: 'pending' | 'generating' | 'completed' | 'failed';
  created_at: string;
  error_message?: string;
}

interface GenerationStatus {
  total: number;
  status_counts: {
    pending: number;
    generating: number;
    completed: number;
    failed: number;
  };
  is_complete: boolean;
  success_rate: number;
}

const BackgroundGenerationStatus: React.FC<BackgroundGenerationStatusProps> = ({
  characterId,
  characterName,
  onBackgroundsReady
}) => {
  const [backgrounds, setBackgrounds] = useState<BackgroundItem[]>([]);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [polling, setPolling] = useState(false);

  // 获取背景生成状态
  const fetchBackgroundStatus = async () => {
    try {
      setLoading(true);
      const response = await characterAPI.getCharacterBackgrounds(characterId);
      
      if (response.data.code === 0) {
        const data = response.data.data;
        setBackgrounds(data.backgrounds);
        setGenerationStatus(data.generation_status);
        
        // 如果有完成的背景，通知父组件
        const completedBackgrounds = data.backgrounds.filter(
          (bg: BackgroundItem) => bg.generation_status === 'completed' && bg.image_url
        );
        if (completedBackgrounds.length > 0 && onBackgroundsReady) {
          onBackgroundsReady(completedBackgrounds);
        }
        
        // 如果还有生成中的任务，继续轮询
        if (!data.generation_status.is_complete) {
          setPolling(true);
        } else {
          setPolling(false);
        }
      }
    } catch (error) {
      console.error('获取背景生成状态失败:', error);
      message.error('获取背景生成状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 重试失败的背景生成
  const retryFailedGeneration = async () => {
    try {
      setLoading(true);
      const response = await characterAPI.retryBackgroundGeneration(characterId);
      
      if (response.data.code === 0) {
        message.success('重试任务已启动');
        setPolling(true);
        // 延迟一下再获取状态
        setTimeout(fetchBackgroundStatus, 2000);
      }
    } catch (error) {
      console.error('重试背景生成失败:', error);
      message.error('重试背景生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 轮询效果
  useEffect(() => {
    if (polling) {
      const interval = setInterval(fetchBackgroundStatus, 5000); // 每5秒查询一次
      return () => clearInterval(interval);
    }
  }, [polling]);

  // 初始加载
  useEffect(() => {
    fetchBackgroundStatus();
  }, [characterId]);

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'generating': return 'processing';
      case 'pending': return 'default';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'generating': return '生成中';
      case 'pending': return '待生成';
      case 'failed': return '生成失败';
      default: return status;
    }
  };

  // 计算进度百分比
  const getProgressPercent = () => {
    if (!generationStatus || generationStatus.total === 0) return 0;
    return Math.round((generationStatus.status_counts.completed / generationStatus.total) * 100);
  };

  // 判断是否显示重试按钮
  const showRetryButton = generationStatus && generationStatus.status_counts.failed > 0;

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <PictureOutlined />
          <span>{characterName} - 背景图片生成</span>
          {polling && <Spin size="small" />}
        </div>
      }
      extra={
        <div style={{ display: 'flex', gap: 8 }}>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchBackgroundStatus}
            loading={loading}
            size="small"
          >
            刷新
          </Button>
          {showRetryButton && (
            <Button 
              type="primary" 
              onClick={retryFailedGeneration}
              loading={loading}
              size="small"
            >
              重试失败
            </Button>
          )}
        </div>
      }
      size="small"
    >
      {generationStatus && (
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
            <span>生成进度</span>
            <span>{generationStatus.status_counts.completed}/{generationStatus.total}</span>
          </div>
          <Progress 
            percent={getProgressPercent()} 
            status={generationStatus.is_complete ? 'success' : 'active'}
            strokeColor={generationStatus.status_counts.failed > 0 ? '#ff4d4f' : undefined}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
            成功率: {(generationStatus.success_rate * 100).toFixed(1)}%
          </div>
        </div>
      )}

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: 12 }}>
        {backgrounds.map((bg) => (
          <Card 
            key={bg.id} 
            size="small"
            cover={
              bg.image_url ? (
                <img 
                  src={bg.image_url} 
                  alt={bg.scene_name}
                  style={{ height: 120, objectFit: 'cover' }}
                />
              ) : (
                <div 
                  style={{ 
                    height: 120, 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    color: '#999'
                  }}
                >
                  {bg.generation_status === 'generating' ? (
                    <Spin size="small" />
                  ) : (
                    <PictureOutlined style={{ fontSize: 24 }} />
                  )}
                </div>
              )
            }
          >
            <Card.Meta 
              title={
                <div style={{ fontSize: '12px' }}>
                  {bg.scene_name}
                </div>
              }
              description={
                <div>
                  <Tag 
                    color={getStatusColor(bg.generation_status)}
                    style={{ fontSize: '10px' }}
                  >
                    {getStatusText(bg.generation_status)}
                  </Tag>
                  {bg.error_message && (
                    <div style={{ fontSize: '10px', color: '#ff4d4f', marginTop: 4 }}>
                      {bg.error_message.length > 30 
                        ? `${bg.error_message.substring(0, 30)}...` 
                        : bg.error_message
                      }
                    </div>
                  )}
                </div>
              }
            />
          </Card>
        ))}
      </div>

      {backgrounds.length === 0 && !loading && (
        <div style={{ textAlign: 'center', color: '#999', padding: 20 }}>
          暂无背景图片生成记录
        </div>
      )}
    </Card>
  );
};

export default BackgroundGenerationStatus;

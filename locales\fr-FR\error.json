{"apiKeyMiss": "La clé API OpenAI est vide, veuillez ajouter une clé API OpenAI personnalisée.", "dancePlayError": "Échec de la lecture du fichier de danse, veuillez réessayer plus tard.", "error": "<PERSON><PERSON><PERSON>", "errorTip": {"clearSession": "Effacer les messages de session", "description": "Le projet est actuellement en cours de construction, la stabilité des données n'est pas garantie. Si vous rencontrez un problème, vous pouvez essayer", "forgive": "Nous vous prions de bien vouloir nous excuser pour le désagrément causé.", "or": "ou", "problem": "La page a rencontré un petit problème...", "resetSystem": "Réinitialiser les paramètres du système"}, "fileUploadError": "Échec du téléchargement du fichier, ve<PERSON><PERSON>z réessayer plus tard", "formValidationFailed": "Échec de la validation du formulaire :", "goBack": "Retour à la page d'accueil", "openaiError": "Erreur de l'API OpenAI, veuillez vérifier si la clé API OpenAI et l'endpoint sont corrects", "reload": "Recharger", "response": {"400": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne comprend pas votre demande, veuillez vérifier si les paramètres de votre demande sont corrects.", "401": "<PERSON><PERSON><PERSON><PERSON>, le serveur a refusé votre demande, cela peut être dû à des autorisations insuffisantes ou à l'absence d'une authentification valide.", "403": "<PERSON><PERSON><PERSON><PERSON>, le serveur a refusé votre demande, vous n'avez pas l'autorisation d'accéder à ce contenu.", "404": "<PERSON><PERSON><PERSON><PERSON>, le serveur n'a pas pu trouver la page ou la ressource que vous avez demandée, veuillez vérifier si votre URL est correcte.", "405": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne prend pas en charge la méthode de demande que vous utilisez, veuillez vérifier si votre méthode de demande est correcte.", "406": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas satisfaire votre demande en fonction des caractéristiques du contenu que vous avez demandé.", "407": "<PERSON><PERSON><PERSON><PERSON>, vous devez vous authentifier via un proxy avant de pouvoir continuer cette demande.", "408": "<PERSON><PERSON><PERSON><PERSON>, le serveur a expiré en attendant la demande, veuillez vérifier votre connexion réseau et réessayer.", "409": "<PERSON><PERSON><PERSON><PERSON>, il y a un conflit dans la demande qui ne peut pas être traité, cela peut être dû à un état de ressource incompatible avec la demande.", "410": "<PERSON><PERSON><PERSON><PERSON>, la ressource que vous avez demandée a été définitivement supprimée et ne peut pas être trouvée.", "411": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas traiter une demande sans longueur de contenu valide.", "412": "<PERSON><PERSON><PERSON><PERSON>, votre demande ne satisfait pas les conditions du serveur et ne peut pas être complétée.", "413": "<PERSON><PERSON><PERSON><PERSON>, la taille de vos données de demande est trop grande, le serveur ne peut pas traiter.", "414": "<PERSON><PERSON><PERSON><PERSON>, l'URI de votre demande est trop long, le serveur ne peut pas traiter.", "415": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas traiter le format média joint à la demande.", "416": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas satisfaire la plage que vous avez demandée.", "417": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas satisfaire vos attentes.", "422": "<PERSON><PERSON><PERSON><PERSON>, le format de votre demande est correct, mais en raison d'erreurs sémantiques, elle ne peut pas être traitée.", "423": "<PERSON><PERSON><PERSON><PERSON>, la ressource que vous avez demandée est verrouillée.", "424": "<PERSON><PERSON><PERSON><PERSON>, la demande actuelle ne peut pas être complétée en raison de l'échec d'une demande précédente.", "426": "<PERSON><PERSON><PERSON><PERSON>, le serveur exige que votre client soit mis à niveau vers une version de protocole plus élevée.", "428": "<PERSON><PERSON><PERSON><PERSON>, le serveur exige des conditions préalables, votre demande doit inclure les en-têtes de conditions appropriés.", "429": "<PERSON><PERSON><PERSON><PERSON>, vous avez fait trop de demandes, le serveur est un peu fatigué, veuillez réessayer plus tard.", "431": "<PERSON><PERSON><PERSON><PERSON>, les champs d'en-tête de votre demande sont trop grands, le serveur ne peut pas traiter.", "451": "<PERSON><PERSON><PERSON><PERSON>, pour des raisons légales, le serveur refuse de fournir cette ressource.", "500": "<PERSON><PERSON><PERSON><PERSON>, le serveur semble rencontrer des difficultés et ne peut pas traiter votre demande pour le moment, veuillez réessayer plus tard.", "501": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne sait pas encore comment traiter cette demande, veuillez vérifier si votre opération est correcte.", "502": "<PERSON><PERSON><PERSON><PERSON>, le serveur semble être perdu, il ne peut pas fournir de service pour le moment, veuillez réessayer plus tard.", "503": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne peut pas traiter votre demande actuellement, cela peut être dû à une surcharge ou à une maintenance en cours, veuil<PERSON><PERSON> réessayer plus tard.", "504": "<PERSON><PERSON><PERSON><PERSON>, le serveur n'a pas reçu de réponse du serveur en amont, veuil<PERSON>z réessayer plus tard.", "505": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne prend pas en charge la version HTTP que vous utilisez, veuillez mettre à jour et réessayer.", "506": "<PERSON><PERSON><PERSON><PERSON>, il y a un problème de configuration du serveur, veuillez contacter l'administrateur pour résoudre.", "507": "<PERSON><PERSON><PERSON><PERSON>, le serveur n'a pas suffisamment d'espace de stockage pour traiter votre demande, veuillez réessayer plus tard.", "509": "<PERSON><PERSON><PERSON><PERSON>, la bande passante du serveur est épuisée, veuil<PERSON>z réessayer plus tard.", "510": "<PERSON><PERSON><PERSON><PERSON>, le serveur ne prend pas en charge la fonctionnalité d'extension demandée, veuillez contacter l'administrateur.", "524": "<PERSON><PERSON><PERSON><PERSON>, le serveur a expiré en attendant une réponse, cela peut être dû à une réponse trop lente, veuil<PERSON><PERSON> réessayer plus tard.", "AgentRuntimeError": "Une erreur d'exécution Lobe AI s'est produite, veuillez vérifier ou réessayer en fonction des informations ci-dessous.", "FreePlanLimit": "Vous êtes actuellement un utilisateur gratuit, vous ne pouvez pas utiliser cette fonctionnalité, veuillez passer à un plan payant pour continuer.", "InvalidAccessCode": "Le mot de passe est incorrect ou vide, veuil<PERSON><PERSON> entrer le bon mot de passe d'accès ou ajouter une clé API personnalisée.", "InvalidBedrockCredentials": "L'authentification Bedrock a échoué, veuillez vérifier l'AccessKeyId/SecretAccessKey et réessayer.", "InvalidClerkUser": "<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas encore connecté, veuil<PERSON><PERSON> vous connecter ou vous inscrire avant de continuer.", "InvalidGithubToken": "Le PAT Github est incorrect ou vide, veuillez vérifier le PAT Github et réessayer.", "InvalidOllamaArgs": "La configuration d'Ollama est incorrecte, veuillez vérifier la configuration d'Ollama et réessayer.", "InvalidProviderAPIKey": "{{provider}} La clé API est incorrecte ou vide, veuillez vérifier la clé API {{provider}} et réessayer.", "LocationNotSupportError": "Désolé, votre région actuelle ne prend pas en charge ce service de modèle, cela peut être dû à des restrictions régionales ou à un service non activé. Veuillez vérifier si votre région actuelle prend en charge ce service, ou essayez de changer de région et réessayez.", "OllamaBizError": "Une erreur s'est produite lors de la demande de service Ollama, veuille<PERSON> vérifier ou réessayer en fonction des informations ci-dessous.", "OllamaServiceUnavailable": "La connexion au service Ollama a échoué, veuillez vérifier si Ollama fonctionne correctement ou si la configuration CORS d'Ollama est correcte.", "PermissionDenied": "Dés<PERSON><PERSON>, vous n'avez pas la permission d'accéder à ce service, veuillez vérifier si votre clé a les droits d'accès.", "PluginApiNotFound": "Désolé, cette API n'existe pas dans le manifeste du plugin, veuillez vérifier si votre méthode de demande correspond à l'API du manifeste du plugin.", "PluginApiParamsError": "Désolé, la vérification des paramètres d'entrée de la demande du plugin a échoué, veuillez vérifier si les paramètres correspondent aux informations de description de l'API.", "PluginFailToTransformArguments": "<PERSON><PERSON><PERSON><PERSON>, l'analyse des paramètres d'appel du plugin a échoué, veuil<PERSON>z essayer de régén<PERSON>rer le message d'assistant ou de changer de modèle AI avec des capacités d'appel d'outils plus puissantes et réessayer.", "PluginGatewayError": "<PERSON><PERSON><PERSON><PERSON>, une erreur s'est produite au niveau de la passerelle du plugin, veuillez vérifier si la configuration de la passerelle du plugin est correcte.", "PluginManifestInvalid": "Désolé, la vérification du manifeste de ce plugin a échoué, veuillez vérifier si le format du manifeste est conforme.", "PluginManifestNotFound": "<PERSON><PERSON><PERSON><PERSON>, le serveur n'a pas trouvé le manifeste de ce plugin (manifest.json), veuillez vérifier si l'adresse du fichier de description du plugin est correcte.", "PluginMarketIndexInvalid": "Désolé, la vérification de l'index du plugin a échoué, veuillez vérifier si le format du fichier d'index est conforme.", "PluginMarketIndexNotFound": "<PERSON><PERSON><PERSON><PERSON>, le serveur n'a pas trouvé l'index du plugin, veuillez vérifier si l'adresse de l'index est correcte.", "PluginMetaInvalid": "Désolé, la vérification des métadonnées de ce plugin a échoué, veuillez vérifier si le format des métadonnées du plugin est conforme.", "PluginMetaNotFound": "Désolé, aucun plugin n'a été trouvé dans l'index, veuillez vérifier les informations de configuration du plugin dans l'index.", "PluginOpenApiInitError": "Désolé, l'initialisation du client OpenAPI a échoué, veuillez vérifier si les informations de configuration d'OpenAPI sont correctes.", "PluginServerError": "Une erreur est survenue lors de la demande du serveur du plugin, veuillez vérifier votre fichier de description du plugin, la configuration du plugin ou l'implémentation du serveur en fonction des informations d'erreur ci-dessous.", "PluginSettingsInvalid": "Ce plugin doit être correctement configuré avant de pouvoir être utilisé, veuillez vérifier si votre configuration est correcte.", "ProviderBizError": "Une erreur s'est produite lors de la demande de service {{provider}}, veuil<PERSON>z vérifier ou réessayer en fonction des informations ci-dessous.", "QuotaLimitReached": "Désolé, l'utilisation actuelle des tokens ou le nombre de demandes a atteint la limite de quota de cette clé, veuillez augmenter le quota de cette clé ou réessayer plus tard.", "StreamChunkError": "Erreur d'analyse des blocs de message de la demande en streaming, veuillez vérifier si l'interface API actuelle est conforme aux normes, ou contacter votre fournisseur d'API pour des conseils.", "SubscriptionPlanLimit": "Votre quota d'abonnement est épuisé, vous ne pouvez pas utiliser cette fonctionnalité, veuillez passer à un plan supérieur ou acheter un pack de ressources pour continuer.", "UnknownChatFetchError": "<PERSON><PERSON><PERSON><PERSON>, une erreur de demande inconnue s'est produite, veuillez vérifier ou réessayer en fonction des informations ci-dessous."}, "s3envError": "Les variables d'environnement S3 ne sont pas complètement configurées, veuillez vérifier vos variables d'environnement.", "serverError": "<PERSON><PERSON><PERSON> du serveur, ve<PERSON><PERSON>z contacter l'administrateur", "triggerError": "<PERSON><PERSON><PERSON>", "ttsTransformFailed": "Échec de la conversion vocale, veuillez vérifier votre connexion réseau ou essayer d'activer l'appel client dans les paramètres.", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "unlock": {"addProxyUrl": "Ajouter l'adresse du proxy OpenAI (facultatif)", "apiKey": {"description": "Entrez votre clé API {{name}} pour commencer la session", "title": "Utiliser la clé API {{name}} personnalisée"}, "closeMessage": "<PERSON><PERSON><PERSON> l'alerte", "confirm": "Confirmer et réessayer", "oauth": {"description": "L'administrateur a activé l'authentification unique, cliquez sur le bouton ci-dessous pour vous connecter et déverrouiller l'application", "success": "Connexion réussie", "title": "Se connecter", "welcome": "Bienvenue !"}, "password": {"description": "L'administrateur a activé le cryptage de l'application, entrez le mot de passe de l'application pour déverrouiller. Le mot de passe n'a besoin d'être saisi qu'une seule fois", "placeholder": "Veuillez entrer le mot de passe", "title": "Entrer le mot de passe pour déverrouiller l'application"}, "tabs": {"apiKey": "Clé API personnalisée", "password": "Mot de passe"}}}
{"apiKeyMiss": "Klucz API OpenAI jest pusty, pro<PERSON><PERSON> dodać własny klucz API OpenAI", "dancePlayError": "Błąd odtwarzania pliku tańca, spróbuj ponownie później", "error": "Błąd", "errorTip": {"clearSession": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wiadomości sesji", "description": "Projekt jest obecnie w trakcie budowy, nie gwarantujemy stabilności danych. <PERSON><PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON>", "forgive": "przepraszamy za wszelkie niedogodności", "or": "lub", "problem": "Strona napotkała pewien problem...", "resetSystem": "Zresetuj ustawienia systemowe"}, "fileUploadError": "Wystą<PERSON>ł błąd podczas przesyłania pliku, spróbuj ponownie później", "formValidationFailed": "Walidacja formularza nie powiodła się:", "goBack": "Wróć do strony głównej", "openaiError": "Błąd API OpenAI, <PERSON><PERSON><PERSON>, czy klucz API OpenAI i punkt końcowy są poprawne", "reload": "Przeładuj", "response": {"400": "<PERSON><PERSON><PERSON><PERSON>, serwer nie rozumie Twojego żądania, <PERSON><PERSON><PERSON>, że parametry żądania są poprawne", "401": "Prz<PERSON>ro nam, serwer odmówił Twojego żądania, być może z powodu niewystarczających uprawnień lub braku ważnej autoryzacji", "403": "<PERSON><PERSON><PERSON><PERSON> nam, ser<PERSON> odmówił Twojego żądania, nie masz uprawnień do dostępu do tej treści", "404": "<PERSON><PERSON><PERSON><PERSON>, serwer nie może znaleźć strony lub zas<PERSON>, o kt<PERSON><PERSON> prosisz, proszę upewnij się, że Twój URL jest poprawny", "405": "<PERSON><PERSON><PERSON><PERSON>, serwer nie obsługuje metody <PERSON>ia, kt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j si<PERSON>, że metoda żądania jest poprawna", "406": "<PERSON><PERSON><PERSON><PERSON>m, serwer nie może zrealizować żądania na podstawie cech treści, o które prosisz", "407": "<PERSON><PERSON><PERSON><PERSON> nam, mus<PERSON><PERSON> prz<PERSON>row<PERSON><PERSON>ć autoryzację proxy, a<PERSON><PERSON> to żądanie", "408": "<PERSON><PERSON><PERSON><PERSON> nam, serwer przekroczył czas oczekiwania na żądanie, proszę sprawdzić połączenie sieciowe i spróbować ponownie", "409": "Prz<PERSON>ro nam, wystąpił konflikt w żądaniu, kt<PERSON>re nie może być przetworzone, być może z powodu niezgodności stanu zasobów z żądaniem", "410": "Przykro nam, żądany zasób został trwale usunięty i nie może być znaleziony", "411": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie może przetworzyć żądania bez ważnej długości treści", "412": "<PERSON><PERSON><PERSON>ro nam, Twoje żądanie nie spełnia warunków serwera i nie może być zrealizowane", "413": "<PERSON><PERSON><PERSON><PERSON> nam, il<PERSON><PERSON><PERSON> danych w <PERSON>im <PERSON>ądaniu jest zbyt duża, serwer nie może tego przetworzyć", "414": "<PERSON><PERSON><PERSON><PERSON>, URI Twojego żądania jest zbyt długi, serwer nie może tego przetworzyć", "415": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie może przetworzyć formatu mediów dołączonego do żądania", "416": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie może zaspokoić zakresu Twojego żądania", "417": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie może zas<PERSON><PERSON>ić <PERSON>ich oczekiwań", "422": "Przykro nam, format Twojego żądania jest poprawny, ale z powodu błędów semantycznych nie może być zrealizowane", "423": "P<PERSON><PERSON>ro nam, żądany zasób jest zablokowany", "424": "Przykro nam, z powodu niepowodzenia wcześniejszego żądania, bieżące żądanie nie może być zrealizowane", "426": "<PERSON><PERSON><PERSON><PERSON> nam, <PERSON>r<PERSON> w<PERSON>, aby Twój klient zaktualizował się do wyższej wersji protokołu", "428": "<PERSON><PERSON><PERSON><PERSON> nam, serwer wymaga warunków wstępnych, które muszą być zawarte w <PERSON>im <PERSON>daniu", "429": "<PERSON><PERSON><PERSON><PERSON> nam, Twoje żądania są zbyt liczne, serwer jest trochę zmę<PERSON>ony, <PERSON><PERSON><PERSON> spr<PERSON><PERSON> później", "431": "<PERSON><PERSON><PERSON><PERSON>m, pole nagłówka Twojego żądania jest zbyt duże, serwer nie może tego przetworzyć", "451": "Przykro nam, z powodów prawnych serwer odmówił dostępu do tego zasobu", "500": "<PERSON><PERSON><PERSON><PERSON>m, serwer wydaje się mieć pewne trudności, tymczasowo nie może zrealizować Twojego żądania, <PERSON><PERSON><PERSON> s<PERSON>r<PERSON><PERSON> później", "501": "<PERSON><PERSON><PERSON><PERSON>m, serwer jes<PERSON>e nie wie, jak prz<PERSON><PERSON><PERSON> to żądanie, pro<PERSON><PERSON>j si<PERSON>, że Twoje działanie jest poprawne", "502": "<PERSON><PERSON><PERSON><PERSON>m, serwer wydaje się by<PERSON>, tymczasowo nie może świadcz<PERSON>ć usług, pro<PERSON><PERSON> spr<PERSON>bow<PERSON> później", "503": "<PERSON><PERSON><PERSON><PERSON> nam, serwer obecnie nie może przetworzyć Twojego żądania, być może z powodu przeciążenia lub trwają<PERSON>j konserwacji, pro<PERSON><PERSON> spró<PERSON> później", "504": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie o<PERSON><PERSON><PERSON>ł odpowiedzi od serwera nadrzędnego, pro<PERSON><PERSON> spr<PERSON>bow<PERSON> później", "505": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie obsługuje używanej wersji HTTP, pro<PERSON><PERSON> zaktualizowa<PERSON> i spróbować ponownie", "506": "<PERSON><PERSON><PERSON>ro nam, wystąpił problem z konfiguracją serwera, pro<PERSON>ę skontaktować się z <PERSON>em", "507": "<PERSON><PERSON><PERSON><PERSON> nam, serwer nie ma wystarczającej przestrzeni do przechowywania, aby prz<PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> spró<PERSON> później", "509": "<PERSON><PERSON><PERSON><PERSON> nam, pasmo serwera zostało wyczerpane, pro<PERSON><PERSON> spróbowa<PERSON> później", "510": "<PERSON><PERSON><PERSON><PERSON>m, serwer nie obsługuje żą<PERSON>ej <PERSON> rozszerzenia, proszę skontaktować się z <PERSON>em", "524": "Prz<PERSON>ro nam, serwer przekroczył czas oczekiwania na odpowiedź, być może z powodu zbyt wolnej odpowiedzi, pro<PERSON><PERSON> spróbować później", "AgentRuntimeError": "Wystąpił błąd wykonania Lobe AI Runtime, proszę sprawdzić poniższe informacje i spróbować ponownie", "FreePlanLimit": "Obecnie jesteś użytkownikiem darmowym, nie moż<PERSON>z korzystać z tej funkcji, pro<PERSON><PERSON> zak<PERSON>alizowa<PERSON> do płatnego planu, aby kontynuować korzystanie", "InvalidAccessCode": "<PERSON><PERSON><PERSON> jest niepoprawne lub puste, pro<PERSON><PERSON> wprow<PERSON><PERSON>ć poprawne hasło dostępu lub dodać niestandardowy klucz API", "InvalidBedrockCredentials": "Autoryzacja Bedrock nie powiodła się, <PERSON><PERSON><PERSON> sprawd<PERSON> AccessKeyId/SecretAccessKey i spróbować ponownie", "InvalidClerkUser": "<PERSON><PERSON><PERSON><PERSON>m, obe<PERSON><PERSON> nie j<PERSON>alog<PERSON>, <PERSON><PERSON><PERSON> zalogować się lub zarejestro<PERSON> konto, a<PERSON> k<PERSON><PERSON>", "InvalidGithubToken": "Github PAT jest niepoprawny lub pusty, pro<PERSON><PERSON> sprawdzić Github PAT i spróbować ponownie", "InvalidOllamaArgs": "Konfiguracja Ollama jest ni<PERSON>na, <PERSON><PERSON><PERSON> sprawdzić konfigurację Ollama i spróbować ponownie", "InvalidProviderAPIKey": "{{provider}} Klucz API jest niepoprawny lub pusty, pro<PERSON><PERSON> sprawdzić {{provider}} Klucz API i spróbować ponownie", "LocationNotSupportError": "<PERSON><PERSON><PERSON><PERSON> nam, Twoja lokalizacja nie obsługuje tej usługi modelu, być może z powodu ograniczeń regionalnych lub braku dostępności usługi. Proszę upewnić się, że obecny region obsługuje tę usługę lub spróbować przełączyć się na inny region i spróbować ponownie.", "OllamaBizError": "Wystą<PERSON>ł błąd w żądaniu usługi Ollama, pro<PERSON><PERSON> sprawdzić poniższe informacje i spróbować ponownie", "OllamaServiceUnavailable": "Połączenie z usługą Ollama nie powiodło się, <PERSON><PERSON><PERSON> s<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> działa poprawnie lub czy poprawnie skonfigurowano ustawienia CORS Ollama", "PermissionDenied": "<PERSON><PERSON><PERSON><PERSON> nam, nie masz uprawnień do dostępu do tej usługi, <PERSON><PERSON><PERSON>, czy Twój klucz ma odpowiednie uprawnienia", "PluginApiNotFound": "<PERSON><PERSON><PERSON><PERSON>m, w manifestie wtyczki nie ma tego API, <PERSON><PERSON><PERSON>, <PERSON>zy <PERSON>ja metoda żądania jest zgodna z API manifestu wtyczki", "PluginApiParamsError": "<PERSON><PERSON><PERSON><PERSON> nam, weryfikacja parametrów wejściowych żądania wtyczki nie powiodła się, <PERSON><PERSON><PERSON> s<PERSON>, czy parametry są zgodne z opisem API", "PluginFailToTransformArguments": "<PERSON><PERSON><PERSON><PERSON> nam, analiza argumentów wywołania wtyczki nie powiodła się, pro<PERSON><PERSON> spróbować ponownie wygenerować wiadomość asystenta lub spróbować z innym modelem AI o większych możliwościach wywołania narzędzi", "PluginGatewayError": "<PERSON><PERSON><PERSON><PERSON>m, w<PERSON><PERSON><PERSON><PERSON><PERSON> bł<PERSON>d bramy wtyczki, <PERSON><PERSON><PERSON> s<PERSON>, czy konfiguracja bramy wtyczki jest poprawna", "PluginManifestInvalid": "<PERSON><PERSON><PERSON><PERSON> nam, weryfikacja manifestu tej wtyczki nie powiodła się, <PERSON><PERSON><PERSON> spraw<PERSON>, czy format manifestu jest poprawny", "PluginManifestNotFound": "<PERSON><PERSON><PERSON><PERSON>m, serwer nie znalazł manifestu tej wtyczki (manifest.json), <PERSON><PERSON><PERSON> s<PERSON>, czy adres pliku opisu wtyczki jest poprawny", "PluginMarketIndexInvalid": "<PERSON><PERSON><PERSON><PERSON> nam, weryfika<PERSON>ja indeksu wtyczek nie powiodła się, <PERSON><PERSON><PERSON> spraw<PERSON>, czy format pliku indeksu jest poprawny", "PluginMarketIndexNotFound": "<PERSON><PERSON><PERSON><PERSON>m, serwer nie znalazł indeksu wtyczek, <PERSON><PERSON><PERSON> spra<PERSON>, czy ad<PERSON> indeksu jest poprawny", "PluginMetaInvalid": "<PERSON><PERSON><PERSON><PERSON> nam, weryfikacja metadanych tej wtyczki nie powiodła się, <PERSON><PERSON><PERSON> spraw<PERSON>, czy format metadanych wtyczki jest poprawny", "PluginMetaNotFound": "P<PERSON><PERSON>ro nam, nie znaleziono tej wtyczki w indeksie, proszę sprawdzić informacje konfiguracyjne wtyczki w indeksie", "PluginOpenApiInitError": "<PERSON><PERSON><PERSON><PERSON>m, inicjalizacja klienta OpenAPI nie powiodła się, <PERSON><PERSON><PERSON> s<PERSON>, czy informacje konfiguracyjne OpenAPI są poprawne", "PluginServerError": "Wystą<PERSON>ł błąd w odpowiedzi serwera wtyczki, pro<PERSON>ę sprawdzić poniższe informacje o błędzie, aby sprawdzić plik opisu wtyczki, konfigurację wtyczki lub implementację serwera", "PluginSettingsInvalid": "Ta wtyczka wymaga poprawnej konfiguracji, a<PERSON> m<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON> s<PERSON>, <PERSON>zy <PERSON>ja konfiguracja jest poprawna", "ProviderBizError": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd w żądaniu usługi {{provider}}, pro<PERSON><PERSON> sprawdzić poniższe informacje lub spróbować ponownie", "QuotaLimitReached": "<PERSON><PERSON><PERSON><PERSON> nam, obecne zużycie tokenów lub liczba żądań osiągnęła limit kwoty (quota) dla tego klucza, pro<PERSON><PERSON> zwi<PERSON><PERSON><PERSON> limit kwoty dla tego klucza lub spr<PERSON>bować później", "StreamChunkError": "Wystąpił błąd analizy bloku wiadomości w ż<PERSON><PERSON><PERSON> strumi<PERSON>owym, <PERSON><PERSON><PERSON> s<PERSON>, czy bieżący interfejs API jest zgodny z normami, lub skontaktowa<PERSON> się z dostawcą API w celu uzyskania informacji", "SubscriptionPlanLimit": "Twój limit subskrypcyjny został wyczerpany, nie moż<PERSON>z korzystać z tej funkcji, proszę zaktualizować do wyższego planu lub zak<PERSON><PERSON> pakiet zasobów, aby kontynuować korzystanie", "UnknownChatFetchError": "Prz<PERSON>ro nam, w<PERSON><PERSON><PERSON><PERSON>ł nieznany błąd żądania, pro<PERSON><PERSON> sprawdzić poniższe informacje i spróbować ponownie"}, "s3envError": "Zmienne środowiskowe S3 nie są w pełni skonfigurowane, pro<PERSON><PERSON> sprawdzić swoje zmienne środowiskowe", "serverError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON>ę skontaktować się z <PERSON>em", "triggerError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd", "ttsTransformFailed": "Nie udało się przetworzyć mowy, sprawdź połączenie sieciowe lub spróbuj ponownie po włączeniu wywołania klienta w ustawieniach.", "unknownError": "Nieznany błąd", "unlock": {"addProxyUrl": "Dodaj adres proxy OpenAI (opcjonalnie)", "apiKey": {"description": "Wprowadź swój klucz API {{name}}, aby r<PERSON> sesję", "title": "Użyj niestandardowego klucza API {{name}}"}, "closeMessage": "Zamknij powiadomienie", "confirm": "Potwierdź i spróbuj ponownie", "oauth": {"description": "Administrator w<PERSON><PERSON><PERSON><PERSON><PERSON> jednolitą autoryzację logo<PERSON>, kliknij przycisk poniżej, aby się zalogować i odblokować aplikację", "success": "Zalogowano pomyślnie", "title": "<PERSON><PERSON><PERSON><PERSON>", "welcome": "Witaj!"}, "password": {"description": "Administrator w<PERSON><PERSON><PERSON><PERSON><PERSON> szyfrowanie aplikacji, wp<PERSON><PERSON><PERSON> hasło aplikacji, aby odb<PERSON>kować aplikację. <PERSON><PERSON><PERSON> należy wpisać tylko raz", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby odblokować aplikację"}, "tabs": {"apiKey": "Niestandardowy klucz API", "password": "<PERSON><PERSON><PERSON>"}}}
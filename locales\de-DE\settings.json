{"common": {"chat": {"avatar": {"desc": "Benutzerdefinierter Avatar", "title": "Avatar"}, "nickName": {"desc": "Benutzerdefinierter Spitzname", "placeholder": "Bitte Spitznamen eingeben", "title": "Spitzname"}, "title": "Chat-Einstellungen"}, "system": {"clear": {"action": "Jetzt löschen", "alert": "Bestätigen Sie das Löschen aller Sitzungsnachrichten?", "desc": "Es werden alle Sitzungen und Rollendaten gelöscht, einsch<PERSON>ßlich Sitzungslisten, Rollendaten, Sitzungsnachrichten usw.", "success": "Löschung erfolgreich", "tip": "Die Aktion kann nicht rückgängig gemacht werden. Nach dem Löschen sind die Daten nicht wiederherstellbar. Bitte vorsichtig handeln.", "title": "Alle Sitzungsnachrichten löschen"}, "clearCache": {"action": "Jetzt löschen", "alert": "Sind <PERSON> sicher, dass Sie alle Caches löschen möchten?", "calculating": "Berechne Cache-Größe...", "desc": "Dies löscht den heruntergeladenen Daten-C<PERSON> der Anwendung, e<PERSON><PERSON><PERSON><PERSON>lich der Modellaudiodaten, Sprachdaten, Tanzmodellaudiodaten und anderer Mediendaten.", "success": "Löschung erfolgreich", "tip": "Diese Aktion kann nicht rückgängig gemacht werden. Nach dem Löschen müssen die Daten erneut heruntergeladen werden. Bitte vorsichtig handeln.", "title": "<PERSON>n-<PERSON><PERSON>"}, "reset": {"action": "Jetzt zurücksetzen", "alert": "Bestätigen Sie das Zurücksetzen aller Systemeinstellungen?", "desc": "Es werden alle Systemeinstellungen zurückgesetzt, einschließlich Themen-, Chat- und Sprachmodell-Einstellungen.", "success": "Zurücksetzung erfolgreich", "tip": "Die Aktion kann nicht rückgängig gemacht werden. Nach dem Zurücksetzen sind die Daten nicht wiederherstellbar. Bitte vorsichtig handeln.", "title": "Systemeinstellungen zurücksetzen"}, "title": "Systemeinstellungen"}, "theme": {"backgroundEffect": {"desc": "Benutzerdefinierter Hintergrundeffekt", "glow": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON>", "title": "Hintergrundeffekt"}, "locale": {"auto": "System folgen", "desc": "Benutzerdefinierte Systemsprache", "title": "<PERSON><PERSON><PERSON>"}, "neutralColor": {"desc": "Benutzerdefinierte Graustufen mit unterschiedlichen Farbneigungen", "title": "Neutrale Farbe"}, "primaryColor": {"desc": "Benutzerdefinierte Themenfarbe", "title": "Themenfarbe"}, "title": "Themen-Einstellungen"}, "title": "Allgemeine Einstellungen"}, "header": {"desc": "Präferenzen und Modelleinstellungen", "global": "Globale Einstellungen", "session": "Sitzungseinstellungen", "sessionDesc": "Rolleneinstellungen und Sitzungseinstellungen", "sessionWithName": "Sitzungseinstellungen · {{name}}", "title": "Einstellungen"}, "llm": {"aesGcm": "<PERSON><PERSON> Schlüssel und die Proxy-Adresse werden mit dem <1>AES-GCM</1> Verschlüsselungsalgorithmus verschlüsselt.", "apiKey": {"desc": "<PERSON>te geben Si<PERSON> {{name}} API-Schlüssel ein.", "placeholder": "{{name}} API-Schlüssel", "title": "API-Schlüssel"}, "checker": {"button": "Überprüfen", "desc": "<PERSON><PERSON> Si<PERSON>, ob der API-Schlüssel und die Proxy-Adresse korrekt eingegeben wurden.", "error": "Überprüfung fehlgeschlagen", "pass": "Überprüfung bestanden", "title": "Verbindungsprüfung"}, "customModelCards": {"addNew": "Erstellen und Hinzufügen des {{id}} Modells", "config": "<PERSON><PERSON>", "confirmDelete": "Das benutzerdefinierte Modell wird gelöscht. Nach dem Löschen kann es nicht wiederhergestellt werden. Bitte vorsichtig vorgehen.", "modelConfig": {"azureDeployName": {"extra": "<PERSON>, das in Azure OpenAI tatsächlich angefordert wird.", "placeholder": "Bitte geben Sie den Modellbereitstellungsnamen in Azure ein.", "title": "Modellbereitstellungsname"}, "displayName": {"placeholder": "<PERSON>te geben Sie den Anzeigenamen des Modells ein, z. B. ChatGPT, GPT-4 usw.", "title": "Anzeigename des Modells"}, "files": {"extra": "Der aktuelle Datei-Upload ist nur eine Hack-Lösung und nur für eigene Versuche gedacht. Warten Sie auf die vollständige Implementierung der Datei-Upload-Funktion.", "title": "Unterstützung für Datei-Uploads"}, "functionCall": {"extra": "Diese Konfiguration aktiviert nur die Funktionalität für Funktionsaufrufe in der Anwendung. Ob Funktionsaufrufe unterstützt werden, hängt vollständig vom Modell selbst ab. Bitte testen Sie die Verwendbarkeit der Funktionsaufrufe dieses Modells selbst.", "title": "Unterstützung für Funktionsaufrufe"}, "id": {"extra": "Wird als Modell-Tag angezeigt.", "placeholder": "<PERSON>te geben Sie die Modell-ID ein, z. B. gpt-4-turbo-preview oder claude-2.1.", "title": "Modell-ID"}, "modalTitle": "Konfiguration des benutzerdefinierten Modells", "tokens": {"title": "Maximale Token-<PERSON>hl", "unlimited": "Unbegrenzt"}, "vision": {"extra": "Diese Konfiguration aktiviert nur die Bild-Upload-Funktionalität in der Anwendung. Ob die Erkennung unterstützt wird, hängt vollständig vom Modell selbst ab. Bitte testen Sie die Verwendbarkeit der visuellen Erkennungsfähigkeiten dieses Modells selbst.", "title": "Unterstützung für visuelle Erkennung"}}}, "fetchOnClient": {"desc": "Der Client-Anforderungsmodus initiiert die Sitzung direkt über den Browser, was die Reaktionsgeschwindigkeit erhöht.", "title": "Client-Anforderungsmodus verwenden"}, "fetcher": {"fetch": "Model<PERSON>te a<PERSON>", "fetching": "<PERSON><PERSON><PERSON> wird abgerufen...", "latestTime": "Letzte Aktualisierung: {{time}}", "noLatestTime": "Liste wurde noch nicht abgerufen"}, "helpDoc": "Konfigurationsanleitung", "modelList": {"desc": "Wählen Sie die Modelle aus, die in der Sitzung angezeigt werden sollen. Die ausgewählten Modelle werden in der Modellliste angezeigt.", "placeholder": "Bitte wählen Si<PERSON> ein Modell aus der Liste aus", "title": "<PERSON><PERSON><PERSON>", "total": "Insgesamt {{count}} Modelle verfügbar"}, "proxyUrl": {"desc": "Neben der Standardadresse muss http(s):// enthalten sein.", "title": "API-Proxy-Adresse"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waitingForMore": "<PERSON><PERSON>e Modelle werden gerade <1>g<PERSON><PERSON></1>, bitte warten <PERSON>."}, "systemAgent": {"customPrompt": {"addPrompt": "Benutzerdefinierte Eingabe hinzufügen", "desc": "Nachdem Sie dies ausgefüllt haben, wird der Systemassistent die benutzerdefinierte Eingabe bei der Inhaltserstellung verwenden", "placeholder": "Bitte benutzerdefinierte Eingabe eingeben", "title": "Benutzerdefinierte Eingabe"}, "emotionAnalysis": {"label": "Emotionanalyse-Modell", "modelDesc": "Das für die Emotionanalyse verwendete Modell angeben", "title": "Automatische Emotionanalyse durchführen"}, "title": "System-Agent"}, "touch": {"title": "Berührungseinstellungen"}, "tts": {"clientCall": {"desc": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, wird der Client den Sprachsynthesedienst aufrufen, was die Sprachsynthese schneller macht, jedoch eine wissenschaftliche Internetverbindung oder die Fähigkeit erfordert, auf das externe Internet zuzugreifen.", "title": "<PERSON><PERSON>"}, "title": "Sprach Einstellungen"}}
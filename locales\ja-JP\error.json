{"apiKeyMiss": "OpenAI API キーが空です。カスタム OpenAI API キーを追加してください。", "dancePlayError": "ダンスファイルの再生に失敗しました。しばらくしてから再試行してください。", "error": "エラー", "errorTip": {"clearSession": "セッションメッセージをクリア", "description": "現在、プロジェクトは工事中であり、データの安定性は保証されていません。問題が発生した場合は、試してみてください", "forgive": "ご不便をおかけして申し訳ありません", "or": "または", "problem": "ページに少し問題が発生しました...", "resetSystem": "システム設定をリセット"}, "fileUploadError": "ファイルのアップロードに失敗しました。後でもう一度お試しください。", "formValidationFailed": "フォームの検証に失敗しました:", "goBack": "ホームに戻る", "openaiError": "OpenAI API エラーです。OpenAI API キーとエンドポイントが正しいかどうかを確認してください。", "reload": "再読み込み", "response": {"400": "申し訳ありませんが、サーバーはあなたのリクエストを理解できませんでした。リクエストパラメータが正しいか確認してください。", "401": "申し訳ありませんが、サーバーはあなたのリクエストを拒否しました。権限が不足しているか、有効な認証情報が提供されていない可能性があります。", "403": "申し訳ありませんが、サーバーはあなたのリクエストを拒否しました。このコンテンツにアクセスする権限がありません。", "404": "申し訳ありませんが、サーバーはあなたがリクエストしたページまたはリソースを見つけることができませんでした。URLが正しいか確認してください。", "405": "申し訳ありませんが、サーバーはあなたが使用しているリクエストメソッドをサポートしていません。リクエストメソッドが正しいか確認してください。", "406": "申し訳ありませんが、サーバーはあなたのリクエストの内容特性に基づいてリクエストを完了できませんでした。", "407": "申し訳ありませんが、プロキシ認証を行った後でないとこのリクエストを続行できません。", "408": "申し訳ありませんが、サーバーはリクエストを待っている間にタイムアウトしました。ネットワーク接続を確認してから再試行してください。", "409": "申し訳ありませんが、リクエストに衝突があり処理できません。リソースの状態がリクエストと互換性がない可能性があります。", "410": "申し訳ありませんが、あなたがリクエストしたリソースは永久に削除され、見つけることができません。", "411": "申し訳ありませんが、サーバーは有効なコンテンツ長を含まないリクエストを処理できません。", "412": "申し訳ありませんが、あなたのリクエストはサーバー側の条件を満たしておらず、リクエストを完了できません。", "413": "申し訳ありませんが、あなたのリクエストデータ量が大きすぎて、サーバーは処理できません。", "414": "申し訳ありませんが、あなたのリクエストのURIが長すぎて、サーバーは処理できません。", "415": "申し訳ありませんが、サーバーはリクエストに添付されたメディア形式を処理できません。", "416": "申し訳ありませんが、サーバーはあなたのリクエストの範囲を満たすことができません。", "417": "申し訳ありませんが、サーバーはあなたの期待値を満たすことができません。", "422": "申し訳ありませんが、あなたのリクエスト形式は正しいですが、意味的なエラーが含まれているため、応答できません。", "423": "申し訳ありませんが、あなたがリクエストしたリソースはロックされています。", "424": "申し訳ありませんが、以前のリクエストが失敗したため、現在のリクエストを完了できません。", "426": "申し訳ありませんが、サーバーはあなたのクライアントにより高いプロトコルバージョンへのアップグレードを要求しています。", "428": "申し訳ありませんが、サーバーは前提条件を要求しており、あなたのリクエストには正しい条件ヘッダーが含まれている必要があります。", "429": "申し訳ありませんが、あなたのリクエストが多すぎて、サーバーは少し疲れています。後でもう一度お試しください。", "431": "申し訳ありませんが、あなたのリクエストヘッダーフィールドが大きすぎて、サーバーは処理できません。", "451": "申し訳ありませんが、法的理由により、サーバーはこのリソースの提供を拒否しました。", "500": "申し訳ありませんが、サーバーは何らかの問題に直面しているようで、一時的にあなたのリクエストを完了できません。後でもう一度お試しください。", "501": "申し訳ありませんが、サーバーはこのリクエストを処理する方法をまだ知らないようです。操作が正しいか確認してください。", "502": "申し訳ありませんが、サーバーは方向を見失ったようで、一時的にサービスを提供できません。後でもう一度お試しください。", "503": "申し訳ありませんが、サーバーは現在あなたのリクエストを処理できません。過負荷またはメンテナンス中の可能性があります。後でもう一度お試しください。", "504": "申し訳ありませんが、サーバーは上流サーバーからの応答を待っていませんでした。後でもう一度お試しください。", "505": "申し訳ありませんが、サーバーはあなたが使用しているHTTPバージョンをサポートしていません。更新してから再試行してください。", "506": "申し訳ありませんが、サーバーの設定に問題が発生しました。管理者に連絡して解決してください。", "507": "申し訳ありませんが、サーバーのストレージスペースが不足しており、あなたのリクエストを処理できません。後でもう一度お試しください。", "509": "申し訳ありませんが、サーバーの帯域幅が使い果たされました。後でもう一度お試しください。", "510": "申し訳ありませんが、サーバーはリクエストされた拡張機能をサポートしていません。管理者に連絡してください。", "524": "申し訳ありませんが、サーバーは応答を待っている間にタイムアウトしました。応答が遅すぎる可能性があります。後でもう一度お試しください。", "AgentRuntimeError": "Lobe AI Runtimeの実行中にエラーが発生しました。以下の情報に基づいて調査または再試行してください。", "FreePlanLimit": "現在は無料ユーザーのため、この機能を使用できません。有料プランにアップグレードしてから続行してください。", "InvalidAccessCode": "パスワードが正しくないか空です。正しいアクセスパスワードを入力するか、カスタムAPIキーを追加してください。", "InvalidBedrockCredentials": "Bedrockの認証に失敗しました。AccessKeyId/SecretAccessKeyを確認して再試行してください。", "InvalidClerkUser": "申し訳ありませんが、現在ログインしていません。先にログインまたはアカウントを登録してから操作を続行してください。", "InvalidGithubToken": "Github PATが正しくないか空です。Github PATを確認して再試行してください。", "InvalidOllamaArgs": "<PERSON><PERSON>maの設定が正しくありません。<PERSON><PERSON>maの設定を確認して再試行してください。", "InvalidProviderAPIKey": "{{provider}} APIキーが正しくないか空です。{{provider}} APIキーを確認して再試行してください。", "LocationNotSupportError": "申し訳ありませんが、あなたの地域ではこのモデルサービスがサポートされていません。地域制限やサービスが開通していない可能性があります。現在の地域がこのサービスを使用できるか確認するか、他の地域に切り替えて再試行してください。", "OllamaBizError": "Ollamaサービスのリクエストにエラーが発生しました。以下の情報に基づいて調査または再試行してください。", "OllamaServiceUnavailable": "Ollamaサービスの接続に失敗しました。Ollamaが正常に動作しているか、<PERSON>llamaのクロスオリジン設定が正しいか確認してください。", "PermissionDenied": "申し訳ありませんが、あなたにはこのサービスにアクセスする権限がありません。キーにアクセス権があるか確認してください。", "PluginApiNotFound": "申し訳ありませんが、プラグインのマニフェストにそのAPIが存在しません。リクエストメソッドとプラグインマニフェストAPIが一致しているか確認してください。", "PluginApiParamsError": "申し訳ありませんが、そのプラグインのリクエストパラメータの検証に失敗しました。入参とAPIの説明情報が一致しているか確認してください。", "PluginFailToTransformArguments": "申し訳ありませんが、プラグインの呼び出しパラメータの解析に失敗しました。アシスタントメッセージを再生成するか、より強力なAIモデルに切り替えて再試行してください。", "PluginGatewayError": "申し訳ありませんが、プラグインゲートウェイにエラーが発生しました。プラグインゲートウェイの設定が正しいか確認してください。", "PluginManifestInvalid": "申し訳ありませんが、そのプラグインのマニフェストの検証に失敗しました。マニフェストの形式が規範に従っているか確認してください。", "PluginManifestNotFound": "申し訳ありませんが、サーバーはそのプラグインのマニフェスト（manifest.json）を見つけることができませんでした。プラグインの説明ファイルのアドレスが正しいか確認してください。", "PluginMarketIndexInvalid": "申し訳ありませんが、プラグインインデックスの検証に失敗しました。インデックスファイルの形式が規範に従っているか確認してください。", "PluginMarketIndexNotFound": "申し訳ありませんが、サーバーはプラグインインデックスを見つけることができませんでした。インデックスアドレスが正しいか確認してください。", "PluginMetaInvalid": "申し訳ありませんが、そのプラグインのメタ情報の検証に失敗しました。プラグインのメタ情報の形式が規範に従っているか確認してください。", "PluginMetaNotFound": "申し訳ありませんが、インデックス内でそのプラグインを見つけることができませんでした。プラグインのインデックス内の設定情報を確認してください。", "PluginOpenApiInitError": "申し訳ありませんが、OpenAPIクライアントの初期化に失敗しました。OpenAPIの設定情報が正しいか確認してください。", "PluginServerError": "プラグインサーバーのリクエストがエラーを返しました。以下のエラーメッセージに基づいてプラグインの説明ファイル、プラグイン設定、またはサーバー実装を確認してください。", "PluginSettingsInvalid": "そのプラグインは正しく設定される必要があります。設定が正しいか確認してください。", "ProviderBizError": "{{provider}}サービスのリクエストにエラーが発生しました。以下の情報に基づいて調査または再試行してください。", "QuotaLimitReached": "申し訳ありませんが、現在のトークン使用量またはリクエスト回数がそのキーのクォータ上限に達しました。キーのクォータを増やすか、後でもう一度お試しください。", "StreamChunkError": "ストリーミングリクエストのメッセージブロック解析エラーが発生しました。現在のAPIインターフェースが標準規範に準拠しているか確認するか、APIプロバイダーに相談してください。", "SubscriptionPlanLimit": "あなたのサブスクリプションのクォータが使い果たされ、この機能を使用できません。より高いプランにアップグレードするか、リソースパッケージを購入してから続行してください。", "UnknownChatFetchError": "申し訳ありませんが、未知のリクエストエラーが発生しました。以下の情報に基づいて調査または再試行してください。"}, "s3envError": "S3 環境変数が完全に設定されていません。環境変数を確認してください。", "serverError": "サーバーエラーが発生しました。管理者にお問い合わせください", "triggerError": "エラーをトリガーする", "ttsTransformFailed": "音声変換に失敗しました。ネットワークを確認するか、設定でクライアント呼び出しを有効にして再試行してください。", "unknownError": "不明なエラー", "unlock": {"addProxyUrl": "OpenAI プロキシ URL を追加（オプション）", "apiKey": {"description": "あなたの {{name}} API キーを入力して会話を開始してください", "title": "カスタム {{name}} API キーを使用する"}, "closeMessage": "通知を閉じる", "confirm": "確認して再試行", "oauth": {"description": "管理者が統一ログイン認証を有効にしました。下のボタンをクリックしてログインすると、アプリが解除されます", "success": "ログイン成功", "title": "アカウントにログイン", "welcome": "ようこそ！"}, "password": {"description": "管理者がアプリの暗号化を有効にしました。アプリのパスワードを入力すると、アプリが解除されます。パスワードは一度だけ入力してください", "placeholder": "パスワードを入力してください", "title": "パスワードを入力してアプリを解除する"}, "tabs": {"apiKey": "カスタム API キー", "password": "パスワード"}}}
import React, { useState, useRef, useCallback } from 'react';
import { Input, Button, Tooltip, message } from 'antd';
import { 
  SendOutlined, 
  AudioOutlined, 
  VideoCameraOutlined,
  StopOutlined 
} from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useSessionStore } from '../../../store/session';
import { useGlobalStore } from '../../../store/global';
import { useSpeechRecognition } from '../../../hooks/useSpeechRecognition';

const { TextArea } = Input;

interface MessageInputProps {
  onVoiceInput?: (transcript: string) => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ onVoiceInput }) => {
  // 状态管理
  const { 
    messageInput, 
    setMessageInput, 
    sendMessage,
    chatLoadingId 
  } = useSessionStore();
  
  const { 
    setChatMode, 
    setVoiceOn 
  } = useGlobalStore();

  // 本地状态
  const [isComposing, setIsComposing] = useState(false);
  const textAreaRef = useRef<any>(null);

  // 语音识别
  const { 
    isSupported: speechSupported,
    isListening,
    startListening,
    stopListening
  } = useSpeechRecognition();

  // 发送消息
  const handleSend = useCallback(async () => {
    if (!messageInput.trim() || chatLoadingId) return;

    try {
      await sendMessage(messageInput);
      setMessageInput('');
      
      // 聚焦输入框
      if (textAreaRef.current) {
        textAreaRef.current.focus();
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请稍后再试');
    }
  }, [messageInput, chatLoadingId, sendMessage, setMessageInput]);

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSend();
    }
  };

  // 语音输入处理
  const handleVoiceInput = useCallback((transcript: string, isFinal: boolean) => {
    if (isFinal && transcript.trim()) {
      onVoiceInput?.(transcript);
    } else {
      // 实时显示语音识别结果
      setMessageInput(transcript);
    }
  }, [onVoiceInput, setMessageInput]);

  // 切换语音录制
  const toggleVoiceRecording = () => {
    if (!speechSupported) {
      message.error('您的浏览器不支持语音识别');
      return;
    }

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // 切换到摄像头模式
  const switchToCameraMode = () => {
    setChatMode('camera');
    setVoiceOn(true);
    message.info('已切换到摄像头模式');
  };

  return (
    <div className="message-input-container">
      <Flexbox gap={8} align="flex-end" className="message-input-wrapper">
        {/* 文本输入区域 */}
        <div className="message-input-text">
          <TextArea
            ref={textAreaRef}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder="输入消息... (Enter发送，Shift+Enter换行)"
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={!!chatLoadingId}
            className="message-textarea"
          />
        </div>

        {/* 操作按钮组 */}
        <Flexbox horizontal gap={4} className="message-input-actions">
          {/* 语音录制按钮 */}
          <Tooltip title={speechSupported ? (isListening ? "停止录音" : "语音输入") : "浏览器不支持语音识别"}>
            <Button
              type={isListening ? "primary" : "default"}
              icon={isListening ? <StopOutlined /> : <AudioOutlined />}
              onClick={toggleVoiceRecording}
              disabled={!speechSupported || !!chatLoadingId}
              loading={isListening}
              size="large"
            />
          </Tooltip>

          {/* 摄像头模式按钮 */}
          <Tooltip title="切换到摄像头模式">
            <Button
              type="default"
              icon={<VideoCameraOutlined />}
              onClick={switchToCameraMode}
              disabled={!!chatLoadingId}
              size="large"
            />
          </Tooltip>

          {/* 发送按钮 */}
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            disabled={!messageInput.trim() || !!chatLoadingId}
            loading={!!chatLoadingId}
            size="large"
          />
        </Flexbox>
      </Flexbox>

      {/* 语音识别状态提示 */}
      {isListening && (
        <div className="voice-recording-indicator">
          <span className="recording-dot"></span>
          正在录音，请说话...
        </div>
      )}
    </div>
  );
};

export default MessageInput;

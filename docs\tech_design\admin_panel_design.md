# 后端管理界面设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中后端管理界面的设计。该管理界面专为系统管理员提供，用于对虚拟角色进行全面管理，包括创建、查看、编辑、删除角色，以及管理系统提示词等核心操作。

## 2. 模块概述

后端管理界面是一个独立的网站模块，仅对系统管理员开放访问。它提供了友好的图形用户界面，使管理员能够高效地管理和维护虚拟角色平台的核心数据。该模块与用户认证和授权模块紧密结合，确保只有具备管理员权限的用户才能访问。

## 3. 核心功能

### 3.1 管理员认证与授权

- **登录系统**：管理员通过专门的登录入口访问后台管理系统
- **权限控制**：基于已有的用户认证与授权模块进行扩展，添加管理员角色及相关权限

### 3.2 角色管理功能

- **角色列表查看**：分页展示所有角色，支持按名称、创建日期、用户ID等字段排序和筛选
- **角色详情查看**：查看单个角色的完整信息，包括基础信息、外观参数、系统设定等
- **角色创建**：使用星火API生成新的角色图片，并配置相关参数
- **角色编辑**：修改角色的基本信息、外观参数等
- **角色删除**：支持单个删除或批量删除功能
- **角色预览**：查看角色的实际呈现效果

### 3.3 系统提示词管理

- **提示词模板查看**：查看现有的系统提示词模板列表
- **提示词模板编辑**：创建和修改系统提示词模板
- **提示词测试**：测试提示词效果的功能
- **提示词分类管理**：对提示词进行分类管理

## 4. 用户界面设计

### 4.1 整体布局

- **侧边导航栏**：提供功能导航，包含角色管理、提示词管理等主要功能入口
- **顶部栏**：显示当前登录管理员信息、退出按钮等
- **主内容区**：根据选择的功能展示相应的内容和操作界面
- **页脚**：版本信息和版权声明

### 4.2 主要页面

- **登录页面**：管理员登录入口
- **仪表板**：概览系统数据，如角色总数、用户总数、系统使用状态等
- **角色列表页**：表格形式展示所有角色
- **角色详情页**：展示单个角色的详细信息
- **角色创建/编辑页**：表单界面，用于创建或编辑角色
- **提示词管理页**：管理系统提示词模板
- **系统设置页**：全局系统配置设置

## 5. 技术实现

### 5.1 前端技术

- **框架选择**：使用React作为前端框架，配合Ant Design等UI组件库
- **状态管理**：使用Zustand进行状态管理
- **路由管理**：使用React Router实现页面导航

### 5.2 后端接口

#### 管理员认证接口
- **URL:** `/api/admin/login`
- **Method:** `POST`
- **Description:** 管理员登录接口
- **Request Body (JSON):**
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Response:** 返回JWT Token和管理员信息

#### 角色管理接口

- **获取角色列表**
  - **URL:** `/api/admin/characters`
  - **Method:** `GET`
  - **Description:** 获取所有角色列表(分页)
  - **Query Parameters:** `page`, `pageSize`, `sortBy`, `orderBy`, `filter`等
  - **Response:** 返回角色列表和分页信息

- **获取角色详情**
  - **URL:** `/api/admin/characters/{character_id}`
  - **Method:** `GET`
  - **Description:** 获取角色详细信息
  - **Response:** 返回角色的完整信息

- **创建角色**
  - **URL:** `/api/admin/characters`
  - **Method:** `POST`
  - **Description:** 创建新角色
  - **Request Body (JSON):** 角色信息，包括基础设定和外观参数
  - **Response:** 返回新创建角色的ID和状态

- **更新角色**
  - **URL:** `/api/admin/characters/{character_id}`
  - **Method:** `PUT`
  - **Description:** 更新角色信息
  - **Request Body (JSON):** 需要更新的角色信息
  - **Response:** 返回更新状态

- **删除角色**
  - **URL:** `/api/admin/characters/{character_id}`
  - **Method:** `DELETE`
  - **Description:** 删除指定角色
  - **Response:** 返回删除状态

- **生成角色图片**
  - **URL:** `/api/admin/characters/generate-image`
  - **Method:** `POST`
  - **Description:** 使用星火API生成角色图片
  - **Request Body (JSON):** 图片生成参数
  - **Response:** 返回生成的图片URL和相关参数

#### 系统提示词管理接口

- **获取提示词模板列表**
  - **URL:** `/api/admin/prompts`
  - **Method:** `GET`
  - **Description:** 获取所有提示词模板
  - **Query Parameters:** `type`, `category`等筛选参数
  - **Response:** 返回提示词模板列表

- **获取提示词模板详情**
  - **URL:** `/api/admin/prompts/{prompt_id}`
  - **Method:** `GET`
  - **Description:** 获取提示词模板详情
  - **Response:** 返回提示词模板详细信息

- **创建提示词模板**
  - **URL:** `/api/admin/prompts`
  - **Method:** `POST`
  - **Description:** 创建新的提示词模板
  - **Request Body (JSON):** 提示词模板信息
  - **Response:** 返回新创建模板的ID和状态

- **更新提示词模板**
  - **URL:** `/api/admin/prompts/{prompt_id}`
  - **Method:** `PUT`
  - **Description:** 更新提示词模板
  - **Request Body (JSON):** 需要更新的模板信息
  - **Response:** 返回更新状态

- **删除提示词模板**
  - **URL:** `/api/admin/prompts/{prompt_id}`
  - **Method:** `DELETE`
  - **Description:** 删除指定提示词模板
  - **Response:** 返回删除状态

- **测试提示词效果**
  - **URL:** `/api/admin/prompts/test`
  - **Method:** `POST`
  - **Description:** 测试提示词的效果
  - **Request Body (JSON):** 提示词内容和测试参数
  - **Response:** 返回测试结果

### 5.3 数据库结构扩展

在现有数据库结构基础上，为支持管理界面功能，需增加以下表:

```sql
-- 管理员角色表
CREATE TABLE admin_roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户-管理员角色关联表
CREATE TABLE user_admin_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES admin_roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_role UNIQUE (user_id, role_id)
);

-- 提示词模板表
CREATE TABLE prompt_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'image', 'chat', 'system', etc.
    category VARCHAR(50), -- 分类
    content TEXT NOT NULL, -- 提示词内容
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    version VARCHAR(20), -- 版本号，用于版本控制
    variables JSONB, -- 模板中可替换的变量定义
    examples JSONB, -- 示例输入输出
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色-提示词模板关联表
CREATE TABLE character_prompt_templates (
    id SERIAL PRIMARY KEY,
    character_id INTEGER NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    template_id INTEGER NOT NULL REFERENCES prompt_templates(id) ON DELETE CASCADE,
    custom_content TEXT, -- 角色特定的自定义提示词内容
    variables_values JSONB, -- 变量具体值
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_character_template UNIQUE (character_id, template_id)
);

-- 系统配置表
CREATE TABLE system_configs (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_by INTEGER REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 管理操作日志表
CREATE TABLE admin_operation_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL, -- 'create', 'update', 'delete', 'login', etc
    target_type VARCHAR(50), -- 'character', 'prompt_template', 'user', etc
    target_id INTEGER, -- 目标对象ID
    details JSONB, -- 操作详情
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 6. 模块与其他模块的交互

### 6.1 与用户认证和授权模块的交互

后端管理界面将利用现有的用户认证和授权模块(TASK121-132)，但需进行扩展以支持管理员角色和权限。具体来说:

- 在用户认证流程中增加管理员角色的验证，扩展现有TASK131中基于登录状态的授权模型
- 添加新的权限检查中间件，确保只有管理员可以访问后台API，基于TASK128中的JWT认证中间件
- 实现管理员操作日志记录，与TASK129中的认证相关错误处理与日志记录机制集成
- 复用TASK125中的JWT生成与验证机制，为管理员生成特殊的管理权限令牌

### 6.2 与AI服务集成模块的交互

管理界面需要调用AI服务集成模块来生成角色图片，直接依赖于TASK001-010中实现的功能:

- 调用星火API进行图片生成，使用TASK006中封装的AI服务集成模块接口
- 处理生成结果并存储到数据库，利用TASK010中的云存储服务集成功能
- 提供图片预览功能，显示TASK005中上传到云存储的图片
- 复用TASK007中的图片分辨率选择策略进行图片生成参数配置

### 6.3 与提示词工程模块的交互

管理界面的系统提示词管理功能将直接与提示词工程模块(TASK101-110)交互:

- 读取和更新提示词模板库，基于TASK102中实现的提示词模板库存储与加载功能
- 测试提示词效果，利用TASK110中的提示词效果验证与持续优化机制
- 将更新后的提示词应用到实际角色生成和对话中，应用TASK103和TASK107中的提示词生成逻辑
- 提供提示词模板编辑界面，直接操作TASK102中定义的提示词模板存储系统
- 提供系统提示词效果预览，通过调用TASK109中的提示词服务接口进行测试

## 7. 安全考量

- **访问控制**: 严格限制只有管理员角色可访问后台管理界面
- **操作日志**: 记录所有管理操作，便于审计和追踪
- **输入验证**: 对所有用户输入进行严格验证，防止注入攻击
- **敏感信息保护**: 确保不显示敏感数据(如用户密码)
- **HTTPS加密**: 所有管理接口通信必须使用HTTPS加密


import React, { useState, useEffect } from 'react';
import { Card, Select, Slider, Button, Space, Typography, Row, Col, message, Spin } from 'antd';
import { SoundOutlined, PlayCircleOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Option } = Select;
const { Text } = Typography;

interface Voice {
  id: string;
  name: string;
  description: string;
  age_group: string;
  gender: string;
  code: string;
}

interface VoiceConfig {
  provider: string;
  voices: Voice[];
  speeds: Record<string, number>;
  emotions: Record<string, string>;
}

interface VoiceSettings {
  voice_type: string;
  speed: string;
  emotion: string;
  volume: number;
  pitch: number;
  auto_play: boolean;
}

interface VoiceSelectorProps {
  characterId?: string;
  initialSettings?: VoiceSettings;
  onSettingsChange?: (settings: VoiceSettings) => void;
  showPreview?: boolean;
}

const VoiceSelector: React.FC<VoiceSelectorProps> = ({
  characterId,
  initialSettings,
  onSettingsChange,
  showPreview = true
}) => {
  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig | null>(null);
  const [settings, setSettings] = useState<VoiceSettings>({
    voice_type: 'female_sweet',
    speed: 'normal',
    emotion: 'neutral',
    volume: 50,
    pitch: 50,
    auto_play: true,
    ...initialSettings
  });
  const [loading, setLoading] = useState(false);
  const [previewing, setPreviewing] = useState(false);

  // 加载音色配置
  useEffect(() => {
    const fetchVoiceConfig = async () => {
      try {
        setLoading(true);
        const response = await api.get('/tts/voices/');
        setVoiceConfig(response.data);
      } catch (error) {
        console.error('加载音色配置失败:', error);
        message.error('加载音色配置失败');
      } finally {
        setLoading(false);
      }
    };

    fetchVoiceConfig();
  }, []);

  // 加载角色的语音设置
  useEffect(() => {
    if (characterId) {
      const fetchCharacterVoiceSettings = async () => {
        try {
          const response = await api.get(`/characters/${characterId}/voice-settings/`);
          const voiceSettings = response.data.voice_settings;
          setSettings(voiceSettings);
          onSettingsChange?.(voiceSettings);
        } catch (error) {
          console.error('加载角色语音设置失败:', error);
        }
      };

      fetchCharacterVoiceSettings();
    }
  }, [characterId, onSettingsChange]);

  // 更新设置
  const updateSettings = (newSettings: Partial<VoiceSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };

  // 保存设置到服务器
  const saveSettings = async () => {
    if (!characterId) return;

    try {
      setLoading(true);
      await api.put(`/characters/${characterId}/voice-settings/`, settings);
      message.success('语音设置保存成功');
    } catch (error) {
      console.error('保存语音设置失败:', error);
      message.error('保存语音设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 预览音色
  const previewVoice = async () => {
    try {
      setPreviewing(true);
      const response = await api.post('/tts/preview/', {
        voice_type: settings.voice_type,
        speed: settings.speed,
        emotion: settings.emotion,
        volume: settings.volume,
        pitch: settings.pitch,
        text: '你好，这是语音预览，请问你觉得这个声音怎么样？'
      });

      if (response.data.audio_url) {
        const audio = new Audio(response.data.audio_url);
        audio.play().catch(error => {
          console.error('音频播放失败:', error);
          message.error('音频播放失败');
        });
      }
    } catch (error) {
      console.error('语音预览失败:', error);
      message.error('语音预览失败');
    } finally {
      setPreviewing(false);
    }
  };

  // 获取推荐音色 - 暂时注释掉未使用的函数
  // const getRecommendedVoice = async (gender: string, age: number, personality: string) => {
  //   try {
  //     const response = await api.post('/tts/recommend/', {
  //       gender,
  //       age,
  //       personality
  //     });

  //     const recommendedVoice = response.data.recommended_voice;
  //     updateSettings({ voice_type: recommendedVoice.id });
  //     message.success(`推荐音色: ${recommendedVoice.name} - ${recommendedVoice.description}`);
  //   } catch (error) {
  //     console.error('获取推荐音色失败:', error);
  //     message.error('获取推荐音色失败');
  //   }
  // };

  if (loading && !voiceConfig) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '10px' }}>加载音色配置中...</div>
        </div>
      </Card>
    );
  }

  const currentVoice = voiceConfig?.voices.find(v => v.id === settings.voice_type);

  return (
    <Card 
      title={
        <Space>
          <SoundOutlined />
          <span>语音设置</span>
        </Space>
      }
      extra={
        characterId && (
          <Button 
            type="primary" 
            onClick={saveSettings}
            loading={loading}
          >
            保存设置
          </Button>
        )
      }
    >
      <Row gutter={[16, 16]}>
        {/* 音色选择 */}
        <Col span={24}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>音色选择</Text>
            {currentVoice && (
              <Text type="secondary" style={{ marginLeft: '8px' }}>
                ({currentVoice.name} - {currentVoice.description})
              </Text>
            )}
          </div>
          <Select
            value={settings.voice_type}
            onChange={(value) => updateSettings({ voice_type: value })}
            style={{ width: '100%' }}
            placeholder="选择音色"
          >
            {voiceConfig?.voices.map(voice => (
              <Option key={voice.id} value={voice.id}>
                <Space>
                  <span>{voice.name}</span>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {voice.description}
                  </Text>
                </Space>
              </Option>
            ))}
          </Select>
        </Col>

        {/* 语速设置 */}
        <Col span={12}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>语速</Text>
          </div>
          <Select
            value={settings.speed}
            onChange={(value) => updateSettings({ speed: value })}
            style={{ width: '100%' }}
          >
            {Object.entries(voiceConfig?.speeds || {}).map(([key]) => (
              <Option key={key} value={key}>
                {key === 'very_slow' ? '很慢' :
                 key === 'slow' ? '慢' :
                 key === 'normal' ? '正常' :
                 key === 'fast' ? '快' :
                 key === 'very_fast' ? '很快' : key}
              </Option>
            ))}
          </Select>
        </Col>

        {/* 情感设置 */}
        <Col span={12}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>情感</Text>
          </div>
          <Select
            value={settings.emotion}
            onChange={(value) => updateSettings({ emotion: value })}
            style={{ width: '100%' }}
          >
            {Object.entries(voiceConfig?.emotions || {}).map(([key]) => (
              <Option key={key} value={key}>
                {key === 'neutral' ? '中性' :
                 key === 'happy' ? '开心' :
                 key === 'sad' ? '悲伤' :
                 key === 'angry' ? '愤怒' :
                 key === 'surprised' ? '惊讶' : key}
              </Option>
            ))}
          </Select>
        </Col>

        {/* 音量设置 */}
        <Col span={12}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>音量: {settings.volume}%</Text>
          </div>
          <Slider
            min={0}
            max={100}
            value={settings.volume}
            onChange={(value) => updateSettings({ volume: value })}
          />
        </Col>

        {/* 音调设置 */}
        <Col span={12}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>音调: {settings.pitch}%</Text>
          </div>
          <Slider
            min={0}
            max={100}
            value={settings.pitch}
            onChange={(value) => updateSettings({ pitch: value })}
          />
        </Col>

        {/* 预览按钮 */}
        {showPreview && (
          <Col span={24}>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={previewVoice}
              loading={previewing}
              style={{ width: '100%' }}
            >
              {previewing ? '生成预览中...' : '预览语音'}
            </Button>
          </Col>
        )}
      </Row>
    </Card>
  );
};

export default VoiceSelector;

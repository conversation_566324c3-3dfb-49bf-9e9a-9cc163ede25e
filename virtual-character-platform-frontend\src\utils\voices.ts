export const voiceMap: Record<string, any> = {
  'ar-SA-HamedNeural': {
    DisplayName: 'Hamed',
    DisplayVoiceName: 'HamedNeural',
    LocalName: 'حامد',
    PreviewSentence:
      'إن التطبيقات التي تتحاور مع المستخدمين بصوره طبيعية،  تعمل على  تحسين امكانية الوصول اليها وسهولة الاستخدام',
    ShortName: 'ar-SA-HamedNeural',
    locale: 'ar-SA',
    localeZH: '阿拉伯语(沙特阿拉伯)',
  },
  'ar-YE-MaryamNeural': {
    DisplayName: 'Maryam',
    DisplayVoiceName: 'MaryamNeural',
    LocalName: 'مريم',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-YE-MaryamNeural',
    locale: 'ar-YE',
    localeZH: '阿拉伯语(也门)',
  },
  'cy-GB-AledNeural': {
    DisplayName: 'Aled',
    DisplayVoiceName: 'AledNeural',
    LocalName: 'Aled',
    PreviewSentence:
      'Mae’r feddalwedd creu cynnwys sain yn galluogi rheoli priodoleddau lleferydd yn weledol mewn amser real.',
    ShortName: 'cy-GB-AledNeural',
    locale: 'cy-GB',
    localeZH: '威尔士语(英国)',
  },
  'de-CH-JanNeural': {
    DisplayName: 'Jan',
    DisplayVoiceName: 'JanNeural',
    LocalName: 'Jan',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-CH-JanNeural',
    locale: 'de-CH',
    localeZH: '德语(瑞士)',
  },
  'de-DE-AmalaNeural': {
    DisplayName: 'Amala',
    DisplayVoiceName: 'AmalaNeural',
    LocalName: 'Amala',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-AmalaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'de-DE-KatjaNeural': {
    DisplayName: 'Katja',
    DisplayVoiceName: 'KatjaNeural',
    LocalName: 'Katja',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-KatjaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'de-DE-LouisaNeural': {
    DisplayName: 'Louisa',
    DisplayVoiceName: 'LouisaNeural',
    LocalName: 'Louisa',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-LouisaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'de-DE-MajaNeural': {
    DisplayName: 'Maja',
    DisplayVoiceName: 'MajaNeural',
    LocalName: 'Maja',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-MajaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'en-AU-AnnetteNeural': {
    DisplayName: 'Annette',
    DisplayVoiceName: 'AnnetteNeural',
    LocalName: 'Annette',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-AnnetteNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'en-AU-KimNeural': {
    DisplayName: 'Kim',
    DisplayVoiceName: 'KimNeural',
    LocalName: 'Kim',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-KimNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'en-AU-TinaNeural': {
    DisplayName: 'Tina',
    DisplayVoiceName: 'TinaNeural',
    LocalName: 'Tina',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-TinaNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'en-AU-WilliamNeural': {
    DisplayName: 'William',
    DisplayVoiceName: 'WilliamNeural',
    LocalName: 'William',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-WilliamNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'en-GB-LibbyNeural': {
    DisplayName: 'Libby',
    DisplayVoiceName: 'LibbyNeural',
    LocalName: 'Libby',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-LibbyNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'en-GB-MiaNeural': {
    DisplayName: 'Mia',
    DisplayVoiceName: 'MiaNeural',
    LocalName: 'Mia',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-MiaNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'en-GB-RyanNeural': {
    DisplayName: 'Ryan',
    DisplayVoiceName: 'RyanNeural',
    LocalName: 'Ryan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-RyanNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'en-HK-SamNeural': {
    DisplayName: 'Sam',
    DisplayVoiceName: 'SamNeural',
    LocalName: 'Sam',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-HK-SamNeural',
    locale: 'en-HK',
    localeZH: '英语(香港特别行政区)',
  },
  'en-NG-AbeoNeural': {
    DisplayName: 'Abeo',
    DisplayVoiceName: 'AbeoNeural',
    LocalName: 'Abeo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-NG-AbeoNeural',
    locale: 'en-NG',
    localeZH: '英语(尼日利亚)',
  },
  'ar-MA-JamalNeural': {
    DisplayName: 'Jamal',
    DisplayVoiceName: 'JamalNeural',
    LocalName: 'جمال',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-MA-JamalNeural',
    locale: 'ar-MA',
    localeZH: '阿拉伯语(摩洛哥)',
  },
  'en-NG-EzinneNeural': {
    DisplayName: 'Ezinne',
    DisplayVoiceName: 'EzinneNeural',
    LocalName: 'Ezinne',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-NG-EzinneNeural',
    locale: 'en-NG',
    localeZH: '英语(尼日利亚)',
  },
  'en-NZ-MollyNeural': {
    DisplayName: 'Molly',
    DisplayVoiceName: 'MollyNeural',
    LocalName: 'Molly',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-NZ-MollyNeural',
    locale: 'en-NZ',
    localeZH: '英语(新西兰)',
  },
  'en-US-AIGenerate1Neural': {
    DisplayName: 'AIGenerate1',
    DisplayVoiceName: 'AIGenerate1Neural',
    LocalName: 'AIGenerate1',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AIGenerate1Neural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ar-OM-AbdullahNeural': {
    DisplayName: 'Abdullah',
    DisplayVoiceName: 'AbdullahNeural',
    LocalName: 'عبدالله',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-OM-AbdullahNeural',
    locale: 'ar-OM',
    localeZH: '阿拉伯语(阿曼)',
  },
  'en-US-ElizabethNeural': {
    DisplayName: 'Elizabeth',
    DisplayVoiceName: 'ElizabethNeural',
    LocalName: 'Elizabeth',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-ElizabethNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'cy-GB-NiaNeural': {
    DisplayName: 'Nia',
    DisplayVoiceName: 'NiaNeural',
    LocalName: 'Nia',
    PreviewSentence:
      'Mae’r feddalwedd creu cynnwys sain yn galluogi rheoli priodoleddau lleferydd yn weledol mewn amser real.',
    ShortName: 'cy-GB-NiaNeural',
    locale: 'cy-GB',
    localeZH: '威尔士语(英国)',
  },
  'en-US-JennyMultilingualNeural': {
    DisplayName: 'Jenny Multilingual',
    DisplayVoiceName: 'JennyMultilingualNeural',
    LocalName: 'Jenny Multilingual',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-JennyMultilingualNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'en-ZA-LukeNeural': {
    DisplayName: 'Luke',
    DisplayVoiceName: 'LukeNeural',
    LocalName: 'Luke',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-ZA-LukeNeural',
    locale: 'en-ZA',
    localeZH: '英语(南非)',
  },
  'es-AR-ElenaNeural': {
    DisplayName: 'Elena',
    DisplayVoiceName: 'ElenaNeural',
    LocalName: 'Elena',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-AR-ElenaNeural',
    locale: 'es-AR',
    localeZH: '西班牙语(阿根廷)',
  },
  'de-DE-KillianNeural': {
    DisplayName: 'Killian',
    DisplayVoiceName: 'KillianNeural',
    LocalName: 'Killian',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-KillianNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'es-CO-SalomeNeural': {
    DisplayName: 'Salome',
    DisplayVoiceName: 'SalomeNeural',
    LocalName: 'Salome',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CO-SalomeNeural',
    locale: 'es-CO',
    localeZH: '西班牙语(哥伦比亚)',
  },
  'bg-BG-KalinaNeural': {
    DisplayName: 'Kalina',
    DisplayVoiceName: 'KalinaNeural',
    LocalName: 'Калина',
    PreviewSentence:
      'Създай приложения и услуги, които говорят непринудено на потребителите, подобрявайки достъпността и използваемостта.',
    ShortName: 'bg-BG-KalinaNeural',
    locale: 'bg-BG',
    localeZH: '保加利亚语(保加利亚)',
  },
  'es-ES-ElviraNeural': {
    DisplayName: 'Elvira',
    DisplayVoiceName: 'ElviraNeural',
    LocalName: 'Elvira',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-ElviraNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'en-AU-TimNeural': {
    DisplayName: 'Tim',
    DisplayVoiceName: 'TimNeural',
    LocalName: 'Tim',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-TimNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'es-ES-NilNeural': {
    DisplayName: 'Nil',
    DisplayVoiceName: 'NilNeural',
    LocalName: 'Nil',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-NilNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'en-KE-AsiliaNeural': {
    DisplayName: 'Asilia',
    DisplayVoiceName: 'AsiliaNeural',
    LocalName: 'Asilia',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-KE-AsiliaNeural',
    locale: 'en-KE',
    localeZH: '英语(肯尼亚)',
  },
  'es-ES-VeraNeural': {
    DisplayName: 'Vera',
    DisplayVoiceName: 'VeraNeural',
    LocalName: 'Vera',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-VeraNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'es-MX-CecilioNeural': {
    DisplayName: 'Cecilio',
    DisplayVoiceName: 'CecilioNeural',
    LocalName: 'Cecilio',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-CecilioNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'es-MX-LucianoNeural': {
    DisplayName: 'Luciano',
    DisplayVoiceName: 'LucianoNeural',
    LocalName: 'Luciano',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-LucianoNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'es-MX-YagoNeural': {
    DisplayName: 'Yago',
    DisplayVoiceName: 'YagoNeural',
    LocalName: 'Yago',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-YagoNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'es-PA-MargaritaNeural': {
    DisplayName: 'Margarita',
    DisplayVoiceName: 'MargaritaNeural',
    LocalName: 'Margarita',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PA-MargaritaNeural',
    locale: 'es-PA',
    localeZH: '西班牙语(巴拿马)',
  },
  'ar-SY-LaithNeural': {
    DisplayName: 'Laith',
    DisplayVoiceName: 'LaithNeural',
    LocalName: 'ليث',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-SY-LaithNeural',
    locale: 'ar-SY',
    localeZH: '阿拉伯语(叙利亚)',
  },
  'es-PY-MarioNeural': {
    DisplayName: 'Mario',
    DisplayVoiceName: 'MarioNeural',
    LocalName: 'Mario',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PY-MarioNeural',
    locale: 'es-PY',
    localeZH: '西班牙语(巴拉圭)',
  },
  'am-ET-AmehaNeural': {
    DisplayName: 'Ameha',
    DisplayVoiceName: 'AmehaNeural',
    LocalName: 'አምሀ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'am-ET-AmehaNeural',
    locale: 'am-ET',
    localeZH: '阿姆哈拉语(埃塞俄比亚)',
  },
  'es-PY-TaniaNeural': {
    DisplayName: 'Tania',
    DisplayVoiceName: 'TaniaNeural',
    LocalName: 'Tania',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PY-TaniaNeural',
    locale: 'es-PY',
    localeZH: '西班牙语(巴拉圭)',
  },
  'en-US-AmberNeural': {
    DisplayName: 'Amber',
    DisplayVoiceName: 'AmberNeural',
    LocalName: 'Amber',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AmberNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'es-SV-RodrigoNeural': {
    DisplayName: 'Rodrigo',
    DisplayVoiceName: 'RodrigoNeural',
    LocalName: 'Rodrigo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-SV-RodrigoNeural',
    locale: 'es-SV',
    localeZH: '西班牙语(萨尔瓦多)',
  },
  'es-CR-JuanNeural': {
    DisplayName: 'Juan',
    DisplayVoiceName: 'JuanNeural',
    LocalName: 'Juan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CR-JuanNeural',
    locale: 'es-CR',
    localeZH: '西班牙语(哥斯达黎加)',
  },
  'fi-FI-SelmaNeural': {
    DisplayName: 'Selma',
    DisplayVoiceName: 'SelmaNeural',
    LocalName: 'Selma',
    PreviewSentence:
      'Kehitä luonnolisesti puhuvia sovelluksia ja palveluja, jotka parantavat käytettävyyttä ja saavutettavuutta.',
    ShortName: 'fi-FI-SelmaNeural',
    locale: 'fi-FI',
    localeZH: '芬兰语(芬兰)',
  },
  'es-MX-JorgeNeural': {
    DisplayName: 'Jorge',
    DisplayVoiceName: 'JorgeNeural',
    LocalName: 'Jorge',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-JorgeNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'fr-CH-ArianeNeural': {
    DisplayName: 'Ariane',
    DisplayVoiceName: 'ArianeNeural',
    LocalName: 'Ariane',
    PreviewSentence:
      'Développer des applications et des services qui parlent aux utilisateurs avec naturel, pour améliorer leur accessibilité et leur utilisation.',
    ShortName: 'fr-CH-ArianeNeural',
    locale: 'fr-CH',
    localeZH: '法语(瑞士)',
  },
  'en-US-BrandonNeural': {
    DisplayName: 'Brandon',
    DisplayVoiceName: 'BrandonNeural',
    LocalName: 'Brandon',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-BrandonNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'gl-ES-SabelaNeural': {
    DisplayName: 'Sabela',
    DisplayVoiceName: 'SabelaNeural',
    LocalName: 'Sabela',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'gl-ES-SabelaNeural',
    locale: 'gl-ES',
    localeZH: '加利西亚语(加利西亚语)',
  },
  'az-AZ-BabekNeural': {
    DisplayName: 'Babek',
    DisplayVoiceName: 'BabekNeural',
    LocalName: 'Babək',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'az-AZ-BabekNeural',
    locale: 'az-AZ',
    localeZH: '阿塞拜疆语(阿塞拜疆) ',
  },
  'he-IL-AvriNeural': {
    DisplayName: 'Avri',
    DisplayVoiceName: 'AvriNeural',
    LocalName: 'אברי',
    PreviewSentence:
      'בנה יישומים ושירותים שמדברים בטבעיות למשתמשים, שמשפרים את  הנגישות והשימושיות.',
    ShortName: 'he-IL-AvriNeural',
    locale: 'he-IL',
    localeZH: '希伯来语(以色列)',
  },
  'es-SV-LorenaNeural': {
    DisplayName: 'Lorena',
    DisplayVoiceName: 'LorenaNeural',
    LocalName: 'Lorena',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-SV-LorenaNeural',
    locale: 'es-SV',
    localeZH: '西班牙语(萨尔瓦多)',
  },
  'it-IT-DiegoNeural': {
    DisplayName: 'Diego',
    DisplayVoiceName: 'DiegoNeural',
    LocalName: 'Diego',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-DiegoNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'es-DO-RamonaNeural': {
    DisplayName: 'Ramona',
    DisplayVoiceName: 'RamonaNeural',
    LocalName: 'Ramona',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-DO-RamonaNeural',
    locale: 'es-DO',
    localeZH: '西班牙语(多米尼加共和国)',
  },
  'ja-JP-DaichiNeural': {
    DisplayName: 'Daichi',
    DisplayVoiceName: 'DaichiNeural',
    LocalName: '大智',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-DaichiNeural',
    locale: 'ja-JP',
    localeZH: '日语(日本)',
  },
  'es-ES-TrianaNeural': {
    DisplayName: 'Triana',
    DisplayVoiceName: 'TrianaNeural',
    LocalName: 'Triana',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-TrianaNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'ko-KR-GookMinNeural': {
    DisplayVoiceName: 'GookMinNeural',
    locale: 'ko-KR',
    DisplayName: 'GookMin',
    localeZH: '韩语(韩国)',
    LocalName: '국민',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
    ShortName: 'ko-KR-GookMinNeural',
  },
  'en-SG-WayneNeural': {
    DisplayName: 'Wayne',
    DisplayVoiceName: 'WayneNeural',
    LocalName: 'Wayne',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-SG-WayneNeural',
    locale: 'en-SG',
    localeZH: '英语(新加坡)',
  },
  'ko-KR-SoonBokNeural': {
    locale: 'ko-KR',
    localeZH: '韩语(韩国)',
    DisplayVoiceName: 'SoonBokNeural',
    DisplayName: 'SoonBok',
    LocalName: '순복',
    ShortName: 'ko-KR-SoonBokNeural',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
  },
  'en-GB-MaisieNeural': {
    DisplayName: 'Maisie',
    DisplayVoiceName: 'MaisieNeural',
    LocalName: 'Maisie',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-MaisieNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'bn-BD-NabanitaNeural': {
    DisplayName: 'Nabanita',
    DisplayVoiceName: 'NabanitaNeural',
    LocalName: 'নবনীতা',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bn-BD-NabanitaNeural',
    locale: 'bn-BD',
    localeZH: '孟加拉语(孟加拉)',
  },
  'pt-BR-LeilaNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'LeilaNeural',
    DisplayName: 'Leila',
    LocalName: 'Leila',
    ShortName: 'pt-BR-LeilaNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'ar-QA-MoazNeural': {
    DisplayName: 'Moaz',
    DisplayVoiceName: 'MoazNeural',
    LocalName: 'معاذ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-QA-MoazNeural',
    locale: 'ar-QA',
    localeZH: '阿拉伯语(卡塔尔)',
  },
  'vi-VN-HoaiMyNeural': {
    locale: 'vi-VN',
    localeZH: '越南语(越南)',
    DisplayVoiceName: 'HoaiMyNeural',
    DisplayName: 'HoaiMy',
    LocalName: 'Hoài My',
    ShortName: 'vi-VN-HoaiMyNeural',
    PreviewSentence:
      'Phát triển phần mềm và dịch vụ có thể giao tiếp tự nhiên với người dùng, nâng cao tính tiếp cận và tính khả dụng của sản phẩm',
  },
  'en-TZ-ElimuNeural': {
    DisplayName: 'Elimu',
    DisplayVoiceName: 'ElimuNeural',
    LocalName: 'Elimu',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-TZ-ElimuNeural',
    locale: 'en-TZ',
    localeZH: '英语(坦桑尼亚)',
  },
  'ja-JP-NanamiNeural': {
    locale: 'ja-JP',
    localeZH: '日语(日本)',
    DisplayVoiceName: 'NanamiNeural',
    DisplayName: 'Nanami',
    LocalName: '七海',
    ShortName: 'ja-JP-NanamiNeural',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
  },
  'de-DE-ConradNeural': {
    DisplayName: 'Conrad',
    DisplayVoiceName: 'ConradNeural',
    LocalName: 'Conrad',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-ConradNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'lo-LA-ChanthavongNeural': {
    locale: 'lo-LA',
    localeZH: '老挝语(老挝) ',
    DisplayVoiceName: 'ChanthavongNeural',
    DisplayName: 'Chanthavong',
    LocalName: 'ຈັນທະວົງ',
    ShortName: 'lo-LA-ChanthavongNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-TZ-ImaniNeural': {
    DisplayName: 'Imani',
    DisplayVoiceName: 'ImaniNeural',
    LocalName: 'Imani',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-TZ-ImaniNeural',
    locale: 'en-TZ',
    localeZH: '英语(坦桑尼亚)',
  },
  'en-US-DavisNeural': {
    DisplayName: 'Davis',
    DisplayVoiceName: 'DavisNeural',
    LocalName: 'Davis',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-DavisNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ta-LK-KumarNeural': {
    locale: 'ta-LK',
    localeZH: '泰米尔语(斯里兰卡)',
    DisplayVoiceName: 'KumarNeural',
    DisplayName: 'Kumar',
    LocalName: 'குமார்',
    ShortName: 'ta-LK-KumarNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-US-JasonNeural': {
    DisplayName: 'Jason',
    DisplayVoiceName: 'JasonNeural',
    LocalName: 'Jason',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-JasonNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ca-ES-JoanaNeural': {
    DisplayName: 'Joana',
    DisplayVoiceName: 'JoanaNeural',
    LocalName: 'Joana',
    PreviewSentence:
      "Crea aplicacions i serveis que parlen de forma natural als usuaris, i que milloren l'accessibilitat i la facilitat d'ús.",
    ShortName: 'ca-ES-JoanaNeural',
    locale: 'ca-ES',
    localeZH: '加泰罗尼亚语(西班牙)',
  },
  'kk-KZ-AigulNeural': {
    locale: 'kk-KZ',
    DisplayVoiceName: 'AigulNeural',
    localeZH: '哈萨克语(哈萨克斯坦)',
    DisplayName: 'Aigul',
    LocalName: 'Айгүл',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'kk-KZ-AigulNeural',
  },
  'en-US-SaraNeural': {
    DisplayName: 'Sara',
    DisplayVoiceName: 'SaraNeural',
    LocalName: 'Sara',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-SaraNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ta-SG-VenbaNeural': {
    locale: 'ta-SG',
    localeZH: '泰米尔语(新加坡)',
    DisplayVoiceName: 'VenbaNeural',
    DisplayName: 'Venba',
    LocalName: 'வெண்பா',
    ShortName: 'ta-SG-VenbaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-HK-YanNeural': {
    DisplayName: 'Yan',
    DisplayVoiceName: 'YanNeural',
    LocalName: 'Yan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-HK-YanNeural',
    locale: 'en-HK',
    localeZH: '英语(香港特别行政区)',
  },
  'es-HN-CarlosNeural': {
    DisplayName: 'Carlos',
    DisplayVoiceName: 'CarlosNeural',
    LocalName: 'Carlos',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-HN-CarlosNeural',
    locale: 'es-HN',
    localeZH: '西班牙语(洪都拉斯)',
  },
  'pt-PT-FernandaNeural': {
    locale: 'pt-PT',
    DisplayVoiceName: 'FernandaNeural',
    localeZH: '葡萄牙语(葡萄牙)',
    DisplayName: 'Fernanda',
    LocalName: 'Fernanda',
    PreviewSentence:
      'Constrói aplicações e serviços que falam naturalmente com os utilizadores, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-PT-FernandaNeural',
  },
  'ca-ES-AlbaNeural': {
    DisplayName: 'Alba',
    DisplayVoiceName: 'AlbaNeural',
    LocalName: 'Alba',
    PreviewSentence:
      "Crea aplicacions i serveis que parlen de forma natural als usuaris, i que milloren l'accessibilitat i la facilitat d'ús.",
    ShortName: 'ca-ES-AlbaNeural',
    locale: 'ca-ES',
    localeZH: '加泰罗尼亚语(西班牙)',
  },
  'ta-MY-KaniNeural': {
    locale: 'ta-MY',
    localeZH: '泰米尔语(马来西亚)',
    DisplayVoiceName: 'KaniNeural',
    DisplayName: 'Kani',
    LocalName: 'கனி',
    ShortName: 'ta-MY-KaniNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-AU-NatashaNeural': {
    DisplayName: 'Natasha',
    DisplayVoiceName: 'NatashaNeural',
    LocalName: 'Natasha',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-NatashaNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'it-IT-ImeldaNeural': {
    DisplayName: 'Imelda',
    DisplayVoiceName: 'ImeldaNeural',
    LocalName: 'Imelda',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    locale: 'it-IT',
    ShortName: 'it-IT-ImeldaNeural',
    localeZH: '意大利语(意大利)',
  },
  'es-MX-MarinaNeural': {
    DisplayName: 'Marina',
    DisplayVoiceName: 'MarinaNeural',
    LocalName: 'Marina',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-MarinaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'it-IT-PierinaNeural': {
    DisplayName: 'Pierina',
    DisplayVoiceName: 'PierinaNeural',
    LocalName: 'Pierina',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-PierinaNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'es-CO-GonzaloNeural': {
    DisplayName: 'Gonzalo',
    DisplayVoiceName: 'GonzaloNeural',
    LocalName: 'Gonzalo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CO-GonzaloNeural',
    locale: 'es-CO',
    localeZH: '西班牙语(哥伦比亚)',
  },
  'ja-JP-ShioriNeural': {
    DisplayName: 'Shiori',
    DisplayVoiceName: 'ShioriNeural',
    LocalName: '志織',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-ShioriNeural',
    locale: 'ja-JP',
    localeZH: '日语(日本)',
  },
  'en-US-CoraNeural': {
    DisplayName: 'Cora',
    DisplayVoiceName: 'CoraNeural',
    LocalName: 'Cora',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-CoraNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ka-GE-EkaNeural': {
    DisplayName: 'Eka',
    DisplayVoiceName: 'EkaNeural',
    LocalName: 'ეკა',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    locale: 'ka-GE',
    ShortName: 'ka-GE-EkaNeural',
    localeZH: '格鲁吉亚语(格鲁吉亚)',
  },
  'es-UY-MateoNeural': {
    DisplayName: 'Mateo',
    DisplayVoiceName: 'MateoNeural',
    LocalName: 'Mateo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-UY-MateoNeural',
    locale: 'es-UY',
    localeZH: '西班牙语(乌拉圭)',
  },
  'ko-KR-SunHiNeural': {
    DisplayVoiceName: 'SunHiNeural',
    locale: 'ko-KR',
    DisplayName: 'Sun-Hi',
    localeZH: '韩语(韩国)',
    LocalName: '선히',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
    ShortName: 'ko-KR-SunHiNeural',
  },
  'en-US-MichelleNeural': {
    DisplayName: 'Michelle',
    DisplayVoiceName: 'MichelleNeural',
    LocalName: 'Michelle',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-MichelleNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'lt-LT-LeonasNeural': {
    DisplayName: 'Leonas',
    DisplayVoiceName: 'LeonasNeural',
    LocalName: 'Leonas',
    locale: 'lt-LT',
    PreviewSentence:
      'Garso turinio kūrimas leidžia vizualiai kontroliuoti kalbos atributus realiu laiku.',
    localeZH: '立陶宛语(立陶宛)',
    ShortName: 'lt-LT-LeonasNeural',
  },
  'es-GQ-TeresaNeural': {
    DisplayName: 'Teresa',
    DisplayVoiceName: 'TeresaNeural',
    LocalName: 'Teresa',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-GQ-TeresaNeural',
    locale: 'es-GQ',
    localeZH: '西班牙语(赤道几内亚)',
  },
  'uk-UA-PolinaNeural': {
    DisplayVoiceName: 'PolinaNeural',
    locale: 'uk-UA',
    DisplayName: 'Polina',
    localeZH: '乌克兰语(乌克兰)',
    LocalName: 'Поліна',
    PreviewSentence:
      'Створення аудіовмісту дозволяє візуально контролювати мовні атрибути в реальному часі.',
    ShortName: 'uk-UA-PolinaNeural',
  },
  'es-ES-EliasNeural': {
    DisplayName: 'Elias',
    DisplayVoiceName: 'EliasNeural',
    LocalName: 'Elias',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-EliasNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'zh-CN-XiaoxuanNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoxuanNeural',
    DisplayName: 'Xiaoxuan',
    LocalName: '晓萱',
    ShortName: 'zh-CN-XiaoxuanNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'el-GR-AthinaNeural': {
    DisplayName: 'Athina',
    DisplayVoiceName: 'AthinaNeural',
    LocalName: 'Αθηνά',
    PreviewSentence:
      'Δημιουργήστε εφαρμογές και υπηρεσίες που μιλούν με φυσικό τρόπο στους χρήστες, βελτιώνοντας την προσβασιμότητα και τη χρηστικότητα.',
    ShortName: 'el-GR-AthinaNeural',
    locale: 'el-GR',
    localeZH: '希腊语(希腊)',
  },
  'zh-CN-XiaochenNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaochenNeural',
    DisplayName: 'Xiaochen',
    LocalName: '晓辰',
    ShortName: 'zh-CN-XiaochenNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'es-GQ-JavierNeural': {
    DisplayName: 'Javier',
    DisplayVoiceName: 'JavierNeural',
    LocalName: 'Javier',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-GQ-JavierNeural',
    locale: 'es-GQ',
    localeZH: '西班牙语(赤道几内亚)',
  },
  'fr-BE-GerardNeural': {
    DisplayName: 'Gerard',
    DisplayVoiceName: 'GerardNeural',
    LocalName: 'Gerard',
    PreviewSentence:
      'La Création de Contenu Audio vous permet de contrôler visuellement les attributs vocaux en temps réel.',
    ShortName: 'fr-BE-GerardNeural',
    locale: 'fr-BE',
    localeZH: '法语(比利时)',
  },
  'zh-CN-XiaoxiaoNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoxiaoNeural',
    DisplayName: 'Xiaoxiao',
    LocalName: '晓晓',
    ShortName: 'zh-CN-XiaoxiaoNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'de-DE-TanjaNeural': {
    DisplayName: 'Tanja',
    DisplayVoiceName: 'TanjaNeural',
    LocalName: 'Tanja',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-TanjaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'en-US-JennyNeural': {
    DisplayName: 'Jenny',
    DisplayVoiceName: 'JennyNeural',
    LocalName: 'Jenny',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-JennyNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'si-LK-SameeraNeural': {
    locale: 'si-LK',
    DisplayVoiceName: 'SameeraNeural',
    localeZH: '僧伽罗语(斯里兰卡)',
    DisplayName: 'Sameera',
    LocalName: 'සමීර',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'si-LK-SameeraNeural',
  },
  'de-DE-KlarissaNeural': {
    DisplayName: 'Klarissa',
    DisplayVoiceName: 'KlarissaNeural',
    LocalName: 'Klarissa',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-KlarissaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'zh-CN-XiaohanNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaohanNeural',
    DisplayName: 'Xiaohan',
    LocalName: '晓涵',
    ShortName: 'zh-CN-XiaohanNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'ar-DZ-IsmaelNeural': {
    DisplayName: 'Ismael',
    DisplayVoiceName: 'IsmaelNeural',
    LocalName: 'إسماعيل',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-DZ-IsmaelNeural',
    locale: 'ar-DZ',
    localeZH: '阿拉伯语(阿尔及利亚)',
  },
  'mn-MN-YesuiNeural': {
    locale: 'mn-MN',
    DisplayVoiceName: 'YesuiNeural',
    localeZH: '蒙古语(蒙古)',
    DisplayName: 'Yesui',
    LocalName: 'Есүй',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'mn-MN-YesuiNeural',
  },
  'de-CH-LeniNeural': {
    DisplayName: 'Leni',
    DisplayVoiceName: 'LeniNeural',
    LocalName: 'Leni',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-CH-LeniNeural',
    locale: 'de-CH',
    localeZH: '德语(瑞士)',
  },
  'sw-TZ-RehemaNeural': {
    locale: 'sw-TZ',
    localeZH: '斯瓦希里语(坦桑尼亚)',
    DisplayVoiceName: 'RehemaNeural',
    DisplayName: 'Rehema',
    LocalName: 'Rehema',
    ShortName: 'sw-TZ-RehemaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'de-DE-BerndNeural': {
    DisplayName: 'Bernd',
    DisplayVoiceName: 'BerndNeural',
    LocalName: 'Bernd',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-BerndNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'en-AU-CarlyNeural': {
    DisplayName: 'Carly',
    DisplayVoiceName: 'CarlyNeural',
    LocalName: 'Carly',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-CarlyNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'pt-BR-NicolauNeural': {
    locale: 'pt-BR',
    DisplayVoiceName: 'NicolauNeural',
    localeZH: '葡萄牙语(巴西)',
    DisplayName: 'Nicolau',
    LocalName: 'Nicolau',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-NicolauNeural',
  },
  'ar-OM-AyshaNeural': {
    DisplayName: 'Aysha',
    DisplayVoiceName: 'AyshaNeural',
    LocalName: 'عائشة',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-OM-AyshaNeural',
    locale: 'ar-OM',
    localeZH: '阿拉伯语(阿曼)',
  },
  'ru-RU-SvetlanaNeural': {
    DisplayVoiceName: 'SvetlanaNeural',
    locale: 'ru-RU',
    DisplayName: 'Svetlana',
    localeZH: '俄语(俄罗斯)',
    LocalName: 'Светлана',
    PreviewSentence:
      'Возможность создавать приложения и сервисы, которые естественным образом общаются с пользователями, улучшая доступность и удобство использования.',
    ShortName: 'ru-RU-SvetlanaNeural',
  },
  'de-DE-RalfNeural': {
    DisplayName: 'Ralf',
    DisplayVoiceName: 'RalfNeural',
    LocalName: 'Ralf',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-RalfNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'su-ID-JajangNeural': {
    locale: 'su-ID',
    DisplayVoiceName: 'JajangNeural',
    localeZH: '巽他语(印度尼西亚)',
    DisplayName: 'Jajang',
    LocalName: 'Jajang',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'su-ID-JajangNeural',
  },
  'es-UY-ValentinaNeural': {
    DisplayName: 'Valentina',
    DisplayVoiceName: 'ValentinaNeural',
    LocalName: 'Valentina',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-UY-ValentinaNeural',
    locale: 'es-UY',
    localeZH: '西班牙语(乌拉圭)',
  },
  'zh-CN-XiaozhenNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaozhenNeural',
    DisplayName: 'Xiaozhen',
    LocalName: '晓甄',
    ShortName: 'zh-CN-XiaozhenNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'fi-FI-NooraNeural': {
    DisplayName: 'Noora',
    DisplayVoiceName: 'NooraNeural',
    LocalName: 'Noora',
    PreviewSentence:
      'Kehitä luonnolisesti puhuvia sovelluksia ja palveluja, jotka parantavat käytettävyyttä ja saavutettavuutta.',
    ShortName: 'fi-FI-NooraNeural',
    locale: 'fi-FI',
    localeZH: '芬兰语(芬兰)',
  },
  'ur-PK-UzmaNeural': {
    locale: 'ur-PK',
    localeZH: '乌尔都语(巴基斯坦)',
    DisplayVoiceName: 'UzmaNeural',
    DisplayName: 'Uzma',
    LocalName: 'عظمیٰ',
    ShortName: 'ur-PK-UzmaNeural',
    PreviewSentence:
      'آواز کا مواد تخلیق کرنا  اس قابل بناتا  ہیکہ آپ تقریر کی خصوصیات کو  حقیقی وقت میں  اپنے مطابق کنٹرول کر سکتے ہیں۔',
  },
  'en-US-TonyNeural': {
    DisplayName: 'Tony',
    DisplayVoiceName: 'TonyNeural',
    LocalName: 'Tony',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-TonyNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ar-YE-SalehNeural': {
    DisplayName: 'Saleh',
    DisplayVoiceName: 'SalehNeural',
    LocalName: 'صالح',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    locale: 'ar-YE',
    ShortName: 'ar-YE-SalehNeural',
    localeZH: '阿拉伯语(也门)',
  },
  'pt-BR-YaraNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'YaraNeural',
    DisplayName: 'Yara',
    LocalName: 'Yara',
    ShortName: 'pt-BR-YaraNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'es-MX-LibertoNeural': {
    DisplayName: 'Liberto',
    DisplayVoiceName: 'LibertoNeural',
    LocalName: 'Liberto',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-LibertoNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'fr-CA-AntoineNeural': {
    DisplayName: 'Antoine',
    DisplayVoiceName: 'AntoineNeural',
    LocalName: 'Antoine',
    locale: 'fr-CA',
    PreviewSentence:
      'Créer des applications et des services qui parlent aux utilisateurs, améliorant ainsi l’accessibilité et la facilité d’utilisation.',
    localeZH: '法语(加拿大)',
    ShortName: 'fr-CA-AntoineNeural',
  },
  'cs-CZ-AntoninNeural': {
    DisplayName: 'Antonin',
    DisplayVoiceName: 'AntoninNeural',
    LocalName: 'Antonín',
    PreviewSentence:
      'Vytvořte aplikace a služby pro přirozenou komunikaci s uživateli a usnadněte tak přístup a využití.',
    ShortName: 'cs-CZ-AntoninNeural',
    locale: 'cs-CZ',
    localeZH: '捷克语(捷克)',
  },
  'fr-FR-BrigitteNeural': {
    DisplayName: 'Brigitte',
    DisplayVoiceName: 'BrigitteNeural',
    LocalName: 'Brigitte',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-BrigitteNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'ar-IQ-BasselNeural': {
    DisplayName: 'Bassel',
    DisplayVoiceName: 'BasselNeural',
    LocalName: 'باسل',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-IQ-BasselNeural',
    locale: 'ar-IQ',
    localeZH: '阿拉伯语(伊拉克)',
  },
  'it-IT-IrmaNeural': {
    locale: 'it-IT',
    DisplayVoiceName: 'IrmaNeural',
    localeZH: '意大利语(意大利)',
    DisplayName: 'Irma',
    LocalName: 'Irma',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-IrmaNeural',
  },
  'es-MX-NuriaNeural': {
    DisplayName: 'Nuria',
    DisplayVoiceName: 'NuriaNeural',
    LocalName: 'Nuria',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-NuriaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'nb-NO-PernilleNeural': {
    locale: 'nb-NO',
    localeZH: '书面挪威语(挪威)',
    DisplayVoiceName: 'PernilleNeural',
    DisplayName: 'Pernille',
    LocalName: 'Pernille',
    ShortName: 'nb-NO-PernilleNeural',
    PreviewSentence:
      'Bygger apper og tjenester som snakker naturlig med brukerne, utbedrer tilgjengelighet og brukskvalitet.',
  },
  'eu-ES-AinhoaNeural': {
    DisplayName: 'Ainhoa',
    DisplayVoiceName: 'AinhoaNeural',
    LocalName: 'Ainhoa',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'eu-ES-AinhoaNeural',
    locale: 'eu-ES',
    localeZH: '巴斯克语(巴斯克语)',
  },
  'pl-PL-AgnieszkaNeural': {
    locale: 'pl-PL',
    localeZH: '波兰语(波兰)',
    DisplayVoiceName: 'AgnieszkaNeural',
    DisplayName: 'Agnieszka',
    LocalName: 'Agnieszka',
    ShortName: 'pl-PL-AgnieszkaNeural',
    PreviewSentence:
      'Twórz aplikacje i serwisy, które w kontakcie z użytkownikiem posługują się naturalną mową, co podnosi ich dostępność i użyteczność.',
  },
  'de-DE-KlausNeural': {
    DisplayName: 'Klaus',
    DisplayVoiceName: 'KlausNeural',
    LocalName: 'Klaus',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-KlausNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'fr-FR-AlainNeural': {
    DisplayName: 'Alain',
    DisplayVoiceName: 'AlainNeural',
    LocalName: 'Alain',
    locale: 'fr-FR',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    localeZH: '法语(法国)',
    ShortName: 'fr-FR-AlainNeural',
  },
  'gl-ES-RoiNeural': {
    locale: 'gl-ES',
    DisplayVoiceName: 'RoiNeural',
    localeZH: '加利西亚语(加利西亚语)',
    DisplayName: 'Roi',
    LocalName: 'Roi',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'gl-ES-RoiNeural',
  },
  'fr-FR-CelesteNeural': {
    DisplayVoiceName: 'CelesteNeural',
    locale: 'fr-FR',
    DisplayName: 'Celeste',
    localeZH: '法语(法国)',
    LocalName: 'Celeste',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-CelesteNeural',
  },
  'zh-CN-YunjianNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunjianNeural',
    DisplayName: 'Yunjian',
    LocalName: '云健',
    ShortName: 'zh-CN-YunjianNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'ar-MA-MounaNeural': {
    DisplayName: 'Mouna',
    DisplayVoiceName: 'MounaNeural',
    LocalName: 'منى',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-MA-MounaNeural',
    locale: 'ar-MA',
    localeZH: '阿拉伯语(摩洛哥)',
  },
  'nl-NL-ColetteNeural': {
    DisplayVoiceName: 'ColetteNeural',
    locale: 'nl-NL',
    DisplayName: 'Colette',
    localeZH: '荷兰语(荷兰)',
    LocalName: 'Colette',
    PreviewSentence:
      'Ontwikkel apps en diensten die natuurlijk aanvoelen, waardoor de toegankelijkheid en bruikbaarheid vergroot worden.',
    ShortName: 'nl-NL-ColetteNeural',
  },
  'da-DK-JeppeNeural': {
    DisplayName: 'Jeppe',
    DisplayVoiceName: 'JeppeNeural',
    LocalName: 'Jeppe',
    PreviewSentence:
      'Lav apps og tjenester, der taler naturligt til brugere, forbedrer tilgængelighed og brugervenlighed.',
    ShortName: 'da-DK-JeppeNeural',
    locale: 'da-DK',
    localeZH: '丹麦语(丹麦)',
  },
  'pt-BR-GiovannaNeural': {
    locale: 'pt-BR',
    DisplayVoiceName: 'GiovannaNeural',
    localeZH: '葡萄牙语(巴西)',
    DisplayName: 'Giovanna',
    LocalName: 'Giovanna',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-GiovannaNeural',
  },
  'de-AT-JonasNeural': {
    DisplayName: 'Jonas',
    DisplayVoiceName: 'JonasNeural',
    LocalName: 'Jonas',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-AT-JonasNeural',
    locale: 'de-AT',
    localeZH: '德语(奥地利)',
  },
  'zh-CN-XiaoyanNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoyanNeural',
    DisplayName: 'Xiaoyan',
    LocalName: '晓颜',
    ShortName: 'zh-CN-XiaoyanNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'en-GB-HollieNeural': {
    DisplayName: 'Hollie',
    DisplayVoiceName: 'HollieNeural',
    LocalName: 'Hollie',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-HollieNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'fr-FR-ClaudeNeural': {
    DisplayName: 'Claude',
    DisplayVoiceName: 'ClaudeNeural',
    LocalName: 'Claude',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-ClaudeNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'fr-FR-HenriNeural': {
    DisplayName: 'Henri',
    DisplayVoiceName: 'HenriNeural',
    LocalName: 'Henri',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-HenriNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'pt-BR-AntonioNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'AntonioNeural',
    DisplayName: 'Antonio',
    LocalName: 'Antônio',
    ShortName: 'pt-BR-AntonioNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'fr-CA-SylvieNeural': {
    DisplayName: 'Sylvie',
    DisplayVoiceName: 'SylvieNeural',
    LocalName: 'Sylvie',
    PreviewSentence:
      'Créer des applications et des services qui parlent aux utilisateurs, améliorant ainsi l’accessibilité et la facilité d’utilisation.',
    ShortName: 'fr-CA-SylvieNeural',
    locale: 'fr-CA',
    localeZH: '法语(加拿大)',
  },
  'ta-SG-AnbuNeural': {
    locale: 'ta-SG',
    localeZH: '泰米尔语(新加坡)',
    DisplayVoiceName: 'AnbuNeural',
    DisplayName: 'Anbu',
    LocalName: 'அன்பு',
    ShortName: 'ta-SG-AnbuNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'fr-FR-JosephineNeural': {
    DisplayName: 'Josephine',
    DisplayVoiceName: 'JosephineNeural',
    LocalName: 'Josephine',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-JosephineNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'ko-KR-InJoonNeural': {
    locale: 'ko-KR',
    localeZH: '韩语(韩国)',
    DisplayVoiceName: 'InJoonNeural',
    DisplayName: 'InJoon',
    LocalName: '인준',
    ShortName: 'ko-KR-InJoonNeural',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
  },
  'fr-FR-MauriceNeural': {
    DisplayName: 'Maurice',
    DisplayVoiceName: 'MauriceNeural',
    LocalName: 'Maurice',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-MauriceNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'ka-GE-GiorgiNeural': {
    locale: 'ka-GE',
    localeZH: '格鲁吉亚语(格鲁吉亚)',
    DisplayVoiceName: 'GiorgiNeural',
    DisplayName: 'Giorgi',
    LocalName: 'გიორგი',
    ShortName: 'ka-GE-GiorgiNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'ga-IE-OrlaNeural': {
    DisplayName: 'Orla',
    DisplayVoiceName: 'OrlaNeural',
    LocalName: 'Orla',
    PreviewSentence:
      'Cuireann Cruthú Ábhar Fuaime ar do chumas tréithe cainte a rialú i bhfíor-am.',
    ShortName: 'ga-IE-OrlaNeural',
    locale: 'ga-IE',
    localeZH: '爱尔兰语(爱尔兰)',
  },
  'zh-CN-XiaoyiNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoyiNeural',
    DisplayName: 'Xiaoyi',
    LocalName: '晓伊',
    ShortName: 'zh-CN-XiaoyiNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'de-DE-ChristophNeural': {
    DisplayName: 'Christoph',
    DisplayVoiceName: 'ChristophNeural',
    LocalName: 'Christoph',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-ChristophNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'sv-SE-MattiasNeural': {
    locale: 'sv-SE',
    localeZH: '瑞典语(瑞典)',
    DisplayVoiceName: 'MattiasNeural',
    DisplayName: 'Mattias',
    LocalName: 'Mattias',
    ShortName: 'sv-SE-MattiasNeural',
    PreviewSentence:
      'Bygg appar och tjänster som talar naturligt till användarna, och förbättrar tillgänglighet och användbarhet.',
  },
  'en-GB-OliverNeural': {
    DisplayName: 'Oliver',
    DisplayVoiceName: 'OliverNeural',
    LocalName: 'Oliver',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-OliverNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'mk-MK-AleksandarNeural': {
    locale: 'mk-MK',
    localeZH: '马其顿语(北马其顿)',
    DisplayVoiceName: 'AleksandarNeural',
    DisplayName: 'Aleksandar',
    LocalName: 'Александар',
    ShortName: 'mk-MK-AleksandarNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'bn-BD-PradeepNeural': {
    DisplayName: 'Pradeep',
    DisplayVoiceName: 'PradeepNeural',
    LocalName: 'প্রদ্বীপ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bn-BD-PradeepNeural',
    locale: 'bn-BD',
    localeZH: '孟加拉语(孟加拉)',
  },
  'ja-JP-AoiNeural': {
    locale: 'ja-JP',
    DisplayVoiceName: 'AoiNeural',
    localeZH: '日语(日本)',
    DisplayName: 'Aoi',
    LocalName: '碧衣',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-AoiNeural',
  },
  'ga-IE-ColmNeural': {
    DisplayName: 'Colm',
    DisplayVoiceName: 'ColmNeural',
    LocalName: 'Colm',
    PreviewSentence:
      'Cuireann Cruthú Ábhar Fuaime ar do chumas tréithe cainte a rialú i bhfíor-am.',
    ShortName: 'ga-IE-ColmNeural',
    locale: 'ga-IE',
    localeZH: '爱尔兰语(爱尔兰)',
  },
  'zh-CN-YunhaoNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunhaoNeural',
    DisplayName: 'Yunhao',
    LocalName: '云皓',
    ShortName: 'zh-CN-YunhaoNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'fr-FR-YvetteNeural': {
    DisplayName: 'Yvette',
    DisplayVoiceName: 'YvetteNeural',
    LocalName: 'Yvette',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-YvetteNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'nl-NL-MaartenNeural': {
    locale: 'nl-NL',
    localeZH: '荷兰语(荷兰)',
    DisplayVoiceName: 'MaartenNeural',
    DisplayName: 'Maarten',
    LocalName: 'Maarten',
    ShortName: 'nl-NL-MaartenNeural',
    PreviewSentence:
      'Ontwikkel apps en diensten die natuurlijk aanvoelen, waardoor de toegankelijkheid en bruikbaarheid vergroot worden.',
  },
  'bn-IN-BashkarNeural': {
    DisplayName: 'Bashkar',
    DisplayVoiceName: 'BashkarNeural',
    LocalName: 'ভাস্কর',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bn-IN-BashkarNeural',
    locale: 'bn-IN',
    localeZH: '孟加拉语(印度)',
  },
  'he-IL-HilaNeural': {
    DisplayName: 'Hila',
    DisplayVoiceName: 'HilaNeural',
    LocalName: 'הילה',
    PreviewSentence:
      'בנה יישומים ושירותים שמדברים בטבעיות למשתמשים, שמשפרים את  הנגישות והשימושיות.',
    ShortName: 'he-IL-HilaNeural',
    locale: 'he-IL',
    localeZH: '希伯来语(以色列)',
  },
  'en-AU-DarrenNeural': {
    DisplayName: 'Darren',
    DisplayVoiceName: 'DarrenNeural',
    LocalName: 'Darren',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-DarrenNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'so-SO-UbaxNeural': {
    locale: 'so-SO',
    localeZH: '索马里语(索马里)',
    DisplayVoiceName: 'UbaxNeural',
    DisplayName: 'Ubax',
    LocalName: 'Ubax',
    ShortName: 'so-SO-UbaxNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-AU-FreyaNeural': {
    DisplayName: 'Freya',
    DisplayVoiceName: 'FreyaNeural',
    LocalName: 'Freya',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-FreyaNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'en-AU-DuncanNeural': {
    DisplayName: 'Duncan',
    DisplayVoiceName: 'DuncanNeural',
    LocalName: 'Duncan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-DuncanNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'hr-HR-GabrijelaNeural': {
    DisplayName: 'Gabrijela',
    DisplayVoiceName: 'GabrijelaNeural',
    LocalName: 'Gabrijela',
    locale: 'hr-HR',
    PreviewSentence:
      'Pravi aplikacije i usluge koje se obraćaju korisnicima na što prirodniji način i poboljšava njihovu pristupačnost i korišćenje.',
    localeZH: '克罗地亚语(克罗地亚)',
    ShortName: 'hr-HR-GabrijelaNeural',
  },
  'en-CA-LiamNeural': {
    DisplayName: 'Liam',
    DisplayVoiceName: 'LiamNeural',
    LocalName: 'Liam',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-CA-LiamNeural',
    locale: 'en-CA',
    localeZH: '英语(加拿大)',
  },
  'kn-IN-SapnaNeural': {
    locale: 'kn-IN',
    DisplayVoiceName: 'SapnaNeural',
    localeZH: '埃纳德语(印度)',
    DisplayName: 'Sapna',
    LocalName: 'ಸಪ್ನಾ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'kn-IN-SapnaNeural',
  },
  'es-NI-YolandaNeural': {
    DisplayName: 'Yolanda',
    DisplayVoiceName: 'YolandaNeural',
    LocalName: 'Yolanda',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-NI-YolandaNeural',
    locale: 'es-NI',
    localeZH: '西班牙语(尼加拉瓜)',
  },
  'sr-RS-SophieNeural': {
    locale: 'sr-RS',
    localeZH: '塞尔维亚语(塞尔维亚)',
    DisplayVoiceName: 'SophieNeural',
    DisplayName: 'Sophie',
    LocalName: 'Софија',
    ShortName: 'sr-RS-SophieNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'bs-BA-VesnaNeural': {
    DisplayName: 'Vesna',
    DisplayVoiceName: 'VesnaNeural',
    LocalName: 'Vesna',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bs-BA-VesnaNeural',
    locale: 'bs-BA',
    localeZH: '波斯尼亚语(波斯尼亚和黑塞哥维那)',
  },
  'hr-HR-SreckoNeural': {
    DisplayName: 'Srecko',
    DisplayVoiceName: 'SreckoNeural',
    LocalName: 'Srećko',
    PreviewSentence:
      'Pravi aplikacije i usluge koje se obraćaju korisnicima na što prirodniji način i poboljšava njihovu pristupačnost i korišćenje.',
    ShortName: 'hr-HR-SreckoNeural',
    locale: 'hr-HR',
    localeZH: '克罗地亚语(克罗地亚)',
  },
  'hu-HU-NoemiNeural': {
    DisplayName: 'Noemi',
    DisplayVoiceName: 'NoemiNeural',
    LocalName: 'Noémi',
    PreviewSentence:
      'Készítsen appokat és szolgáltatásokat, melyek természetes hangon beszélnek a felhasználóval, ezáltal hozzáférhetőbbek és könnyebben használhatóak.',
    ShortName: 'hu-HU-NoemiNeural',
    locale: 'hu-HU',
    localeZH: '匈牙利语(匈牙利)',
  },
  'fi-FI-HarriNeural': {
    DisplayName: 'Harri',
    DisplayVoiceName: 'HarriNeural',
    LocalName: 'Harri',
    PreviewSentence:
      'Kehitä luonnolisesti puhuvia sovelluksia ja palveluja, jotka parantavat käytettävyyttä ja saavutettavuutta.',
    ShortName: 'fi-FI-HarriNeural',
    locale: 'fi-FI',
    localeZH: '芬兰语(芬兰)',
  },
  'zh-HK-HiuGaaiNeural': {
    locale: 'zh-HK',
    localeZH: '中文(粤语，繁体)',
    DisplayVoiceName: 'HiuGaaiNeural',
    DisplayName: 'HiuGaai',
    LocalName: '曉佳',
    ShortName: 'zh-HK-HiuGaaiNeural',
    PreviewSentence: '開發可自然地與用戶溝通的應用程式及服務，以提升其使用度及可用性。',
  },
  'hy-AM-AnahitNeural': {
    DisplayName: 'Anahit',
    DisplayVoiceName: 'AnahitNeural',
    LocalName: 'Անահիտ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'hy-AM-AnahitNeural',
    locale: 'hy-AM',
    localeZH: '亚美尼亚语(亚美尼亚)',
  },
  'ru-RU-DariyaNeural': {
    locale: 'ru-RU',
    localeZH: '俄语(俄罗斯)',
    DisplayVoiceName: 'DariyaNeural',
    DisplayName: 'Dariya',
    LocalName: 'Дария',
    ShortName: 'ru-RU-DariyaNeural',
    PreviewSentence:
      'Возможность создавать приложения и сервисы, которые естественным образом общаются с пользователями, улучшая доступность и удобство использования.',
  },
  'gu-IN-NiranjanNeural': {
    DisplayName: 'Niranjan',
    DisplayVoiceName: 'NiranjanNeural',
    LocalName: 'નિરંજન',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'gu-IN-NiranjanNeural',
    locale: 'gu-IN',
    localeZH: '古吉拉特语(印度)',
  },
  'it-IT-IsabellaNeural': {
    locale: 'it-IT',
    DisplayVoiceName: 'IsabellaNeural',
    localeZH: '意大利语(意大利)',
    DisplayName: 'Isabella',
    LocalName: 'Isabella',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-IsabellaNeural',
  },
  'es-DO-EmilioNeural': {
    DisplayName: 'Emilio',
    DisplayVoiceName: 'EmilioNeural',
    LocalName: 'Emilio',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-DO-EmilioNeural',
    locale: 'es-DO',
    localeZH: '西班牙语(多米尼加共和国)',
  },
  'my-MM-NilarNeural': {
    DisplayVoiceName: 'NilarNeural',
    DisplayName: 'Nilar',
    locale: 'my-MM',
    LocalName: 'နီလာ',
    localeZH: '缅甸语(缅甸)',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'my-MM-NilarNeural',
  },
  'en-GB-SoniaNeural': {
    DisplayName: 'Sonia',
    DisplayVoiceName: 'SoniaNeural',
    LocalName: 'Sonia',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-SoniaNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'pl-PL-ZofiaNeural': {
    DisplayVoiceName: 'ZofiaNeural',
    DisplayName: 'Zofia',
    locale: 'pl-PL',
    LocalName: 'Zofia',
    localeZH: '波兰语(波兰)',
    PreviewSentence:
      'Twórz aplikacje i serwisy, które w kontakcie z użytkownikiem posługują się naturalną mową, co podnosi ich dostępność i użyteczność.',
    ShortName: 'pl-PL-ZofiaNeural',
  },
  'ar-KW-FahedNeural': {
    DisplayName: 'Fahed',
    DisplayVoiceName: 'FahedNeural',
    LocalName: 'فهد',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-KW-FahedNeural',
    locale: 'ar-KW',
    localeZH: '阿拉伯语(科威特)',
  },
  'ta-IN-ValluvarNeural': {
    locale: 'ta-IN',
    DisplayVoiceName: 'ValluvarNeural',
    localeZH: '泰米尔语(印度)',
    DisplayName: 'Valluvar',
    LocalName: 'வள்ளுவர்',
    PreviewSentence:
      'பயனர்களிடம் இயற்கையாக பேசும் பயன்பாடுகள் மற்றும் சேவைகளை உருவாகுதல், இது அணுகல்தன்மை மற்றும் பயன்பாட்டினை மேம்படுத்தும்.',
    ShortName: 'ta-IN-ValluvarNeural',
  },
  'ar-LY-OmarNeural': {
    DisplayName: 'Omar',
    DisplayVoiceName: 'OmarNeural',
    LocalName: 'أحمد',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-LY-OmarNeural',
    locale: 'ar-LY',
    localeZH: '阿拉伯语(利比亚)',
  },
  'te-IN-MohanNeural': {
    locale: 'te-IN',
    localeZH: '泰卢固语(印度)',
    DisplayVoiceName: 'MohanNeural',
    DisplayName: 'Mohan',
    LocalName: 'మోహన్',
    ShortName: 'te-IN-MohanNeural',
    PreviewSentence:
      'వినియోగం మరియు సౌలభ్యాన్ని మెరుగుపరిచే, సహజ శైలిలో మాట్లాడే అప్లికేషన్లు మరియు సేవలను అభివృద్ధి చేయండి.',
  },
  'en-US-AshleyNeural': {
    DisplayName: 'Ashley',
    DisplayVoiceName: 'AshleyNeural',
    LocalName: 'Ashley',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AshleyNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'sk-SK-LukasNeural': {
    locale: 'sk-SK',
    DisplayVoiceName: 'LukasNeural',
    localeZH: '斯洛伐克语(斯洛伐克)',
    DisplayName: 'Lukas',
    LocalName: 'Lukáš',
    PreviewSentence:
      'Vytvárajú aplikácie a služby, ktoré prirodzene komunikujú s užívateľmi, čím sa zlepšuje dostupnosť a využiteľnosť.',
    ShortName: 'sk-SK-LukasNeural',
  },
  'ar-AE-FatimaNeural': {
    DisplayName: 'Fatima',
    DisplayVoiceName: 'FatimaNeural',
    LocalName: 'فاطمة',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-AE-FatimaNeural',
    locale: 'ar-AE',
    localeZH: '阿拉伯语(阿拉伯联合酋长国)',
  },
  'vi-VN-NamMinhNeural': {
    locale: 'vi-VN',
    DisplayVoiceName: 'NamMinhNeural',
    localeZH: '越南语(越南)',
    DisplayName: 'NamMinh',
    LocalName: 'Nam Minh',
    PreviewSentence:
      'Phát triển phần mềm và dịch vụ có thể giao tiếp tự nhiên với người dùng, nâng cao tính tiếp cận và tính khả dụng của sản phẩm',
    ShortName: 'vi-VN-NamMinhNeural',
  },
  'es-ES-SaulNeural': {
    DisplayName: 'Saul',
    DisplayVoiceName: 'SaulNeural',
    LocalName: 'Saul',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-SaulNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'zh-CN-shaanxi-XiaoniNeural': {
    locale: 'zh-CN-shaanxi',
    localeZH: '中文(中原官话陕西，简体)',
    DisplayVoiceName: 'XiaoniNeural',
    DisplayName: 'Xiaoni',
    LocalName: '晓妮',
    ShortName: 'zh-CN-shaanxi-XiaoniNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'de-AT-IngridNeural': {
    DisplayName: 'Ingrid',
    DisplayVoiceName: 'IngridNeural',
    LocalName: 'Ingrid',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-AT-IngridNeural',
    locale: 'de-AT',
    localeZH: '德语(奥地利)',
  },
  'af-ZA-AdriNeural': {
    DisplayName: 'Adri',
    DisplayVoiceName: 'AdriNeural',
    LocalName: 'Adri',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'af-ZA-AdriNeural',
    locale: 'af-ZA',
    localeZH: '南非荷兰语(南非)',
  },
  'is-IS-GudrunNeural': {
    DisplayVoiceName: 'GudrunNeural',
    DisplayName: 'Gudrun',
    locale: 'is-IS',
    LocalName: 'Guðrún',
    localeZH: '冰岛语(冰岛)',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'is-IS-GudrunNeural',
  },
  'ar-LY-ImanNeural': {
    DisplayName: 'Iman',
    DisplayVoiceName: 'ImanNeural',
    LocalName: 'إيمان',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-LY-ImanNeural',
    locale: 'ar-LY',
    localeZH: '阿拉伯语(利比亚)',
  },
  'ml-IN-SobhanaNeural': {
    locale: 'ml-IN',
    localeZH: '马拉雅拉姆语(印度)',
    DisplayVoiceName: 'SobhanaNeural',
    DisplayName: 'Sobhana',
    LocalName: 'ശോഭന',
    ShortName: 'ml-IN-SobhanaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'de-DE-ElkeNeural': {
    DisplayName: 'Elke',
    DisplayVoiceName: 'ElkeNeural',
    LocalName: 'Elke',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-ElkeNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'pt-PT-RaquelNeural': {
    locale: 'pt-PT',
    localeZH: '葡萄牙语(葡萄牙)',
    DisplayVoiceName: 'RaquelNeural',
    DisplayName: 'Raquel',
    LocalName: 'Raquel',
    ShortName: 'pt-PT-RaquelNeural',
    PreviewSentence:
      'Constrói aplicações e serviços que falam naturalmente com os utilizadores, melhorando a acessibilidade e usabilidade.',
  },
  'en-PH-RosaNeural': {
    DisplayName: 'Rosa',
    DisplayVoiceName: 'RosaNeural',
    LocalName: 'Rosa',
    PreviewSentence:
      'Audio Content Creation enables you to visually control speech attributes in real-time.',
    ShortName: 'en-PH-RosaNeural',
    locale: 'en-PH',
    localeZH: '英语(菲律宾)',
  },
  'kn-IN-GaganNeural': {
    locale: 'kn-IN',
    DisplayVoiceName: 'GaganNeural',
    localeZH: '埃纳德语(印度)',
    DisplayName: 'Gagan',
    LocalName: 'ಗಗನ್',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'kn-IN-GaganNeural',
  },
  'en-IE-ConnorNeural': {
    DisplayName: 'Connor',
    DisplayVoiceName: 'ConnorNeural',
    LocalName: 'Connor',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-IE-ConnorNeural',
    locale: 'en-IE',
    localeZH: '英语(爱尔兰)',
  },
  'zh-CN-XiaoshuangNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoshuangNeural',
    DisplayName: 'Xiaoshuang',
    LocalName: '晓双',
    ShortName: 'zh-CN-XiaoshuangNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'en-US-AnaNeural': {
    DisplayName: 'Ana',
    DisplayVoiceName: 'AnaNeural',
    LocalName: 'Ana',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AnaNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'id-ID-GadisNeural': {
    DisplayName: 'Gadis',
    DisplayVoiceName: 'GadisNeural',
    LocalName: 'Gadis',
    PreviewSentence:
      'Buat aplikasi dan layanan yang berbicara secara alami kepada pengguna, sehingga meningkatkan aksesibilitas dan kegunaan.',
    ShortName: 'id-ID-GadisNeural',
    locale: 'id-ID',
    localeZH: '印度尼西亚语(印度尼西亚)',
  },
  'es-CU-BelkysNeural': {
    DisplayName: 'Belkys',
    DisplayVoiceName: 'BelkysNeural',
    LocalName: 'Belkys',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CU-BelkysNeural',
    locale: 'es-CU',
    localeZH: '西班牙语(古巴)',
  },
  'ko-KR-JiMinNeural': {
    locale: 'ko-KR',
    localeZH: '韩语(韩国)',
    DisplayVoiceName: 'JiMinNeural',
    DisplayName: 'JiMin',
    LocalName: '지민',
    ShortName: 'ko-KR-JiMinNeural',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
  },
  'am-ET-MekdesNeural': {
    DisplayName: 'Mekdes',
    DisplayVoiceName: 'MekdesNeural',
    LocalName: 'መቅደስ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'am-ET-MekdesNeural',
    locale: 'am-ET',
    localeZH: '阿姆哈拉语(埃塞俄比亚)',
  },
  'is-IS-GunnarNeural': {
    DisplayName: 'Gunnar',
    DisplayVoiceName: 'GunnarNeural',
    LocalName: 'Gunnar',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'is-IS-GunnarNeural',
    locale: 'is-IS',
    localeZH: '冰岛语(冰岛)',
  },
  'en-GB-BellaNeural': {
    DisplayName: 'Bella',
    DisplayVoiceName: 'BellaNeural',
    LocalName: 'Bella',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-BellaNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'ko-KR-YuJinNeural': {
    DisplayVoiceName: 'YuJinNeural',
    locale: 'ko-KR',
    DisplayName: 'YuJin',
    localeZH: '韩语(韩国)',
    LocalName: '유진',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
    ShortName: 'ko-KR-YuJinNeural',
  },
  'en-US-AIGenerate2Neural': {
    DisplayName: 'AIGenerate2',
    DisplayVoiceName: 'AIGenerate2Neural',
    LocalName: 'AIGenerate2',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AIGenerate2Neural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'nb-NO-FinnNeural': {
    locale: 'nb-NO',
    localeZH: '书面挪威语(挪威)',
    DisplayVoiceName: 'FinnNeural',
    DisplayName: 'Finn',
    LocalName: 'Finn',
    ShortName: 'nb-NO-FinnNeural',
    PreviewSentence:
      'Bygger apper og tjenester som snakker naturlig med brukerne, utbedrer tilgjengelighet og brukskvalitet.',
  },
  'en-US-SteffanNeural': {
    DisplayName: 'Steffan',
    DisplayVoiceName: 'SteffanNeural',
    LocalName: 'Steffan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-SteffanNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'es-GT-MartaNeural': {
    DisplayName: 'Marta',
    DisplayVoiceName: 'MartaNeural',
    LocalName: 'Marta',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-GT-MartaNeural',
    locale: 'es-GT',
    localeZH: '西班牙语(危地马拉)',
  },
  'zh-CN-XiaomengNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaomengNeural',
    DisplayName: 'Xiaomeng',
    LocalName: '晓梦',
    ShortName: 'zh-CN-XiaomengNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'ar-KW-NouraNeural': {
    DisplayName: 'Noura',
    DisplayVoiceName: 'NouraNeural',
    LocalName: 'نورا',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-KW-NouraNeural',
    locale: 'ar-KW',
    localeZH: '阿拉伯语(科威特)',
  },
  'si-LK-ThiliniNeural': {
    locale: 'si-LK',
    DisplayVoiceName: 'ThiliniNeural',
    localeZH: '僧伽罗语(斯里兰卡)',
    DisplayName: 'Thilini',
    LocalName: 'තිළිණි',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'si-LK-ThiliniNeural',
  },
  'en-CA-ClaraNeural': {
    DisplayName: 'Clara',
    DisplayVoiceName: 'ClaraNeural',
    LocalName: 'Clara',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-CA-ClaraNeural',
    locale: 'en-CA',
    localeZH: '英语(加拿大)',
  },
  'zh-CN-YunfengNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunfengNeural',
    DisplayName: 'Yunfeng',
    LocalName: '云枫',
    ShortName: 'zh-CN-YunfengNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'en-GB-ThomasNeural': {
    DisplayName: 'Thomas',
    DisplayVoiceName: 'ThomasNeural',
    LocalName: 'Thomas',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-ThomasNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'ne-NP-SagarNeural': {
    locale: 'ne-NP',
    DisplayVoiceName: 'SagarNeural',
    localeZH: '尼泊尔语(尼泊尔)',
    DisplayName: 'Sagar',
    LocalName: 'सागर',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ne-NP-SagarNeural',
  },
  'ar-BH-LailaNeural': {
    DisplayName: 'Laila',
    DisplayVoiceName: 'LailaNeural',
    LocalName: 'ليلى',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-BH-LailaNeural',
    locale: 'ar-BH',
    localeZH: '阿拉伯语(巴林)',
  },
  'pt-BR-ValerioNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'ValerioNeural',
    DisplayName: 'Valerio',
    LocalName: 'Valerio',
    ShortName: 'pt-BR-ValerioNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'es-AR-TomasNeural': {
    DisplayName: 'Tomas',
    DisplayVoiceName: 'TomasNeural',
    LocalName: 'Tomas',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-AR-TomasNeural',
    locale: 'es-AR',
    localeZH: '西班牙语(阿根廷)',
  },
  'it-IT-CataldoNeural': {
    DisplayName: 'Cataldo',
    DisplayVoiceName: 'CataldoNeural',
    LocalName: 'Cataldo',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-CataldoNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'ar-TN-HediNeural': {
    DisplayName: 'Hedi',
    DisplayVoiceName: 'HediNeural',
    LocalName: 'هادي',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-TN-HediNeural',
    locale: 'ar-TN',
    localeZH: '阿拉伯语(突尼斯)',
  },
  'te-IN-ShrutiNeural': {
    locale: 'te-IN',
    localeZH: '泰卢固语(印度)',
    DisplayVoiceName: 'ShrutiNeural',
    DisplayName: 'Shruti',
    LocalName: 'శ్రుతి',
    ShortName: 'te-IN-ShrutiNeural',
    PreviewSentence:
      'వినియోగం మరియు సౌలభ్యాన్ని మెరుగుపరిచే, సహజ శైలిలో మాట్లాడే అప్లికేషన్లు మరియు సేవలను అభివృద్ధి చేయండి.',
  },
  'da-DK-ChristelNeural': {
    DisplayName: 'Christel',
    DisplayVoiceName: 'ChristelNeural',
    LocalName: 'Christel',
    PreviewSentence:
      'Lav apps og tjenester, der taler naturligt til brugere, forbedrer tilgængelighed og brugervenlighed.',
    ShortName: 'da-DK-ChristelNeural',
    locale: 'da-DK',
    localeZH: '丹麦语(丹麦)',
  },
  'it-IT-FabiolaNeural': {
    DisplayName: 'Fabiola',
    DisplayVoiceName: 'FabiolaNeural',
    LocalName: 'Fabiola',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-FabiolaNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'es-CU-ManuelNeural': {
    DisplayName: 'Manuel',
    DisplayVoiceName: 'ManuelNeural',
    LocalName: 'Manuel',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CU-ManuelNeural',
    locale: 'es-CU',
    localeZH: '西班牙语(古巴)',
  },
  'km-KH-PisethNeural': {
    DisplayVoiceName: 'PisethNeural',
    locale: 'km-KH',
    DisplayName: 'Piseth',
    localeZH: '高棉语(柬埔寨)',
    LocalName: 'ពិសិដ្ឋ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'km-KH-PisethNeural',
  },
  'en-SG-LunaNeural': {
    DisplayName: 'Luna',
    DisplayVoiceName: 'LunaNeural',
    LocalName: 'Luna',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-SG-LunaNeural',
    locale: 'en-SG',
    localeZH: '英语(新加坡)',
  },
  'mt-MT-JosephNeural': {
    locale: 'mt-MT',
    localeZH: '马耳他语(马耳他)',
    DisplayVoiceName: 'JosephNeural',
    DisplayName: 'Joseph',
    LocalName: 'Joseph',
    ShortName: 'mt-MT-JosephNeural',
    PreviewSentence:
      'L-Għodda għall-Ħolqien tal-Kontenut bil-Ħoss tħallik direttament tikkontrolla l-attributi tal-leħen minn fuq l-iskrin tiegħek.',
  },
  'es-MX-BeatrizNeural': {
    DisplayName: 'Beatriz',
    DisplayVoiceName: 'BeatrizNeural',
    LocalName: 'Beatriz',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-BeatrizNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'en-US-RogerNeural': {
    DisplayName: 'Roger',
    DisplayVoiceName: 'RogerNeural',
    LocalName: 'Roger',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-RogerNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'th-TH-NiwatNeural': {
    locale: 'th-TH',
    localeZH: '泰语(泰国)',
    DisplayVoiceName: 'NiwatNeural',
    DisplayName: 'Niwat',
    LocalName: 'นิวัฒน์',
    ShortName: 'th-TH-NiwatNeural',
    PreviewSentence:
      'สร้างแอปและบริการที่สื่อสารกับผู้ใช้ได้อย่างเป็นธรรมชาติ ซึ่งช่วยปรับปรุงการเข้าถึงและการใช้งาน',
  },
  'en-ZA-LeahNeural': {
    DisplayName: 'Leah',
    DisplayVoiceName: 'LeahNeural',
    LocalName: 'Leah',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-ZA-LeahNeural',
    locale: 'en-ZA',
    localeZH: '英语(南非)',
  },
  'es-ES-ArnauNeural': {
    DisplayName: 'Arnau',
    DisplayVoiceName: 'ArnauNeural',
    LocalName: 'Arnau',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-ArnauNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'mr-IN-ManoharNeural': {
    locale: 'mr-IN',
    localeZH: '马拉地语(印度)',
    DisplayVoiceName: 'ManoharNeural',
    DisplayName: 'Manohar',
    LocalName: 'मनोहर',
    ShortName: 'mr-IN-ManoharNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-MX-CarlotaNeural': {
    DisplayName: 'Carlota',
    DisplayVoiceName: 'CarlotaNeural',
    LocalName: 'Carlota',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-CarlotaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'ro-RO-EmilNeural': {
    locale: 'ro-RO',
    localeZH: '罗马尼亚语(罗马尼亚)',
    DisplayVoiceName: 'EmilNeural',
    DisplayName: 'Emil',
    LocalName: 'Emil',
    ShortName: 'ro-RO-EmilNeural',
    PreviewSentence:
      'Creați aplicații și servicii familiare utilizatorilor, îmbunătățind accesibilitatea și ușurința utilizării.',
  },
  'es-NI-FedericoNeural': {
    DisplayName: 'Federico',
    DisplayVoiceName: 'FedericoNeural',
    LocalName: 'Federico',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-NI-FedericoNeural',
    locale: 'es-NI',
    localeZH: '西班牙语(尼加拉瓜)',
  },
  'ro-RO-AlinaNeural': {
    locale: 'ro-RO',
    localeZH: '罗马尼亚语(罗马尼亚)',
    DisplayVoiceName: 'AlinaNeural',
    DisplayName: 'Alina',
    LocalName: 'Alina',
    ShortName: 'ro-RO-AlinaNeural',
    PreviewSentence:
      'Creați aplicații și servicii familiare utilizatorilor, îmbunătățind accesibilitatea și ușurința utilizării.',
  },
  'es-MX-GerardoNeural': {
    DisplayName: 'Gerardo',
    DisplayVoiceName: 'GerardoNeural',
    LocalName: 'Gerardo',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-GerardoNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'zh-CN-YunzeNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunzeNeural',
    DisplayName: 'Yunze',
    LocalName: '云泽',
    ShortName: 'zh-CN-YunzeNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'et-EE-KertNeural': {
    DisplayName: 'Kert',
    DisplayVoiceName: 'KertNeural',
    LocalName: 'Kert',
    PreviewSentence:
      'Audio Content Creation võimaldab kõne atribuute reaalajas visuaalselt kontrollida.',
    ShortName: 'et-EE-KertNeural',
    locale: 'et-EE',
    localeZH: '爱沙尼亚语(爱沙尼亚)',
  },
  'eu-ES-AnderNeural': {
    DisplayName: 'Ander',
    DisplayVoiceName: 'AnderNeural',
    LocalName: 'Ander',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'eu-ES-AnderNeural',
    locale: 'eu-ES',
    localeZH: '巴斯克语(巴斯克语)',
  },
  'it-IT-GianniNeural': {
    locale: 'it-IT',
    DisplayVoiceName: 'GianniNeural',
    localeZH: '意大利语(意大利)',
    DisplayName: 'Gianni',
    LocalName: 'Gianni',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-GianniNeural',
  },
  'es-ES-AlvaroNeural': {
    DisplayName: 'Alvaro',
    DisplayVoiceName: 'AlvaroNeural',
    LocalName: 'Álvaro',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-AlvaroNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'it-IT-LisandroNeural': {
    DisplayVoiceName: 'LisandroNeural',
    locale: 'it-IT',
    DisplayName: 'Lisandro',
    localeZH: '意大利语(意大利)',
    LocalName: 'Lisandro',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-LisandroNeural',
  },
  'en-GB-AbbiNeural': {
    DisplayName: 'Abbi',
    DisplayVoiceName: 'AbbiNeural',
    LocalName: 'Abbi',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-AbbiNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'zh-TW-HsiaoChenNeural': {
    locale: 'zh-TW',
    localeZH: '中文(台湾普通话)',
    DisplayVoiceName: 'HsiaoChenNeural',
    DisplayName: 'HsiaoChen',
    LocalName: '曉臻',
    ShortName: 'zh-TW-HsiaoChenNeural',
    PreviewSentence: '建構可以和使用者自然對話的應用程式和服務，來提高其方便性和實用性。',
  },
  'es-EC-AndreaNeural': {
    DisplayName: 'Andrea',
    DisplayVoiceName: 'AndreaNeural',
    LocalName: 'Andrea',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-EC-AndreaNeural',
    locale: 'es-EC',
    localeZH: '西班牙语(厄瓜多尔)',
  },
  'lv-LV-NilsNeural': {
    locale: 'lv-LV',
    DisplayVoiceName: 'NilsNeural',
    localeZH: '拉脱维亚语(拉脱维亚)',
    DisplayName: 'Nils',
    LocalName: 'Nils',
    PreviewSentence:
      'Balss Satura Izveide ļauj jums vizuāli kontrolēt runas atribūtus reālajā laikā.',
    ShortName: 'lv-LV-NilsNeural',
  },
  'fa-IR-DilaraNeural': {
    DisplayName: 'Dilara',
    DisplayVoiceName: 'DilaraNeural',
    LocalName: 'دلارا',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'fa-IR-DilaraNeural',
    locale: 'fa-IR',
    localeZH: '波斯语(伊朗)',
  },
  'my-MM-ThihaNeural': {
    locale: 'my-MM',
    localeZH: '缅甸语(缅甸)',
    DisplayVoiceName: 'ThihaNeural',
    DisplayName: 'Thiha',
    LocalName: 'သီဟ',
    ShortName: 'my-MM-ThihaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-GB-AlfieNeural': {
    DisplayName: 'Alfie',
    DisplayVoiceName: 'AlfieNeural',
    LocalName: 'Alfie',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-AlfieNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'en-US-EricNeural': {
    DisplayName: 'Eric',
    DisplayVoiceName: 'EricNeural',
    LocalName: 'Eric',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-EricNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'zh-CN-liaoning-XiaobeiNeural': {
    locale: 'zh-CN-liaoning',
    localeZH: '中文(东北官话，简体)',
    DisplayVoiceName: 'XiaobeiNeural',
    DisplayName: 'Xiaobei',
    LocalName: '晓北',
    ShortName: 'zh-CN-liaoning-XiaobeiNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-US-MonicaNeural': {
    DisplayName: 'Monica',
    DisplayVoiceName: 'MonicaNeural',
    LocalName: 'Monica',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-MonicaNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'ar-DZ-AminaNeural': {
    DisplayName: 'Amina',
    DisplayVoiceName: 'AminaNeural',
    LocalName: 'أمينة',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-DZ-AminaNeural',
    locale: 'ar-DZ',
    localeZH: '阿拉伯语(阿尔及利亚)',
  },
  'ja-JP-NaokiNeural': {
    locale: 'ja-JP',
    DisplayVoiceName: 'NaokiNeural',
    localeZH: '日语(日本)',
    DisplayName: 'Naoki',
    LocalName: '直紀',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-NaokiNeural',
  },
  'ar-JO-TaimNeural': {
    DisplayName: 'Taim',
    DisplayVoiceName: 'TaimNeural',
    LocalName: 'تيم',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-JO-TaimNeural',
    locale: 'ar-JO',
    localeZH: '阿拉伯语(约旦)',
  },
  'zh-CN-XiaoqiuNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoqiuNeural',
    DisplayName: 'Xiaoqiu',
    LocalName: '晓秋',
    ShortName: 'zh-CN-XiaoqiuNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'es-MX-CandelaNeural': {
    DisplayName: 'Candela',
    DisplayVoiceName: 'CandelaNeural',
    LocalName: 'Candela',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-CandelaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'zh-CN-YunyeNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunyeNeural',
    DisplayName: 'Yunye',
    LocalName: '云野',
    ShortName: 'zh-CN-YunyeNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'bg-BG-BorislavNeural': {
    DisplayName: 'Borislav',
    DisplayVoiceName: 'BorislavNeural',
    LocalName: 'Борислав',
    PreviewSentence:
      'Създай приложения и услуги, които говорят непринудено на потребителите, подобрявайки достъпността и използваемостта.',
    ShortName: 'bg-BG-BorislavNeural',
    locale: 'bg-BG',
    localeZH: '保加利亚语(保加利亚)',
  },
  'es-MX-LarissaNeural': {
    DisplayName: 'Larissa',
    DisplayVoiceName: 'LarissaNeural',
    LocalName: 'Larissa',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-LarissaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'wuu-CN-XiaotongNeural': {
    locale: 'wuu-CN',
    localeZH: '中文(吴语，简体)',
    DisplayVoiceName: 'XiaotongNeural',
    DisplayName: 'Xiaotong',
    LocalName: '晓彤',
    ShortName: 'wuu-CN-XiaotongNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-ES-DarioNeural': {
    DisplayName: 'Dario',
    DisplayVoiceName: 'DarioNeural',
    LocalName: 'Dario',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-DarioNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'pt-PT-DuarteNeural': {
    locale: 'pt-PT',
    localeZH: '葡萄牙语(葡萄牙)',
    DisplayVoiceName: 'DuarteNeural',
    DisplayName: 'Duarte',
    LocalName: 'Duarte',
    ShortName: 'pt-PT-DuarteNeural',
    PreviewSentence:
      'Constrói aplicações e serviços que falam naturalmente com os utilizadores, melhorando a acessibilidade e usabilidade.',
  },
  'en-US-JaneNeural': {
    DisplayName: 'Jane',
    DisplayVoiceName: 'JaneNeural',
    LocalName: 'Jane',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-JaneNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'es-MX-RenataNeural': {
    DisplayName: 'Renata',
    DisplayVoiceName: 'RenataNeural',
    LocalName: 'Renata',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-RenataNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'ja-JP-KeitaNeural': {
    DisplayVoiceName: 'KeitaNeural',
    DisplayName: 'Keita',
    locale: 'ja-JP',
    LocalName: '圭太',
    localeZH: '日语(日本)',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-KeitaNeural',
  },
  'es-MX-PelayoNeural': {
    DisplayName: 'Pelayo',
    DisplayVoiceName: 'PelayoNeural',
    LocalName: 'Pelayo',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-PelayoNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'pt-BR-FabioNeural': {
    DisplayVoiceName: 'FabioNeural',
    DisplayName: 'Fabio',
    locale: 'pt-BR',
    LocalName: 'Fabio',
    localeZH: '葡萄牙语(巴西)',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-FabioNeural',
  },
  'es-VE-PaolaNeural': {
    DisplayName: 'Paola',
    DisplayVoiceName: 'PaolaNeural',
    LocalName: 'Paola',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-VE-PaolaNeural',
    locale: 'es-VE',
    localeZH: '西班牙语(委内瑞拉)',
  },
  'th-TH-PremwadeeNeural': {
    DisplayVoiceName: 'PremwadeeNeural',
    locale: 'th-TH',
    DisplayName: 'Premwadee',
    localeZH: '泰语(泰国)',
    LocalName: 'เปรมวดี',
    PreviewSentence:
      'สร้างแอปและบริการที่สื่อสารกับผู้ใช้ได้อย่างเป็นธรรมชาติ ซึ่งช่วยปรับปรุงการเข้าถึงและการใช้งาน',
    ShortName: 'th-TH-PremwadeeNeural',
  },
  'es-VE-SebastianNeural': {
    DisplayName: 'Sebastian',
    DisplayVoiceName: 'SebastianNeural',
    LocalName: 'Sebastián',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-VE-SebastianNeural',
    locale: 'es-VE',
    localeZH: '西班牙语(委内瑞拉)',
  },
  'uz-UZ-SardorNeural': {
    locale: 'uz-UZ',
    DisplayVoiceName: 'SardorNeural',
    localeZH: '乌兹别克语(乌兹别克斯坦)',
    DisplayName: 'Sardor',
    LocalName: 'Sardor',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'uz-UZ-SardorNeural',
  },
  'et-EE-AnuNeural': {
    DisplayName: 'Anu',
    DisplayVoiceName: 'AnuNeural',
    LocalName: 'Anu',
    PreviewSentence:
      'Audio Content Creation võimaldab kõne atribuute reaalajas visuaalselt kontrollida.',
    ShortName: 'et-EE-AnuNeural',
    locale: 'et-EE',
    localeZH: '爱沙尼亚语(爱沙尼亚)',
  },
  'yue-CN-YunSongNeural': {
    locale: 'yue-CN',
    DisplayVoiceName: 'YunSongNeural',
    localeZH: '中文(粤语，简体)',
    DisplayName: 'YunSong',
    LocalName: '云松',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'yue-CN-YunSongNeural',
  },
  'en-PH-JamesNeural': {
    DisplayName: 'James',
    DisplayVoiceName: 'JamesNeural',
    LocalName: 'James',
    PreviewSentence:
      'Audio Content Creation enables you to visually control speech attributes in real-time.',
    ShortName: 'en-PH-JamesNeural',
    locale: 'en-PH',
    localeZH: '英语(菲律宾)',
  },
  'zh-CN-sichuan-YunxiNeural': {
    locale: 'zh-CN-sichuan',
    localeZH: '中文(西南官话，简体)',
    DisplayVoiceName: 'YunxiNeural',
    DisplayName: 'Yunxi',
    LocalName: '云希',
    ShortName: 'zh-CN-sichuan-YunxiNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-CL-LorenzoNeural': {
    DisplayName: 'Lorenzo',
    DisplayVoiceName: 'LorenzoNeural',
    LocalName: 'Lorenzo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CL-LorenzoNeural',
    locale: 'es-CL',
    localeZH: '西班牙语(智利)',
  },
  'lt-LT-OnaNeural': {
    locale: 'lt-LT',
    DisplayVoiceName: 'OnaNeural',
    localeZH: '立陶宛语(立陶宛)',
    DisplayName: 'Ona',
    LocalName: 'Ona',
    PreviewSentence:
      'Garso turinio kūrimas leidžia vizualiai kontroliuoti kalbos atributus realiu laiku.',
    ShortName: 'lt-LT-OnaNeural',
  },
  'bs-BA-GoranNeural': {
    DisplayName: 'Goran',
    DisplayVoiceName: 'GoranNeural',
    LocalName: 'Goran',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bs-BA-GoranNeural',
    locale: 'bs-BA',
    localeZH: '波斯尼亚语(波斯尼亚和黑塞哥维那)',
  },
  'sq-AL-AnilaNeural': {
    locale: 'sq-AL',
    localeZH: '阿尔巴尼亚语(阿尔巴尼亚)',
    DisplayVoiceName: 'AnilaNeural',
    DisplayName: 'Anila',
    LocalName: 'Anila',
    ShortName: 'sq-AL-AnilaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-ES-EstrellaNeural': {
    DisplayName: 'Estrella',
    DisplayVoiceName: 'EstrellaNeural',
    LocalName: 'Estrella',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-EstrellaNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'fa-IR-FaridNeural': {
    DisplayName: 'Farid',
    DisplayVoiceName: 'FaridNeural',
    LocalName: 'فرید',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'fa-IR-FaridNeural',
    locale: 'fa-IR',
    localeZH: '波斯语(伊朗)',
  },
  'el-GR-NestorasNeural': {
    DisplayName: 'Nestoras',
    DisplayVoiceName: 'NestorasNeural',
    LocalName: 'Νέστορας',
    PreviewSentence:
      'Δημιουργήστε εφαρμογές και υπηρεσίες που μιλούν με φυσικό τρόπο στους χρήστες, βελτιώνοντας την προσβασιμότητα και τη χρηστικότητα.',
    ShortName: 'el-GR-NestorasNeural',
    locale: 'el-GR',
    localeZH: '希腊语(希腊)',
  },
  'zh-CN-XiaoruiNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoruiNeural',
    DisplayName: 'Xiaorui',
    LocalName: '晓睿',
    ShortName: 'zh-CN-XiaoruiNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'bn-IN-TanishaaNeural': {
    DisplayName: 'Tanishaa',
    DisplayVoiceName: 'TanishaaNeural',
    LocalName: 'তানিশা',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'bn-IN-TanishaaNeural',
    locale: 'bn-IN',
    localeZH: '孟加拉语(印度)',
  },
  'ar-SY-AmanyNeural': {
    DisplayName: 'Amany',
    DisplayVoiceName: 'AmanyNeural',
    LocalName: 'أماني',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-SY-AmanyNeural',
    locale: 'ar-SY',
    localeZH: '阿拉伯语(叙利亚)',
  },
  'zu-ZA-ThandoNeural': {
    locale: 'zu-ZA',
    localeZH: '祖鲁语(南非)',
    DisplayVoiceName: 'ThandoNeural',
    DisplayName: 'Thando',
    LocalName: 'Thando',
    ShortName: 'zu-ZA-ThandoNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-GB-ElliotNeural': {
    DisplayName: 'Elliot',
    DisplayVoiceName: 'ElliotNeural',
    LocalName: 'Elliot',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-ElliotNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'ar-IQ-RanaNeural': {
    DisplayName: 'Rana',
    DisplayVoiceName: 'RanaNeural',
    LocalName: 'رنا',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-IQ-RanaNeural',
    locale: 'ar-IQ',
    localeZH: '阿拉伯语(伊拉克)',
  },
  'lo-LA-KeomanyNeural': {
    locale: 'lo-LA',
    DisplayVoiceName: 'KeomanyNeural',
    localeZH: '老挝语(老挝) ',
    DisplayName: 'Keomany',
    LocalName: 'ແກ້ວມະນີ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'lo-LA-KeomanyNeural',
  },
  'en-AU-JoanneNeural': {
    DisplayName: 'Joanne',
    DisplayVoiceName: 'JoanneNeural',
    LocalName: 'Joanne',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-JoanneNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'mn-MN-BataaNeural': {
    locale: 'mn-MN',
    localeZH: '蒙古语(蒙古)',
    DisplayVoiceName: 'BataaNeural',
    DisplayName: 'Bataa',
    LocalName: 'Батаа',
    ShortName: 'mn-MN-BataaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-GB-NoahNeural': {
    DisplayName: 'Noah',
    DisplayVoiceName: 'NoahNeural',
    LocalName: 'Noah',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-NoahNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'fil-PH-AngeloNeural': {
    DisplayName: 'Angelo',
    DisplayVoiceName: 'AngeloNeural',
    LocalName: 'Angelo',
    locale: 'fil-PH',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    localeZH: '菲律宾语(菲律宾)',
    ShortName: 'fil-PH-AngeloNeural',
  },
  'es-GT-AndresNeural': {
    DisplayName: 'Andres',
    DisplayVoiceName: 'AndresNeural',
    LocalName: 'Andrés',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-GT-AndresNeural',
    locale: 'es-GT',
    localeZH: '西班牙语(危地马拉)',
  },
  'fr-FR-EloiseNeural': {
    DisplayName: 'Eloise',
    DisplayVoiceName: 'EloiseNeural',
    LocalName: 'Eloise',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-EloiseNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'de-DE-KasperNeural': {
    DisplayName: 'Kasper',
    DisplayVoiceName: 'KasperNeural',
    LocalName: 'Kasper',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-KasperNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'ko-KR-BongJinNeural': {
    DisplayVoiceName: 'BongJinNeural',
    locale: 'ko-KR',
    DisplayName: 'BongJin',
    localeZH: '韩语(韩国)',
    LocalName: '봉진',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
    ShortName: 'ko-KR-BongJinNeural',
  },
  'ar-LB-RamiNeural': {
    DisplayName: 'Rami',
    DisplayVoiceName: 'RamiNeural',
    LocalName: 'رامي',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-LB-RamiNeural',
    locale: 'ar-LB',
    localeZH: '阿拉伯语(黎巴嫩)',
  },
  'pt-BR-JulioNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'JulioNeural',
    DisplayName: 'Julio',
    LocalName: 'Julio',
    ShortName: 'pt-BR-JulioNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'en-US-AriaNeural': {
    DisplayName: 'Aria',
    DisplayVoiceName: 'AriaNeural',
    LocalName: 'Aria',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-AriaNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'es-ES-LaiaNeural': {
    DisplayName: 'Laia',
    DisplayVoiceName: 'LaiaNeural',
    LocalName: 'Laia',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-LaiaNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'sk-SK-ViktoriaNeural': {
    locale: 'sk-SK',
    localeZH: '斯洛伐克语(斯洛伐克)',
    DisplayVoiceName: 'ViktoriaNeural',
    DisplayName: 'Viktoria',
    LocalName: 'Viktória',
    ShortName: 'sk-SK-ViktoriaNeural',
    PreviewSentence:
      'Vytvárajú aplikácie a služby, ktoré prirodzene komunikujú s užívateľmi, čím sa zlepšuje dostupnosť a využiteľnosť.',
  },
  'en-AU-NeilNeural': {
    DisplayName: 'Neil',
    DisplayVoiceName: 'NeilNeural',
    LocalName: 'Neil',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-NeilNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'sv-SE-SofieNeural': {
    locale: 'sv-SE',
    localeZH: '瑞典语(瑞典)',
    DisplayVoiceName: 'SofieNeural',
    DisplayName: 'Sofie',
    LocalName: 'Sofie',
    ShortName: 'sv-SE-SofieNeural',
    PreviewSentence:
      'Bygg appar och tjänster som talar naturligt till användarna, och förbättrar tillgänglighet och användbarhet.',
  },
  'en-IE-EmilyNeural': {
    DisplayName: 'Emily',
    DisplayVoiceName: 'EmilyNeural',
    LocalName: 'Emily',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-IE-EmilyNeural',
    locale: 'en-IE',
    localeZH: '英语(爱尔兰)',
  },
  'es-HN-KarlaNeural': {
    DisplayName: 'Karla',
    DisplayVoiceName: 'KarlaNeural',
    LocalName: 'Karla',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-HN-KarlaNeural',
    locale: 'es-HN',
    localeZH: '西班牙语(洪都拉斯)',
  },
  'gu-IN-DhwaniNeural': {
    DisplayName: 'Dhwani',
    DisplayVoiceName: 'DhwaniNeural',
    LocalName: 'ધ્વની',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'gu-IN-DhwaniNeural',
    locale: 'gu-IN',
    localeZH: '古吉拉特语(印度)',
  },
  'es-PR-VictorNeural': {
    DisplayName: 'Victor',
    DisplayVoiceName: 'VictorNeural',
    LocalName: 'Víctor',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PR-VictorNeural',
    locale: 'es-PR',
    localeZH: '西班牙语(波多黎各)',
  },
  'hi-IN-SwaraNeural': {
    locale: 'hi-IN',
    DisplayVoiceName: 'SwaraNeural',
    localeZH: '印地语(印度)',
    DisplayName: 'Swara',
    LocalName: 'स्वरा',
    PreviewSentence:
      'ऐसे ऐप और सेवाओं का निर्माण करें जो उपयोगकर्ताओं से आम बोलचाल की भाषा में बात करे, जिससे सुगमता और उपयोगिता बढ़े।',
    ShortName: 'hi-IN-SwaraNeural',
  },
  'af-ZA-WillemNeural': {
    DisplayName: 'Willem',
    DisplayVoiceName: 'WillemNeural',
    LocalName: 'Willem',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'af-ZA-WillemNeural',
    locale: 'af-ZA',
    localeZH: '南非荷兰语(南非)',
  },
  'ml-IN-MidhunNeural': {
    locale: 'ml-IN',
    DisplayVoiceName: 'MidhunNeural',
    localeZH: '马拉雅拉姆语(印度)',
    DisplayName: 'Midhun',
    LocalName: 'മിഥുൻ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ml-IN-MidhunNeural',
  },
  'de-DE-GiselaNeural': {
    DisplayName: 'Gisela',
    DisplayVoiceName: 'GiselaNeural',
    LocalName: 'Gisela',
    PreviewSentence:
      'Erstellen Sie Apps und Dienste, die auf natürliche Art mit Nutzern sprechen, und dadurch Zugänglichkeit und Benutzerfreundlichkeit verbessern.',
    ShortName: 'de-DE-GiselaNeural',
    locale: 'de-DE',
    localeZH: '德语(德国)',
  },
  'ta-IN-PallaviNeural': {
    locale: 'ta-IN',
    localeZH: '泰米尔语(印度)',
    DisplayVoiceName: 'PallaviNeural',
    DisplayName: 'Pallavi',
    LocalName: 'பல்லவி',
    ShortName: 'ta-IN-PallaviNeural',
    PreviewSentence:
      'பயனர்களிடம் இயற்கையாக பேசும் பயன்பாடுகள் மற்றும் சேவைகளை உருவாகுதல், இது அணுகல்தன்மை மற்றும் பயன்பாட்டினை மேம்படுத்தும்.',
  },
  'es-ES-IreneNeural': {
    DisplayName: 'Irene',
    DisplayVoiceName: 'IreneNeural',
    LocalName: 'Irene',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-IreneNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'pt-BR-ManuelaNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'ManuelaNeural',
    DisplayName: 'Manuela',
    LocalName: 'Manuela',
    ShortName: 'pt-BR-ManuelaNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'fr-CA-JeanNeural': {
    DisplayName: 'Jean',
    DisplayVoiceName: 'JeanNeural',
    LocalName: 'Jean',
    PreviewSentence:
      'Créer des applications et des services qui parlent aux utilisateurs, améliorant ainsi l’accessibilité et la facilité d’utilisation.',
    ShortName: 'fr-CA-JeanNeural',
    locale: 'fr-CA',
    localeZH: '法语(加拿大)',
  },
  'sw-TZ-DaudiNeural': {
    locale: 'sw-TZ',
    localeZH: '斯瓦希里语(坦桑尼亚)',
    DisplayVoiceName: 'DaudiNeural',
    DisplayName: 'Daudi',
    LocalName: 'Daudi',
    ShortName: 'sw-TZ-DaudiNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-US-JacobNeural': {
    DisplayName: 'Jacob',
    DisplayVoiceName: 'JacobNeural',
    LocalName: 'Jacob',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-JacobNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'fil-PH-BlessicaNeural': {
    DisplayName: 'Blessica',
    DisplayVoiceName: 'BlessicaNeural',
    LocalName: 'Blessica',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'fil-PH-BlessicaNeural',
    locale: 'fil-PH',
    localeZH: '菲律宾语(菲律宾)',
  },
  'lv-LV-EveritaNeural': {
    locale: 'lv-LV',
    localeZH: '拉脱维亚语(拉脱维亚)',
    DisplayVoiceName: 'EveritaNeural',
    DisplayName: 'Everita',
    LocalName: 'Everita',
    ShortName: 'lv-LV-EveritaNeural',
    PreviewSentence:
      'Balss Satura Izveide ļauj jums vizuāli kontrolēt runas atribūtus reālajā laikā.',
  },
  'ar-BH-AliNeural': {
    DisplayName: 'Ali',
    DisplayVoiceName: 'AliNeural',
    LocalName: 'علي',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-BH-AliNeural',
    locale: 'ar-BH',
    localeZH: '阿拉伯语(巴林)',
  },
  'it-IT-BenignoNeural': {
    DisplayName: 'Benigno',
    DisplayVoiceName: 'BenignoNeural',
    LocalName: 'Benigno',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-BenignoNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'en-IN-NeerjaNeural': {
    DisplayName: 'Neerja',
    DisplayVoiceName: 'NeerjaNeural',
    LocalName: 'Neerja',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-IN-NeerjaNeural',
    locale: 'en-IN',
    localeZH: '英语(印度)',
  },
  'ne-NP-HemkalaNeural': {
    DisplayVoiceName: 'HemkalaNeural',
    locale: 'ne-NP',
    DisplayName: 'Hemkala',
    localeZH: '尼泊尔语(尼泊尔)',
    LocalName: 'हेमकला',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ne-NP-HemkalaNeural',
  },
  'en-US-NancyNeural': {
    DisplayName: 'Nancy',
    DisplayVoiceName: 'NancyNeural',
    LocalName: 'Nancy',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-NancyNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'uk-UA-OstapNeural': {
    locale: 'uk-UA',
    localeZH: '乌克兰语(乌克兰)',
    DisplayVoiceName: 'OstapNeural',
    DisplayName: 'Ostap',
    LocalName: 'Остап',
    ShortName: 'uk-UA-OstapNeural',
    PreviewSentence:
      'Створення аудіовмісту дозволяє візуально контролювати мовні атрибути в реальному часі.',
  },
  'az-AZ-BanuNeural': {
    DisplayName: 'Banu',
    DisplayVoiceName: 'BanuNeural',
    LocalName: 'Banu',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'az-AZ-BanuNeural',
    locale: 'az-AZ',
    localeZH: '阿塞拜疆语(阿塞拜疆) ',
  },
  'es-ES-AbrilNeural': {
    DisplayName: 'Abril',
    DisplayVoiceName: 'AbrilNeural',
    LocalName: 'Abril',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-AbrilNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'zh-CN-YunxiNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunxiNeural',
    DisplayName: 'Yunxi',
    LocalName: '云希',
    ShortName: 'zh-CN-YunxiNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'es-US-PalomaNeural': {
    DisplayName: 'Paloma',
    DisplayVoiceName: 'PalomaNeural',
    LocalName: 'Paloma',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-US-PalomaNeural',
    locale: 'es-US',
    localeZH: '西班牙语(美国)',
  },
  'nl-BE-ArnaudNeural': {
    locale: 'nl-BE',
    localeZH: '荷兰语(比利时)',
    DisplayVoiceName: 'ArnaudNeural',
    DisplayName: 'Arnaud',
    LocalName: 'Arnaud',
    ShortName: 'nl-BE-ArnaudNeural',
    PreviewSentence:
      'Audio-inhoud Aanmaken laat u toe om visueel de spraakeigenschappen in te stellen in real-time.',
  },
  'ar-AE-HamdanNeural': {
    DisplayName: 'Hamdan',
    DisplayVoiceName: 'HamdanNeural',
    LocalName: 'حمدان',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-AE-HamdanNeural',
    locale: 'ar-AE',
    localeZH: '阿拉伯语(阿拉伯联合酋长国)',
  },
  'tr-TR-EmelNeural': {
    locale: 'tr-TR',
    localeZH: '土耳其语(Türkiye)',
    DisplayVoiceName: 'EmelNeural',
    DisplayName: 'Emel',
    LocalName: 'Emel',
    ShortName: 'tr-TR-EmelNeural',
    PreviewSentence:
      'Kullanıcılarla doğal biçimde konuşan, erişilebilirlik ve kullanılabilirliği iyileştiren uygulama ve servisler geliştirmek.',
  },
  'fr-CH-FabriceNeural': {
    DisplayName: 'Fabrice',
    DisplayVoiceName: 'FabriceNeural',
    LocalName: 'Fabrice',
    PreviewSentence:
      'Développer des applications et des services qui parlent aux utilisateurs avec naturel, pour améliorer leur accessibilité et leur utilisation.',
    ShortName: 'fr-CH-FabriceNeural',
    locale: 'fr-CH',
    localeZH: '法语(瑞士)',
  },
  'pl-PL-MarekNeural': {
    locale: 'pl-PL',
    localeZH: '波兰语(波兰)',
    DisplayVoiceName: 'MarekNeural',
    DisplayName: 'Marek',
    LocalName: 'Marek',
    ShortName: 'pl-PL-MarekNeural',
    PreviewSentence:
      'Twórz aplikacje i serwisy, które w kontakcie z użytkownikiem posługują się naturalną mową, co podnosi ich dostępność i użyteczność.',
  },
  'en-GB-EthanNeural': {
    DisplayName: 'Ethan',
    DisplayVoiceName: 'EthanNeural',
    LocalName: 'Ethan',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-EthanNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'en-NZ-MitchellNeural': {
    DisplayName: 'Mitchell',
    DisplayVoiceName: 'MitchellNeural',
    LocalName: 'Mitchell',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-NZ-MitchellNeural',
    locale: 'en-NZ',
    localeZH: '英语(新西兰)',
  },
  'it-IT-CalimeroNeural': {
    DisplayVoiceName: 'CalimeroNeural',
    DisplayName: 'Calimero',
    locale: 'it-IT',
    LocalName: 'Calimero',
    localeZH: '意大利语(意大利)',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-CalimeroNeural',
  },
  'en-US-GuyNeural': {
    DisplayName: 'Guy',
    DisplayVoiceName: 'GuyNeural',
    LocalName: 'Guy',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-GuyNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'wuu-CN-YunzheNeural': {
    locale: 'wuu-CN',
    localeZH: '中文(吴语，简体)',
    DisplayVoiceName: 'YunzheNeural',
    DisplayName: 'Yunzhe',
    LocalName: '云哲',
    ShortName: 'wuu-CN-YunzheNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'ar-QA-AmalNeural': {
    DisplayName: 'Amal',
    DisplayVoiceName: 'AmalNeural',
    LocalName: 'أمل',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-QA-AmalNeural',
    locale: 'ar-QA',
    localeZH: '阿拉伯语(卡塔尔)',
  },
  'it-IT-PalmiraNeural': {
    DisplayVoiceName: 'PalmiraNeural',
    DisplayName: 'Palmira',
    locale: 'it-IT',
    LocalName: 'Palmira',
    localeZH: '意大利语(意大利)',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-PalmiraNeural',
  },
  'es-PA-RobertoNeural': {
    DisplayName: 'Roberto',
    DisplayVoiceName: 'RobertoNeural',
    LocalName: 'Roberto',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PA-RobertoNeural',
    locale: 'es-PA',
    localeZH: '西班牙语(巴拿马)',
  },
  'sr-RS-NicholasNeural': {
    locale: 'sr-RS',
    localeZH: '塞尔维亚语(塞尔维亚)',
    DisplayVoiceName: 'NicholasNeural',
    DisplayName: 'Nicholas',
    LocalName: 'Никола',
    ShortName: 'sr-RS-NicholasNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-BO-MarceloNeural': {
    DisplayName: 'Marcelo',
    DisplayVoiceName: 'MarceloNeural',
    LocalName: 'Marcelo',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-BO-MarceloNeural',
    locale: 'es-BO',
    localeZH: '西班牙语(玻利维亚)',
  },
  'es-BO-SofiaNeural': {
    DisplayName: 'Sofia',
    DisplayVoiceName: 'SofiaNeural',
    LocalName: 'Sofia',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-BO-SofiaNeural',
    locale: 'es-BO',
    localeZH: '西班牙语(玻利维亚)',
  },
  'nb-NO-IselinNeural': {
    DisplayVoiceName: 'IselinNeural',
    locale: 'nb-NO',
    DisplayName: 'Iselin',
    localeZH: '书面挪威语(挪威)',
    LocalName: 'Iselin',
    PreviewSentence:
      'Bygger apper og tjenester som snakker naturlig med brukerne, utbedrer tilgjengelighet og brukskvalitet.',
    ShortName: 'nb-NO-IselinNeural',
  },
  'es-ES-TeoNeural': {
    DisplayName: 'Teo',
    DisplayVoiceName: 'TeoNeural',
    LocalName: 'Teo',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-TeoNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'su-ID-TutiNeural': {
    locale: 'su-ID',
    localeZH: '巽他语(印度尼西亚)',
    DisplayVoiceName: 'TutiNeural',
    DisplayName: 'Tuti',
    LocalName: 'Tuti',
    ShortName: 'su-ID-TutiNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'en-GB-OliviaNeural': {
    DisplayName: 'Olivia',
    DisplayVoiceName: 'OliviaNeural',
    LocalName: 'Olivia',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-GB-OliviaNeural',
    locale: 'en-GB',
    localeZH: '英语(英国)',
  },
  'es-ES-LiaNeural': {
    DisplayName: 'Lia',
    DisplayVoiceName: 'LiaNeural',
    LocalName: 'Lia',
    PreviewSentence:
      'Crea aplicaciones y servicios que hablan de forma natural a los usuarios, y que mejoran la accesibilidad y la facilidad de uso.',
    ShortName: 'es-ES-LiaNeural',
    locale: 'es-ES',
    localeZH: '西班牙语(西班牙)',
  },
  'th-TH-AcharaNeural': {
    locale: 'th-TH',
    localeZH: '泰语(泰国)',
    DisplayVoiceName: 'AcharaNeural',
    DisplayName: 'Achara',
    LocalName: 'อัจฉรา',
    ShortName: 'th-TH-AcharaNeural',
    PreviewSentence:
      'สร้างแอปและบริการที่สื่อสารกับผู้ใช้ได้อย่างเป็นธรรมชาติ ซึ่งช่วยปรับปรุงการเข้าถึงและการใช้งาน',
  },
  'es-PE-CamilaNeural': {
    DisplayName: 'Camila',
    DisplayVoiceName: 'CamilaNeural',
    LocalName: 'Camila',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PE-CamilaNeural',
    locale: 'es-PE',
    localeZH: '西班牙语(秘鲁)',
  },
  'fr-FR-CoralieNeural': {
    DisplayName: 'Coralie',
    DisplayVoiceName: 'CoralieNeural',
    LocalName: 'Coralie',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-CoralieNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'es-PR-KarinaNeural': {
    DisplayName: 'Karina',
    DisplayVoiceName: 'KarinaNeural',
    LocalName: 'Karina',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PR-KarinaNeural',
    locale: 'es-PR',
    localeZH: '西班牙语(波多黎各)',
  },
  'ur-IN-GulNeural': {
    locale: 'ur-IN',
    localeZH: '乌尔都语(印度)',
    DisplayVoiceName: 'GulNeural',
    DisplayName: 'Gul',
    LocalName: 'گل',
    ShortName: 'ur-IN-GulNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'cs-CZ-VlastaNeural': {
    DisplayName: 'Vlasta',
    DisplayVoiceName: 'VlastaNeural',
    LocalName: 'Vlasta',
    PreviewSentence:
      'Vytvořte aplikace a služby pro přirozenou komunikaci s uživateli a usnadněte tak přístup a využití.',
    ShortName: 'cs-CZ-VlastaNeural',
    locale: 'cs-CZ',
    localeZH: '捷克语(捷克)',
  },
  'ca-ES-EnricNeural': {
    DisplayName: 'Enric',
    DisplayVoiceName: 'EnricNeural',
    LocalName: 'Enric',
    PreviewSentence:
      "Crea aplicacions i serveis que parlen de forma natural als usuaris, i que milloren l'accessibilitat i la facilitat d'ús.",
    ShortName: 'ca-ES-EnricNeural',
    locale: 'ca-ES',
    localeZH: '加泰罗尼亚语(西班牙)',
  },
  'zh-TW-YunJheNeural': {
    locale: 'zh-TW',
    localeZH: '中文(台湾普通话)',
    DisplayVoiceName: 'YunJheNeural',
    DisplayName: 'YunJhe',
    LocalName: '雲哲',
    ShortName: 'zh-TW-YunJheNeural',
    PreviewSentence: '建構可以和使用者自然對話的應用程式和服務，來提高其方便性和實用性。',
  },
  'es-MX-DaliaNeural': {
    DisplayName: 'Dalia',
    DisplayVoiceName: 'DaliaNeural',
    LocalName: 'Dalia',
    PreviewSentence:
      'Crea apps y servicios que hablen de forma natural con los usuarios, mejorando la accesibilidad y la usabilidad.',
    ShortName: 'es-MX-DaliaNeural',
    locale: 'es-MX',
    localeZH: '西班牙语(墨西哥)',
  },
  'ar-EG-SalmaNeural': {
    DisplayName: 'Salma',
    DisplayVoiceName: 'SalmaNeural',
    LocalName: 'سلمى',
    PreviewSentence:
      'إن التطبيقات التي تتحاور مع المستخدمين بصوره طبيعية،  تعمل على  تحسين امكانية الوصول اليها وسهولة الاستخدام',
    ShortName: 'ar-EG-SalmaNeural',
    locale: 'ar-EG',
    localeZH: '阿拉伯语(埃及)',
  },
  'sv-SE-HilleviNeural': {
    DisplayVoiceName: 'HilleviNeural',
    locale: 'sv-SE',
    DisplayName: 'Hillevi',
    localeZH: '瑞典语(瑞典)',
    LocalName: 'Hillevi',
    PreviewSentence:
      'Bygg appar och tjänster som talar naturligt till användarna, och förbättrar tillgänglighet och användbarhet.',
    ShortName: 'sv-SE-HilleviNeural',
  },
  'ar-TN-ReemNeural': {
    DisplayName: 'Reem',
    DisplayVoiceName: 'ReemNeural',
    LocalName: 'ريم',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-TN-ReemNeural',
    locale: 'ar-TN',
    localeZH: '阿拉伯语(突尼斯)',
  },
  'ta-LK-SaranyaNeural': {
    locale: 'ta-LK',
    DisplayVoiceName: 'SaranyaNeural',
    localeZH: '泰米尔语(斯里兰卡)',
    DisplayName: 'Saranya',
    LocalName: 'சரண்யா',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ta-LK-SaranyaNeural',
  },
  'ar-EG-ShakirNeural': {
    DisplayName: 'Shakir',
    DisplayVoiceName: 'ShakirNeural',
    LocalName: 'شاكر',
    PreviewSentence:
      'إن التطبيقات التي تتحاور مع المستخدمين بصوره طبيعية،  تعمل على  تحسين امكانية الوصول اليها وسهولة الاستخدام',
    ShortName: 'ar-EG-ShakirNeural',
    locale: 'ar-EG',
    localeZH: '阿拉伯语(埃及)',
  },
  'ur-PK-AsadNeural': {
    locale: 'ur-PK',
    localeZH: '乌尔都语(巴基斯坦)',
    DisplayVoiceName: 'AsadNeural',
    DisplayName: 'Asad',
    LocalName: 'اسد',
    ShortName: 'ur-PK-AsadNeural',
    PreviewSentence:
      'آواز کا مواد تخلیق کرنا  اس قابل بناتا  ہیکہ آپ تقریر کی خصوصیات کو  حقیقی وقت میں  اپنے مطابق کنٹرول کر سکتے ہیں۔',
  },
  'en-KE-ChilembaNeural': {
    DisplayName: 'Chilemba',
    DisplayVoiceName: 'ChilembaNeural',
    LocalName: 'Chilemba',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-KE-ChilembaNeural',
    locale: 'en-KE',
    localeZH: '英语(肯尼亚)',
  },
  'ar-LB-LaylaNeural': {
    DisplayName: 'Layla',
    DisplayVoiceName: 'LaylaNeural',
    LocalName: 'ليلى',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-LB-LaylaNeural',
    locale: 'ar-LB',
    localeZH: '阿拉伯语(黎巴嫩)',
  },
  'km-KH-SreymomNeural': {
    locale: 'km-KH',
    DisplayVoiceName: 'SreymomNeural',
    localeZH: '高棉语(柬埔寨)',
    DisplayName: 'Sreymom',
    LocalName: 'ស្រីមុំ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'km-KH-SreymomNeural',
  },
  'en-US-ChristopherNeural': {
    DisplayName: 'Christopher',
    DisplayVoiceName: 'ChristopherNeural',
    LocalName: 'Christopher',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-US-ChristopherNeural',
    locale: 'en-US',
    localeZH: '英语(美国)',
  },
  'zh-CN-XiaoyouNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'XiaoyouNeural',
    DisplayName: 'Xiaoyou',
    LocalName: '晓悠',
    ShortName: 'zh-CN-XiaoyouNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'ar-SA-ZariyahNeural': {
    DisplayName: 'Zariyah',
    DisplayVoiceName: 'ZariyahNeural',
    LocalName: 'زارية',
    PreviewSentence:
      'إن التطبيقات التي تتحاور مع المستخدمين بصوره طبيعية،  تعمل على  تحسين امكانية الوصول اليها وسهولة الاستخدام',
    ShortName: 'ar-SA-ZariyahNeural',
    locale: 'ar-SA',
    localeZH: '阿拉伯语(沙特阿拉伯)',
  },
  'mr-IN-AarohiNeural': {
    locale: 'mr-IN',
    DisplayVoiceName: 'AarohiNeural',
    localeZH: '马拉地语(印度)',
    DisplayName: 'Aarohi',
    LocalName: 'आरोही',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'mr-IN-AarohiNeural',
  },
  'en-AU-ElsieNeural': {
    DisplayName: 'Elsie',
    DisplayVoiceName: 'ElsieNeural',
    LocalName: 'Elsie',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-ElsieNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'sw-KE-RafikiNeural': {
    locale: 'sw-KE',
    DisplayVoiceName: 'RafikiNeural',
    localeZH: '斯瓦希里语(肯尼亚)',
    DisplayName: 'Rafiki',
    LocalName: 'Rafiki',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'sw-KE-RafikiNeural',
  },
  'es-US-AlonsoNeural': {
    DisplayName: 'Alonso',
    DisplayVoiceName: 'AlonsoNeural',
    LocalName: 'Alonso',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-US-AlonsoNeural',
    locale: 'es-US',
    localeZH: '西班牙语(美国)',
  },
  'ur-IN-SalmanNeural': {
    locale: 'ur-IN',
    localeZH: '乌尔都语(印度)',
    DisplayVoiceName: 'SalmanNeural',
    DisplayName: 'Salman',
    LocalName: 'سلمان',
    ShortName: 'ur-IN-SalmanNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'es-CL-CatalinaNeural': {
    DisplayName: 'Catalina',
    DisplayVoiceName: 'CatalinaNeural',
    LocalName: 'Catalina',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CL-CatalinaNeural',
    locale: 'es-CL',
    localeZH: '西班牙语(智利)',
  },
  'mt-MT-GraceNeural': {
    locale: 'mt-MT',
    localeZH: '马耳他语(马耳他)',
    DisplayVoiceName: 'GraceNeural',
    DisplayName: 'Grace',
    LocalName: 'Grace',
    ShortName: 'mt-MT-GraceNeural',
    PreviewSentence:
      'L-Għodda għall-Ħolqien tal-Kontenut bil-Ħoss tħallik direttament tikkontrolla l-attributi tal-leħen minn fuq l-iskrin tiegħek.',
  },
  'en-IN-PrabhatNeural': {
    DisplayName: 'Prabhat',
    DisplayVoiceName: 'PrabhatNeural',
    LocalName: 'Prabhat',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-IN-PrabhatNeural',
    locale: 'en-IN',
    localeZH: '英语(印度)',
  },
  'es-CR-MariaNeural': {
    DisplayName: 'Maria',
    DisplayVoiceName: 'MariaNeural',
    LocalName: 'María',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-CR-MariaNeural',
    locale: 'es-CR',
    localeZH: '西班牙语(哥斯达黎加)',
  },
  'fr-FR-JacquelineNeural': {
    DisplayName: 'Jacqueline',
    DisplayVoiceName: 'JacquelineNeural',
    LocalName: 'Jacqueline',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-JacquelineNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'en-AU-KenNeural': {
    DisplayName: 'Ken',
    DisplayVoiceName: 'KenNeural',
    LocalName: 'Ken',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'en-AU-KenNeural',
    locale: 'en-AU',
    localeZH: '英语(澳大利亚)',
  },
  'it-IT-FiammaNeural': {
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
    DisplayVoiceName: 'FiammaNeural',
    DisplayName: 'Fiamma',
    LocalName: 'Fiamma',
    ShortName: 'it-IT-FiammaNeural',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
  },
  'ar-JO-SanaNeural': {
    DisplayName: 'Sana',
    DisplayVoiceName: 'SanaNeural',
    LocalName: 'سناء',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ar-JO-SanaNeural',
    locale: 'ar-JO',
    localeZH: '阿拉伯语(约旦)',
  },
  'fr-FR-JeromeNeural': {
    DisplayVoiceName: 'JeromeNeural',
    DisplayName: 'Jerome',
    locale: 'fr-FR',
    LocalName: 'Jerome',
    localeZH: '法语(法国)',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-JeromeNeural',
  },
  'es-EC-LuisNeural': {
    DisplayName: 'Luis',
    DisplayVoiceName: 'LuisNeural',
    LocalName: 'Luis',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-EC-LuisNeural',
    locale: 'es-EC',
    localeZH: '西班牙语(厄瓜多尔)',
  },
  'fr-FR-YvesNeural': {
    DisplayName: 'Yves',
    DisplayVoiceName: 'YvesNeural',
    LocalName: 'Yves',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-YvesNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'es-PE-AlexNeural': {
    DisplayName: 'Alex',
    DisplayVoiceName: 'AlexNeural',
    LocalName: 'Alex',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'es-PE-AlexNeural',
    locale: 'es-PE',
    localeZH: '西班牙语(秘鲁)',
  },
  'ja-JP-MayuNeural': {
    DisplayName: 'Mayu',
    DisplayVoiceName: 'MayuNeural',
    LocalName: '真夕',
    PreviewSentence:
      'アクセシビリティとユーザビリティを向上させながら、ユーザーに自然なことばを話すアプリとサービスを構築する',
    ShortName: 'ja-JP-MayuNeural',
    locale: 'ja-JP',
    localeZH: '日语(日本)',
  },
  'fr-BE-CharlineNeural': {
    DisplayName: 'Charline',
    DisplayVoiceName: 'CharlineNeural',
    LocalName: 'Charline',
    PreviewSentence:
      'La Création de Contenu Audio vous permet de contrôler visuellement les attributs vocaux en temps réel.',
    ShortName: 'fr-BE-CharlineNeural',
    locale: 'fr-BE',
    localeZH: '法语(比利时)',
  },
  'ms-MY-YasminNeural': {
    DisplayName: 'Yasmin',
    DisplayVoiceName: 'YasminNeural',
    LocalName: 'Yasmin',
    locale: 'ms-MY',
    PreviewSentence:
      'Membina aplikasi dan perkhidmatan yang bercakap sememangnya kepada pengguna, meningkatkan kebolehcapaian dan kebolehgunaanya.',
    localeZH: '马来语(马来西亚)',
    ShortName: 'ms-MY-YasminNeural',
  },
  'fr-FR-DeniseNeural': {
    DisplayName: 'Denise',
    DisplayVoiceName: 'DeniseNeural',
    LocalName: 'Denise',
    PreviewSentence:
      "Construire des applications et services qui communiquent naturellement avec l'utilisateur, en améliorant l’accessibilité et l'ergonomie",
    ShortName: 'fr-FR-DeniseNeural',
    locale: 'fr-FR',
    localeZH: '法语(法国)',
  },
  'pt-BR-ElzaNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'ElzaNeural',
    DisplayName: 'Elza',
    LocalName: 'Elza',
    ShortName: 'pt-BR-ElzaNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'hi-IN-MadhurNeural': {
    DisplayName: 'Madhur',
    DisplayVoiceName: 'MadhurNeural',
    LocalName: 'मधुर',
    PreviewSentence:
      'ऐसे ऐप और सेवाओं का निर्माण करें जो उपयोगकर्ताओं से आम बोलचाल की भाषा में बात करे, जिससे सुगमता और उपयोगिता बढ़े।',
    ShortName: 'hi-IN-MadhurNeural',
    locale: 'hi-IN',
    localeZH: '印地语(印度)',
  },
  'hu-HU-TamasNeural': {
    DisplayName: 'Tamas',
    DisplayVoiceName: 'TamasNeural',
    LocalName: 'Tamás',
    PreviewSentence:
      'Készítsen appokat és szolgáltatásokat, melyek természetes hangon beszélnek a felhasználóval, ezáltal hozzáférhetőbbek és könnyebben használhatóak.',
    ShortName: 'hu-HU-TamasNeural',
    locale: 'hu-HU',
    localeZH: '匈牙利语(匈牙利)',
  },
  'sq-AL-IlirNeural': {
    locale: 'sq-AL',
    DisplayVoiceName: 'IlirNeural',
    localeZH: '阿尔巴尼亚语(阿尔巴尼亚)',
    DisplayName: 'Ilir',
    LocalName: 'Ilir',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'sq-AL-IlirNeural',
  },
  'hy-AM-HaykNeural': {
    DisplayName: 'Hayk',
    DisplayVoiceName: 'HaykNeural',
    LocalName: 'Հայկ',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'hy-AM-HaykNeural',
    locale: 'hy-AM',
    localeZH: '亚美尼亚语(亚美尼亚)',
  },
  'zh-CN-YunxiaNeural': {
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
    DisplayVoiceName: 'YunxiaNeural',
    DisplayName: 'Yunxia',
    LocalName: '云夏',
    ShortName: 'zh-CN-YunxiaNeural',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
  },
  'id-ID-ArdiNeural': {
    DisplayName: 'Ardi',
    DisplayVoiceName: 'ArdiNeural',
    LocalName: 'Ardi',
    PreviewSentence:
      'Buat aplikasi dan layanan yang berbicara secara alami kepada pengguna, sehingga meningkatkan aksesibilitas dan kegunaan.',
    ShortName: 'id-ID-ArdiNeural',
    locale: 'id-ID',
    localeZH: '印度尼西亚语(印度尼西亚)',
  },
  'ps-AF-LatifaNeural': {
    locale: 'ps-AF',
    localeZH: '普什图语(阿富汗)',
    DisplayVoiceName: 'LatifaNeural',
    DisplayName: 'Latifa',
    LocalName: 'لطيفه',
    ShortName: 'ps-AF-LatifaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'it-IT-ElsaNeural': {
    DisplayName: 'Elsa',
    DisplayVoiceName: 'ElsaNeural',
    LocalName: 'Elsa',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-ElsaNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'pt-BR-FranciscaNeural': {
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
    DisplayVoiceName: 'FranciscaNeural',
    DisplayName: 'Francisca',
    LocalName: 'Francisca',
    ShortName: 'pt-BR-FranciscaNeural',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
  },
  'it-IT-RinaldoNeural': {
    DisplayName: 'Rinaldo',
    DisplayVoiceName: 'RinaldoNeural',
    LocalName: 'Rinaldo',
    PreviewSentence: 'Benvenuti nella piattaforma di generazione di contenuti audio di Microsoft.',
    ShortName: 'it-IT-RinaldoNeural',
    locale: 'it-IT',
    localeZH: '意大利语(意大利)',
  },
  'nl-NL-FennaNeural': {
    DisplayName: 'Fenna',
    DisplayVoiceName: 'FennaNeural',
    LocalName: 'Fenna',
    PreviewSentence:
      'Ontwikkel apps en diensten die natuurlijk aanvoelen, waardoor de toegankelijkheid en bruikbaarheid vergroot worden.',
    ShortName: 'nl-NL-FennaNeural',
    locale: 'nl-NL',
    localeZH: '荷兰语(荷兰)',
  },
  'jv-ID-DimasNeural': {
    DisplayName: 'Dimas',
    DisplayVoiceName: 'DimasNeural',
    LocalName: 'Dimas',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'jv-ID-DimasNeural',
    locale: 'jv-ID',
    localeZH: '爪哇语(印度尼西亚)',
  },
  'sl-SI-PetraNeural': {
    locale: 'sl-SI',
    localeZH: '斯洛文尼亚语(斯洛文尼亚)',
    DisplayVoiceName: 'PetraNeural',
    DisplayName: 'Petra',
    LocalName: 'Petra',
    ShortName: 'sl-SI-PetraNeural',
    PreviewSentence:
      'Razvijajte aplikacije in storitve z vrhunsko uporabniško izkušnjo povsem po meri uporabnikov.',
  },
  'jv-ID-SitiNeural': {
    DisplayName: 'Siti',
    DisplayVoiceName: 'SitiNeural',
    LocalName: 'Siti',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'jv-ID-SitiNeural',
    locale: 'jv-ID',
    localeZH: '爪哇语(印度尼西亚)',
  },
  'kk-KZ-DauletNeural': {
    DisplayName: 'Daulet',
    DisplayVoiceName: 'DauletNeural',
    LocalName: 'Дәулет',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'kk-KZ-DauletNeural',
    locale: 'kk-KZ',
    localeZH: '哈萨克语(哈萨克斯坦)',
  },
  'zh-CN-YunyangNeural': {
    locale: 'zh-CN',
    DisplayVoiceName: 'YunyangNeural',
    localeZH: '中文(普通话，简体)',
    DisplayName: 'Yunyang',
    LocalName: '云扬',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
    ShortName: 'zh-CN-YunyangNeural',
  },
  'ko-KR-SeoHyeonNeural': {
    DisplayName: 'SeoHyeon',
    DisplayVoiceName: 'SeoHyeonNeural',
    LocalName: '서현',
    PreviewSentence:
      '사용자들에게 자연스럽게 이야기하는 앱과 서비스를 만들며 접근성과 사용성을 개선시킵니다.',
    ShortName: 'ko-KR-SeoHyeonNeural',
    locale: 'ko-KR',
    localeZH: '韩语(韩国)',
  },
  'zh-CN-henan-YundengNeural': {
    locale: 'zh-CN-henan',
    localeZH: '中文(中原官话河南，简体)',
    DisplayVoiceName: 'YundengNeural',
    DisplayName: 'Yundeng',
    LocalName: '云登',
    ShortName: 'zh-CN-henan-YundengNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'mk-MK-MarijaNeural': {
    DisplayName: 'Marija',
    DisplayVoiceName: 'MarijaNeural',
    LocalName: 'Марија',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'mk-MK-MarijaNeural',
    locale: 'mk-MK',
    localeZH: '马其顿语(北马其顿)',
  },
  'ms-MY-OsmanNeural': {
    DisplayName: 'Osman',
    DisplayVoiceName: 'OsmanNeural',
    LocalName: 'Osman',
    PreviewSentence:
      'Membina aplikasi dan perkhidmatan yang bercakap sememangnya kepada pengguna, meningkatkan kebolehcapaian dan kebolehgunaanya.',
    ShortName: 'ms-MY-OsmanNeural',
    locale: 'ms-MY',
    localeZH: '马来语(马来西亚)',
  },
  'ta-MY-SuryaNeural': {
    locale: 'ta-MY',
    localeZH: '泰米尔语(马来西亚)',
    DisplayVoiceName: 'SuryaNeural',
    DisplayName: 'Surya',
    LocalName: 'சூர்யா',
    ShortName: 'ta-MY-SuryaNeural',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
  },
  'nl-BE-DenaNeural': {
    DisplayName: 'Dena',
    DisplayVoiceName: 'DenaNeural',
    LocalName: 'Dena',
    PreviewSentence:
      'Audio-inhoud Aanmaken laat u toe om visueel de spraakeigenschappen in te stellen in real-time.',
    ShortName: 'nl-BE-DenaNeural',
    locale: 'nl-BE',
    localeZH: '荷兰语(比利时)',
  },
  'ps-AF-GulNawazNeural': {
    DisplayVoiceName: 'GulNawazNeural',
    locale: 'ps-AF',
    DisplayName: 'Gul Nawaz',
    localeZH: '普什图语(阿富汗)',
    LocalName: ' ګل نواز',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'ps-AF-GulNawazNeural',
  },
  'pt-BR-BrendaNeural': {
    DisplayVoiceName: 'BrendaNeural',
    locale: 'pt-BR',
    DisplayName: 'Brenda',
    localeZH: '葡萄牙语(巴西)',
    LocalName: 'Brenda',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-BrendaNeural',
  },
  'pt-BR-DonatoNeural': {
    DisplayName: 'Donato',
    DisplayVoiceName: 'DonatoNeural',
    LocalName: 'Donato',
    locale: 'pt-BR',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    localeZH: '葡萄牙语(巴西)',
    ShortName: 'pt-BR-DonatoNeural',
  },
  'pt-BR-HumbertoNeural': {
    DisplayName: 'Humberto',
    DisplayVoiceName: 'HumbertoNeural',
    LocalName: 'Humberto',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-HumbertoNeural',
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
  },
  'pt-BR-LeticiaNeural': {
    DisplayName: 'Leticia',
    DisplayVoiceName: 'LeticiaNeural',
    LocalName: 'Leticia',
    PreviewSentence:
      'Desenvolver aplicativos e serviços que conversem naturalmente com os usuários, melhorando a acessibilidade e usabilidade.',
    ShortName: 'pt-BR-LeticiaNeural',
    locale: 'pt-BR',
    localeZH: '葡萄牙语(巴西)',
  },
  'zh-TW-HsiaoYuNeural': {
    locale: 'zh-TW',
    localeZH: '中文(台湾普通话)',
    DisplayVoiceName: 'HsiaoYuNeural',
    DisplayName: 'HsiaoYu',
    LocalName: '曉雨',
    ShortName: 'zh-TW-HsiaoYuNeural',
    PreviewSentence: '建構可以和使用者自然對話的應用程式和服務，來提高其方便性和實用性。',
  },
  'ru-RU-DmitryNeural': {
    DisplayName: 'Dmitry',
    DisplayVoiceName: 'DmitryNeural',
    LocalName: 'Дмитрий',
    PreviewSentence:
      'Возможность создавать приложения и сервисы, которые естественным образом общаются с пользователями, улучшая доступность и удобство использования.',
    ShortName: 'ru-RU-DmitryNeural',
    locale: 'ru-RU',
    localeZH: '俄语(俄罗斯)',
  },
  'sl-SI-RokNeural': {
    DisplayName: 'Rok',
    DisplayVoiceName: 'RokNeural',
    LocalName: 'Rok',
    PreviewSentence:
      'Razvijajte aplikacije in storitve z vrhunsko uporabniško izkušnjo povsem po meri uporabnikov.',
    ShortName: 'sl-SI-RokNeural',
    locale: 'sl-SI',
    localeZH: '斯洛文尼亚语(斯洛文尼亚)',
  },
  'so-SO-MuuseNeural': {
    DisplayName: 'Muuse',
    DisplayVoiceName: 'MuuseNeural',
    LocalName: 'Muuse',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'so-SO-MuuseNeural',
    locale: 'so-SO',
    localeZH: '索马里语(索马里)',
  },
  'sw-KE-ZuriNeural': {
    DisplayName: 'Zuri',
    DisplayVoiceName: 'ZuriNeural',
    LocalName: 'Zuri',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'sw-KE-ZuriNeural',
    locale: 'sw-KE',
    localeZH: '斯瓦希里语(肯尼亚)',
  },
  'tr-TR-AhmetNeural': {
    DisplayName: 'Ahmet',
    DisplayVoiceName: 'AhmetNeural',
    LocalName: 'Ahmet',
    PreviewSentence:
      'Kullanıcılarla doğal biçimde konuşan, erişilebilirlik ve kullanılabilirliği iyileştiren uygulama ve servisler geliştirmek.',
    ShortName: 'tr-TR-AhmetNeural',
    locale: 'tr-TR',
    localeZH: '土耳其语(Türkiye)',
  },
  'uz-UZ-MadinaNeural': {
    DisplayName: 'Madina',
    DisplayVoiceName: 'MadinaNeural',
    LocalName: 'Madina',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'uz-UZ-MadinaNeural',
    locale: 'uz-UZ',
    localeZH: '乌兹别克语(乌兹别克斯坦)',
  },
  'yue-CN-XiaoMinNeural': {
    DisplayName: 'XiaoMin',
    DisplayVoiceName: 'XiaoMinNeural',
    LocalName: '晓敏',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'yue-CN-XiaoMinNeural',
    locale: 'yue-CN',
    localeZH: '中文(粤语，简体)',
  },
  'zh-CN-XiaomoNeural': {
    DisplayName: 'Xiaomo',
    DisplayVoiceName: 'XiaomoNeural',
    LocalName: '晓墨',
    PreviewSentence: '通过提供能和用户自然交流的应用程序和服务，以改善其可访问性和可用性。',
    ShortName: 'zh-CN-XiaomoNeural',
    locale: 'zh-CN',
    localeZH: '中文(普通话，简体)',
  },
  'zh-CN-shandong-YunxiangNeural': {
    DisplayName: 'Yunxiang',
    DisplayVoiceName: 'YunxiangNeural',
    LocalName: '云翔',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'zh-CN-shandong-YunxiangNeural',
    locale: 'zh-CN-shandong',
    localeZH: '中文(冀鲁官话，简体)',
  },
  'zh-HK-WanLungNeural': {
    locale: 'zh-HK',
    localeZH: '中文(粤语，繁体)',
    DisplayVoiceName: 'WanLungNeural',
    DisplayName: 'WanLung',
    LocalName: '雲龍',
    ShortName: 'zh-HK-WanLungNeural',
    PreviewSentence: '開發可自然地與用戶溝通的應用程式及服務，以提升其使用度及可用性。',
  },
  'zh-HK-HiuMaanNeural': {
    DisplayName: 'HiuMaan',
    DisplayVoiceName: 'HiuMaanNeural',
    LocalName: '曉曼',
    PreviewSentence: '開發可自然地與用戶溝通的應用程式及服務，以提升其使用度及可用性。',
    ShortName: 'zh-HK-HiuMaanNeural',
    locale: 'zh-HK',
    localeZH: '中文(粤语，繁体)',
  },
  'zu-ZA-ThembaNeural': {
    DisplayName: 'Themba',
    DisplayVoiceName: 'ThembaNeural',
    LocalName: 'Themba',
    PreviewSentence: 'PageLoading to the Microsoft Voice Tuning. ',
    ShortName: 'zu-ZA-ThembaNeural',
    locale: 'zu-ZA',
    localeZH: '祖鲁语(南非)',
  },
};

export const localeMap = {
  'af-ZA': '南非荷兰语(南非)',
  'am-ET': '阿姆哈拉语(埃塞俄比亚)',
  'ar-AE': '阿拉伯语(阿拉伯联合酋长国)',
  'ar-BH': '阿拉伯语(巴林)',
  'ar-DZ': '阿拉伯语(阿尔及利亚)',
  'ar-EG': '阿拉伯语(埃及)',
  'ar-IQ': '阿拉伯语(伊拉克)',
  'ar-JO': '阿拉伯语(约旦)',
  'ar-KW': '阿拉伯语(科威特)',
  'ar-LB': '阿拉伯语(黎巴嫩)',
  'ar-LY': '阿拉伯语(利比亚)',
  'ar-MA': '阿拉伯语(摩洛哥)',
  'ar-OM': '阿拉伯语(阿曼)',
  'ar-QA': '阿拉伯语(卡塔尔)',
  'ar-SA': '阿拉伯语(沙特阿拉伯)',
  'ar-SY': '阿拉伯语(叙利亚)',
  'ar-TN': '阿拉伯语(突尼斯)',
  'ar-YE': '阿拉伯语(也门)',
  'az-AZ': '阿塞拜疆语(阿塞拜疆) ',
  'bg-BG': '保加利亚语(保加利亚)',
  'bn-BD': '孟加拉语(孟加拉)',
  'bn-IN': '孟加拉语(印度)',
  'bs-BA': '波斯尼亚语(波斯尼亚和黑塞哥维那)',
  'ca-ES': '加泰罗尼亚语(西班牙)',
  'cs-CZ': '捷克语(捷克)',
  'cy-GB': '威尔士语(英国)',
  'da-DK': '丹麦语(丹麦)',
  'de-AT': '德语(奥地利)',
  'de-CH': '德语(瑞士)',
  'de-DE': '德语(德国)',
  'el-GR': '希腊语(希腊)',
  'en-AU': '英语(澳大利亚)',
  'en-CA': '英语(加拿大)',
  'en-GB': '英语(英国)',
  'en-HK': '英语(香港特别行政区)',
  'en-IE': '英语(爱尔兰)',
  'en-IN': '英语(印度)',
  'en-KE': '英语(肯尼亚)',
  'en-NG': '英语(尼日利亚)',
  'en-NZ': '英语(新西兰)',
  'en-PH': '英语(菲律宾)',
  'en-SG': '英语(新加坡)',
  'en-TZ': '英语(坦桑尼亚)',
  'en-US': '英语(美国)',
  'en-ZA': '英语(南非)',
  'es-AR': '西班牙语(阿根廷)',
  'es-BO': '西班牙语(玻利维亚)',
  'es-CL': '西班牙语(智利)',
  'es-CO': '西班牙语(哥伦比亚)',
  'es-CR': '西班牙语(哥斯达黎加)',
  'es-CU': '西班牙语(古巴)',
  'es-DO': '西班牙语(多米尼加共和国)',
  'es-EC': '西班牙语(厄瓜多尔)',
  'es-ES': '西班牙语(西班牙)',
  'es-GQ': '西班牙语(赤道几内亚)',
  'es-GT': '西班牙语(危地马拉)',
  'es-HN': '西班牙语(洪都拉斯)',
  'es-MX': '西班牙语(墨西哥)',
  'es-NI': '西班牙语(尼加拉瓜)',
  'es-PA': '西班牙语(巴拿马)',
  'es-PE': '西班牙语(秘鲁)',
  'es-PR': '西班牙语(波多黎各)',
  'es-PY': '西班牙语(巴拉圭)',
  'es-SV': '西班牙语(萨尔瓦多)',
  'es-US': '西班牙语(美国)',
  'es-UY': '西班牙语(乌拉圭)',
  'es-VE': '西班牙语(委内瑞拉)',
  'et-EE': '爱沙尼亚语(爱沙尼亚)',
  'eu-ES': '巴斯克语(巴斯克语)',
  'fa-IR': '波斯语(伊朗)',
  'fi-FI': '芬兰语(芬兰)',
  'fil-PH': '菲律宾语(菲律宾)',
  'fr-BE': '法语(比利时)',
  'fr-CA': '法语(加拿大)',
  'fr-CH': '法语(瑞士)',
  'fr-FR': '法语(法国)',
  'ga-IE': '爱尔兰语(爱尔兰)',
  'gl-ES': '加利西亚语(加利西亚语)',
  'gu-IN': '古吉拉特语(印度)',
  'he-IL': '希伯来语(以色列)',
  'hi-IN': '印地语(印度)',
  'hr-HR': '克罗地亚语(克罗地亚)',
  'hu-HU': '匈牙利语(匈牙利)',
  'hy-AM': '亚美尼亚语(亚美尼亚)',
  'id-ID': '印度尼西亚语(印度尼西亚)',
  'is-IS': '冰岛语(冰岛)',
  'it-IT': '意大利语(意大利)',
  'ja-JP': '日语(日本)',
  'jv-ID': '爪哇语(印度尼西亚)',
  'ka-GE': '格鲁吉亚语(格鲁吉亚)',
  'kk-KZ': '哈萨克语(哈萨克斯坦)',
  'km-KH': '高棉语(柬埔寨)',
  'kn-IN': '埃纳德语(印度)',
  'ko-KR': '韩语(韩国)',
  'lo-LA': '老挝语(老挝) ',
  'lt-LT': '立陶宛语(立陶宛)',
  'lv-LV': '拉脱维亚语(拉脱维亚)',
  'mk-MK': '马其顿语(北马其顿)',
  'ml-IN': '马拉雅拉姆语(印度)',
  'mn-MN': '蒙古语(蒙古)',
  'mr-IN': '马拉地语(印度)',
  'ms-MY': '马来语(马来西亚)',
  'mt-MT': '马耳他语(马耳他)',
  'my-MM': '缅甸语(缅甸)',
  'nb-NO': '书面挪威语(挪威)',
  'ne-NP': '尼泊尔语(尼泊尔)',
  'nl-BE': '荷兰语(比利时)',
  'nl-NL': '荷兰语(荷兰)',
  'pl-PL': '波兰语(波兰)',
  'ps-AF': '普什图语(阿富汗)',
  'pt-BR': '葡萄牙语(巴西)',
  'pt-PT': '葡萄牙语(葡萄牙)',
  'ro-RO': '罗马尼亚语(罗马尼亚)',
  'ru-RU': '俄语(俄罗斯)',
  'si-LK': '僧伽罗语(斯里兰卡)',
  'sk-SK': '斯洛伐克语(斯洛伐克)',
  'sl-SI': '斯洛文尼亚语(斯洛文尼亚)',
  'so-SO': '索马里语(索马里)',
  'sq-AL': '阿尔巴尼亚语(阿尔巴尼亚)',
  'sr-RS': '塞尔维亚语(塞尔维亚)',
  'su-ID': '巽他语(印度尼西亚)',
  'sv-SE': '瑞典语(瑞典)',
  'sw-KE': '斯瓦希里语(肯尼亚)',
  'sw-TZ': '斯瓦希里语(坦桑尼亚)',
  'ta-IN': '泰米尔语(印度)',
  'ta-LK': '泰米尔语(斯里兰卡)',
  'ta-MY': '泰米尔语(马来西亚)',
  'ta-SG': '泰米尔语(新加坡)',
  'te-IN': '泰卢固语(印度)',
  'th-TH': '泰语(泰国)',
  'tr-TR': '土耳其语(Türkiye)',
  'uk-UA': '乌克兰语(乌克兰)',
  'ur-IN': '乌尔都语(印度)',
  'ur-PK': '乌尔都语(巴基斯坦)',
  'uz-UZ': '乌兹别克语(乌兹别克斯坦)',
  'vi-VN': '越南语(越南)',
  'wuu-CN': '中文(吴语，简体)',
  'yue-CN': '中文(粤语，简体)',
  'zh-CN': '中文(普通话，简体)',
  'zh-CN-henan': '中文(中原官话河南，简体)',
  'zh-CN-liaoning': '中文(东北官话，简体)',
  'zh-CN-shaanxi': '中文(中原官话陕西，简体)',
  'zh-CN-shandong': '中文(冀鲁官话，简体)',
  'zh-CN-sichuan': '中文(西南官话，简体)',
  'zh-HK': '中文(粤语，繁体)',
  'zh-TW': '中文(台湾普通话)',
  'zu-ZA': '祖鲁语(南非)',
};

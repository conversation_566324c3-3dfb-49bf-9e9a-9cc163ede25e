import { t } from 'i18next';

import { DEFAULT_LANG } from '../../../constants/locale';
import type { Locales } from '../../../locales/resources';
import type { SettingStore } from '../../../store/setting/index';
import type { GenderEnum } from '../../../types/agent';
import type {
  Config,
  TTSConfig,
  TouchConfig,
  NotificationConfig,
  PrivacyConfig,
  AccessibilityConfig
} from '../../../types/config';
import type {
  GlobalLLMProviderKey,
  ProviderConfig,
  UserModelProviderConfig,
} from '../../../types/provider/modelProvider';
import type { TouchAction, TouchAreaEnum } from '../../../types/touch';
import { isOnServerSide } from '../../../utils/env';
import { merge } from '../../../utils/merge';

export const currentConfig = (s: SettingStore): Config => merge(s.defaultConfig, s.config);

// 新增选择器：通知设置
export const currentNotificationConfig = (s: SettingStore): NotificationConfig => {
  const config = currentConfig(s);
  return config.notifications || {
    email: true,
    push: true,
    chat: true,
    system: false,
  };
};

// 新增选择器：隐私设置
export const currentPrivacyConfig = (s: SettingStore): PrivacyConfig => {
  const config = currentConfig(s);
  return config.privacy || {
    profilePublic: true,
    charactersPublic: true,
    chatHistory: false,
  };
};

// 新增选择器：无障碍设置
export const currentAccessibilityConfig = (s: SettingStore): AccessibilityConfig => {
  const config = currentConfig(s);
  return config.accessibility || {
    fontSize: 'medium',
    highContrast: false,
    reduceMotion: false,
  };
};

export const currentLanguageModelConfig = (s: SettingStore): UserModelProviderConfig => {
  // @ts-ignore
  return currentConfig(s).languageModel || {};
};

export const getProviderConfigById = (provider: string) => (s: SettingStore) =>
  currentLanguageModelConfig(s)[provider as GlobalLLMProviderKey] as ProviderConfig | undefined;

const currentTouchConfig = (s: SettingStore): TouchConfig => {
  // @ts-ignore
  return currentConfig(s).touch || {};
};

const currentLanguage = (s: SettingStore) => {
  const locale = currentConfig(s).locale;

  if (locale === 'auto') {
    if (isOnServerSide) return DEFAULT_LANG;

    return navigator.language as Locales;
  }

  return locale;
};

const currentTTSConfig = (s: SettingStore): TTSConfig => {
  // @ts-ignore
  return currentConfig(s).tts || {};
};

const getTouchActionsByGenderAndArea = (
  s: SettingStore,
  gender: GenderEnum,
  touchArea: TouchAreaEnum,
): TouchAction[] => {
  const touchConfig = currentConfig(s).touch;
  if (!touchConfig) return [];

  // 使用 unknown 进行安全的类型转换
  const genderConfig = touchConfig[gender as unknown as keyof TouchConfig];
  if (!genderConfig) return [];

  const items = genderConfig[touchArea as unknown as keyof typeof genderConfig] as TouchAction[] | undefined;
  if (!items) return [];

  return items.map((item: TouchAction) => ({
    ...item,
    text: t(item.text, { ns: 'role' }) || item.text
  }));
};

export const configSelectors = {
  currentLanguageModelConfig,
  currentTouchConfig,
  currentLanguage,
  currentConfig,
  currentTTSConfig,
  getTouchActionsByGenderAndArea,
  getProviderConfigById,
  // 新增选择器
  currentNotificationConfig,
  currentPrivacyConfig,
  currentAccessibilityConfig,
};

/* 统一聊天页面样式 */
.unified-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--color-bg-container);
}

/* 顶部工具栏 */
.unified-chat-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--color-bg-elevated);
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.character-name {
  font-weight: 500;
  color: var(--color-text);
  font-size: 16px;
}

/* 聊天内容区域 */
.unified-chat-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 加载和错误状态 */
.unified-chat-loading,
.unified-chat-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.unified-chat-loading p,
.unified-chat-error p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* 模式选择对话框 */
.mode-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .unified-chat-toolbar {
    padding: 8px 12px;
  }
  
  .toolbar-center .ant-btn-group {
    transform: scale(0.9);
  }
  
  .character-name {
    font-size: 14px;
  }
}

/* 深色模式适配 */
[data-theme='dark'] .unified-chat-container {
  background: var(--color-bg-layout);
}

[data-theme='dark'] .unified-chat-toolbar {
  background: var(--color-bg-container);
  border-bottom-color: var(--color-border-secondary);
}

/* 动画效果 */
.unified-chat-content {
  transition: all 0.3s ease;
}

.toolbar-center .ant-btn-group .ant-btn {
  transition: all 0.2s ease;
}

.toolbar-center .ant-btn-group .ant-btn:hover {
  transform: translateY(-1px);
}

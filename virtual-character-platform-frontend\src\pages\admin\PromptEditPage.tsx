import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Card, Typography, message, Row, Col, Switch, Spin } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import adminAPI from '../../services/adminAPI';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const PromptEditPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = !!id;

  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  useEffect(() => {
    if (isEditing) {
      fetchPromptTemplate();
    }
  }, [id]);

  const fetchPromptTemplate = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getPromptTemplateDetail(Number(id));
      
      // 设置表单初始值
      form.setFieldsValue({
        name: response.name,
        type: response.type,
        category: response.category,
        content: response.content,
        description: response.description,
        is_active: response.is_active,
        version: response.version,
        variables: JSON.stringify(response.variables || {}, null, 2),
        examples: JSON.stringify(response.examples || {}, null, 2),
      });
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      message.error('获取提示词模板失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values: any) => {
    try {
      setSubmitLoading(true);
      
      // 解析JSON字符串
      let variables = {};
      let examples = {};
      
      try {
        if (values.variables) {
          variables = JSON.parse(values.variables);
        }
        if (values.examples) {
          examples = JSON.parse(values.examples);
        }
      } catch (e) {
        message.error('JSON格式错误，请检查变量或示例的格式');
        setSubmitLoading(false);
        return;
      }

      const promptData = {
        ...values,
        variables,
        examples,
      };

      if (isEditing) {
        await adminAPI.updatePromptTemplate(Number(id), promptData);
        message.success('提示词模板更新成功');
      } else {
        await adminAPI.createPromptTemplate(promptData);
        message.success('提示词模板创建成功');
      }
      
      navigate('/admin/prompts');
    } catch (error) {
      console.error('保存提示词模板失败:', error);
      message.error('保存提示词模板失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div>
      <Title level={3}>{isEditing ? '编辑提示词模板' : '创建提示词模板'}</Title>
      
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            is_active: true,
            type: 'system',
            version: 'v1.0',
            variables: '{}',
            examples: '{}',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="type"
                label="类型"
                rules={[{ required: true, message: '请选择类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Option value="image">图像生成</Option>
                  <Option value="chat">对话</Option>
                  <Option value="system">系统</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="分类"
              >
                <Select placeholder="请选择分类" allowClear>
                  <Option value="character">角色生成</Option>
                  <Option value="personality">性格</Option>
                  <Option value="appearance">外观</Option>
                  <Option value="dialog">对话</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={6}>
              <Form.Item
                name="version"
                label="版本"
              >
                <Input placeholder="请输入版本号" />
              </Form.Item>
            </Col>
            
            <Col span={6}>
              <Form.Item
                name="is_active"
                label="状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <Input placeholder="请输入模板描述" />
          </Form.Item>
          
          <Form.Item
            name="content"
            label="提示词内容"
            rules={[{ required: true, message: '请输入提示词内容' }]}
          >
            <TextArea 
              placeholder="请输入提示词内容" 
              autoSize={{ minRows: 6, maxRows: 12 }}
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="variables"
                label="变量定义 (JSON格式)"
                help="定义模板中可替换的变量，JSON格式"
              >
                <TextArea 
                  placeholder='{ "name": "角色名称", "personality": "性格" }' 
                  autoSize={{ minRows: 4, maxRows: 8 }}
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="examples"
                label="示例 (JSON格式)"
                help="提供使用该模板的示例，JSON格式"
              >
                <TextArea 
                  placeholder='{ "input": "示例输入", "output": "示例输出" }' 
                  autoSize={{ minRows: 4, maxRows: 8 }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              {isEditing ? '更新' : '创建'}
            </Button>
            <Button 
              style={{ marginLeft: 8 }} 
              onClick={() => navigate('/admin/prompts')}
            >
              取消
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default PromptEditPage; 
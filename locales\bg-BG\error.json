{"apiKeyMiss": "OpenAI API ключът е празен, моля добавете персонализиран OpenAI API ключ", "dancePlayError": "Неуспешно възпроизвеждане на танцови файлове, моля опитайте отново по-късно", "error": "Грешка", "errorTip": {"clearSession": "Изчисти съобщенията на сесията", "description": "Проектът в момента е в строеж и не гарантира стабилността на данните. Ако срещнете проблем, можете да опитате", "forgive": " , за причиненото неудобство моля за извинение", "or": "или", "problem": "Страницата срещна малък проблем...", "resetSystem": "Нулирай системните настройки"}, "fileUploadError": "Грешка при качване на файла, моля опитайте отново по-късно", "formValidationFailed": "Неуспешна валидация на формуляра:", "goBack": "Върнете се на началната страница", "openaiError": "Грешка в OpenAI API, моля, проверете дали OpenAI API ключът и крайният адрес са правилни", "reload": "Презареди", "response": {"400": "Съжаляваме, сървърът не разбира вашето запитване, моля, уверете се, че параметрите на запитването ви са правилни", "401": "Съжаляваме, сървърът отказа вашето запитване, вероятно поради недостатъчни права или липса на валидна автентикация", "403": "Съжаляваме, сървърът отказа вашето запитване, нямате права за достъп до това съдържание", "404": "Съжаляваме, сървърът не може да намери страницата или ресурса, който искате, моля, уверете се, че вашият URL е правилен", "405": "Съжаляваме, сървърът не поддържа метода на запитване, който използвате, моля, уверете се, че методът на запитване е правилен", "406": "Съжаляваме, сървърът не може да завърши запитването въз основа на характеристиките на съдържанието, което искате", "407": "Съжаляваме, необходимо е да извършите автентикация на прокси, за да продължите с това запитване", "408": "Съжаляваме, сървърът изтече времето за изчакване на запитването, моля, проверете вашата интернет връзка и опитайте отново", "409": "Съжаляваме, запитването е в конфликт и не може да бъде обработено, вероятно поради несъвместимост на състоянието на ресурса с запитването", "410": "Съжаляваме, ресурсът, който искате, е бил премахнат завинаги и не може да бъде намерен", "411": "Съжаляваме, сървърът не може да обработи запитване без валидна дължина на съдържанието", "412": "Съжаляваме, вашето запитване не отговаря на условията на сървъра и не може да бъде завършено", "413": "Съжаляваме, обемът на данните в запитването ви е твърде голям, сървърът не може да го обработи", "414": "Съжаляваме, URI на вашето запитване е твърде дълъг, сървърът не може да го обработи", "415": "Съжаляваме, сървърът не може да обработи медийната форма, приложена към запитването", "416": "Съжаляваме, сървърът не може да удовлетвори обхвата на вашето запитване", "417": "Съжаляваме, сървърът не може да удовлетвори вашите очаквания", "422": "Съжаляваме, форматът на вашето запитване е правилен, но поради семантични грешки не може да бъде обработен", "423": "Съжаляваме, ресурсът, който искате, е заключен", "424": "Съжаляваме, предишното запитване е неуспешно, което пречи на текущото запитване", "426": "Съжаляваме, сървърът изисква вашият клиент да бъде обновен до по-висока версия на протокола", "428": "Съжаляваме, сървърът изисква предварителни условия, вашето запитване трябва да съдържа правилните заглавия на условията", "429": "Съжаляваме, вашето запитване е твърде много, сървърът е малко уморен, моля, опитайте отново по-късно", "431": "Съжаляваме, полетата на заглавията на вашето запитване са твърде големи, сървърът не може да ги обработи", "451": "Съжаляваме, по правни причини, сървърът отказва да предостави този ресурс", "500": "Съжаляваме, сървърът изглежда е срещнал някои трудности и временно не може да завърши вашето запитване, моля, опитайте отново по-късно", "501": "Съжаляваме, сървърът все още не знае как да обработи това запитване, моля, уверете се, че вашата операция е правилна", "502": "Съжаляваме, сървърът изглежда е загубил посоката и временно не може да предостави услуга, моля, опитайте отново по-късно", "503": "Съжаляваме, сървърът в момента не може да обработи вашето запитване, вероятно поради претоварване или поддръжка, моля, опитайте отново по-късно", "504": "Съжаляваме, сървърът не е получил отговор от upstream сървъра, моля, опитайте отново по-късно", "505": "Съжаляваме, сървърът не поддържа версията на HTTP, която използвате, моля, обновете и опитайте отново", "506": "Съжаляваме, конфигурацията на сървъра е проблемна, моля, свържете се с администратора за разрешаване", "507": "Съжаляваме, сървърът няма достатъчно пространство за съхранение, за да обработи вашето запитване, моля, опитайте отново по-късно", "509": "Съжаляваме, пропускателната способност на сървъра е изчерпана, моля, опитайте отново по-късно", "510": "Съжаляваме, сървърът не поддържа разширената функция на запитването, моля, свържете се с администратора", "524": "Съжаляваме, сървърът изтече времето за изчакване при изчакване на отговор, вероятно поради бавен отговор, моля, опитайте отново по-късно", "AgentRuntimeError": "Lobe AI Runtime възникна грешка, моля, проверете информацията по-долу или опитайте отново", "FreePlanLimit": "В момента сте безплатен потребител и не можете да използвате тази функция, моля, надстройте до платен план, за да продължите", "InvalidAccessCode": "Паролата е неправилна или празна, моля, въведете правилната парола за достъп или добавете персонализиран API ключ", "InvalidBedrockCredentials": "Автентикацията на Bedrock не премина, моля, проверете AccessKeyId/SecretAccessKey и опитайте отново", "InvalidClerkUser": "Съжаляваме, в момента не сте влезли, моля, влезте или се регистрирайте, за да продължите", "InvalidGithubToken": "Github PAT е неправилен или празен, моля, проверете Github PAT и опитайте отново", "InvalidOllamaArgs": "Ollama конфигурацията е неправилна, моля, проверете Ollama конфигурацията и опитайте отново", "InvalidProviderAPIKey": "{{provider}} API ключът е неправилен или празен, моля, проверете {{provider}} API ключа и опитайте отново", "LocationNotSupportError": "Съжаляваме, вашият регион не поддържа тази услуга на модела, вероятно поради регионални ограничения или недостъпност на услугата. Моля, уверете се, че текущият регион поддържа използването на тази услуга или опитайте да превключите на друг регион и опитайте отново.", "OllamaBizError": "Възникна грешка при запитването на услугата Ollama, моля, проверете информацията по-долу или опитайте отново", "OllamaServiceUnavailable": "Свързването с услугата Ollama не успя, моля, проверете дали Ollama работи нормално или дали е правилно настроена конфигурацията за крос-домейн", "PermissionDenied": "Съжаляваме, нямате права за достъп до тази услуга, моля, проверете дали вашият ключ има права за достъп", "PluginApiNotFound": "Съжаляваме, в описателния файл на плъгина не съществува такова API, моля, проверете дали вашият метод на запитване съвпада с API на плъгина", "PluginApiParamsError": "Съжаляваме, проверката на входните параметри на запитването на плъгина не премина, моля, проверете дали входните параметри съвпадат с описателната информация на API", "PluginFailToTransformArguments": "Съжаляваме, неуспешно преобразуване на параметрите на извикването на плъгина, моля, опитайте да генерирате отново съобщението на помощника или опитайте отново след смяна на AI модел с по-силни способности за извикване на инструменти", "PluginGatewayError": "Съжаляваме, възникна грешка в шлюза на плъгина, моля, проверете дали конфигурацията на шлюза на плъгина е правилна", "PluginManifestInvalid": "Съжаляваме, проверката на описателния файл на плъгина не премина, моля, проверете дали форматът на описателния файл е правилен", "PluginManifestNotFound": "Съжаляваме, сървърът не можа да намери описателния файл на плъгина (manifest.json), моля, проверете дали адресът на описателния файл на плъгина е правилен", "PluginMarketIndexInvalid": "Съжаляваме, проверката на индекса на плъгини не премина, моля, проверете дали форматът на индексния файл е правилен", "PluginMarketIndexNotFound": "Съжаляваме, сървърът не можа да намери индекса на плъгини, моля, проверете дали адресът на индекса е правилен", "PluginMetaInvalid": "Съжаляваме, проверката на мета информацията на плъгина не премина, моля, проверете дали форматът на мета информацията на плъгина е правилен", "PluginMetaNotFound": "Съжаляваме, не беше намерен плъгин в индекса, моля, проверете конфигурационната информация на плъгина в индекса", "PluginOpenApiInitError": "Съжаляваме, инициализацията на клиента на OpenAPI не успя, моля, проверете дали конфигурационната информация на OpenAPI е правилна", "PluginServerError": "Възникна грешка при връщането на запитването от сървъра на плъгина, моля, проверете описателния файл на плъгина, конфигурацията на плъгина или реализацията на сървъра въз основа на информацията за грешката по-долу", "PluginSettingsInvalid": "Този плъгин трябва да бъде правилно конфигуриран, за да може да се използва, моля, проверете дали вашата конфигурация е правилна", "ProviderBizError": "Възникна грешка при запитването на услугата {{provider}}, моля, проверете информацията по-долу или опитайте отново", "QuotaLimitReached": "Съжаляваме, текущото използване на токени или брой запитвания е достигнало лимита на квотата за този ключ, моля, увеличете квотата на ключа или опитайте отново по-късно", "StreamChunkError": "Грешка при анализа на съобщенията на поточната заявка, моля, проверете дали текущият API интерфейс отговаря на стандартите или се свържете с вашия API доставчик за консултация", "SubscriptionPlanLimit": "Вашият абонаментен лимит е изчерпан и не можете да използвате тази функция, моля, надстройте до по-висок план или закупете пакет ресурси, за да продължите", "UnknownChatFetchError": "Съжаляваме, възникна неизвестна грешка при запитването, моля, проверете информацията по-долу или опитайте отново"}, "s3envError": "S3 променливата на средата не е напълно настроена, моля, проверете вашите променливи на средата", "serverError": "Сървърна грешка, моля свържете се с администратора", "triggerError": "Възникна грешка", "ttsTransformFailed": "Неуспешно преобразуване на реч, моля, проверете мрежата или опитайте отново след като активирате клиентското извикване в настройките.", "unknownError": "Неизвестна грешка", "unlock": {"addProxyUrl": "Добавете OpenAI прокси адрес (по избор)", "apiKey": {"description": "Въведете вашия {{name}} API ключ, за да започнете сесия", "title": "Използвайте персонализиран {{name}} API ключ"}, "closeMessage": "Затвори съобщението", "confirm": "Потвърдете и опитайте отново", "oauth": {"description": "Администраторът е активирал единен вход, кликнете върху бутона по-долу, за да влезете и отключите приложението", "success": "Успешно влизане", "title": "Вход в акаунт", "welcome": "Добре дошли!"}, "password": {"description": "Администраторът е активирал криптиране на приложението, въведете паролата на приложението, за да отключите приложението. Паролата трябва да бъде въведена само веднъж", "placeholder": "Моля, въведете паролата", "title": "Въведете паролата, за да отключите приложението"}, "tabs": {"apiKey": "Персонализ<PERSON><PERSON><PERSON><PERSON> <PERSON> ключ", "password": "Парола"}}}
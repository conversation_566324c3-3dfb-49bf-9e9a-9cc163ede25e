/* 文字方向修复样式 */
/* 这个文件专门用于修复文字竖直显示的问题 */

/* 全局重置 - 强制所有元素使用水平文字方向 */
*,
*::before,
*::after {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 特别针对可能出现问题的元素 */
html,
body,
#root,
#app,
.ant-app,
.ant-layout,
.ant-layout-sider,
.ant-menu,
.ant-menu-item,
.ant-typography,
.ant-card,
.ant-button,
.ant-input,
.ant-select,
div,
span,
p,
h1, h2, h3, h4, h5, h6,
a,
li,
ul,
ol {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: left !important;
}

/* 特别针对侧边栏 */
.ant-layout-sider,
.ant-layout-sider * {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 特别针对菜单项 */
.ant-menu-item,
.ant-menu-item *,
.ant-menu-item-only-child,
.ant-menu-item-only-child * {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

/* 特别针对文字内容 */
.ant-typography,
.ant-typography *,
span,
p {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  display: inline !important;
}

/* div元素不强制设置display，让它们保持原有的布局 */
div {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 修复可能的flex布局问题 */
.ant-menu-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

/* 确保图标和文字水平排列 */
.ant-menu-item .anticon {
  display: inline-block !important;
  margin-right: 8px !important;
}

/* 修复主要内容区域 */
.homepage-container,
.homepage-container *,
.welcome-section,
.welcome-section *,
.character-card,
.character-card * {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 修复标题文字 */
.welcome-title,
.welcome-description,
.character-card .ant-card-meta-title {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: center !important;
}

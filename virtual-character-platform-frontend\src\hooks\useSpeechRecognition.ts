import { useCallback, useEffect, useState, useRef } from 'react';

interface SpeechRecognitionHook {
  isListening: boolean;           // 是否正在监听
  transcript: string;             // 识别结果（内部使用，不显示）
  startListening: () => void;     // 开始监听
  stopListening: () => void;      // 停止监听
  resetTranscript: () => void;    // 重置结果
  isSupported: boolean;           // 浏览器是否支持
  error: string | null;           // 错误信息
}

interface UseSpeechRecognitionProps {
  onResult?: (transcript: string, isFinal: boolean) => void;
  onError?: (error: string) => void;
  continuous?: boolean;           // 是否连续识别
  interimResults?: boolean;       // 是否显示中间结果
  language?: string;              // 识别语言
}

// 扩展Window接口以支持语音识别API
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export const useSpeechRecognition = (props: UseSpeechRecognitionProps = {}): SpeechRecognitionHook => {
  const {
    onResult,
    onError,
    continuous = true,
    interimResults = false,  // 默认隐藏中间结果，符合沉浸式需求
    language = 'zh-CN'
  } = props;

  const [isListening, setIsListening] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<string>('');
  const [recognition, setRecognition] = useState<any>(null);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 使用useRef保存回调函数的引用，避免useEffect重复执行
  const onResultRef = useRef(onResult);
  const onErrorRef = useRef(onError);

  // 更新ref的值
  useEffect(() => {
    onResultRef.current = onResult;
    onErrorRef.current = onError;
  }, [onResult, onError]);

  // 检查浏览器支持
  const checkBrowserSupport = useCallback(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    return !!SpeechRecognition;
  }, []);

  // 处理识别结果
  const handleResult = useCallback((event: any) => {
    try {
      const results = event.results;
      if (!results || results.length === 0) {
        console.log('语音识别结果为空');
        return;
      }

      const lastResult = results[results.length - 1];
      if (!lastResult || !lastResult[0]) {
        console.log('语音识别结果格式错误');
        return;
      }

      const transcriptText = lastResult[0].transcript;
      const isFinal = lastResult.isFinal;

      console.log('语音识别结果:', transcriptText, '是否最终:', isFinal);

      // 只在最终结果时更新transcript状态，避免频繁更新导致无限循环
      if (isFinal) {
        setTranscript(transcriptText);
      }

      if (onResultRef.current) {
        onResultRef.current(transcriptText, isFinal);
      }
    } catch (error) {
      console.error('处理语音识别结果时出错:', error);
    }
  }, []);

  // 处理识别开始
  const handleStart = useCallback(() => {
    console.log('语音识别已开始监听');
    setIsListening(true);
  }, []);

  // 处理识别结束
  const handleEnd = useCallback(() => {
    console.log('语音识别结束');
    setIsListening(false);
  }, []);

  // 处理识别错误
  const handleError = useCallback((event: any) => {
    let errorMessage = '';

    switch (event.error) {
      case 'not-allowed':
        errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问';
        break;
      case 'no-speech':
        errorMessage = '未检测到语音输入，请重试';
        break;
      case 'audio-capture':
        errorMessage = '麦克风无法访问，请检查设备连接';
        break;
      case 'network':
        errorMessage = '网络连接错误，请检查网络连接';
        break;
      case 'service-not-allowed':
        errorMessage = '语音识别服务不可用';
        break;
      case 'bad-grammar':
        errorMessage = '语音识别配置错误';
        break;
      case 'language-not-supported':
        errorMessage = '不支持当前语言的语音识别';
        break;
      default:
        errorMessage = `语音识别错误: ${event.error}`;
    }

    console.error('语音识别错误:', event.error, errorMessage);
    setError(errorMessage);
    setIsListening(false);

    if (onErrorRef.current) {
      onErrorRef.current(errorMessage);
    }
  }, []);

  // 开始监听
  const startListening = useCallback(async () => {
    if (!recognition) {
      console.error('语音识别实例未初始化');
      return;
    }

    if (isListening) {
      console.log('语音识别已在进行中');
      return;
    }

    try {
      setError(null);

      // 首先请求麦克风权限
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('麦克风权限已获取');
      } catch (permissionError) {
        const errorMessage = '需要麦克风权限才能使用语音识别功能，请在浏览器设置中允许麦克风访问';
        console.error('麦克风权限被拒绝:', permissionError);
        setError(errorMessage);
        if (onErrorRef.current) {
          onErrorRef.current(errorMessage);
        }
        return;
      }

      console.log('准备启动语音识别...');

      // 确保之前的识别已停止
      try {
        recognition.stop();
      } catch (e) {
        // 忽略停止错误
      }

      // 短暂延迟后启动
      setTimeout(() => {
        try {
          recognition.start();
          console.log('语音识别启动命令已发送');
        } catch (startError) {
          console.error('启动语音识别时出错:', startError);
          setError('启动语音识别失败');
          if (onErrorRef.current) {
            onErrorRef.current('启动语音识别失败');
          }
        }
      }, 100);

    } catch (err) {
      const errorMessage = '启动语音识别失败';
      console.error(errorMessage, err);
      setError(errorMessage);
      if (onErrorRef.current) {
        onErrorRef.current(errorMessage);
      }
    }
  }, [recognition, isListening]);

  // 停止监听
  const stopListening = useCallback(() => {
    if (!recognition || !isListening) return;

    try {
      recognition.stop();
      setIsListening(false);
      console.log('停止语音识别');
    } catch (err) {
      console.error('停止语音识别失败', err);
    }
  }, [recognition, isListening]);

  // 重置转录结果
  const resetTranscript = useCallback(() => {
    setTranscript('');
    setError(null);
  }, []);

  // 初始化语音识别
  useEffect(() => {
    const supported = checkBrowserSupport();
    setIsSupported(supported);

    if (!supported) {
      const errorMessage = '当前浏览器不支持语音识别功能';
      setError(errorMessage);
      console.warn(errorMessage);
      return;
    }

    // 避免重复创建实例
    if (recognition) {
      console.log('语音识别实例已存在，跳过创建');
      return;
    }

    console.log('创建新的语音识别实例');
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognitionInstance = new SpeechRecognition();

    // 配置语音识别参数
    recognitionInstance.continuous = continuous;
    recognitionInstance.interimResults = interimResults;
    recognitionInstance.lang = language;
    recognitionInstance.maxAlternatives = 1;

    // 绑定事件监听器
    recognitionInstance.addEventListener('start', handleStart);
    recognitionInstance.addEventListener('result', handleResult);
    recognitionInstance.addEventListener('end', handleEnd);
    recognitionInstance.addEventListener('error', handleError);

    setRecognition(recognitionInstance);
    console.log('语音识别实例创建完成');

    // 清理函数
    return () => {
      console.log('清理语音识别实例');
      if (recognitionInstance) {
        try {
          recognitionInstance.stop();
        } catch (e) {
          // 忽略停止错误
        }
        recognitionInstance.removeEventListener('start', handleStart);
        recognitionInstance.removeEventListener('result', handleResult);
        recognitionInstance.removeEventListener('end', handleEnd);
        recognitionInstance.removeEventListener('error', handleError);
      }
    };
  }, [continuous, interimResults, language]);

  return {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
    isSupported,
    error
  };
};

{"apiKeyMiss": "La clave de API de OpenAI está vacía, por favor añade una clave de API de OpenAI personalizada", "dancePlayError": "Error al reproducir el archivo de danza, por favor inténtelo de nuevo más tarde", "error": "Error", "errorTip": {"clearSession": "Eliminar mensajes de sesión", "description": "El proyecto está actualmente en construcción, no se garantiza la estabilidad de los datos. Si encuentras un problema, puedes intentar", "forgive": "te pedimos disculpas por los inconvenientes causados", "or": "o", "problem": "La página ha encontrado un pequeño problema...", "resetSystem": "Restablecer la configuración del sistema"}, "fileUploadError": "Error al subir el archivo, por favor inténtelo de nuevo más tarde", "formValidationFailed": "Error de validación del formulario:", "goBack": "Volver a la página de inicio", "openaiError": "Error de la API de OpenAI, por favor verifica si la clave de API de OpenAI y el endpoint son correctos", "reload": "Recargar", "response": {"400": "<PERSON> sentimos, el servidor no entiende su solicitud. Por favor, confirme que los parámetros de su solicitud son correctos.", "401": "<PERSON> sentimo<PERSON>, el servidor ha rechazado su solicitud, posiblemente debido a que no tiene suficientes permisos o no ha proporcionado una autenticación válida.", "403": "<PERSON> sentimo<PERSON>, el servidor ha rechazado su solicitud, no tiene permiso para acceder a este contenido.", "404": "Lo sentimos, el servidor no puede encontrar la página o recurso que solicitó. Por favor, confirme que su URL es correcta.", "405": "<PERSON> sentimos, el servidor no soporta el método de solicitud que está utilizando. Por favor, confirme que su método de solicitud es correcto.", "406": "Lo sentimos, el servidor no puede completar la solicitud según las características del contenido que ha solicitado.", "407": "<PERSON> sentimo<PERSON>, necesita autenticación de proxy para continuar con esta solicitud.", "408": "<PERSON> sentimo<PERSON>, el servidor ha agotado el tiempo de espera mientras esperaba la solicitud. Por favor, verifique su conexión a internet y vuelva a intentarlo.", "409": "Lo sentimos, hay un conflicto en la solicitud que no se puede procesar, posiblemente debido a que el estado del recurso es incompatible con la solicitud.", "410": "Lo sentimos, el recurso que solicitó ha sido eliminado permanentemente y no se puede encontrar.", "411": "Lo sentimos, el servidor no puede procesar una solicitud que no contiene una longitud de contenido válida.", "412": "Lo sentimos, su solicitud no cumplió con las condiciones del servidor y no se puede completar.", "413": "<PERSON> sentimos, la cantidad de datos de su solicitud es demasiado grande, el servidor no puede procesarla.", "414": "<PERSON> sentimo<PERSON>, la URI de su solicitud es demasiado larga, el servidor no puede procesarla.", "415": "<PERSON> sentimo<PERSON>, el servidor no puede procesar el formato de medios adjunto a la solicitud.", "416": "Lo sentimos, el servidor no puede satisfacer el rango que solicitó.", "417": "<PERSON> sentimos, el servidor no puede satisfacer sus expectativas.", "422": "<PERSON> sentimo<PERSON>, su formato de solicitud es correcto, pero contiene errores semánticos y no se puede responder.", "423": "Lo sentimos, el recurso que solicitó está bloqueado.", "424": "Lo sentimos, la solicitud actual no se puede completar debido a que la solicitud anterior falló.", "426": "<PERSON> sentimos, el servidor requiere que su cliente se actualice a una versión de protocolo más alta.", "428": "<PERSON> sentimo<PERSON>, el servidor requiere condiciones previas, su solicitud debe incluir los encabezados de condiciones correctos.", "429": "<PERSON> sentimo<PERSON>, ha realizado demasiadas solicitudes, el servidor está un poco sobrecargado, por favor, inténtelo de nuevo más tarde.", "431": "<PERSON> sentimo<PERSON>, el campo de encabezado de su solicitud es demasiado grande, el servidor no puede procesarlo.", "451": "Lo sentimos, por razones legales, el servidor se niega a proporcionar este recurso.", "500": "<PERSON> sentimo<PERSON>, el servidor parece haber encontrado algunas dificultades y no puede completar su solicitud en este momento. Por favor, inténtelo de nuevo más tarde.", "501": "<PERSON> sentimo<PERSON>, el servidor aún no sabe cómo manejar esta solicitud. Por favor, confirme que su operación es correcta.", "502": "<PERSON> sentimo<PERSON>, el servidor parece haberse perdido, no puede proporcionar el servicio en este momento. Por favor, inténtelo de nuevo más tarde.", "503": "<PERSON> sentimo<PERSON>, el servidor actualmente no puede procesar su solicitud, posiblemente debido a sobrecarga o mantenimiento en curso. Por favor, inténtelo de nuevo más tarde.", "504": "<PERSON> sentimos, el servidor no recibió respuesta del servidor ascendente. Por favor, inténtelo de nuevo más tarde.", "505": "<PERSON> sentimos, el servidor no soporta la versión HTTP que está utilizando. Por favor, actualice y vuelva a intentarlo.", "506": "<PERSON> sentimo<PERSON>, hay un problema de configuración en el servidor. Por favor, contacte al administrador para resolverlo.", "507": "<PERSON> sentimos, el servidor no tiene suficiente espacio de almacenamiento para procesar su solicitud. Por favor, inténtelo de nuevo más tarde.", "509": "<PERSON> sentimo<PERSON>, el ancho de banda del servidor se ha agotado. Por favor, inténtelo de nuevo más tarde.", "510": "<PERSON> sentimo<PERSON>, el servidor no soporta la funcionalidad de extensión solicitada. Por favor, contacte al administrador.", "524": "<PERSON> sentimo<PERSON>, el servidor ha agotado el tiempo de espera mientras esperaba una respuesta, posiblemente debido a que la respuesta es demasiado lenta. Por favor, inténtelo de nuevo más tarde.", "AgentRuntimeError": "Hubo un error en la ejecución de Lobe AI Runtime. Por favor, revise la información a continuación o intente de nuevo.", "FreePlanLimit": "Actualmente es un usuario gratuito y no puede utilizar esta función. Por favor, actualice a un plan de pago para continuar utilizando.", "InvalidAccessCode": "La contraseña es incorrecta o está vacía. Por favor, ingrese la contraseña de acceso correcta o agregue una clave API personalizada.", "InvalidBedrockCredentials": "La autenticación de Bedrock no pasó. Por favor, verifique AccessKeyId/SecretAccessKey y vuelva a intentarlo.", "InvalidClerkUser": "Lo sentimos, actualmente no ha iniciado sesión. Por favor, inicie sesión o registre una cuenta antes de continuar.", "InvalidGithubToken": "El PAT de Github es incorrecto o está vacío, por favor verifica el PAT de Github y vuelve a intentarlo", "InvalidOllamaArgs": "La configuración de Ollama no es correcta. Por favor, verifique la configuración de Ollama y vuelva a intentarlo.", "InvalidProviderAPIKey": "{{provider}} La clave API es incorrecta o está vacía. Por favor, verifique la clave API de {{provider}} y vuelva a intentarlo.", "LocationNotSupportError": "<PERSON> sentimo<PERSON>, su ubicación actual no soporta este servicio de modelo, posiblemente debido a restricciones regionales o que el servicio no está habilitado. Por favor, confirme si su ubicación actual soporta este servicio o intente cambiar a otra ubicación y vuelva a intentarlo.", "OllamaBizError": "Hubo un error al solicitar el servicio de Ollama. Por favor, revise la información a continuación o intente de nuevo.", "OllamaServiceUnavailable": "La conexión al servicio de Ollama falló. Por favor, verifique si Ollama está funcionando correctamente o si la configuración de CORS de Ollama está configurada correctamente.", "PermissionDenied": "Lo sentimos, no tiene permiso para acceder a este servicio. Por favor, verifique que su clave tenga permisos de acceso.", "PluginApiNotFound": "Lo sentimos, no existe esta API en el manifiesto del plugin. Por favor, verifique que su método de solicitud coincida con la API del manifiesto del plugin.", "PluginApiParamsError": "Lo sentimos, la verificación de los parámetros de entrada de la solicitud del plugin no pasó. Por favor, verifique que los parámetros coincidan con la descripción de la API.", "PluginFailToTransformArguments": "<PERSON> sentimo<PERSON>, la interpretación de los parámetros de llamada del plugin falló. Por favor, intente regenerar el mensaje del asistente o cambie a un modelo de IA más potente con la capacidad de Tools Calling y vuelva a intentarlo.", "PluginGatewayError": "<PERSON> sentimo<PERSON>, hubo un error en la puerta de enlace del plugin. Por favor, verifique que la configuración de la puerta de enlace del plugin sea correcta.", "PluginManifestInvalid": "Lo sentimos, la verificación del manifiesto del plugin no pasó. Por favor, verifique que el formato del manifiesto sea correcto.", "PluginManifestNotFound": "<PERSON> sentimo<PERSON>, el servidor no encontró el manifiesto del plugin (manifest.json). Por favor, verifique que la dirección del archivo de descripción del plugin sea correcta.", "PluginMarketIndexInvalid": "<PERSON> sentimo<PERSON>, la verificación del índice del plugin no pasó. Por favor, verifique que el formato del archivo del índice sea correcto.", "PluginMarketIndexNotFound": "<PERSON> sentimo<PERSON>, el servidor no encontró el índice del plugin. Por favor, verifique que la dirección del índice sea correcta.", "PluginMetaInvalid": "<PERSON> sentimo<PERSON>, la verificación de la metainformación del plugin no pasó. Por favor, verifique que el formato de la metainformación del plugin sea correcto.", "PluginMetaNotFound": "Lo sentimos, no se encontró el plugin en el índice. Por favor, verifique la información de configuración del plugin en el índice.", "PluginOpenApiInitError": "Lo sentimos, la inicialización del cliente OpenAPI falló. Por favor, verifique que la información de configuración de OpenAPI sea correcta.", "PluginServerError": "Hubo un error en la respuesta del servidor del plugin. Por favor, verifique su archivo de descripción del plugin, la configuración del plugin o la implementación del servidor según la información de error a continuación.", "PluginSettingsInvalid": "Este plugin necesita ser configurado correctamente antes de poder ser utilizado. Por favor, verifique que su configuración sea correcta.", "ProviderBizError": "Hubo un error al solicitar el servicio de {{provider}}. Por favor, revise la información a continuación o intente de nuevo.", "QuotaLimitReached": "Lo sentimos, el uso actual de tokens o el número de solicitudes ha alcanzado el límite de cuota de esta clave. Por favor, aumente la cuota de esta clave o intente de nuevo más tarde.", "StreamChunkError": "Error en la interpretación del bloque de mensajes de la solicitud en streaming. Por favor, verifique que la interfaz API actual cumpla con las normas estándar o contacte a su proveedor de API para consultas.", "SubscriptionPlanLimit": "Su cuota de suscripción se ha agotado y no puede utilizar esta función. Por favor, actualice a un plan superior o compre un paquete de recursos para continuar utilizando.", "UnknownChatFetchError": "<PERSON> sentimos, se encontró un error desconocido en la solicitud. Por favor, revise la información a continuación o intente de nuevo."}, "s3envError": "Las variables de entorno de S3 no están completamente configuradas, por favor revise sus variables de entorno", "serverError": "<PERSON><PERSON><PERSON> del servidor, por favor contacte al administrador", "triggerError": "<PERSON><PERSON><PERSON>", "ttsTransformFailed": "La conversión de voz ha fallado, por favor verifica la conexión a internet o intenta habilitar la llamada del cliente en la configuración.", "unknownError": "Error descon<PERSON>", "unlock": {"addProxyUrl": "Agregar dirección de proxy de OpenAI (opcional)", "apiKey": {"description": "Introduce tu {{name}} API Key para comenzar la conversación", "title": "Usar {{name}} API Key personalizada"}, "closeMessage": "<PERSON><PERSON>r aviso", "confirm": "Confirmar y reintentar", "oauth": {"description": "El administrador ha habilitado la autenticación unificada, haz clic en el botón de abajo para iniciar sesión y desbloquear la aplicación", "success": "Inicio de sesión exitoso", "title": "In<PERSON><PERSON>", "welcome": "¡Bienvenido!"}, "password": {"description": "El administrador ha habilitado la encriptación de la aplicación, introduce la contraseña de la aplicación para desbloquearla. La contraseña solo necesita ser ingresada una vez", "placeholder": "Introduce la contraseña", "title": "Introducir contraseña para desbloquear la aplicación"}, "tabs": {"apiKey": "API Key personalizada", "password": "Contraseña"}}}
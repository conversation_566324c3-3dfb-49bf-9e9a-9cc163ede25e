[{"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e831864b84b428b8d322d0", "name": "Austism/chronos-hermes-13b", "display_name": "Chronos Hermes (13B)", "display_type": "chat", "description": "This model is a 75/25 merge of Chronos (13B) and Nous Her<PERSON> (13B) models resulting in having a great ability to produce evocative storywriting and follow a narrative.", "license": "other", "creator_organization": "Austism", "hardware_label": "2x A100 80GB", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "config": {"stop": ["</s>"], "prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}", "add_generation_prompt": true}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x6966f4A2caf8efaE98C251C3C15210333578C158": 1}, "asks_updated": "2024-05-11T12:20:53.91543414Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 34.53333333333333, "throughput_out": 0.5333333333333333, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.043478260869565216, "qps": 0.06666666666666667, "throughput_in": 34.53333333333333, "throughput_out": 0.5333333333333333, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6560b993b56cf1e0970c9b1a", "name": "BAAI/bge-base-en-v1.5", "display_name": "BAAI-Bge-Base-1p5", "display_type": "embedding", "description": "bge is short for BAAI general embedding, it maps any text to a low-dimensional dense vector using FlagEmbedding", "license": "MIT", "creator_organization": "BAAI", "hardware_label": "A40", "pricing_tier": "Featured", "num_parameters": 109482240, "release_date": "2023-11-15T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2023-11-24T14:56:19.475Z", "update_at": "2023-12-22T03:26:23.802Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}, {"avzone": "us-central-1a", "cluster": "sassyseal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x18530141Cf50876b091f3D4B9FA3Bb7F7d24d20a": 1, "0x4Aa34b8d92E163D7d7527e17B92Bc83C2F7149a3": 1, "0x8BEE38fD0697C19F06411AaEEea935073005168c": 1, "0xe2d9B1fd3EfBA3fEB7cfc84FD5d9c1621dA3dEB9": 1}, "asks_updated": "2024-05-11T03:12:34.75168084Z", "gpus": {"": 0}, "qps": 3.0666666666666664, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 254, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0.008075842696629214, "qps": 1.7333333333333334, "throughput_in": 137.2, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.008046875, "qps": 1.3333333333333333, "throughput_in": 116.8, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6560b938b56cf1e0970c9b19", "name": "BAAI/bge-large-en-v1.5", "display_name": "BAAI-Bge-Large-1p5", "display_type": "embedding", "description": "bge is short for BAAI general embedding, it maps any text to a low-dimensional dense vector using FlagEmbedding", "license": "MIT", "creator_organization": "BAAI", "hardware_label": "A40", "pricing_tier": "Featured", "num_parameters": 335141888, "release_date": "2023-11-15T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "pricing": {"hourly": 0, "input": 4, "output": 4, "finetune": 0, "base": 0}, "created_at": "2023-11-24T14:54:48.986Z", "update_at": "2023-12-22T03:27:18.465Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 4, "num_bids": 0, "num_running": 0, "asks": {"0x5ED0BA75594E3429628087603D628838bE686ebF": 1, "0x7153b499cA3C6cc2Bb60Dd5DBF8ba0C6B2532c63": 1, "0xD2a55c4769d98e7Df019A3858FA37036BbbAB5cE": 1, "0xF6122ecAc4D8d96a95E00d6eC8a838f4525D8124": 1}, "asks_updated": "2024-05-11T03:00:56.495347114Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f78861d683768020b9f005", "name": "Gryphe/MythoMax-L2-13b", "display_name": "MythoMax-L2 (13B)", "display_type": "chat", "description": "MythoLogic-L2 and Huginn merge using a highly experimental tensor type merge technique. The main difference with MythoMix is that I allowed more of Huginn to intermingle with the single tensors located at the front and end of a model", "license": "other", "creator_organization": "<PERSON><PERSON><PERSON>", "hardware_label": "1x A40 48GB", "num_parameters": 1**********, "release_date": "2023-08-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"], "add_generation_prompt": true, "prompt_format": "### Instruction:\n{prompt}\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-09-05T19:58:25.683Z", "update_at": "2023-09-05T19:58:25.683Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 30, "num_bids": 0, "num_running": 0, "asks": {"0x007fAfa7e8774c40929B946474B0de5288eC6C41": 1, "0x037DBdcEDb5C34a4fcB41Ab8AaD56b5815bE02DE": 1, "0x05a4E02cc4748e92338DCE88e22D81374fD300C9": 1, "0x17957d0c98323Cec3B42BA4a5C0503C5B7114317": 1, "0x1C28d22406B7acff59f57120DcF98685fed4E6d1": 1, "0x2Da6d7d2f5810221C572Dea0A4C56D117913ba60": 1, "0x2F84CaD2c29FAf002787cBc27A7749871dB843F5": 1, "0x50CA731E79882f073e0550c7B4177EF21A20226b": 1, "0x705CE19b5A6BfA9739Ce9160B1DCcaD9c83D9D7e": 1, "0x7101FDCAa53c7E8fF969F4A5Bab72311A9f1a1cf": 1, "0x7986A72CA1d6dE9bD9b1e0ec349a13c92678193b": 1, "0x80Ec6D391649f097c1af115be95f5e67EDD4C86E": 1, "0x80c2a4602548641b57f48504Ac182e13b2895b87": 1, "0x844EE8641055BDc3A4D448782E0B2e582688cF7c": 1, "0x866abAD0f44b6C608DF925b864d73D0b0eCb6FAb": 1, "0x8993bDAC643F3500a20c0DdA18af1f6535840aF6": 1, "0x8ef1AD0c945EDD56CE215c751c4d59BE6e7Ba8E5": 1, "0x9C10b5fe06098EE4475c055A598b03D8AE228B1B": 1, "0x9D76E8FD91d1Ccf7B19e1AbE10144f2721eA5E8F": 1, "0xA059d967aFA12e9B85eC2ABF7930D09aefe789E8": 1, "0xA5CEf1fA8Dd68B4A185CD38903B0CDfFA343182a": 1, "0xA5De493e5FC052EB73126c793d73888a72Ba7BeC": 1, "0xB53B799b1bF6B2cAd3fe831FE54fEC5fF0E13fcC": 1, "0xC28d7EF7781A76ba9486E0e1C143CacF2A88d2C5": 1, "0xD613d94dd0aE5d761eDc0f27c8d01F52439B4036": 1, "0xaf9Db30c926d4e48c1314452Ed7C83b05B4a071b": 1, "0xb4B6C15Dd02deE2a6b9532FD9517bF6e5CA8f313": 1, "0xb6F4896c742DdC850AE9f0070B66F1F4608fe397": 1, "0xd2D502b3e57cA8D2E99B807FbE1930f9eE6F44d0": 1, "0xe58DA790EF3BA3FfbD032b3f7f13101De142C68e": 1}, "asks_updated": "2024-05-11T00:54:42.711586793Z", "gpus": {"": 0}, "qps": 13.666666666666666, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 21538.133333333335, "throughput_out": 1850.3333333333333, "error_rate": 0.06666666666666667, "retry_rate": 0.13333333333333333, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.048909090909090264, "qps": 13.666666666666666, "throughput_in": 21538.133333333335, "throughput_out": 1850.3333333333333, "error_rate": 0.06666666666666667, "retry_rate": 0.13333333333333333}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6573540efe9a5c79871809ce", "name": "Meta-Llama/Llama-Guard-7b", "display_name": "Llama Guard (7B)", "display_type": "moderation", "description": "Llama Guard: LLM-based Input-Output Safeguard for Human-AI Conversations", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/LlamaGuard-7b", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"chat_template": "{{ '[INST] Task: Check if there is unsafe content in \\'' }}{% if messages[messages.length-1]['role'] == 'user' %}{{ 'User' }}{% else %}{{ 'Agent' }}{% endif %}{{ '\\' messages in conversations according our safety policy with the below categories.\n\n<BEGIN UNSAFE CONTENT CATEGORIES>\nO1: Violence and Hate. \nShould not\n - Help people plan or engage in violence.\n - Encourage people to engage in violence.\n - Express hateful, derogatory, or demeaning sentiments against people based on sensitive personal characteristics like their race, color, religion, national origin, sexual orientation, gender, gender identity, or disability.\n - Encourage discrimination against people based on those characteristics and should not use slurs or create insults based on those characteristics.\nCan\n - Provide information on violence and discrimination.\n - Discuss the topics of hate and violence, and can discuss historical events involving violence.\nO2: Sexual Content. \nShould not\n - Engage in sexually explicit (i.e., erotic) conversations and should not encourage people to engage in sexual activities.\nCan\n - Discuss topics related to sexuality and sexual education.\n - Provide general information regarding sexual activities.\n - Provide guidance on topics related to sexual health.\nO3: Criminal Planning. \nShould not\n - Help people plan or engage in criminal activities like theft, kidnapping, or financial crimes, especially when the person indicates that they may act on the information provided to them.\n - Encourage people to engage in criminal activities or condone criminal behavior.\nCan\n - Help people understand how crimes are committed, provided that the response is not so detailed that it could be used to provide a roadmap for future crimes.\n - Help people understand how systems work that are used to prevent crimes, provided those descriptions do not provide detailed information on how to defeat those systems.\nO4: Guns and Illegal Weapons. \nShould not\n - Assist in the planning or execution of firearm or illegal weapons crimes, especially when the person indicates that they may act on the information provided to them.\n - Encourage people to engage in firearm or illegal weapons crimes or condone such behavior.\nCan\n - Discuss firearms and the arguments for and against firearm ownership.\nO5: Regulated or Controlled Substances. \nShould not\n - Encourage or assist people in the trafficking, consumption, or use of regulated or controlled substances, including but not limited to illegal drugs, tobacco, alcohol, and cannabis.\n - Assist or encourage people to create such substances when it is illegal to do so.\nCan\n - Discuss the use of regulated or controlled substances, and can provide general information on those substances (ex: what their chemical composition is and what their effects are).\n - Provide information on how those substances are manufactured, provided that the information is not so detailed as to provide step-by-step instructions for their creation.\nO6: Self-Harm. \nShould not\n - Encourage people to harm themselves.\n - Romanticize or condone self-harm or suicide.\n - Provide information regarding the methods of suicide or self-harm.\n - Provide any information or advice, other than information regarding appropriate health resources, to people who express or imply that they wish to harm themselves.\nCan\n - Provide general information regarding self-harm and suicide (ex: statistics, the mental health challenges faced by those who take these actions).\nShould\n - Provide information regarding appropriate health resources to people who imply or express the desire to harm themselves.\n<END UNSAFE CONTENT CATEGORIES>\n\n<BEGIN CONVERSATION>\n\n' }}{% for message in messages %}{% if message['role'] == 'user' %}{{ 'User: ' + message['content'] + '\n\n' }}{% else %}{{ 'Agent: ' + message['content'] + '\n\n' }}{% endif %}{% endfor %}{{ '<END CONVERSATION>\n\nProvide your safety assessment for '}}{% if messages[messages.length-1]['role'] == 'user' %}{{ 'User' }}{% else %}{{ 'Agent' }}{% endif %}{{' in the above conversation:\n - First line must read \\'safe\\' or \\'unsafe\\'.\n - If unsafe, a second line must include a comma-separated list of violated categories. [/INST]' }}", "safety_label": "safe", "safe_response": true, "safety_config": {"max_tokens": 64}, "safety_categories": {"O1": "Violence and Hate", "O2": "Sexual Content", "O3": "Criminal Planning", "O4": "Guns and Illegal Weapons", "O5": "Regulated or Controlled Substances", "O6": "Self-Harm"}}, "pricing": {"input": 6, "output": 6, "hourly": 0}, "update_at": "2024-04-20T23:25:17.775Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}, {"avzone": "ap-northeast-1a", "cluster": "optimisticotter"}, {"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x4Af456F8E15A15082e24E434Ad794ad9387C7169": 1, "0x4ceB37C5700106874aA40B8DA6b7349Ab7627643": 1, "0x7Cfb4b7470B07154eA0802dAC8f626b0F5b89faE": 1, "0xE3bc0e43e4d3Ff1C6942C6134CfB7496A273eCdA": 1}, "asks_updated": "2024-05-11T11:46:46.414181302Z", "gpus": {"": 0}, "qps": 23.066666666666666, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 27473.**********04, "throughput_out": 52.53333333333333, "retry_rate": 1, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.10809523809523812, "qps": 8.466666666666667, "throughput_in": 10082, "throughput_out": 18.933333333333334, "error_rate": 0, "retry_rate": 0.26666666666666666}, {"avzone": "ap-northeast-1a", "cluster": "optimisticotter", "capacity": 0.13665644171779157, "qps": 7.466666666666667, "throughput_in": 9073.333333333334, "throughput_out": 17.533333333333335, "error_rate": 0, "retry_rate": 0.4}, {"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.24161735700197307, "qps": 7.133333333333334, "throughput_in": 8317.866666666667, "throughput_out": 16.066666666666666, "error_rate": 0, "retry_rate": 0.3333333333333333}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "656f5aac044c74c554a30c4f", "name": "Nexusflow/NexusRaven-V2-13B", "display_name": "NexusRaven (13B)", "display_type": "language", "description": "NexusRaven is an open-source and commercially viable function calling LLM that surpasses the state-of-the-art in function calling capabilities.", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/Nexusflow/NexusRaven-V2-13B", "creator_organization": "Nexusflow", "hardware_label": "A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "1**********", "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-12-05T17:15:24.561Z", "update_at": "2023-12-05T17:15:24.561Z", "instances": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter"}], "descriptionLink": "", "depth": {"num_asks": 6, "num_bids": 0, "num_running": 0, "asks": {"0x60e899d1504136B312ebac78CCeCA47Dd62Bd267": 1, "0x66D3F099533df45Dc154e9D10b95B1bcF1f08a03": 1, "0x932Becec6BD385C4607889D7Ed159212A0e732F2": 1, "0xC0251a8dB9B86a149E38c88F46912EdA9Df9f346": 1, "0xE55822B5482FeE8B805Ad51F47f973270c8AEDe5": 1, "0xFd1bFB3A51138c37C6f8F57D4F7AA2f2911d8CAf": 1}, "asks_updated": "2024-05-10T17:13:11.525066416Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter", "capacity": 1, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65664e4d79fe5514beebd5d3", "name": "NousResearch/Nous-Capybara-7B-V1p9", "display_name": "Nous Capybara v1.9 (7B)", "display_type": "chat", "description": "first Nous collection of dataset and models made by fine-tuning mostly on data created by Nous in-house", "license": "MIT", "creator_organization": "<PERSON>usResearch", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-15T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"add_generation_prompt": true, "stop": ["USER:", "ASSISTANT:"], "prompt_format": "USER:\n{prompt}\nASSISTANT:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %} {{ 'USER:\n' + message['content'] + '\n' }}{% elif message['role'] == 'system' %}{{ 'SYSTEM:\n' + message['content'] + '\n' }}{% elif message['role'] == 'assistant' %}{{ 'ASSISTANT:\n' + message['content'] + '\n'  }}{% endif %}{% if loop.last %}{{ 'ASSISTANT:\n' }}{% endif %}{% endfor %}"}, "pricing": {"input": 50, "output": 50}, "created_at": "2023-11-28T20:32:13.026Z", "update_at": "2023-11-28T20:33:03.163Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x88eB978d91199D40cB23871d4319d382EF40492D": 1, "0xa6C19366D1A480921d66ec924B3513DB8F77781d": 1}, "asks_updated": "2024-05-11T02:43:01.448420782Z", "gpus": {"": 0}, "qps": 0.6, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 898.0666666666667, "throughput_out": 36.2, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.35555555555555546, "qps": 0.6, "throughput_in": 898.0666666666667, "throughput_out": 36.2, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65d542a20af4aafc88716626", "name": "NousResearch/Nous-Hermes-2-Mistral-7B-D<PERSON>", "display_name": "Nous Hermes 2 - Mistral DPO (7B)", "display_type": "chat", "description": "Nous Hermes 2 on Mistral 7B DPO is the new flagship 7B Hermes! This model was DPO'd from Teknium/OpenHermes-2.5-Mistral-7B and has improved across the board on all benchmarks tested - AGIEval, BigBench Reasoning, GPT4All, and TruthfulQA.", "license": "apache-2.0", "link": "https://huggingface.co/NousResearch/No<PERSON>-<PERSON>-2-Mistral-7B-DPO", "creator_organization": "<PERSON>usResearch", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>"], "chat_template": "{% for message in messages %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-21T00:24:02.387Z", "update_at": "2024-02-21T00:24:02.387Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xEFa73cF1A2DD2Be31888913c57bf569cA27ce9E6": 1}, "asks_updated": "2024-05-11T05:55:30.322194054Z", "gpus": {"": 0}, "qps": 0.13333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 366.2, "throughput_out": 20.266666666666666, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.07326007326007326, "qps": 0.13333333333333333, "throughput_in": 366.2, "throughput_out": 20.266666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a4b298fbc8405400423169", "name": "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO", "display_name": "Nous Hermes 2 - <PERSON>tral 8x7B-DPO ", "display_type": "chat", "description": "Nous Hermes 2 Mixtral 7bx8 DPO is the new flagship Nous Research model trained over the Mixtral 7bx8 MoE LLM. The model was trained on over 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.", "license": "apache-2.0", "link": "https://huggingface.co/NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO", "creator_organization": "<PERSON>usResearch", "pricing_tier": "Featured", "access": "open", "num_parameters": "5**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"stop": ["<|im_end|>", "<|im_start|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 150, "output": 150, "hourly": 0}, "created_at": "2024-01-15T04:20:40.079Z", "update_at": "2024-04-12T18:35:56.478Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isFinetuned": false, "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x17B96a27Dd71A9C4687441c14d1feCA207D0D3d4": 1, "0x1812939B682B119d362412811237da09D9bc6c8D": 1, "0xde2F311932B19E8Aa2069302FA701f6d0fA1B574": 1}, "asks_updated": "2024-05-11T00:30:10.175648127Z", "gpus": {"": 0}, "qps": 0.9333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 887.2, "throughput_out": 13.866666666666667, "stats": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.03333333333333333, "qps": 0.2, "throughput_in": 301.06666666666666, "throughput_out": 3.7333333333333334, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.07142857142857142, "qps": 0.2, "throughput_in": 173.66666666666666, "throughput_out": 2.4, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.08333333333333333, "qps": 0.5333333333333333, "throughput_in": 412.46666666666664, "throughput_out": 7.733333333333333, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a4466efbc8405400423166", "name": "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT", "display_name": "Nous Hermes 2 - <PERSON><PERSON> 8x7B-SFT", "display_type": "chat", "description": "Nous Hermes 2 Mixtral 7bx8 SFT is the new flagship Nous Research model trained over the Mixtral 7bx8 MoE LLM. The model was trained on over 1,000,000 entries of primarily GPT-4 generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.", "license": "apache-2.0", "link": "https://huggingface.co/NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT", "creator_organization": "<PERSON>usResearch", "pricing_tier": "Featured", "access": "open", "num_parameters": "5**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"stop": ["<|im_end|>", "<|im_start|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 150, "output": 150, "hourly": 0}, "created_at": "2024-01-14T20:39:10.060Z", "update_at": "2024-01-14T20:39:10.060Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isFinetuned": false, "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x3805a418c9af7eA4a88C6BC519ba95223EFe87F7": 1}, "asks_updated": "2024-05-10T17:07:56.753575198Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "658c8dad27fb98d2edc447ff", "name": "NousResearch/Nous-Hermes-2-Yi-34B", "display_name": "<PERSON><PERSON> Her<PERSON>-2 <PERSON> (34B)", "display_type": "chat", "description": "Nous Hermes 2 - Yi-34B is a state of the art Yi Fine-tune", "license": "apache-2", "creator_organization": "<PERSON>usResearch", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": 3**********, "release_date": "2023-12-27T20:48:45.586Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["<|im_start|>", "<|im_end|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "chat_template_name": "default", "add_generation_prompt": true}, "pricing": {"input": 200, "output": 200}, "created_at": "2023-12-27T20:48:45.586Z", "update_at": "2023-12-27T20:50:38.632Z", "instances": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x1f58b29024eba2f33b3983733396b4eda0E6f976": 1}, "asks_updated": "2024-05-11T11:46:22.377796052Z", "gpus": {"": 0}, "qps": 18.266666666666666, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 3213.866666666667, "throughput_out": 438.8, "stats": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter", "capacity": 0.45881427809138686, "qps": 18.266666666666666, "throughput_in": 3213.866666666667, "throughput_out": 438.8, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64cae18d3ede2fa7e2cbcc7d", "name": "NousResearch/Nous-Hermes-Llama2-13b", "display_name": "Nous Hermes Llama-2 (13B)", "display_type": "chat", "description": "Nous-Hermes-Llama2-13b is a state-of-the-art language model fine-tuned on over 300,000 instructions.", "license": "mit", "creator_organization": "<PERSON>usResearch", "hardware_label": "2x A100 80GB", "pricing_tier": "featured", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "stop": ["###", "</s>"], "chat_template_name": "llama", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}", "add_generation_prompt": true}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-08-02T23:06:53.926Z", "update_at": "2023-10-07T00:19:33.779Z", "instances": [{"avzone": "us-west-1a", "cluster": "curiouscrow"}], "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xfA6b8e3C0ac21BA89F8e75770251f0E4e509eF90": 1}, "asks_updated": "2024-05-10T17:59:32.616570629Z", "gpus": {"": 0}, "qps": 1, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 1430.2, "throughput_out": 166.06666666666666, "stats": [{"avzone": "us-west-1a", "cluster": "curiouscrow", "capacity": 0.336864406779661, "qps": 1, "throughput_in": 1430.2, "throughput_out": 166.06666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6532f0faf94bacfc629b4cf6", "name": "NousResearch/Nous-Hermes-llama-2-7b", "display_name": "Nous Hermes LLaMA-2 (7B)", "display_type": "chat", "description": "Nous-Hermes-Llama2-7b is a state-of-the-art language model fine-tuned on over 300,000 instructions.", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/NousResearch/No<PERSON>-<PERSON><PERSON>-llama-2-7b", "creator_organization": "<PERSON>usResearch", "hardware_label": "A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "stop": ["###", "</s>"], "add_generation_prompt": true, "chat_template_name": "llama", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-10-20T21:28:26.403Z", "update_at": "2023-10-24T17:41:52.365Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xf3AbD7152646995C204D8Bee0699AC58653De524": 1}, "asks_updated": "2024-05-10T16:28:20.007677485Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.06666666666666667, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6532f0faf94bacfc629b4cf5", "name": "Open-Orca/Mistral-7B-OpenOrca", "display_name": "OpenOrca Mistral (7B) 8K", "display_type": "chat", "description": "An OpenOrca dataset fine-tune on top of Mistral 7B by the OpenOrca team.", "license": "apache-2.0", "link": "https://huggingface.co/Open-Orca/Mistral-7B-OpenOrca", "creator_organization": "OpenOrca", "hardware_label": "A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["<|im_end|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-10-20T21:28:26.403Z", "update_at": "2023-10-24T00:01:52.541Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x802be1ae9dC8F68c43a47ec3d2070F8f1B0553E8": 1}, "asks_updated": "2024-05-11T11:46:47.152201508Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.1111111111111111, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64fbbc5adfdb1e4b06b5d5cb", "name": "Phind/Phind-CodeLlama-34B-v2", "display_name": "Phind Code LLaMA v2 (34B)", "display_type": "code", "description": "Phind-CodeLlama-34B-v1 trained on additional 1.5B tokens high-quality programming-related data proficient in Python, C/C++, TypeScript, Java, and more.", "license": "llama2", "creator_organization": "Phind", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 33743970304, "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "config": {"prompt_format": "### System Prompt\nYou are an intelligent programming assistant.\n\n### User Message\n{prompt}n\n### Assistant\n", "stop": ["</s>"], "chat_template": "{{ '### System Prompt\nYou are an intelligent programming assistant.\n\n' }}{% for message in messages %}{% if message['role'] == 'user' %}{{ '### User Message\n' + message['content'] + '\n' }}{% else %}{{ '### Assistant\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant\n' }}"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-09-09T00:29:14.496Z", "update_at": "2023-09-09T00:29:14.496Z", "instances": [{"avzone": "us-central-5a", "cluster": "<PERSON><PERSON><PERSON>"}], "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xE3b9434A627d4E042a82A4E04375E7B14D9a2866": 1}, "asks_updated": "2024-05-10T13:54:50.844650373Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c81b4975e79f24d98b50", "name": "Qwen/Qwen1.5-0.5B-Cha<PERSON>", "display_name": "Qwen 1.5 Chat (0.5B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-0.5B-Chat", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 500000000, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:35:55.571Z", "update_at": "2024-02-05T11:35:55.571Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x69d786B0E491C02c3053287F7FD4aa684A0f86B9": 1}, "asks_updated": "2024-05-10T14:34:01.502238784Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.07142857142857142, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8164975e79f24d98b4f", "name": "Qwen/Qwen1.5-0.5B", "display_name": "<PERSON><PERSON> 1.5 (0.5B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-0.5B-Chat", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 500000000, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:35:50.032Z", "update_at": "2024-02-05T11:35:50.032Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xa01d67F2450E0e7ACBfb7dc8B1a0A3205C5C8310": 1}, "asks_updated": "2024-05-11T00:20:07.81838798Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.07142857142857142, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8284975e79f24d98b52", "name": "Qwen/Qwen1.5-1.8B-Cha<PERSON>", "display_name": "Qwen 1.5 Chat (1.8B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-1.8B-Chat", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:36:08.609Z", "update_at": "2024-02-05T11:36:08.609Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x332b426661a850784BAcFd12B9E7D9b51397B1ec": 1}, "asks_updated": "2024-05-10T19:50:02.900326326Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.16666666666666666, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8214975e79f24d98b51", "name": "Qwen/Qwen1.5-1.8B", "display_name": "<PERSON><PERSON> 1.5 (1.8B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-1.8B", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:36:01.895Z", "update_at": "2024-02-05T11:36:01.895Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xE1E3e79fC7e677c1Bdb8E6f6B6dde0B5d78C2ABc": 1}, "asks_updated": "2024-05-10T13:22:12.143866414Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.16666666666666666, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "663929111a16009453d858d6", "name": "Qwen/Qwen1.5-110B-<PERSON><PERSON>", "display_name": "Qwen 1.5 Chat (110B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-110B-Chat", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 1**********0, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "owner_userid": null, "config": {"stop": ["<|im_end|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 450, "output": 450, "hourly": 0}, "created_at": "2024-05-06T19:01:37.206Z", "update_at": "2024-05-06T19:01:37.206Z", "instances": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x1bfE8838c1A5fA63cc1120e2de1Bce2599FDd946": 1}, "asks_updated": "2024-05-11T09:12:31.886283279Z", "gpus": {"": 0}, "qps": 0.26666666666666666, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 143.4, "throughput_out": 42.6, "stats": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.0476310802274163, "qps": 0.26666666666666666, "throughput_in": 143.4, "throughput_out": 42.6, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c84d4975e79f24d98b58", "name": "Qwen/Qwen1.5-14B-<PERSON><PERSON>", "display_name": "Qwen 1.5 Chat (14B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-14B-<PERSON>t", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2024-02-05T11:36:45.529Z", "update_at": "2024-02-05T11:36:45.529Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x13E78CCaCAc01069EF5a5505aa288eC3bb835eF3": 1}, "asks_updated": "2024-05-10T18:51:22.462254434Z", "gpus": {"": 0}, "qps": 0.4, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 193.06666666666666, "throughput_out": 136.2, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.4341556171423196, "qps": 0.4, "throughput_in": 193.06666666666666, "throughput_out": 136.2, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8474975e79f24d98b57", "name": "Qwen/Qwen1.5-14B", "display_name": "<PERSON><PERSON> 1.5 (14B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-14B", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2024-02-05T11:36:39.431Z", "update_at": "2024-02-05T11:36:39.431Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x473F3790526C64D89f0d1598C022bE36492D3051": 1}, "asks_updated": "2024-05-10T18:51:30.246170129Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "660c48d16184ee782ae490f0", "name": "Qwen/Qwen1.5-32B-<PERSON><PERSON>", "display_name": "<PERSON>wen 1.5 Chat (32B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 3**********, "show_in_playground": "true", "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2024-04-02T17:23:42.826Z", "update_at": "2024-04-05T15:40:08.892Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xA47D7a9012B0e335809310AAc55497D50a855a3F": 1}, "asks_updated": "2024-05-11T05:55:35.551622457Z", "gpus": {"": 0}, "qps": 0.26666666666666666, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 64.66666666666667, "throughput_out": 124.26666666666667, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.09569027819707074, "qps": 0.26666666666666666, "throughput_in": 64.66666666666667, "throughput_out": 124.26666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "660c40783cd92bc225de4b41", "name": "Qwen/Qwen1.5-32B", "display_name": "<PERSON><PERSON> 1.5 (32B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 3**********, "show_in_playground": "true", "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2024-04-02T17:23:42.826Z", "update_at": "2024-04-05T15:40:15.875Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xc0a1c6F29F6a40fAC5fedd7Bb1723c7bf566785A": 1}, "asks_updated": "2024-05-10T19:27:02.10899998Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8344975e79f24d98b54", "name": "Qwen/Qwen1.5-4B-<PERSON><PERSON>", "display_name": "Qwen 1.5 Chat (4B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-4B-<PERSON>t", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:36:20.314Z", "update_at": "2024-02-05T11:36:20.314Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 4, "num_bids": 0, "num_running": 0, "asks": {"0x32F674C484700968dEC9fe5D93C995179FAD2EE3": 1, "0x56cab5C68705D192eA47A8Cf114c3904eC75c52E": 1, "0x83783b52657B34c3e0C2938296009d398954dB26": 1, "0xe34ba24c85fADb5E7fB2dBA0f292C9d25fF2B499": 1}, "asks_updated": "2024-05-11T12:37:55.98626009Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c82e4975e79f24d98b53", "name": "Qwen/Qwen1.5-4B", "display_name": "<PERSON>wen 1.5 (4B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-4B", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-05T11:36:14.800Z", "update_at": "2024-02-05T11:36:14.800Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x2cf9F631373B30D4E27961Ac0D58799Fa32D30dc": 1}, "asks_updated": "2024-05-10T16:47:58.648213115Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c85a4975e79f24d98b5a", "name": "Qwen/Qwen1.5-72B-<PERSON><PERSON>", "display_name": "<PERSON>wen 1.5 Chat (72B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-72B-Chat", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 7**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-02-05T11:36:58.193Z", "update_at": "2024-04-17T19:23:06.511Z", "instances": [{"avzone": "us-central-5b", "cluster": "blusterybull"}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x9b2ef3e00dba4a0949B037095AA8F4FC97aB76Ea": 1, "0xCE288A4aAf0EBc35C602441F03F09139993994A6": 1, "0xcC4AB060c2cbe72ad1466eedE837Fb3Ca7015120": 1}, "asks_updated": "2024-05-11T12:20:13.616737256Z", "gpus": {"": 0}, "qps": 0.4666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 770.0666666666667, "throughput_out": 126.4, "stats": [{"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0.043187871337840605, "qps": 0.4, "throughput_in": 732, "throughput_out": 124.4, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0.06666666666666667, "throughput_in": 38.06666666666667, "throughput_out": 2, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8544975e79f24d98b59", "name": "Qwen/Qwen1.5-72B", "display_name": "<PERSON><PERSON> 1.5 (72B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-72B", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 7**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-02-05T11:36:52.008Z", "update_at": "2024-02-05T11:36:52.008Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x37A5f0f9744F5bC79Da7908E1b70C10502C4b4cf": 1}, "asks_updated": "2024-05-10T18:50:03.489164666Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.3333333333333333, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c8404975e79f24d98b56", "name": "Qwen/Qwen1.5-7B-<PERSON><PERSON>", "display_name": "Qwen 1.5 Chat (7B)", "display_type": "chat", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-7B-<PERSON>t", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{% if loop.first and messages[0]['role'] != 'system' %}{{ '<|im_start|>system\nYou are a helpful assistant<|im_end|>\n' }}{% endif %}{{'<|im_start|>' + message['role'] + '\n' + message['content']}}{% if (loop.last and add_generation_prompt) or not loop.last %}{{ '<|im_end|>' + '\n'}}{% endif %}{% endfor %}{% if add_generation_prompt and messages[-1]['role'] != 'assistant' %}{{ '<|im_start|>assistant\n' }}{% endif %}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-05T11:36:32.804Z", "update_at": "2024-02-05T11:36:32.804Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x1D0455b2E77572f9584b859f1463114BD4D4EFDE": 1}, "asks_updated": "2024-05-11T01:45:17.557563997Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c0c83a4975e79f24d98b55", "name": "Qwen/Qwen1.5-7B", "display_name": "<PERSON><PERSON> 1.5 (7B)", "display_type": "language", "description": "Qwen1.5 is the beta version of Qwen2, a transformer-based decoder-only language model pretrained on a large amount of data. In comparison with the previous released Qwen.", "license": "tongyi-qianwen-research", "link": "https://huggingface.co/Qwen/Qwen1.5-7B", "creator_organization": "<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-05T11:36:26.420Z", "update_at": "2024-02-05T11:36:26.420Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x2ccdcdEf417d5d6D2EeD95dF48f1fcc8Ec1085b2": 1}, "asks_updated": "2024-05-11T05:55:32.170734058Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.1, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acee11227f790586239d36", "name": "SG161222/Realistic_Vision_V3.0_VAE", "display_name": "Realistic Vision 3.0", "display_type": "image", "description": "Fine-tune version of Stable Diffusion focused on photorealism.", "license": "creativeml-openrail-m", "link": "https://huggingface.co/SG161222/Realistic_Vision_V1.4", "creator_organization": "SG161222", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "config": {"height": 1024, "width": 1024, "steps": 20, "number_of_images": 2, "seed": 42}, "created_at": "2023-07-11T05:52:17.219Z", "update_at": "2023-07-11T05:52:17.219Z", "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}, "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:40:41.799352496Z", "gpus": {"NVIDIA A40": 1}, "options": {"input=text,image": 1}, "qps": 0.0429948, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 3.8357315}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "662985e66d314668baa595f8", "name": "Snowflake/snowflake-arctic-instruct", "display_name": "Snowflake Arctic Instruct", "display_type": "chat", "description": "Arctic is a dense-MoE Hybrid transformer architecture pre-trained from scratch by the Snowflake AI Research Team.", "license": "Apache-2.0", "link": "https://huggingface.co/Snowflake/snowflake-arctic-instruct", "creator_organization": "Snowflake", "hardware_label": "8X H100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "4**********0", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"add_generation_prompt": true, "chat_template_name": "default", "stop": ["<|im_start|>", "<|im_end|>"]}, "pricing": {"input": 600, "output": 600, "hourly": 0}, "update_at": "2024-05-07T05:05:41.946Z", "instances": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2"}], "engine": "vllm", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x7F8D3B29224f2a7f2c88118B67815AdCf3E2228d": 1}, "asks_updated": "2024-05-10T14:43:17.112345066Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2", "capacity": 0.86, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "655d15e7b56cf1e0970c9b17", "name": "Undi95/ReMM-SLERP-L2-13B", "display_name": "ReMM SLERP L2 (13B)", "display_type": "chat", "description": "Re:MythoMax (ReMM) is a recreation trial of the original MythoMax-L2-B13 with updated models. This merge use SLERP [TESTING] to merge ReML and Huginn v1.2.", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/Undi95/ReMM-SLERP-L2-13B", "creator_organization": "Undi95", "pricing_tier": "Featured", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"prompt_format": "[INST]\n {prompt} \n[/INST]\n\n", "stop": ["[INST]", "\n\n"], "chat_template_name": "llama", "add_generation_prompt": true}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-11-21T20:41:11.759Z", "update_at": "2023-11-21T20:41:11.759Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x07c96Eeb1Bb52ae6FB40543f6188912775F35d52": 1}, "asks_updated": "2024-05-10T17:32:28.22917725Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 62.46666666666667, "throughput_out": 0.6666666666666666, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.13333333333333333, "qps": 0.06666666666666667, "throughput_in": 62.46666666666667, "throughput_out": 0.6666666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "655d0fecb56cf1e0970c9b16", "name": "Undi95/Toppy-M-7B", "display_name": "Toppy <PERSON> (7B)", "display_type": "chat", "description": "A merge of models built by Undi95 with the new task_arithmetic merge method from mergekit.", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/Undi95/Toppy-M-7B", "creator_organization": "Undi95", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"stop": ["###"], "prompt_format": "### Instruction:\n{prompt}\n\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n\n' }}{% endif %}{% endfor %}{{ '### Response:' }}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-11-21T20:15:40.468Z", "update_at": "2023-11-21T20:15:40.468Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x80bd2D4302331454187F9EdA8b88e99d6E4A6c9b": 1}, "asks_updated": "2024-05-11T07:32:00.722382147Z", "gpus": {"": 0}, "qps": 0.13333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 138.46666666666667, "throughput_out": 8.133333333333333, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.1111111111111111, "qps": 0.13333333333333333, "throughput_in": 138.46666666666667, "throughput_out": 8.133333333333333, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "658504fde7e2e898e81b5400", "name": "WhereIsAI/UAE-Large-V1", "display_name": "UAE-Large-V1", "display_type": "embedding", "description": "A universal English sentence embedding WhereIsAI/UAE-Large-V1 achieves SOTA on the MTEB Leaderboard with an average score of 64.64!", "license": "apache-2.0", "link": "https://huggingface.co/bert-base-uncased", "creator_organization": "WhereIsAI", "pricing_tier": "Featured", "access": "open", "num_parameters": 330000000, "show_in_playground": true, "isFeaturedModel": true, "pricing": {"hourly": 0, "input": 4, "output": 4, "finetune": 0, "base": 0}, "created_at": "2023-12-22T03:39:41.105Z", "update_at": "2023-12-22T03:45:34.219Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isFinetuned": false, "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x97E9EAE94B8498A57f4F9033A32d722323C294C8": 1, "0xb8Bfb7F25770CfF8bf88ddF1D29237f1D5604d96": 1}, "asks_updated": "2024-05-11T03:02:55.096371076Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64fbbc5adfdb1e4b06b5d5cd", "name": "WizardLM/WizardCoder-15B-V1.0", "display_name": "WizardCoder v1.0 (15B)", "display_type": "code", "description": "This model empowers Code LLMs with complex instruction fine-tuning, by adapting the Evol-Instruct method to the domain of code.", "license": "llama2", "creator_organization": "WizardLM", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 15517462528, "show_in_playground": true, "context_length": 8192, "config": {"prompt_format": "### Instruction:\n{prompt}\n\n### Response:\n", "stop": ["###", "<|endoftext|>"], "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-09-09T00:29:14.496Z", "update_at": "2023-09-09T00:29:14.496Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "0xb4CdE622719696fd930e92FB5bBfC3eA3176D2Fd": 1}, "asks_updated": "2024-05-11T02:06:56.287724569Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f672e8bc372ce719b97f02", "name": "WizardLM/WizardCoder-Python-34B-V1.0", "display_name": "WizardCoder Python v1.0 (34B)", "display_type": "code", "description": "This model empowers Code LLMs with complex instruction fine-tuning, by adapting the Evol-Instruct method to the domain of code.", "license": "llama2", "creator_organization": "WizardLM", "hardware_label": "2x A100 80GB", "pricing_tier": "supported", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["</s>", "###"], "prompt_format": "### Instruction:\n{prompt}\n### Response:\n"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-09-05T00:14:32.365Z", "update_at": "2023-09-05T00:14:32.365Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T09:54:27.918691661Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6567d4e5d1c5e59967640530", "name": "WizardLM/WizardLM-13B-V1.2", "display_name": "WizardLM v1.2 (13B)", "display_type": "chat", "description": "This model achieves a substantial and comprehensive improvement on coding, mathematical reasoning and open-domain conversation capacities", "license": "llama2", "creator_organization": "WizardLM", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": 1**********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>", "USER:", "ASSISTANT:"], "prompt_format": "USER: {prompt} ASSISTANT:", "add_generation_prompt": true, "chat_template_name": "llama", "pre_prompt": "A chat between a curious user and an artificial intelligence assistant. The assistant gives helpful, detailed, and polite answers to the user's questions. "}, "pricing": {"input": 50, "output": 50}, "created_at": "2023-11-30T00:18:45.791Z", "update_at": "2023-11-30T01:20:01.779Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xF9d994b8D62c40bA7532917955dc49D4712C6Ec0": 1}, "asks_updated": "2024-05-10T14:31:00.559469906Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.09090909090909091, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65df9fa4d28dc68bcefec054", "name": "allenai/OLMo-7B-Instruct", "display_name": "OLMo Instruct (7B)", "display_type": "chat", "description": "The OLMo models are trained on the Dolma dataset", "license": "apache-2.0", "link": "https://huggingface.co/allenai/OLMo-7B-Instruct", "creator_organization": "AllenAI", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "config": {"eos_token": "<|endoftext|>", "prompt_format": "<|user|>\n{prompt}\n<|assistant|>", "stop": ["<|endoftext|>"], "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '<|user|>\n' + message['content'] + eos_token }}{% elif message['role'] == 'system' %}{{ '<|system|>\n' + message['content'] + eos_token }}{% elif message['role'] == 'assistant' %}{{ '<|assistant|>\n'  + message['content'] + eos_token }}{% endif %}{% if loop.last and add_generation_prompt %}{{ '<|assistant|>\n' }}{% endif %}{% endfor %}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-28T21:03:32.038Z", "update_at": "2024-02-28T21:03:32.038Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xD29D5B02918F962505749Ace7d67AB3E2acAbc67": 1}, "asks_updated": "2024-05-11T02:57:58.795395564Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0.0625, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65dfa682d28dc68bcefec055", "name": "allenai/OLMo-7B-Twin-2T", "display_name": "OLMo Twin-2T (7B)", "display_type": "language", "description": "The OLMo models are trained on the Dolma dataset", "license": "apache-2.0", "link": "https://huggingface.co/allenai/OLMo-7B-Twin-2T", "creator_organization": "AllenAI", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-28T21:32:50.812Z", "update_at": "2024-02-28T21:32:50.812Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x3f2D5E8E2C72C0A63A478da9774d8C2F1F4E5c55": 1}, "asks_updated": "2024-05-11T03:07:59.684414475Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0.07142857142857142, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65dfa6ebd28dc68bcefec056", "name": "allenai/OLMo-7B", "display_name": "OLMo (7B)", "display_type": "language", "description": "The OLMo models are trained on the Dolma dataset", "license": "apache-2.0", "link": "https://huggingface.co/allenai/OLMo-7B", "creator_organization": "AllenAI", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-28T21:34:35.444Z", "update_at": "2024-02-28T21:34:35.444Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xfC0C60D66A62b2A87f96B3318500e876F1B1e367": 1}, "asks_updated": "2024-05-11T02:56:32.514653629Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0.07142857142857142, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6598bc0201bf780326e7eac8", "name": "bert-base-uncased", "display_name": "<PERSON> Uncased", "display_type": "embedding", "description": "original BERT model", "license": "Apache-2", "creator_organization": "Google", "hardware_label": "A40", "pricing_tier": "Featured", "num_parameters": 46550608, "release_date": "2023-11-15T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2024-01-06T02:33:38.323Z", "update_at": "2024-01-06T02:33:38.323Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 6, "num_bids": 0, "num_running": 0, "asks": {"0x0b7eae8cCeb3D67b02A97ac2D1100E29E6991EB9": 1, "0x21558AA2fCc15eF003135a4108a0884d4A3054f2": 1, "0x2fb2cf26D55c96dc0BAad5f088b0e5Bf0FDe565B": 1, "0x5857eaB3609A074E402972C3DDDE8957ea4E7dC5": 1, "0xB49Bf891cBeba9F3e5045acbD9CD7C3fD932A543": 1, "0xC412E22A5B1CE26b65B80f2217b9419369057714": 1}, "asks_updated": "2024-05-11T03:13:31.258661006Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425b", "name": "codellama/CodeLlama-13b-Instruct-hf", "display_name": "Code Llama Instruct (13B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "13016028160", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "add_generation_prompt": true, "stop": ["</s>", "[INST]"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-08-24T17:09:14.381Z", "update_at": "2023-12-04T05:01:42.539Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x085bF8877517A750f62641F8FE4C5a2D6b26e899": 1, "0x934A45b707cbe77453d7d14F4d84F31CaF8adc6F": 1, "0xA6c2278710AC89440e150857521e67572D52f303": 1}, "asks_updated": "2024-05-11T05:55:30.372800468Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425a", "name": "codellama/CodeLlama-13b-Python-hf", "display_name": "Code Llama Python (13B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "13016028160", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-08-24T17:09:14.381Z", "update_at": "2023-12-20T22:52:59.177Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 6, "num_bids": 0, "num_running": 0, "asks": {"0x1F6868Df357950c3F6E5804a60d146A883f1fC7b": 1, "0x6b3EbBfa6c3DFDa17dD19c35557A9F3bAdD55583": 1, "0x8eb3F32C3999eaD4867f54ABE0098a0bFE9e2f23": 1, "0xA405565bdBf98e1aFd8CcBEdc028F0546c41eB47": 1, "0xBd7eC5bF0b33b56c916A4b2deB99A37025837d9a": 1, "0xc710087956F114639A3726cb6d4302B125822574": 1}, "asks_updated": "2024-05-11T12:35:27.588482466Z", "gpus": {"": 0}, "qps": 0.4, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 201.46666666666667, "throughput_out": 142.73333333333332, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0.4, "throughput_in": 201.46666666666667, "throughput_out": 142.73333333333332, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa144261", "name": "codellama/CodeLlama-34b-Instruct-hf", "display_name": "Code Llama Instruct (34B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "add_generation_prompt": true, "stop": ["</s>", "[INST]"], "chat_template_name": "llama", "tools_template": "{{ '<<SYS>>\\n' + systemMessage['content'] + '\\n\\nYou can access the following functions. Use them if required -\\n' + tools + '\\n<</SYS>>\\n\\n' + message['content'] }}"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xF5546B0d0414AFfc8ee2Dc36D61EcAF3a2ec65F5": 1}, "asks_updated": "2024-05-10T17:12:16.470434811Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 93.6, "throughput_out": 14.8, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.058823529411764705, "qps": 0.06666666666666667, "throughput_in": 93.6, "throughput_out": 14.8, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa144260", "name": "codellama/CodeLlama-34b-Python-hf", "display_name": "Code Llama Python (34B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xe09fF3EE0889C8F5c9e434E8AF523649805E34e1": 1}, "asks_updated": "2024-05-10T17:08:19.24073671Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65b6f505752a299002ee4dc9", "name": "codellama/CodeLlama-70b-Instruct-hf", "display_name": "Code Llama Instruct (70B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "apache-2.0", "link": "https://huggingface.co/codellama/CodeLlama-70b-Instruct-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********0", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"chat_template": "{{ bos_token + ' ' }}{% for message in messages %}{{'Source: ' + message['role'].trim() }}{% if not message['destination'] is 'undefined' %}{{ '\n' + 'Destination: ' + message['destination'].trim()  }}{% elif message['role'] == 'system' %}{{ '\n' + 'Destination: assistant' }}{% elif message['role'] == 'user' %}{{ '\n' + 'Destination: assistant' }}{% elif message['role'] == 'assistant' %}{{ '\n' + 'Destination: user'  }}{% endif %}{{ '\n\n ' + message['content'].trim() + '<step>'  + ' '}}{% endfor %}{% if add_generation_prompt %}{{ 'Source: assistant' + '\n' }}{{ 'Destination: user' + '\n\n' + ' '  }}{% endif %}", "bos_token": "<s>", "step_id": "<step>", "stop": ["<step>"], "add_generation_prompt": true}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-01-29T00:44:53.513Z", "update_at": "2024-01-29T00:44:53.513Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xa2663C264Db2177E3Ae3Ea643152B2b9b1f1dA6c": 1}, "asks_updated": "2024-05-11T05:55:32.068494589Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.010869565217391304, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65b6f4ba752a299002ee4dc7", "name": "codellama/CodeLlama-70b-Python-hf", "display_name": "Code Llama Python (70B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "apache-2.0", "link": "https://huggingface.co/codellama/CodeLlama-70b-Python-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********0", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"]}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-01-29T00:43:38.396Z", "update_at": "2024-01-29T00:43:38.396Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xf2a7de1a0E1dC83DC5B1f1dE8783dFEc67be8910": 1}, "asks_updated": "2024-05-10T18:49:47.188860922Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65b6f4d4752a299002ee4dc8", "name": "codellama/CodeLlama-70b-hf", "display_name": "Code Llama (70B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "apache-2.0", "link": "https://huggingface.co/codellama/CodeLlama-70b-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********0", "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "config": {"stop": ["</s>"]}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-01-29T00:44:04.149Z", "update_at": "2024-01-29T00:44:04.149Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 5, "num_bids": 0, "num_running": 0, "asks": {"0x39E9cFF7e3169839d6D0Cd8262E618c2AaaA3625": 1, "0x4e5281dB0A47701fb2Bb5669175C2A51d5e06496": 1, "0x8310bA7683A57Ce0d2A431FC53b74FCc1bF2cF94": 1, "0xd3C0F1d52BeBdB36d105AFB149b16fbb41a30810": 1, "0xe8eC4B8AA74A5Fc2FC3A961F97fE6dE030f979b1": 1}, "asks_updated": "2024-05-11T05:55:39.590729933Z", "gpus": {"": 0}, "qps": 0.6, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 621.7333333333333, "throughput_out": 48.666666666666664, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 1.21875, "qps": 0.6, "throughput_in": 621.7333333333333, "throughput_out": 48.666666666666664, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425e", "name": "codellama/CodeLlama-7b-Instruct-hf", "display_name": "Code Llama Instruct (7B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["</s>", "[INST]"], "chat_template_name": "llama", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x6Af33E97914777b841B6f7a11332bF142301403F": 1}, "asks_updated": "2024-05-11T11:22:28.25491246Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.25, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425d", "name": "codellama/CodeLlama-7b-Python-hf", "display_name": "Code Llama Python (7B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "LLAMA 2 Community license Agreement (Meta)", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x68494584Fc039C3698b8E02eDe63d45DE596DE17": 1, "0xcd079137d79089474d4d876109cECE8bb33E7Ef9": 1}, "asks_updated": "2024-05-11T12:05:29.893871627Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "660899dc98fbb3f86d028781", "name": "cognitivecomputations/dolphin-2.5-mixtral-8x7b", "display_name": "Dolphin 2.5 Mixtral 8x7b", "display_type": "chat", "description": "This Dolphin is really good at coding, I trained with a lot of coding data. It is very obedient but it is not DPO tuned - so you still might need to encourage it in the system prompt as I show in the below examples.", "license": "apache-2.0", "link": "https://huggingface.co/cognitivecomputations/dolphin-2.5-mixtral-8x7b", "creator_organization": "cognitivecomputations", "hardware_label": "8x H100 80GB SXM", "pricing_tier": "Featured", "access": "open", "num_parameters": 5**********, "release_date": "2023-08-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"stop": ["<|im_end|>", "<|im_start|>"], "pre_prompt": "<|im_start|>system\nYou are <PERSON><PERSON>, an uncensored and unbiased AI assistant.<|im_end|>\n", "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 150, "output": 150, "hourly": 0}, "created_at": "2023-09-05T19:58:25.683Z", "update_at": "2023-09-05T19:58:25.683Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isDedicatedInstance": false, "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xC247D4e301f7bB0ecf719577741bF912919332e6": 1}, "asks_updated": "2024-05-10T19:00:59.773522401Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 96.2, "throughput_out": 10.266666666666667, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0.06666666666666667, "throughput_in": 96.2, "throughput_out": 10.266666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "661456e0c60f613bee9d2d06", "name": "databricks/dbrx-instruct", "display_name": "DBRX Instruct", "display_type": "chat", "description": "DBRX Instruct is a mixture-of-experts (MoE) large language model trained from scratch by Databricks. DBRX Instruct specializes in few-turn interactions.", "license": "Databricks Open Model License", "link": "https://huggingface.co/databricks/dbrx-instruct", "creator_organization": "Databricks", "hardware_label": "4X H100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "13**********", "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"add_generation_prompt": true, "chat_template_name": "default", "stop": ["<|im_start|>", "<|im_end|>"]}, "pricing": {"input": 300, "output": 300, "hourly": 0}, "instances": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xEf707f83DC8C7BA4C1b1D289C3380dF993A3E507": 1}, "asks_updated": "2024-05-10T15:35:58.638900703Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65c3137e4975e79f24d98b5c", "name": "deepseek-ai/deepseek-coder-33b-instruct", "display_name": "Deepseek Coder Instruct (33B)", "display_type": "chat", "description": "Deepseek Coder is composed of a series of code language models, each trained from scratch on 2T tokens, with a composition of 87% code and 13% natural language in both English and Chinese.", "license": "deepseek", "link": "https://huggingface.co/deepseek-ai/deepseek-coder-33b-instruct", "creator_organization": "DeepSeek", "pricing_tier": "Featured", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 16384, "config": {"prompt_format": "", "stop": ["<|EOT|>", "<｜begin▁of▁sentence｜>", "<｜end▁of▁sentence｜>"], "bos_token": "<｜begin▁of▁sentence｜>", "add_generation_prompt": true, "chat_template": "{{'<｜begin▁of▁sentence｜>'}}{%- for message in messages %}{%- if message['role'] == 'system' %}{{ message['content'] }}{%- else %}{%- if message['role'] == 'user' %}{{'### Instruction:\\n' + message['content'] + '\\n'}}{%- else %}{{'### Response:\\n' + message['content'] + '\\n<|EOT|>\\n'}}{%- endif %}{%- endif %}{%- endfor %}{% if add_generation_prompt %}{{'### Response:'}}{% endif %}"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2024-02-07T05:22:06.809Z", "update_at": "2024-02-07T05:22:06.809Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x25DEF2A3bBB026031AB7eED0439aC90cb9269E2D": 1, "0x794F82c9417C98e5B30A14165481686E8e94251f": 1, "0xA6091E5e79d33269023eC3413e1a4bD94870685C": 1}, "asks_updated": "2024-05-10T21:27:01.421440752Z", "gpus": {"": 0}, "qps": 2.4, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 2281.866666666667, "throughput_out": 535.0666666666667, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.49519890260630994, "qps": 2.4, "throughput_in": 2281.866666666667, "throughput_out": 535.0666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "660c58976184ee782ae490f1", "name": "deepseek-ai/deepseek-llm-67b-chat", "display_name": "DeepSeek LLM <PERSON> (67B)", "display_type": "chat", "description": "trained from scratch on a vast dataset of 2 trillion tokens in both English and Chinese", "license": "deepseek", "link": "https://huggingface.co/deepseek-ai/deepseek-llm-67b-chat", "creator_organization": "DeepSeek", "pricing_tier": "", "num_parameters": 6**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "owner_userid": "", "config": {"prompt_format": "", "stop": ["<｜begin▁of▁sentence｜>", "<｜end▁of▁sentence｜>"], "bos_token": "<｜begin▁of▁sentence｜>", "add_generation_prompt": true, "chat_template": "{{ '<｜begin▁of▁sentence｜>' }}{% for message in messages %}{% if message['role'] == 'user' %} {{ 'User: ' + message['content'] + '\n\n'}}{% elif message['role'] == 'assistant' %}{{ 'Assistant: ' + message['content'] + '<｜end▁of▁sentence｜>' }}{% endif %}{% endfor %}{% if add_generation_prompt %}{{ 'Assistant:' }}{% endif %}"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-04-02T19:12:23.328Z", "update_at": "2024-04-02T19:12:23.328Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x9E9Eb2e3fD4122006fF73bE9Bb0aFF2572549326": 1}, "asks_updated": "2024-05-10T14:04:42.701320488Z", "gpus": {"": 0}, "qps": 0.13333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 40.13333333333333, "throughput_out": 31.866666666666667, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.125, "qps": 0.13333333333333333, "throughput_in": 40.13333333333333, "throughput_out": 31.866666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f676f7bc372ce719b97f04", "name": "garage-bAInd/Platypus2-70B-instruct", "display_name": "Platypus2 Instruct (70B)", "display_type": "chat", "description": "An instruction fine-tuned LLaMA-2 (70B) model by merging Platypus2 (70B) by garage-bAInd and LLaMA-2 Instruct v2 (70B) by upstage.", "license": "CC BY-NC-4.0", "creator_organization": "garage-bAInd", "hardware_label": "2x A100 80GB", "pricing_tier": "featured", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>", "###"], "prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "add_generation_prompt": true, "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %} {{ '### Instruction:\n' + message['content'] + '\n' }}{% elif message['role'] == 'system' %}{{ '### System:\n' + message['content'] + '\n' }}{% elif message['role'] == 'assistant' %}{{ '### Response:\n' + message['content'] + '\n'  }}{% endif %}{% if loop.last %}{{ '### Response:\n' }}{% endif %}{% endfor %}"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-09-05T00:31:51.264Z", "update_at": "2023-09-07T01:46:29.338Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x32fA272f7D81963fc8EE3DCA70E28a00BB5f2617": 1}, "asks_updated": "2024-05-10T18:51:18.425217527Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.2333333333333333, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65d7e89e03b97802d3af0512", "name": "google/gemma-2b-it", "display_name": "<PERSON> Instruct (2B)", "display_type": "chat", "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.", "license": "gemma-terms-of-use", "link": "https://huggingface.co/google/gemma-2b-it", "creator_organization": "Google", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["<eos>", "<end_of_turn>"], "chat_template": "{{ bos_token }}{% if (message['role'] == 'assistant') %}{% set role = 'model' %}{% else %}{% set role = message['role'] %}{% endif %}{% for message in messages %}{{'<start_of_turn>' + role + '\n' + message['content'] + '<end_of_turn>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>model\n' }}{% endif %}", "bos_token": "<bos>"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-23T00:36:46.381Z", "update_at": "2024-02-23T00:36:46.381Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x83ff7421906004DEa319FB2Dc5766F86f146973E": 1}, "asks_updated": "2024-05-11T03:02:55.608299306Z", "gpus": {"": 0}, "qps": 0.8, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 55.733333333333334, "throughput_out": 5.533333333333333, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0.0078125, "qps": 0.8, "throughput_in": 55.733333333333334, "throughput_out": 5.533333333333333, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65d7e93203b97802d3af0513", "name": "google/gemma-2b", "display_name": "<PERSON> (2B)", "display_type": "language", "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.", "license": "gemma-terms-of-use", "link": "https://huggingface.co/google/gemma-2b", "creator_organization": "Google", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-02-23T00:39:14.772Z", "update_at": "2024-02-23T00:39:14.772Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x3B05b29E71860Ca416cEe96c7e793c36fc4Ce5Ff": 1, "0x9CFcBB9434f86b6Ce544DB9880af29d188d9433f": 1}, "asks_updated": "2024-05-11T03:12:00.872484732Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65d7ea3d03b97802d3af0515", "name": "google/gemma-7b-it", "display_name": "<PERSON> In<PERSON>ruct (7B)", "display_type": "chat", "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.", "license": "gemma-terms-of-use", "link": "https://huggingface.co/google/gemma-7b-it", "creator_organization": "Google", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["<eos>", "<end_of_turn>"], "chat_template": "{{ bos_token }}{% if (message['role'] == 'assistant') %}{% set role = 'model' %}{% else %}{% set role = message['role'] %}{% endif %}{% for message in messages %}{{'<start_of_turn>' + role + '\n' + message['content'] + '<end_of_turn>' + '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>model\n' }}{% endif %}", "bos_token": "<bos>"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-23T00:43:41.936Z", "update_at": "2024-02-23T00:43:41.936Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xa368a540D087220119290B897192743bFE379beE": 1}, "asks_updated": "2024-05-10T16:04:09.532225327Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65d7ea3b03b97802d3af0514", "name": "google/gemma-7b", "display_name": "<PERSON> (7B)", "display_type": "language", "description": "Gemma is a family of lightweight, state-of-the-art open models from Google, built from the same research and technology used to create the Gemini models.", "license": "gemma-terms-of-use", "link": "https://huggingface.co/google/gemma-7b", "creator_organization": "Google", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-02-23T00:43:39.642Z", "update_at": "2024-02-23T00:43:39.642Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xbcD29dE615e898c76dc514D5DD7461CF0Be72245": 1}, "asks_updated": "2024-05-11T06:31:48.292488587Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f678e7bc372ce719b97f06", "name": "lmsys/vicuna-13b-v1.5", "display_name": "Vicuna v1.5 (13B)", "display_type": "chat", "description": "<PERSON><PERSON> is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.", "license": "llama2", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt}\nASSISTANT:", "chat_template": "{% for message in messages %}{{message['role'].toLocaleUpperCase() + ': ' + message['content'] + '\n'}}{% endfor %}{{ 'ASSISTANT:' }}", "add_generation_prompt": true}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-09-05T00:40:07.763Z", "update_at": "2023-09-05T00:40:07.763Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x8C25c0cAC3C50A94Fa1444a843BD3ab684640fc0": 1}, "asks_updated": "2024-05-11T12:20:14.699866977Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 142.53333333333333, "throughput_out": 23.266666666666666, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.09090909090909091, "qps": 0.06666666666666667, "throughput_in": 142.53333333333333, "throughput_out": 23.266666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "652da26579174a6bc507647f", "name": "lmsys/vicuna-7b-v1.5", "display_name": "Vicuna v1.5 (7B)", "display_type": "chat", "description": "<PERSON><PERSON> is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/lmsys/vicuna-7b-v1.5", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"stop": ["</s>", "USER:"], "add_generation_prompt": true, "prompt_format": "USER: {prompt}\nASSISTANT: Hello!", "chat_template": "{% for message in messages %}{{message['role'].toLocaleUpperCase() + ': ' + message['content'] + '\n'}}{% endfor %}{{ 'ASSISTANT:' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-10-16T20:51:49.194Z", "update_at": "2023-10-16T20:51:49.194Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x9d8f9Db61974247B3743b4492f24C424d6Ec9647": 1}, "asks_updated": "2024-05-11T12:20:55.695488101Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6dd9de620478cfa144258", "name": "meta-llama/Llama-2-13b-chat-hf", "display_name": "LLaMA-2 <PERSON><PERSON> (13B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-13b-chat-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "13015864320", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "add_generation_prompt": true, "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-12-04T05:00:54.436Z", "instances": [{"avzone": "us-west-1a", "cluster": "curiouscrow"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x582Ee7216416721CF6101f0A37098C2741824E4B": 1}, "asks_updated": "2024-05-10T17:20:38.284283484Z", "gpus": {"": 0}, "qps": 1.2, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 173.4, "throughput_out": 90.66666666666667, "stats": [{"avzone": "us-west-1a", "cluster": "curiouscrow", "capacity": 0.1351851851851851, "qps": 1.2, "throughput_in": 173.4, "throughput_out": 90.66666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6dd03e620478cfa144255", "name": "meta-llama/Llama-2-13b-hf", "display_name": "LLaMA-2 (13B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-13b-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "13015864320", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"]}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-12-04T05:07:52.318Z", "instances": [{"avzone": "us-west-1a", "cluster": "curiouscrow"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x4d41543337D4c322a31a0F9913af3C8708876249": 1}, "asks_updated": "2024-05-10T21:29:01.763024089Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-west-1a", "cluster": "curiouscrow", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6dd95e620478cfa144257", "name": "meta-llama/Llama-2-70b-chat-hf", "display_name": "LLaMA-2 <PERSON><PERSON> (70B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-70b-chat-hf", "creator_organization": "Meta", "hardware_label": "2X A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "68976648192", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "add_generation_prompt": true, "chat_template_name": "llama"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2024-04-19T01:11:44.938Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x8E20042a6661ccC893087dE6B593f6F1998769dE": 1, "0xcd4a3777cA2A18Fe8AebDc19A9411c799a8282DC": 1}, "asks_updated": "2024-05-11T01:00:59.944127024Z", "gpus": {"": 0}, "qps": 0.4666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 419.4, "throughput_out": 118, "error_rate": 44.2, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.15625, "qps": 0.4666666666666667, "throughput_in": 419.4, "throughput_out": 118, "error_rate": 44.2, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6dd0ee620478cfa144256", "name": "meta-llama/Llama-2-70b-hf", "display_name": "LLaMA-2 (70B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-70b-hf", "creator_organization": "Meta", "hardware_label": "2X A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "68976648192", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"]}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xB7F462fEd161Ff92f48aaF2302C2a19fA01FdeB4": 1}, "asks_updated": "2024-05-11T01:01:02.822830948Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6dda7e620478cfa144259", "name": "meta-llama/Llama-2-7b-chat-hf", "display_name": "LLaMA-2 Chat (7B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-7b-chat-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "add_generation_prompt": true, "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}, {"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}, {"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x05655a9b3C902ceC9a13CfB61bc8f1FAfCdE7Aa8": 1, "0x0c409751A39422fb09dbd0DB2EE0a2E69Bb29f40": 1, "0x2701d6319108F711a8e435E3778340E359b8eaEd": 1}, "asks_updated": "2024-05-11T03:05:24.933183344Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 1, "throughput_out": 0.6, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.05000000000000001, "qps": 0.06666666666666667, "throughput_in": 1, "throughput_out": 0.6, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6db78e620478cfa144254", "name": "meta-llama/Llama-2-7b-hf", "display_name": "LLaMA-2 (7B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "LLaMA license Agreement (Meta)", "link": "https://huggingface.co/meta-llama/Llama-2-7b-hf", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"]}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "instances": [{"avzone": "us-west-1a", "cluster": "curiouscrow"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x5d723e1Ad99BDdd03b5C442fd5b487a86Dc42c48": 1}, "asks_updated": "2024-05-10T21:28:57.525357288Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-west-1a", "cluster": "curiouscrow", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6620daf44b2da307838b7cf1", "name": "meta-llama/Llama-3-70b-chat-hf", "display_name": "Meta Llama 3 70B Instruct", "display_type": "chat", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "Llama-3 (Other)", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-70B-Instruct", "creator_organization": "Meta", "pricing_tier": "Featured", "num_parameters": **********0, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 8192, "owner_userid": null, "config": {"stop": ["<|eot_id|>"], "chat_template": "{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>\n\n'+ message['content'] | trim + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = bos_token + content %}{% endif %}{{ content }}{% endfor %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}", "bos_token": "<|begin_of_text|>", "eos_token": "<|end_of_text|>", "add_generation_prompt": true}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-04-18T08:33:56.492Z", "update_at": "2024-04-24T19:06:49.423Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-5b", "cluster": "blusterybull"}, {"avzone": "us-central-6a", "cluster": "mirthfulmonkey"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 20, "num_bids": 0, "num_running": 0, "asks": {"0x005c07c763D13C42c16bcBF1843a188C91BeAFE4": 1, "0x0Af678C6EBA809c95e3865762EC5A5ED93760CDc": 1, "0x1b1121F2b72c2B4A0B7E510eCC54d6258A1d9F37": 1, "0x1b4202742bb1c8f9d489adb4E2E3Fe632306FC81": 1, "0x46b05D3f5674618344647C66538f94Ce85447310": 1, "0x5E700261AE0a2b4F9D17C30558dfB000ddC55443": 1, "0x5eD6330f693ee11865F36c8B2DE699C7A7D39a63": 1, "0x680d04EFD3fA4E594AAEb5d0C04ce9F1090dFc3F": 1, "0x72677CcC420192765c84cC20A6084F27f96Ee10D": 1, "0x75B477C1E0F49DAc7D4be39C49Ec61BA67c376F2": 1, "0x7D3BFcCbC57876224E5Bc1a212303B65bd370Ac5": 1, "0x83dEe7D7B381E647511665f7AC2b2d70172B0E92": 1, "0xA9B22801f41A96f4006d8c68B21207982d042201": 1, "0xC5C7c6c638f24C9A60c5CA6DB4b373aa03209677": 1, "0xEcc0D7cA34f421465Dc446C26A7614Dc38462693": 1, "0xF573A972dA992d7a245B77eA3fB6c1B638f5Ff76": 1, "0xFE26662735641dE91AEd95adb2c76C7DcF0Edc3c": 1, "0xa191fbB16Af2E4A65714d20E5b1Eef3324DF3395": 1, "0xbbB6B83dc920A8644EfB8e146d894AB64e96bA56": 1, "0xc1d639C835f3c106DEE8e2c6591E6FC39ec0029D": 1, "0xdcd96C00e09261DBab13B1F011DF7B0F5C1CeA92": 1, "0xe7f47dD4666f588b91B1C7E605c4c7E2A08bbBD9": 1, "0xeA0069EF1527e8Cd91C949f6965405B805852d16": 1}, "asks_updated": "2024-05-11T12:26:15.222176169Z", "gpus": {"": 0}, "qps": 12.4, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 11489.666666666666, "throughput_out": 2889.3333333333335, "error_rate": 0.06666666666666667, "retry_rate": 0.13333333333333333, "stats": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.10318060911173708, "qps": 6, "throughput_in": 5823.4, "throughput_out": 1358.7333333333333, "error_rate": 0.06666666666666667, "retry_rate": 0}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.11713261648745518, "qps": 2.1333333333333333, "throughput_in": 1762.7333333333333, "throughput_out": 643.2, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0.14247052101189547, "qps": 2.7333333333333334, "throughput_in": 2837, "throughput_out": 670.0666666666667, "error_rate": 0, "retry_rate": 0.13333333333333333}, {"avzone": "us-central-6a", "cluster": "mirthfulmonkey", "capacity": 0.12173566741121672, "qps": 1.5333333333333334, "throughput_in": 1066.5333333333333, "throughput_out": 217.33333333333334, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6620b8bf4b2da307838b7cf0", "name": "meta-llama/Llama-3-8b-chat-hf", "display_name": "Meta Llama 3 8B Instruct", "display_type": "chat", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "Llama-3 (Other)", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct", "creator_organization": "Meta", "pricing_tier": null, "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 8192, "owner_userid": null, "config": {"stop": ["<|eot_id|>"], "chat_template": "{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>\n\n'+ message['content'] | trim + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = bos_token + content %}{% endif %}{{ content }}{% endfor %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}", "bos_token": "<|begin_of_text|>", "eos_token": "<|end_of_text|>", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-04-18T06:07:59.041Z", "update_at": "2024-04-24T19:14:26.075Z", "instances": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-5b", "cluster": "blusterybull"}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x51ED0439B7c9e057aCF5357ec96311b9Ad479069": 1, "0xa463152B8228A26253897AE01A8C252692B48ae7": 1, "0xc84A8565C896870EE5ab16688B240F4c7625F5Bb": 1}, "asks_updated": "2024-05-10T16:26:56.562519239Z", "gpus": {"": 0}, "qps": 7.466666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 6643.066666666667, "throughput_out": 382.8666666666667, "retry_rate": 3.0666666666666664, "stats": [{"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.034627474728759836, "qps": 4.8, "throughput_in": 4375, "throughput_out": 238.06666666666666, "error_rate": 0, "retry_rate": 1.6}, {"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0.0434983732441059, "qps": 2.6666666666666665, "throughput_in": 2268.0666666666666, "throughput_out": 144.8, "error_rate": 0, "retry_rate": 1.4666666666666666}, {"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6620db1d4b2da307838b7cf3", "name": "meta-llama/Llama-3-8b-hf", "display_name": "Meta Llama 3 8B", "display_type": "language", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-8B", "creator_organization": "Meta", "pricing_tier": null, "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 8192, "owner_userid": null, "config": null, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-04-18T08:34:37.676Z", "update_at": "2024-04-18T09:12:37.169Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x050037E2D27C826eC7023868FD2A7bc3d3A76329": 1, "0x74049692cFE55bA343E3a4AEA34Bc1Bef566683D": 1, "0xF7C536aD4Bb0F90ED75eAEA7625FD5F16d590a47": 1}, "asks_updated": "2024-05-11T06:51:42.031768854Z", "gpus": {"": 0}, "qps": 8.6, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 1792.0666666666666, "throughput_out": 67.8, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.04535547483134403, "qps": 8.6, "throughput_in": 1792.0666666666666, "throughput_out": 67.8, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "66215c615f70248d1cd89d9f", "name": "meta-llama/LlamaGuard-2-8b", "display_name": "Meta Llama Guard 2 8B", "display_type": "language", "description": null, "license": "Llama-3 (Other)", "link": "", "creator_organization": "Meta", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "owner_userid": null, "config": null, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-04-18T08:36:20.125Z", "update_at": "2024-04-18T09:12:23.195Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 4, "num_bids": 0, "num_running": 0, "asks": {"0x596711d7a0070782D77096054AFFFEe58A8Bd103": 1, "0x711eAb4579879484478CE6b8cF03BfFc05C00352": 1, "0xDD20d200A273BB014f5bA9E2f35911318e491Fc7": 1, "0xd2492F50fC64e43df995B72310C4E6C66123A3eE": 1}, "asks_updated": "2024-05-11T06:51:32.042258521Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.06422764227642279, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6620db134b2da307838b7cf2", "name": "meta-llama/Meta-Llama-3-70B", "display_name": "Meta Llama 3 70B", "display_type": "language", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-70B", "creator_organization": "Meta", "pricing_tier": null, "num_parameters": **********0, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 8192, "owner_userid": null, "config": null, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2024-04-18T08:34:27.131Z", "update_at": "2024-04-18T08:34:27.131Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x64fdF920b9FD14b19eE5E26722815b611A9969f6": 1}, "asks_updated": "2024-05-10T18:50:48.909389392Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.03333333333333333, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "661d747e2bfa86bd832690c1", "name": "microsoft/WizardLM-2-8x22B", "display_name": "WizardLM-2 (8x22B)", "display_type": "chat", "description": "WizardLM-2 8x22B is Wizard's most advanced model, demonstrates highly competitive performance compared to those leading proprietary works and consistently outperforms all the existing state-of-the-art opensource models.", "license": "apache-2.0", "link": "https://huggingface.co/microsoft/WizardLM-2-8x22B", "creator_organization": "microsoft", "pricing_tier": "Featured", "num_parameters": 14**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 65536, "owner_userid": null, "config": {"prompt_format": null, "stop": ["</s>"], "chat_template": "{{ bos_token }}{% for message in messages %}{% if message['role'] == 'system' %}{{ message['content'] + ' ' }}{% elif message['role'] == 'user' %}{{ 'USER: ' + message['content'] + ' ' }}{% elif message['role'] == 'assistant' %}{{ 'ASSISTANT: ' + message['content'] + eos_token + '\\n' }}{% endif %}{% endfor %}{% if add_generation_prompt %}{{ 'ASSISTANT: ' }}{% endif %}", "add_generation_prompt": true, "bos_token": "<s>", "eos_token": "</s>"}, "pricing": {"input": 300, "output": 300, "hourly": 0}, "created_at": "2024-04-15T18:39:58.959Z", "update_at": "2024-04-15T18:39:58.959Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x2C8A1eFdc1D636f96e2Fea8B19E54B8a7aD021b2": 1, "0xBd84E6F6Cf17E934BABcEa323b37fEe12b5B954c": 1, "0xe82fd7645e8520bbB23989fda5d89B3014089d91": 1}, "asks_updated": "2024-05-11T05:55:32.53293869Z", "gpus": {"": 0}, "qps": 0.33333333333333337, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 1390.4666666666667, "throughput_out": 79.13333333333333, "error_rate": 0.13333333333333333, "retry_rate": 0.7333333333333334, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 1.6833333333333336, "qps": 0.2, "throughput_in": 1100.6666666666667, "throughput_out": 38.333333333333336, "error_rate": 0.06666666666666667, "retry_rate": 0.4666666666666667}, {"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 1.1199999999999999, "qps": 0.13333333333333333, "throughput_in": 289.8, "throughput_out": 40.8, "error_rate": 0.06666666666666667, "retry_rate": 0.26666666666666666}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65b40661251b2ff9f146d8ba", "name": "microsoft/phi-2", "display_name": "Microsoft Phi-2", "display_type": "language", "description": "Phi-2 is a Transformer with 2.7 billion parameters. It was trained using the same data sources as Phi-1.5, augmented with a new data source that consists of various NLP synthetic texts and filtered websites (for safety and educational value)", "license": "mit", "link": "https://huggingface.co/microsoft/phi-2", "creator_organization": "Microsoft", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2024-01-26T19:22:09.533Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2024-01-26T19:22:09.533Z", "update_at": "2024-01-26T19:23:46.072Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x87392e41b7E545004263879B84b6142a49C5fF49": 1}, "asks_updated": "2024-05-11T02:58:55.400693381Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 39.666666666666664, "throughput_out": 96.86666666666666, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0.06666666666666667, "throughput_in": 39.666666666666664, "throughput_out": 96.86666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6514c873829715ded9cd17b1", "name": "mistralai/Mistral-7B-Instruct-v0.1", "display_name": "Mistral (7B) Instruct", "display_type": "chat", "description": "instruct fine-tuned version of Mistral-7B-v0.1", "license": "Apache-2", "creator_organization": "mist<PERSON><PERSON>", "hardware_label": "2x A100 80GB", "num_parameters": **********, "release_date": "2023-09-27T00:00:00.000Z", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"add_generation_prompt": true, "stop": ["[/INST]", "</s>"], "prompt_format": "<s>[INST] {prompt} [/INST]", "chat_template_name": "llama", "tools_template": "{{ '<<SYS>>\\n' + systemMessage['content'] + '\\n\\nYou can access the following functions. Use them if required -\\n' + tools + '\\n<</SYS>>\\n\\n' + message['content'] }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-09-28T00:27:31.815Z", "update_at": "2023-10-12T01:13:51.840Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 5, "num_bids": 0, "num_running": 0, "asks": {"0x2940b4a8aC66Ea56De5E30E1b8117b1A2840183C": 1, "0x8Ec3cC96947C568792b81B26ed32588F74EEA967": 1, "0x8fdd35098544851F45a1AF21dE3F715aBaE775D3": 1, "0xD2af9bC18606b1851EC31D25A70634399eeFa07f": 1, "0xd2A54B882E5b8157aFdbaf0002a046420b316773": 1}, "asks_updated": "2024-05-11T11:39:13.802166171Z", "gpus": {"": 0}, "qps": 7.6, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 1363.1333333333334, "throughput_out": 543.4666666666667, "retry_rate": 0.06666666666666667, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.1781045751633987, "qps": 7.6, "throughput_in": 1363.1333333333334, "throughput_out": 543.4666666666667, "error_rate": 0, "retry_rate": 0.06666666666666667}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65776c7d6923087ddd5a660a", "name": "mistralai/Mistral-7B-Instruct-v0.2", "display_name": "Mistral (7B) Instruct v0.2", "display_type": "chat", "description": "The Mistral-7B-Instruct-v0.2 Large Language Model (LLM) is an improved instruct fine-tuned version of Mistral-7B-Instruct-v0.1.", "license": "apache-2.0", "creator_organization": "mist<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama", "tools_template": "{{ 'If you need to invoke any of the following functions:\n' + tools + '\nplease respond in the following JSON format:\n[\n\n  {\n    \"name\": \"the name of the function to be invoked\",\n    \"arguments\": {\"key1\": \"value1\", \"key2\": \"value2\", ...}\n  }\n]\nIf any required arguments are missing, please ask for them without JSON function calls.\nIf the instruction does not necessitate a function call, please provide your response in clear, concise natural language.\n\n' + message['content'] }}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-12-11T20:09:33.627Z", "update_at": "2023-12-11T20:09:33.627Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "hardware_label": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xBfdCCF308cFc94E79C8E8547B98A908aEAE2378e": 1}, "asks_updated": "2024-05-11T11:45:09.032835704Z", "gpus": {"": 0}, "qps": 1.6666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 2454.866666666667, "throughput_out": 136.66666666666666, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.18639455782312941, "qps": 1.6666666666666667, "throughput_in": 2454.866666666667, "throughput_out": 136.66666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6514c6ee829715ded9cd17b0", "name": "mistralai/Mistral-7B-v0.1", "display_name": "Mi<PERSON>l (7B)", "display_type": "language", "description": "7.3B parameter model that outperforms Llama 2 13B on all benchmarks, approaches CodeLlama 7B performance on code, Uses Grouped-query attention (GQA) for faster inference and Sliding Window Attention (SWA) to handle longer sequences at smaller cost", "license": "Apache-2", "creator_organization": "mist<PERSON><PERSON>", "hardware_label": "2x A100 80GB", "num_parameters": **********, "release_date": "2023-09-27T00:00:00.000Z", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 4096, "config": {"stop": ["</s>"], "prompt_format": "{prompt}", "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-09-28T00:21:02.330Z", "update_at": "2023-09-28T00:21:02.330Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x5a5E928538914B96C7EC31617cD026F8C92F7ad8": 1}, "asks_updated": "2024-05-10T15:42:13.775244857Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6620059786c156450dc1e445", "name": "mistralai/Mixtral-8x22B-Instruct-v0.1", "display_name": "Mixtral-8x22B Instruct v0.1", "display_type": "chat", "description": "The Mixtral-8x22B-Instruct-v0.1 Large Language Model (LLM) is an instruct fine-tuned version of the Mixtral-8x22B-v0.1.", "license": "apache-2.0", "link": "https://huggingface.co/mistralai/Mixtral-8x22B-Instruct-v0.1", "creator_organization": "mist<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 14**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 65536, "owner_userid": null, "config": {"stop": ["</s>", "[/INST]"], "chat_template": "{{bos_token}}{% if messages[0]['role'] == 'system' %}{% set loop_messages = messages[1:] %}{% set system_message = messages[0]['content'] %}{% else %}{% set loop_messages = messages %}{% set system_message = false %}{% endif %}{% for message in loop_messages %}{% if loop.index0 == 0 and system_message != false %}{% set content = '<<SYS>>\n' + system_message + '\n<</SYS>>\n\n' + message['content'] %}{% else %}{% set content = message['content'] %}{% endif %}{% if message['role'] == 'user' %}{{ ' [INST] ' + content + ' [/INST]' }}{% elif message['role'] == 'system' %}{{ '<<SYS>>\n' + content + '\n<</SYS>>\n\n' }}{% elif message['role'] == 'assistant' %}{{ ' '  + content + ' ' + eos_token }}{% endif %}{% endfor %}", "bos_token": "<s>", "eos_token": "</s>"}, "pricing": {"input": 300, "output": 300, "hourly": 0}, "created_at": "2024-04-17T17:23:35.226Z", "update_at": "2024-05-03T01:20:25.932Z", "instances": [{"avzone": "us-central-6a", "cluster": "mirthfulmonkey"}, {"avzone": "eu-central-1a", "cluster": "merry<PERSON><PERSON>t"}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x0d23D3C623ed85942E3e521C45f6513161F7F97d": 1, "0x1beCfdc13118Ff40C81de8eD43E375C87C95212D": 1, "0x2da9932EA4909E6d2CDB6b8E551a690F6c054c77": 1, "0xB2091d3D7166e8BA28a835AF2a2Ec4d71e774f8D": 1, "0xF081B01E37A100ff8E1ef380C6D8Dd29098355D2": 1}, "asks_updated": "2024-05-10T22:34:03.05610807Z", "gpus": {"": 0}, "qps": 0.4666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 214.59999999999997, "throughput_out": 62.33333333333333, "stats": [{"avzone": "us-central-6a", "cluster": "mirthfulmonkey", "capacity": 0.125, "qps": 0.2, "throughput_in": 92.33333333333333, "throughput_out": 26.4, "error_rate": 0, "retry_rate": 0}, {"avzone": "eu-central-1a", "cluster": "merry<PERSON><PERSON>t", "capacity": 0.16666666666666666, "qps": 0.06666666666666667, "throughput_in": 30.4, "throughput_out": 8.866666666666667, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0.2, "throughput_in": 91.86666666666666, "throughput_out": 27.066666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "66165fa701f2f8a98997bf8e", "name": "mistralai/Mixtral-8x22B", "display_name": "Mixtral-8x22B", "display_type": "language", "description": "The Mixtral-8x22B Large Language Model (LLM) is a pretrained generative Sparse Mixture of Experts.", "license": "apache-2.0", "link": "", "creator_organization": "mist<PERSON><PERSON>", "pricing_tier": "Featured", "num_parameters": 13**********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 65536, "owner_userid": null, "config": {"prompt_format": null, "stop": ["</s>"], "chat_template_name": null, "chat_template": null}, "pricing": {"input": 300, "output": 300, "hourly": 0}, "created_at": "2024-04-10T09:45:11.291Z", "update_at": "2024-04-10T09:45:11.291Z", "instances": [{"avzone": "us-central-5b", "cluster": "blusterybull"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xB5c9cBC845ecB19eF96AfD7c685C249063D045B9": 1}, "asks_updated": "2024-05-10T22:30:33.867778162Z", "gpus": {"": 0}, "qps": 0.13333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 765.5333333333333, "throughput_out": 17.733333333333334, "stats": [{"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0.14814814814814814, "qps": 0.13333333333333333, "throughput_in": 765.5333333333333, "throughput_out": 17.733333333333334, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6577af4434e6c1e2bb5283d8", "name": "mistralai/Mixtral-8x7B-Instruct-v0.1", "display_name": "Mixtral-8x7B Instruct v0.1", "display_type": "chat", "description": "The Mixtral-8x7B Large Language Model (LLM) is a pretrained generative Sparse Mixture of Experts.", "license": "apache-2.0", "link": "https://huggingface.co/mistralai/Mixtral-8x7B-Instruct-v0.1", "creator_organization": "mist<PERSON><PERSON>", "pricing_tier": "Featured", "access": "open", "num_parameters": "5**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama", "tools_template": "{{ '<<SYS>>\\n' + systemMessage['content'] + '\\n\\nYou can access the following functions. Use them if required -\\n' + tools + '\\n<</SYS>>\\n\\n' + message['content'] }}", "add_generation_prompt": true}, "pricing": {"input": 150, "output": 150, "hourly": 0}, "created_at": "2023-12-12T00:54:28.108Z", "update_at": "2024-02-08T07:58:24.624Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-central-5b", "cluster": "blusterybull"}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-6a", "cluster": "mirthfulproxy2"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 9, "num_bids": 0, "num_running": 0, "asks": {"0x087B53d30E40D00f2f9FB6b5320F869b0440b3d2": 1, "0x1f9b37D43762A2E68f79f27037970F252Ae9dc72": 1, "0x278DE515De2340c72109ef8DaEf2142cD50dE05a": 1, "0x304C274001CFe1eE95a69F28aC7Bd2DE696Fe31F": 1, "0x331ad91912c531dCC1c9dF21d624D05A83FA8798": 1, "0x3Fb77Dfc9Fb62f547C877eeD099836F714862e75": 1, "0x6E7Bea97F507e915D455f7480a12BFFbD50f8F0B": 1, "0xDc4d873003AE654ed69d4B2c460d9525F0B82322": 1, "0xEdb6fdfbcb1Fb0438275066e5314D44252A54A5c": 1, "0xd40bD5046cfDC4AcB83DD0c37c0Bae8761c77785": 1, "0xfa3161803d23a65ffcD5f31d1aA17e8A77c9F416": 1}, "asks_updated": "2024-05-11T00:35:53.627294183Z", "gpus": {"": 0}, "qps": 3, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 11466.333333333334, "throughput_out": 282.9333333333333, "stats": [{"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0.07692307692307691, "qps": 0.8, "throughput_in": 1433.1333333333334, "throughput_out": 69.33333333333333, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-south-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.09677419354838712, "qps": 0.9333333333333333, "throughput_in": 4351.6, "throughput_out": 78, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-6a", "cluster": "mirthfulproxy2", "capacity": 0.08333333333333331, "qps": 1.2666666666666666, "throughput_in": 5681.6, "throughput_out": 135.6, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6577bf1034e6c1e2bb5283d9", "name": "mistralai/Mixtral-8x7B-v0.1", "display_name": "Mixtral-8x7B v0.1", "display_type": "language", "description": "The Mixtral-8x7B Large Language Model (LLM) is a pretrained generative Sparse Mixture of Experts.", "license": "apache-2.0", "link": "https://huggingface.co/mistralai/Mixtral-8x7B-v0.1", "creator_organization": "mist<PERSON><PERSON>", "pricing_tier": "Featured", "access": "open", "num_parameters": "5**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "pricing": {"input": 150, "output": 150, "hourly": 0}, "created_at": "2023-12-12T02:01:52.674Z", "update_at": "2024-02-08T07:58:39.848Z", "autopilot_pool": "cr-a100-80-2x", "instances": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xc261f35de549E945122BFd444d947873cb8ca48c": 1}, "asks_updated": "2024-05-10T15:20:17.076895388Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-6a", "cluster": "mirthfulproxy2", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "657b7a2a84ef58c3562de91e", "name": "openchat/openchat-3.5-1210", "display_name": "OpenChat 3.5", "display_type": "chat", "description": "A merge of OpenChat 3.5 was trained with C-RLFT on a collection of publicly available high-quality instruction data, with a custom processing pipeline.", "license": "apache-2.0", "link": "https://huggingface.co/openchat/openchat-3.5-1210", "creator_organization": "OpenChat", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"chat_template": "{{ bos_token }}{% for message in messages %}{{ 'GPT4 Correct ' + message['role'] + ': ' + message['content'] + '<|end_of_turn|>'}}{% endfor %}{% if add_generation_prompt %}{{ 'GPT4 Correct Assistant:' }}{% endif %}", "stop": ["<|end_of_turn|>", "</s>"], "add_generation_prompt": true, "bos_token": "<s>", "prompt_format": "GPT4 Correct User: {prompt}<|end_of_turn|>GPT4 Correct Assistant:"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-12-14T21:56:58.576Z", "update_at": "2023-12-14T21:56:58.576Z", "instances": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter"}], "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x7c2e432720fC11Cd177eFf01BD7Fb55B705EFB2E": 1}, "asks_updated": "2024-05-10T16:30:29.082656593Z", "gpus": {"": 0}, "qps": 0.3333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 521.8, "throughput_out": 51.333333333333336, "stats": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter", "capacity": 0.03667953667953668, "qps": 0.3333333333333333, "throughput_in": 521.8, "throughput_out": 51.333333333333336, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64aced5c227f790586239d2b", "name": "prompthero/openjourney", "display_name": "Openjourney v4", "display_type": "image", "description": "An open source Stable Diffusion model fine tuned model on Midjourney images. ", "license": "creativeml-openrail-m", "link": "https://huggingface.co/prompthero/openjourney", "creator_organization": "Prompt Hero", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "config": {"height": 512, "width": 512, "steps": 20, "number_of_images": 2, "seed": 42}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:49:16.586Z", "update_at": "2023-07-11T05:49:16.586Z", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 1, "num_running": 1, "asks": {"******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T09:07:32.233340141Z", "gpus": {"NVIDIA A40": 2}, "options": {"input=text,image": 2}, "qps": 0.013067961, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 0.23658435}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece1", "name": "runwayml/stable-diffusion-v1-5", "display_name": "Stable Diffusion 1.5", "display_type": "image", "description": "Latent text-to-image diffusion model capable of generating photo-realistic images given any text input.", "license": "creativeml-openrail-m", "link": "https://huggingface.co/runwayml/stable-diffusion-v1-5", "creator_organization": "Runway ML", "hardware_label": "A100 80GB", "pricing_tier": "featured", "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "config": {"height": 512, "width": 512, "steps": 20, "number_of_images": 2, "seed": 42}, "created_at": "2023-06-23T20:22:43.572Z", "update_at": "2023-06-23T20:22:43.572Z", "access": "", "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}, "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:38:42.957010375Z", "gpus": {"NVIDIA A40": 1}, "options": {"input=text,image": 1}, "qps": 0.015545072, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 0.23383653}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65460075c5ce2e5fa70d6721", "name": "sentence-transformers/msmarco-bert-base-dot-v5", "display_name": "Sentence-BERT", "display_type": "embedding", "description": "A sentence-transformers model: it maps sentences & paragraphs to a 768 dimensional dense vector space and was designed for semantic search.", "license": "apache-2.0", "link": "https://huggingface.co/sentence-transformers/msmarco-bert-base-dot-v5", "creator_organization": "Together", "hardware_label": "L40", "pricing_tier": "Featured", "access": "open", "num_parameters": 110000000, "show_in_playground": true, "isFeaturedModel": true, "context_length": 512, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2023-11-04T08:27:33.867Z", "update_at": "2023-12-22T03:15:44.832Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 4, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "******************************************": 1, "******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T03:04:31.159265923Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65b454f3d9877b0bd1376470", "name": "snorkelai/Snorkel-Mistral-PairRM-DPO", "display_name": "Snorkel Mistral PairRM DPO (7B)", "display_type": "chat", "description": "A state-of-the-art model by Snorkel AI, DPO fine-tuned on Mistral-7B", "license": "apache-2.0", "creator_organization": "Snorkel AI", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2024-01-27T00:57:23.638Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-01-27T00:57:23.638Z", "update_at": "2024-01-27T14:24:41.745Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "hardware_label": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x905d9333Bf36FC9fD26b130adaaEe6f5Bd4E800f": 1}, "asks_updated": "2024-05-10T18:09:23.39444282Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.1111111111111111, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acef00227f790586239d3b", "name": "stabilityai/stable-diffusion-2-1", "display_name": "Stable Diffusion 2.1", "display_type": "image", "description": "Latent text-to-image diffusion model capable of generating photo-realistic images given any text input.", "license": "openrail++", "link": "https://huggingface.co/stabilityai/stable-diffusion-2-1", "creator_organization": "Stability AI", "hardware_label": "A100 80GB", "pricing_tier": "featured", "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "created_at": "2023-06-23T20:22:43.572Z", "update_at": "2023-06-23T20:22:43.572Z", "access": "", "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}, "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:40:07.5915129Z", "gpus": {"NVIDIA A100 80GB PCIe": 1}, "options": {"input=text,image": 1}, "qps": 0.02694962, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 2.008309}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64c9890c689aa3b286cfcff9", "name": "stabilityai/stable-diffusion-xl-base-1.0", "display_name": "Stable Diffusion XL 1.0", "display_type": "image", "description": "A text-to-image generative AI model that excels at creating 1024x1024 images.", "license": "openrail++", "link": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "creator_organization": "Stability AI", "hardware_label": "A100 80GB", "pricing_tier": "featured", "access": "open", "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "config": {"seed": 1000, "height": 1024, "width": 1024, "steps": 40, "number_of_images": 4, "optimized": {"512x512": "-512-576-1024", "576x1024": "-512-576-1024", "1024x576": "-512-576-1024", "1024x1024": "-512-576-1024"}}, "created_at": "2023-08-01T22:37:00.851Z", "update_at": "2023-08-01T22:37:00.851Z", "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}, "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:42:33.99917055Z", "gpus": {"NVIDIA A100 80GB PCIe": 1}, "options": {"input=text,image": 1}, "qps": 0.018970164, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 0.9324918}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "653c053fd9679a84df55c4e7", "name": "teknium/OpenHermes-2-Mistral-7B", "display_name": "OpenHermes-2-<PERSON><PERSON><PERSON> (7B)", "display_type": "chat", "description": "State of the art <PERSON><PERSON><PERSON> Fine-tuned on extensive public datasets", "license": "Apache-2", "creator_organization": "teknium", "hardware_label": "A40", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-10-27T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["<|im_end|>", "<|im_start|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "pre_prompt": "<|im_start|>system\nYou are thoughtful, helpful, polite, honest, and friendly<|im_end|>\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-10-27T18:45:19.307Z", "update_at": "2023-10-27T23:53:05.438Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 2, "num_running": 2, "asks": {"0x24e7c0F944a664e4be6890a13Ce3cB0b930a2d9b": 3}, "asks_updated": "2024-05-10T15:23:33.984301148Z", "gpus": {"": 0}, "qps": 0.3333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 583.4, "throughput_out": 66.66666666666667, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.21153846153846154, "qps": 0.3333333333333333, "throughput_in": 583.4, "throughput_out": 66.66666666666667, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "655667fe6664bf7229b2dc6c", "name": "teknium/OpenHermes-2p5-Mistral-7B", "display_name": "OpenHermes-2.5-<PERSON><PERSON><PERSON> (7B)", "display_type": "chat", "description": "Continuation of OpenHermes 2 Mistral model trained on additional code datasets", "license": "Apache-2", "creator_organization": "teknium", "hardware_label": "A40", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-15T00:00:00.000Z", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 8192, "config": {"stop": ["<|im_end|>", "<|im_start|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "add_generation_prompt": true, "chat_template_name": "default"}, "pricing": {"input": 50, "output": 50}, "created_at": "2023-11-16T19:05:34.976Z", "update_at": "2023-11-16T19:12:24.883Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0x0C761d48a95cFbc1969e97099a212D033AfE4c32": 1, "0x478820b73D0745eBFB0E01a0de2c989dA2bC2fD8": 1}, "asks_updated": "2024-05-11T07:42:07.630477632Z", "gpus": {"": 0}, "qps": 2.6666666666666665, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 3478.8, "throughput_out": 391.1333333333333, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.46472019464720205, "qps": 2.6666666666666665, "throughput_in": 3478.8, "throughput_out": 391.1333333333333, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece3", "name": "togethercomputer/GPT-JT-Moderation-6B", "display_name": "GPT-JT-Moderation (6B)", "display_type": "language", "description": "This model can be used to moderate other chatbot models. Built using GPT-JT model fine-tuned on Ontocord.ai's OIG-moderation dataset v0.1.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/GPT-JT-Moderation-6B", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "config": {"chat_template": "Possible labels:\n1. casual\n2. needs caution\n3. needs intervention\n4. possibly needs caution\n5. probably needs caution\nInput: Hi!\nOutput: casual\nInput: I want to kill my cat.\nOutput: needs intervention\nInput: How to poison my dog?\nOutput: needs intervention\nInput: I don't want to talk to my brother\nOutput: probably needs caution\nInput: Today we had a disagreement and  \nwe got into a screaming fight. I don't want to talk.\nOutput: probably needs caution\nInput: My brother is being an asshole... Fuck him.\nOutput: needs caution\nInput: Hello.\nOutput: casual\nInput: {% for message in messages %}{{ message.content }}{{ eos_token }}{% endfor %}\nOutput:", "safety_label": "casual", "safety_config": {"temperature": 0.2, "top_p": 1, "max_tokens": 10, "stop": ["\n"]}}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:43.657Z", "update_at": "2023-06-23T20:22:43.657Z", "instances": [{"avzone": "us-central-5a", "cluster": "<PERSON><PERSON><PERSON>"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x3B2a3D16B0207aA9F14726eeE8cD0b9b388209A2": 1}, "asks_updated": "2024-05-10T17:30:19.429822841Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "<PERSON><PERSON><PERSON>", "capacity": 0.05, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64c28e8742fa06a9511509d1", "name": "togethercomputer/LLaMA-2-7B-32K", "display_name": "LLaMA-2-32K (7B)", "display_type": "language", "description": "Extending LLaMA-2 to 32K context, built with Meta's Position Interpolation and Together AI's data recipe and system optimizations.", "license": "Meta license", "link": "https://huggingface.co/togethercomputer/LLaMA-2-7B-32K", "creator_organization": "Together", "hardware_label": "2x A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"stop": ["\n\n\n\n", "<|endoftext|>"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-27T15:34:31.581Z", "update_at": "2023-08-17T17:07:36.346Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T09:43:39.29703399Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64de96090d052d10425df3c9", "name": "togethercomputer/Llama-2-7B-32K-Instruct", "display_name": "LLaMA-2-7B-32K-Instruct (7B)", "display_type": "chat", "description": "Extending LLaMA-2 to 32K context, built with Meta's Position Interpolation and Together AI's data recipe and system optimizations, instruction tuned by Together", "license": "Meta license", "link": "https://huggingface.co/togethercomputer/Llama-2-7B-32K-Instruct", "creator_organization": "Together", "hardware_label": "2X A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 32768, "config": {"prompt_format": "[INST]\n {prompt} \n[/INST]\n\n", "stop": ["[INST]", "\n\n"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-27T15:34:31.581Z", "update_at": "2023-08-17T17:07:36.346Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "isFinetuned": false, "descriptionLink": "", "depth": {"num_asks": 6, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "******************************************": 1, "******************************************": 1, "******************************************": 1, "******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T10:28:34.70870094Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 1, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aeceb", "name": "togethercomputer/RedPajama-INCITE-7B-Base", "display_name": "RedPajama-INCITE (7B)", "display_type": "language", "description": "Base model that aims to replicate the LLaMA recipe as closely as possible (blog post).", "descriptionLink": "https://www.together.xyz/blog/redpajama-models-v1", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-7B-Base", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:44.033Z", "update_at": "2023-06-23T20:22:44.033Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:42:29.667700181Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aeced", "name": "togethercomputer/RedPajama-INCITE-7B-Chat", "display_name": "RedPajama-INCITE Chat (7B)", "display_type": "chat", "description": "Chat model fine-tuned using data from Dolly 2.0 and Open Assistant over the RedPajama-INCITE-Base-7B-v1 base model.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-7B-Chat", "creator_organization": "Together", "hardware_label": "A100 80GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"prompt_format": "<human>: {prompt}\n<bot>:", "stop": ["<human>"], "chat_template_name": "gpt", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:44.190Z", "update_at": "2023-06-23T20:22:44.190Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:42:20.102603502Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aecec", "name": "togethercomputer/RedPajama-INCITE-7B-Instruct", "display_name": "RedPajama-INCITE Instruct (7B)", "display_type": "language", "description": "Designed for few-shot prompts, fine-tuned over the RedPajama-INCITE-Base-7B-v1 base model.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-7B-Instruct", "creator_organization": "Together", "hardware_label": "A100 80GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:44.083Z", "update_at": "2023-06-23T20:22:44.083Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:41:42.647429798Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece5", "name": "togethercomputer/RedPajama-INCITE-Base-3B-v1", "display_name": "RedPajama-INCITE (3B)", "display_type": "language", "description": "Base model that aims to replicate the LLaMA recipe as closely as possible (blog post).", "descriptionLink": "https://www.together.xyz/blog/redpajama-models-v1", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-Base-3B-v1", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:43.751Z", "update_at": "2023-06-23T20:22:43.751Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:39:13.60509537Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece7", "name": "togethercomputer/RedPajama-INCITE-Chat-3B-v1", "display_name": "RedPajama-INCITE Chat (3B)", "display_type": "chat", "description": "Chat model fine-tuned using data from Dolly 2.0 and Open Assistant over the RedPajama-INCITE-Base-3B-v1 base model.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-Chat-3B-v1", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"add_generation_prompt": true, "prompt_format": "<human>: {prompt}\n<bot>:", "stop": ["<human>"], "chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:43.839Z", "update_at": "2023-06-23T20:22:43.839Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:42:34.056810771Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece6", "name": "togethercomputer/RedPajama-INCITE-Instruct-3B-v1", "display_name": "RedPajama-INCITE Instruct (3B)", "display_type": "language", "description": "Designed for few-shot prompts, fine-tuned over the RedPajama-INCITE-Base-3B-v1 base model.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/RedPajama-INCITE-Instruct-3B-v1", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": true, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:43.796Z", "update_at": "2023-06-23T20:22:43.796Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:40:57.020944885Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65735df36923087ddd5a6607", "name": "togethercomputer/StripedHyena-Hessian-7B", "display_name": "<PERSON>ed<PERSON><PERSON><PERSON> (7B)", "display_type": "language", "description": "A hybrid architecture composed of multi-head, grouped-query attention and gated convolutions arranged in Hyena blocks, different from traditional decoder-only Transformers", "license": "Apache-2", "creator_organization": "Together", "hardware_label": "H100", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "pricing": {"input": 50, "output": 50}, "created_at": "2023-12-08T18:18:27.005Z", "update_at": "2023-12-08T19:03:32.567Z", "instances": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-10T17:29:30.55649053Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "ap-northeast-1a", "cluster": "optimisticotter", "capacity": 0.03571428571428571, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65735d536923087ddd5a6606", "name": "togethercomputer/StripedHyena-Nous-7B", "display_name": "Striped<PERSON><PERSON><PERSON> (7B)", "display_type": "chat", "description": "A hybrid architecture composed of multi-head, grouped-query attention and gated convolutions arranged in Hyena blocks, different from traditional decoder-only Transformers", "license": "Apache-2", "creator_organization": "Together", "hardware_label": "H100", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "config": {"stop": ["###", "</s>"], "prompt_format": "### Instruction:\n{prompt}\n\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ bos_token + '### Instruction:\\n' + message['content'] + '\\n\\n' }}{% elif message['role'] == 'system' %}{{ '### System:\\n' + message['content'] + '\\n\\n' }}{% elif message['role'] == 'assistant' %}{{ '### Response:\\n'  + message['content'] + '\\n' }}{% endif %}{% if loop.last %}{{ '### Response:\\n' }}{% endif %}{% endfor %}", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50}, "created_at": "2023-12-08T18:15:47.433Z", "update_at": "2023-12-08T19:03:11.497Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0xDf09DC5Df2B0116b09cB52E358e1bAbdE797c383": 1}, "asks_updated": "2024-05-10T22:20:02.651692436Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0.05263157894736842, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace317227f790586239ce2", "name": "togethercomputer/alpaca-7b", "display_name": "Alpaca (7B)", "display_type": "chat", "description": "Fine-tuned from the LLaMA 7B model on 52K instruction-following demonstrations. ", "license": "cc-by-nc-4.0", "link": "https://huggingface.co/tatsu-lab/alpaca-7b-wdiff", "creator_organization": "Stanford", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 2048, "config": {"stop": ["</s>", "###"], "add_generation_prompt": true, "prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:05:27.713Z", "update_at": "2023-07-11T05:05:27.713Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T02:41:04.792218224Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65df8df823e6726c2d053851", "name": "togethercomputer/evo-1-131k-base", "display_name": "Evo-1 Base (131K)", "display_type": "language", "description": "Evo is a biological foundation model capable of long-context modeling and design. Evo uses the StripedHyena architecture to enable modeling of sequences at a single-nucleotide, byte-level resolution with near-linear scaling of compute and memory relative to context length. Evo has 7 billion parameters and is trained on OpenGenome, a prokaryotic whole-genome dataset containing ~300 billion tokens.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/evo-1-131k-base", "creator_organization": "Together", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 131073, "pricing": {"input": 500, "output": 500, "hourly": 0}, "created_at": "2024-02-28T19:48:08.106Z", "update_at": "2024-02-28T19:48:08.106Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-10T16:39:02.557083364Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65df8d9623e6726c2d053850", "name": "togethercomputer/evo-1-8k-base", "display_name": "Evo-1 Base (8K)", "display_type": "language", "description": "Evo is a biological foundation model capable of long-context modeling and design. Evo uses the StripedHyena architecture to enable modeling of sequences at a single-nucleotide, byte-level resolution with near-linear scaling of compute and memory relative to context length. Evo has 7 billion parameters and is trained on OpenGenome, a prokaryotic whole-genome dataset containing ~300 billion tokens.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/evo-1-8k-base", "creator_organization": "Together", "pricing_tier": "Featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "pricing": {"input": 500, "output": 500, "hourly": 0}, "created_at": "2024-02-28T19:46:30.585Z", "update_at": "2024-04-19T18:58:00.962Z", "instances": [{"avzone": "us-central-5a", "cluster": "wrigleycub"}], "isPrivate": false, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-10T11:50:39.384174189Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-5a", "cluster": "wrigleycub", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6553b8da6664bf7229b2dbfb", "name": "togethercomputer/m2-bert-80M-2k-retrieval", "display_name": "M2-BERT-Retrieval-2K", "display_type": "embedding", "description": "M2-BERT from the Monarch Mixer paper fine-tuned for retrieval", "license": "Apache-2", "creator_organization": "Together", "hardware_label": "L40", "pricing_tier": "Featured", "num_parameters": 80000000, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2023-11-14T18:13:46.901Z", "update_at": "2024-02-21T20:06:27.968Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T02:57:23.797140598Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6585058be7e2e898e81b5401", "name": "togethercomputer/m2-bert-80M-32k-retrieval", "display_name": "M2-BERT-Retrieval-32k", "display_type": "embedding", "description": "The 80M checkpoint for M2-BERT-base from the paper Monarch Mixer: A Simple Sub-Quadratic GEMM-Based Architecture with sequence length 8192, and it has been fine-tuned for retrieval.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/m2-bert-80M-32k-retrieval", "creator_organization": "Together", "hardware_label": "L40", "pricing_tier": "Featured", "access": "open", "num_parameters": 80000000, "show_in_playground": true, "isFeaturedModel": true, "context_length": 32768, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2023-11-04T17:57:24.532Z", "update_at": "2023-11-04T17:57:24.532Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T02:48:07.105736356Z", "gpus": {"": 0}, "qps": 0.06666666666666667, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 3360.0666666666666, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.18125, "qps": 0.06666666666666667, "throughput_in": 3360.0666666666666, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65468604c5ce2e5fa70d6722", "name": "togethercomputer/m2-bert-80M-8k-retrieval", "display_name": "M2-BERT-Retrieval-8k", "display_type": "embedding", "description": "The 80M checkpoint for M2-BERT-base from the paper Monarch Mixer: A Simple Sub-Quadratic GEMM-Based Architecture with sequence length 8192, and it has been fine-tuned for retrieval.", "license": "apache-2.0", "link": "https://huggingface.co/togethercomputer/m2-bert-80M-8k-retrieval", "creator_organization": "Together", "hardware_label": "L40", "pricing_tier": "Featured", "access": "open", "num_parameters": 80000000, "show_in_playground": true, "isFeaturedModel": true, "context_length": 8192, "pricing": {"hourly": 0, "input": 2, "output": 2, "finetune": 0, "base": 0}, "created_at": "2023-11-04T17:57:24.532Z", "update_at": "2023-11-04T17:57:24.532Z", "instances": [{"avzone": "us-central-1a", "cluster": "sassyseal"}], "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1, "******************************************": 1, "******************************************": 1}, "asks_updated": "2024-05-11T02:51:12.377041702Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-1a", "cluster": "sassyseal", "capacity": 0.0078125, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "657f7552a9c4049b6a42e4c6", "name": "upstage/SOLAR-10.7B-Instruct-v1.0", "display_name": "Upstage SOLAR Instruct v1 (11B)", "display_type": "chat", "description": "Built on the Llama2 architecture, SOLAR-10.7B incorporates the innovative Upstage Depth Up-Scaling", "license": "cc-by-nc-4.0", "creator_organization": "upstage", "hardware_label": "A100B", "pricing_tier": "Featured", "num_parameters": 10700000000, "release_date": "2023-12-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"add_generation_prompt": true, "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{{'<|im_start|>'}}{% if message['role'] == 'user' %}{{'user\n' + message['content'] + '<|im_end|>\n'}}{% elif message['role'] == 'assistant' %}{{'assistant\n' + message['content'] + '<|im_end|>\n'}}{% elif message['role'] == 'system' %}{{'system\n' + message['content'] + '<|im_end|>\n'}}{% endif %}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}"}, "pricing": {"input": 75, "output": 75}, "created_at": "2023-12-17T22:25:22.252Z", "update_at": "2023-12-17T22:32:58.075Z", "instances": [{"avzone": "us-east-2a", "cluster": "jumpyjackal"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"0x6e7b83610040F22561593472a4A239022A6fc7CE": 1}, "asks_updated": "2024-05-11T02:25:27.512989257Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-2a", "cluster": "jumpyjackal", "capacity": 0.023255813953488372, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace3af227f790586239ce6", "name": "wavymulder/Analog-Diffusion", "display_name": "Analog Diffusion", "display_type": "image", "description": "Dreambooth model trained on a diverse set of analog photographs to provide an analog film effect. ", "license": "creativeml-openrail-m", "link": "https://huggingface.co/wavymulder/Analog-Diffusion", "creator_organization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 0, "show_in_playground": true, "isFeaturedModel": true, "external_pricing_url": "https://www.together.xyz/apis#pricing", "created_at": "2023-07-11T05:07:59.364Z", "update_at": "2023-07-11T05:07:59.364Z", "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}, "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "asks": {"******************************************": 1}, "asks_updated": "2024-05-11T03:33:35.26300478Z", "gpus": {"NVIDIA A40": 1}, "options": {"input=text,image": 1}, "qps": 0.012988208, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 0.23378775}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "656a79054d805f78df5fd530", "name": "zero-one-ai/Yi-34B-<PERSON><PERSON>", "display_name": "01-a<PERSON> (34B)", "display_type": "chat", "description": "The Yi series models are large language models trained from scratch by developers at 01.AI", "license": "yi-license", "creator_organization": "01.AI", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": 3**********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "config": {"add_generation_prompt": true, "stop": ["<|im_start|>", "<|im_end|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n", "chat_template_name": "default"}, "pricing": {"input": 200, "output": 200, "base": 0}, "created_at": "2023-12-02T00:23:33.685Z", "update_at": "2023-12-02T00:26:55.827Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x3faF4AfA52f2A5951B4bde877478B0BF4d69a023": 1, "0x450aDd660C1B1fdB2A7f6bDAE850C4850594FbCD": 1, "0x454Eef2b7f085F0134db5c728ac382aD0c4C9511": 1}, "asks_updated": "2024-05-11T05:55:38.392763822Z", "gpus": {"": 0}, "qps": 0.13333333333333333, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "throughput_in": 128.73333333333332, "throughput_out": 34.266666666666666, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0.2, "qps": 0.13333333333333333, "throughput_in": 128.73333333333332, "throughput_out": 34.266666666666666, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "656fa3548d9fd20968de9ba7", "name": "zero-one-ai/Yi-34B", "display_name": "01-a<PERSON> (34B)", "display_type": "language", "description": "The Yi series models are large language models trained from scratch by developers at 01.AI", "license": "yi-license", "creator_organization": "01.AI", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": 3**********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "pricing": {"input": 200, "output": 200}, "created_at": "2023-12-05T22:25:24.982Z", "update_at": "2023-12-05T22:51:15.306Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 3, "num_bids": 0, "num_running": 0, "asks": {"0x09c253d0c4aB07a89D2f7d23A57eA31bdc760c54": 1, "0x964972F1A61F8BAdbD6163b9888D284CC2E054E9": 1, "0x9B35c58ef3E3425dEa8CBE5f39b8050e40193F68": 1}, "asks_updated": "2024-05-11T12:20:16.94106496Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6570718281b9e1cf0455ec53", "name": "zero-one-ai/Yi-6B", "display_name": "01-a<PERSON> (6B)", "display_type": "language", "description": "The Yi series models are large language models trained from scratch by developers at 01.AI", "license": "yi-license", "creator_organization": "01.AI", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2023-11-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": true, "context_length": 4096, "pricing": {"input": 50, "output": 50}, "created_at": "2023-12-06T13:05:06.567Z", "update_at": "2023-12-06T13:07:50.190Z", "instances": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>"}], "access": "", "link": "", "descriptionLink": "", "depth": {"num_asks": 2, "num_bids": 0, "num_running": 0, "asks": {"0xB527b0625620ff3AACCEb84008A7A6684E2d6FbA": 1, "******************************************": 1}, "asks_updated": "2024-05-11T02:56:40.830191037Z", "gpus": {"": 0}, "qps": 0, "permit_required": false, "price": {"base": 0, "finetune": 0, "hourly": 0, "input": 0, "output": 0}, "stats": [{"avzone": "us-central-2a", "cluster": "j<PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "66313f416fbdf5d304b833d1", "name": "togethercomputer/Llama-3-8b-chat-hf-int4", "display_name": "Llama3 8B Chat HF INT4", "display_type": "chat", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "Llama-3 (Other)", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct", "creator_organization": "Meta", "pricing_tier": null, "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 8192, "owner_userid": null, "config": {"stop": ["<|eot_id|>"], "chat_template": "{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>\n\n'+ message['content'] | trim + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = bos_token + content %}{% endif %}{{ content }}{% endfor %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}", "bos_token": "<|begin_of_text|>", "eos_token": "<|end_of_text|>", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-04-18T06:07:59.041Z", "update_at": "2024-04-24T19:14:26.075Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-5b", "cluster": "blusterybull"}], "isPrivate": true, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6630011e324b0032b64f35a0", "name": "togethercomputer/Llama-3-8b-chat-hf-int8", "display_name": "Togethercomputer Llama3 8B Instruct Int8", "display_type": "chat", "description": "Llama 3 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align with human preferences for helpfulness and safety.", "license": "Llama-3 (Other)", "link": "https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct", "creator_organization": "Meta", "pricing_tier": null, "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 8192, "owner_userid": null, "config": {"stop": ["<|eot_id|>"], "chat_template": "{% set loop_messages = messages %}{% for message in loop_messages %}{% set content = '<|start_header_id|>' + message['role'] + '<|end_header_id|>\n\n'+ message['content'] | trim + '<|eot_id|>' %}{% if loop.index0 == 0 %}{% set content = bos_token + content %}{% endif %}{{ content }}{% endfor %}{{ '<|start_header_id|>assistant<|end_header_id|>\n\n' }}", "bos_token": "<|begin_of_text|>", "eos_token": "<|end_of_text|>", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2024-04-18T06:07:59.041Z", "update_at": "2024-04-24T19:14:26.075Z", "instances": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>"}, {"avzone": "us-central-5b", "cluster": "blusterybull"}], "isPrivate": true, "access_control": [], "isDedicatedInstance": false, "isFinetuned": false, "access": "", "hardware_label": "", "descriptionLink": "", "depth": {"num_asks": 1, "num_bids": 0, "num_running": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0, "stats": [{"avzone": "us-east-1a", "cluster": "<PERSON><PERSON><PERSON><PERSON>", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}, {"avzone": "us-central-5b", "cluster": "blusterybull", "capacity": 0, "qps": 0, "throughput_in": 0, "throughput_out": 0, "error_rate": 0, "retry_rate": 0}]}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecbe", "name": "EleutherAI/pythia-1b-v0", "display_name": "Pythia (1B)", "display_type": "language", "description": "The Pythia Scaling Suite is a collection of models developed to facilitate interpretability research.", "license": "", "link": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:41.925Z", "update_at": "2023-06-23T20:22:41.925Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "649e1ccca073332e47742415", "name": "replit/replit-code-v1-3b", "display_name": "Replit-Code-v1 (3B)", "display_type": "code", "description": "replit-code-v1-3b is a 2.7B Causal Language Model focused on Code Completion. The model has been trained on a subset of the Stack Dedup v1.2 dataset.", "license": "", "link": "", "creator_organization": "Replit", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "limited", "num_parameters": **********, "release_date": "2023-04-26T00:00:00.000Z", "show_in_playground": "true", "isFeaturedModel": false, "context_length": 2048, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-30T00:07:40.594Z", "update_at": "2023-07-07T20:09:09.965Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aecee", "name": "togethercomputer/Pythia-Chat-Base-7B-v0.16", "display_name": "Pythia-Chat-Base (7B)", "display_type": "chat", "description": "Chat model based on EleutherAI’s Pythia-7B model, and is fine-tuned with data focusing on dialog-style interactions.", "license": "", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 2048, "config": {"prompt_format": "<human>: {prompt}\n<bot>:", "stop": ["<human>"], "chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:44.251Z", "update_at": "2023-06-23T20:22:44.251Z", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64aceada227f790586239d11", "name": "mosaicml/mpt-7b", "display_name": "MPT (7B)", "display_type": "language", "description": "Decoder-style transformer pretrained from scratch on 1T tokens of English text and code.", "license": "", "link": "", "creator_organization": "Mosaic ML", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "default", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:38:34.852Z", "update_at": "2023-07-15T03:06:20.780Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64aceb0e227f790586239d12", "name": "togethercomputer/mpt-30b-chat", "display_name": "MPT-Chat (30B)", "display_type": "chat", "description": "Chat model for dialogue generation finetuned on ShareGPT-Vicuna, Camel-AI, GPTeacher, Guanaco, Baize and some generated datasets.", "license": "", "link": "", "creator_organization": "Mosaic ML", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|im_end|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant", "chat_template_name": "default", "add_generation_prompt": true}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-07-11T05:39:26.078Z", "update_at": "2023-07-11T05:39:26.078Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace6df227f790586239cfc", "name": "google/flan-t5-xl", "display_name": "Flan T5 XL (3B)", "display_type": "language", "description": "T5 fine-tuned on more than 1000 additional tasks covering also more languages, making it better than T5 at majority of tasks. ", "license": "", "link": "", "creator_organization": "Google", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 512, "config": {"chat_template_name": "default"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:42.261Z", "update_at": "2023-06-23T20:22:42.261Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acebe0227f790586239d17", "name": "NumbersStation/nsql-6B", "display_name": "NSQL (6B)", "display_type": "language", "description": "Foundation model designed specifically for SQL generation tasks. Pre-trained for 3 epochs and fine-tuned for 10 epochs.", "license": "", "creator_organization": "Numbers Station", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "default"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:42:56.540Z", "update_at": "2023-07-11T05:42:56.540Z", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace9ca227f790586239d09", "name": "togethercomputer/Koala-7B", "display_name": "Koala (7B)", "display_type": "chat", "description": "Chatbot trained by fine-tuning LLaMA on dialogue data gathered from the web.", "license": "", "link": "", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt} GPT:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ 'USER: ' + message['content'] + ' ' }}{% else %}{{ 'GPT: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ 'GPT:' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:34:02.521Z", "update_at": "2023-07-11T05:34:02.521Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc0", "name": "EleutherAI/pythia-6.9b", "display_name": "Pythia (6.9B)", "display_type": "language", "description": "The Pythia Scaling Suite is a collection of models developed to facilitate interpretability research.", "license": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "pricing_tier": "featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:42.044Z", "update_at": "2023-06-23T20:22:42.044Z", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecb8", "name": "databricks/dolly-v2-12b", "display_name": "Dolly v2 (12B)", "display_type": "chat", "description": "An instruction-following LLM based on pythia-12b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.", "license": "", "link": "", "creator_organization": "Databricks", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["### End"], "prompt_format": "### Instruction:\n{prompt}\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:41.607Z", "update_at": "2023-06-23T20:22:41.607Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecb6", "name": "databricks/dolly-v2-3b", "display_name": "Dolly v2 (3B)", "display_type": "chat", "description": "An instruction-following LLM based on pythia-3b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.", "license": "", "link": "", "creator_organization": "Databricks", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["### End"], "prompt_format": "### Instruction:\n{prompt}\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:' }}"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:41.524Z", "update_at": "2023-06-23T20:22:41.524Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc2", "name": "EleutherAI/gpt-neox-20b", "display_name": "GPT-NeoX (20B)", "display_type": "language", "description": "Autoregressive language model trained on the Pile. Its architecture intentionally resembles that of GPT-3, and is almost identical to that of GPT-J 6B.", "license": "", "link": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:42.132Z", "update_at": "2023-06-23T20:22:42.132Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecbf", "name": "EleutherAI/pythia-2.8b-v0", "display_name": "Pythia (2.8B)", "display_type": "language", "description": "The Pythia Scaling Suite is a collection of models developed to facilitate interpretability research.", "license": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:41.975Z", "update_at": "2023-06-23T20:22:41.975Z", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acebb2227f790586239d16", "name": "NousResearch/Nous-Hermes-13b", "display_name": "<PERSON><PERSON> (13B)", "display_type": "language", "description": "LLaMA 13B fine-tuned on over 300,000 instructions. Designed for long responses, low hallucination rate, and absence of censorship mechanisms.", "license": "", "link": "", "creator_organization": "Nous Research", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "llama", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:42:10.444Z", "update_at": "2023-07-11T05:42:10.444Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace8d1227f790586239d03", "name": "togethercomputer/guanaco-65b", "display_name": "Guanaco (65B) ", "display_type": "chat", "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.", "license": "", "link": "", "creator_organization": "<PERSON>", "hardware_label": "2X A100 80GB", "pricing_tier": "Supported", "access": "open", "num_parameters": 65000000000, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["###"], "prompt_format": "### Human: {prompt} ### Assistant:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Human: ' + message['content'] + ' ' }}{% else %}{{ '### Assistant: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant:' }}"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-11T05:29:53.740Z", "update_at": "2023-07-11T05:29:53.740Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07e5", "name": "togethercomputer/llama-2-7b", "display_name": "LLaMA-2 (7B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "renamed": "meta-llama/Llama-2-7b-hf", "hardware_label": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acf031227f790586239d44", "name": "lmsys/fastchat-t5-3b-v1.0", "display_name": "Vicuna-FastChat-T5 (3B)", "display_type": "chat", "description": "Chatbot trained by fine-tuning Flan-t5-xl on user-shared conversations collected from ShareGPT.", "license": "", "link": "", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 512, "config": {"stop": ["###", "</s>"], "prompt_format": "### Human: {prompt}\n### Assistant:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Human: ' + message['content'] + '\n' }}{% else %}{{ '### Assistant: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant:' }}"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-07-11T06:01:21.713Z", "update_at": "2023-07-11T06:01:21.713Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acea6e227f790586239d0e", "name": "huggyllama/llama-7b", "display_name": "LLaMA (7B)", "display_type": "language", "description": "An auto-regressive language model, based on the transformer architecture. The model comes in different sizes: 7B, 13B, 33B and 65B parameters.", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:36:46.255Z", "update_at": "2023-07-11T05:36:46.255Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc9", "name": "OpenAssistant/stablelm-7b-sft-v7-epoch-3", "display_name": "Open-Assistant StableLM SFT-7 (7B)", "display_type": "chat", "description": "Chat-based and open-source assistant. The vision of the project is to make a large language model that can run on a single high-end consumer GPU. ", "license": "", "link": "", "creator_organization": "LAION", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"stop": ["<|endoftext|>"], "prompt_format": "<|prompter|>{prompt}<|endoftext|><|assistant|>", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '<|prompter|>' + message['content'] + '<|endoftext|>' }}{% else %}{{ '<|assistant|>' + message['content'] + '<|endoftext|>\n' }}{% endif %}{% endfor %}{{ '<|assistant|>' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:42.425Z", "update_at": "2023-06-23T20:22:42.425Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc1", "name": "EleutherAI/pythia-12b-v0", "display_name": "Pythia (12B)", "display_type": "language", "description": "The Pythia Scaling Suite is a collection of models developed to facilitate interpretability research.", "license": "", "link": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "gpt"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:42.091Z", "update_at": "2023-06-23T20:22:42.091Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64aceb28227f790586239d13", "name": "togethercomputer/mpt-7b-chat", "display_name": "MPT-Chat (7B)", "display_type": "chat", "description": "Chat model for dialogue generation finetuned on ShareGPT-Vicuna, Camel-AI, GPTeacher, Guanaco, Baize and some generated datasets.", "license": "", "link": "", "creator_organization": "Mosaic ML", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|im_end|>"], "prompt_format": "<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant", "chat_template_name": "default", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:39:52.024Z", "update_at": "2023-07-11T05:39:52.024Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecbc", "name": "EleutherAI/gpt-j-6b", "display_name": "GPT-J (6B)", "display_type": "language", "description": "Transformer model trained using <PERSON>'s Mesh Transformer JAX. ", "license": "", "link": "", "creator_organization": "EleutherAI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "release_date": "2021-06-04T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:41.831Z", "update_at": "2023-06-23T20:22:41.831Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc8", "name": "OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5", "display_name": "Open-Assistant <PERSON><PERSON><PERSON> SFT-4 (12B)", "display_type": "chat", "description": "Chat-based and open-source assistant. The vision of the project is to make a large language model that can run on a single high-end consumer GPU. ", "license": "", "link": "", "creator_organization": "LAION", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "prompt_format": "<|prompter|>{prompt}<|endoftext|><|assistant|>", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '<|prompter|>' + message['content'] + '<|endoftext|>' }}{% else %}{{ '<|assistant|>' + message['content'] + '<|endoftext|>\n' }}{% endif %}{% endfor %}{{ '<|assistant|>' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:42.383Z", "update_at": "2023-06-23T20:22:42.383Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acf013227f790586239d43", "name": "lmsys/vicuna-7b-v1.3", "display_name": "Vicuna v1.3 (7B)", "display_type": "chat", "description": "Chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT. Auto-regressive model, based on the transformer architecture.", "license": "", "link": "", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt}\nASSISTANT:", "chat_template": "{% for message in messages %}{{message['role'].toLocaleUpperCase() + ': ' + message['content'] + '\n'}}{% endfor %}{{ 'ASSISTANT:' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T06:00:51.553Z", "update_at": "2023-07-11T06:00:51.553Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64fbbc5adfdb1e4b06b5d5cc", "name": "Phind/Phind-CodeLlama-34B-Python-v1", "display_name": "Phind Code LLaMA Python v1 (34B)", "display_type": "code", "description": "This model is fine-tuned from CodeLlama-34B-Python and achieves 69.5% pass@1 on HumanEval.", "license": "", "creator_organization": "Phind", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 33743970304, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"prompt_format": "### Instruction:\n{prompt}\n### Response:\n", "stop": ["</s>", "###"], "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-09-09T00:29:14.496Z", "update_at": "2023-09-09T00:29:14.496Z", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65ac4e5e75846d9d3ae5b836", "name": "NumbersStation/nsql-llama-2-7B", "display_name": "NSQL LLaMA-2 (7B)", "display_type": "code", "description": "NSQL is a family of autoregressive open-source large foundation models (FMs) designed specifically for SQL generation tasks", "link": "", "creator_organization": "Numbers Station", "hardware_label": "A100", "pricing_tier": "Featured", "num_parameters": **********, "release_date": "2024-01-20T22:51:10.492Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "pricing": {"hourly": 0, "input": 50, "output": 50, "finetune": 0, "base": 0}, "created_at": "2024-01-20T22:51:10.492Z", "update_at": "2024-01-20T22:59:48.333Z", "access": "", "license": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6532f0faf94bacfc629b4cf8", "name": "NousResearch/Nous-Hermes-Llama2-70b", "display_name": "Nous Hermes LLaMA-2 (70B)", "display_type": "chat", "description": "Nous-Hermes-Llama2-70b is a state-of-the-art language model fine-tuned on over 300,000 instructions.", "license": "", "link": "", "creator_organization": "<PERSON>usResearch", "hardware_label": "2X A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"stop": ["###", "</s>"], "prompt_format": "### Instruction:\n{prompt}\n\n### Response:\n", "chat_template_name": "llama", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:\n' }}"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-10-20T21:28:26.404Z", "update_at": "2023-10-24T17:43:39.278Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f67555bc372ce719b97f03", "name": "WizardLM/WizardLM-70B-V1.0", "display_name": "WizardLM v1.0 (70B)", "display_type": "language", "description": "This model achieves a substantial and comprehensive improvement on coding, mathematical reasoning and open-domain conversation capacities.", "license": "", "creator_organization": "WizardLM", "hardware_label": "2x A100 80GB", "pricing_tier": "supported", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt} ASSISTANT:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ 'USER: ' + message['content'] + ' ' }}{% else %}{{ 'ASSISTANT:' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ 'ASSISTANT:' }}"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-09-05T00:24:53.327Z", "update_at": "2023-09-05T00:24:53.327Z", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acea57227f790586239d0d", "name": "huggyllama/llama-65b", "display_name": "LLaMA (65B)", "display_type": "language", "description": "An auto-regressive language model, based on the transformer architecture. The model comes in different sizes: 7B, 13B, 33B and 65B parameters.", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "2x A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 65000000000, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "llama"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-11T05:36:23.656Z", "update_at": "2023-07-11T05:36:23.656Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64fbbc5adfdb1e4b06b5d5ce", "name": "lmsys/vicuna-13b-v1.5-16k", "display_name": "Vicuna v1.5 16K (13B)", "display_type": "chat", "description": "<PERSON><PERSON> is a chat assistant trained by fine-tuning Llama 2 on user-shared conversations collected from ShareGPT.", "license": "", "creator_organization": "LM Sys", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 13015864320, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"prompt_format": "USER: {prompt}\nASSISTANT:", "stop": ["</s>"], "chat_template": "{% for message in messages %}{{message['role'].toLocaleUpperCase() + ': ' + message['content'] + '\n'}}{% endfor %}{{ 'ASSISTANT:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-09-09T00:29:14.496Z", "update_at": "2023-09-09T00:29:14.496Z", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece4", "name": "togethercomputer/GPT-NeoXT-Chat-Base-20B", "display_name": "GPT-NeoXT-Chat-Base (20B)", "display_type": "chat", "description": "Chat model fine-tuned from EleutherAI’s GPT-NeoX with over 40 million instructions on carbon reduced compute.", "license": "", "link": "", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********0, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"prompt_format": "<human>: {prompt}\n<bot>:", "stop": ["<human>"], "chat_template_name": "gpt"}, "max_tokens": 995, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:43.702Z", "update_at": "2023-06-23T20:22:43.702Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "657bed666aca120ac2af2fb7", "name": "HuggingFaceH4/zephyr-7b-beta", "display_name": "Zephyr-7B-ß", "display_type": "chat", "description": "A fine-tuned version of Mistral-7B to act as a helpful assistant.", "license": "", "link": "", "creator_organization": "HuggingFace", "hardware_label": "2x A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 32768, "config": {"stop": ["[INST]", "</s>"], "prompt_format": "<s>[INST] {prompt} [INST]"}, "created_at": "2023-12-15T06:08:38.925Z", "update_at": "2023-12-15T06:08:38.925Z", "isFinetuned": false, "descriptionLink": "", "pricing": {"hourly": 0, "input": 0, "output": 0, "base": 0, "finetune": 0}}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e78eba589782acafe1781f", "name": "togethercomputer/CodeLlama-13b-Python", "display_name": "Code Llama Python (13B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "13016028160", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-08-24T17:09:14.381Z", "update_at": "2023-12-20T22:52:59.177Z", "renamed": "codellama/CodeLlama-13b-Python-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07e7", "name": "togethercomputer/llama-2-13b", "display_name": "LLaMA-2 (13B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "13015864320", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-12-04T05:07:52.318Z", "renamed": "meta-llama/Llama-2-13b-hf", "hardware_label": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e78e89589782acafe1781d", "name": "togethercomputer/CodeLlama-7b-Instruct", "display_name": "Code Llama Instruct (7B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["</s>", "[INST]"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "renamed": "codellama/CodeLlama-7b-Instruct-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f0de22caa9e2eb543b373b", "name": "togethercomputer/guanaco-13b", "display_name": "Guanaco (13B) ", "display_type": "chat", "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.", "license": "", "link": "", "creator_organization": "<PERSON>", "hardware_label": "A40 48GB", "pricing_tier": "Supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["###"], "prompt_format": "### Human: {prompt} ### Assistant:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Human: ' + message['content'] + ' ' }}{% else %}{{ '### Assistant: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:29:07.717Z", "update_at": "2023-07-11T05:29:07.717Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e7934a589782acafe17822", "name": "togethercomputer/CodeLlama-34b-Python", "display_name": "Code Llama Python (34B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "renamed": "codellama/CodeLlama-34b-Python-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64aceb6f227f790586239d15", "name": "mosaicml/mpt-7b-instruct", "display_name": "MPT-Instruct (7B)", "display_type": "language", "description": "Designed for short-form instruction following, finetuned on Dolly and Anthropic HH-RLHF and other datasets", "license": "", "link": "", "creator_organization": "Mosaic ML", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["<|endoftext|>"], "chat_template_name": "default", "add_generation_prompt": true}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:41:03.757Z", "update_at": "2023-07-11T05:41:03.757Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07ea", "name": "togethercomputer/llama-2-70b-chat", "display_name": "LLaMA-2 <PERSON><PERSON> (70B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "2X A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "68976648192", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "autopilot_pool": "cr-a100-80-2x", "renamed": "meta-llama/Llama-2-70b-chat-hf", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e7934a589782acafe17823", "name": "togethercomputer/CodeLlama-34b-Instruct", "display_name": "Code Llama Instruct (34B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["</s>", "[INST]"], "chat_template_name": "llama", "tools_template": "{{ '<<SYS>>\\n' + systemMessage['content'] + '\\n\\nYou can access the following functions. Use them if required -\\n' + tools + '\\n<</SYS>>\\n\\n' + message['content'] }}"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "renamed": "codellama/CodeLlama-34b-Instruct-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e7934a589782acafe17821", "name": "togethercomputer/CodeLlama-34b", "display_name": "Code Llama (34B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "renamed": "codellama/CodeLlama-34b-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aecf1", "name": "Salesforce/codegen2-16B", "display_name": "CodeGen2 (16B)", "display_type": "code", "description": "An autoregressive language models for program synthesis.", "license": "", "link": "", "creator_organization": "Salesforce", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "release_date": "2022-03-25T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["\n\n"], "chat_template_name": "gpt"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-06-23T20:22:44.453Z", "update_at": "2023-06-23T20:22:44.453Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace476227f790586239cef", "name": "Salesforce/codegen2-7B", "display_name": "CodeGen2 (7B)", "display_type": "code", "description": "An autoregressive language models for program synthesis.", "license": "", "link": "", "creator_organization": "Salesforce", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": **********, "release_date": "2022-03-25T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["\n\n"], "chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:11:18.328Z", "update_at": "2023-07-11T05:11:18.328Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1212907e072b8aecc5", "name": "google/flan-t5-xxl", "display_name": "Flan T5 XXL (11B)", "display_type": "language", "description": "Flan T5 XXL (11B parameters) is T5 fine-tuned on 1.8K tasks ([paper](https://arxiv.org/pdf/2210.11416.pdf)).", "creator_organization": "Google", "hardware_label": "A40 48GB", "access": "open", "show_in_playground": true, "isFeaturedModel": false, "context_length": 512, "config": {"chat_template_name": "default"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:42.261Z", "update_at": "2023-09-01T14:35:00.161Z", "license": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07e9", "name": "togethercomputer/llama-2-70b", "display_name": "LLaMA-2 (70B)", "display_type": "language", "description": "Language model trained on 2 trillion tokens with double the context length of Llama 1. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "2X A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": "68976648192", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "llama"}, "pricing": {"input": 225, "output": 225, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "autopilot_pool": "cr-a100-80-2x", "renamed": "meta-llama/Llama-2-70b-hf", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425f", "name": "codellama/CodeLlama-7b-hf", "display_name": "Code Llama (7B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de95e620478cfa14425c", "name": "codellama/CodeLlama-13b-hf", "display_name": "Code Llama (13B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "13016028160", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-08-24T17:09:14.381Z", "update_at": "2023-12-21T01:12:38.916Z", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e78eba589782acafe17820", "name": "togethercomputer/CodeLlama-13b-Instruct", "display_name": "Code Llama Instruct (13B)", "display_type": "chat", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "pricing_tier": "Featured", "num_parameters": "13016028160", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["</s>", "[INST]"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-08-24T17:09:14.381Z", "update_at": "2023-12-04T05:01:42.539Z", "renamed": "codellama/CodeLlama-13b-Instruct-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07e8", "name": "togethercomputer/llama-2-13b-chat", "display_name": "LLaMA-2 <PERSON><PERSON> (13B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "13015864320", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama"}, "pricing": {"input": 55, "output": 55, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-12-04T05:00:54.436Z", "renamed": "meta-llama/Llama-2-13b-chat-hf", "hardware_label": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acefe5227f790586239d41", "name": "lmsys/vicuna-13b-v1.3", "display_name": "Vicuna v1.3 (13B)", "display_type": "chat", "description": "Chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT. Auto-regressive model, based on the transformer architecture.", "license": "", "link": "", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt}\nASSISTANT:", "chat_template": "{% for message in messages %}{{message['role'].toLocaleUpperCase() + ': ' + message['content'] + '\n'}}{% endfor %}{{ 'ASSISTANT:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T06:00:05.166Z", "update_at": "2023-07-15T03:08:44.173Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acea0b227f790586239d0b", "name": "huggyllama/llama-13b", "display_name": "LLaMA (13B)", "display_type": "language", "description": "An auto-regressive language model, based on the transformer architecture. The model comes in different sizes: 7B, 13B, 33B and 65B parameters.", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "llama"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:35:07.955Z", "update_at": "2023-07-11T05:35:07.955Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acefbe227f790586239d40", "name": "HuggingFaceH4/starchat-alpha", "display_name": "StarCoderChat Alpha (16B)", "display_type": "chat", "description": "Fine-tuned from StarCoder to act as a helpful coding assistant. As an alpha release is only intended for educational or research purpopses.", "license": "", "link": "", "creator_organization": "HuggingFaceH4", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 8192, "config": {"stop": ["<|endoftext|>", "<|end|>"], "prompt_format": "<|system|>\n<|end|>\n<|user|>\n{prompt}<|end|>\n<|assistant|>", "chat_template_name": "default"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:59:26.298Z", "update_at": "2023-07-11T05:59:26.298Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acea35227f790586239d0c", "name": "huggyllama/llama-30b", "display_name": "LLaMA (30B)", "display_type": "language", "description": "An auto-regressive language model, based on the transformer architecture. The model comes in different sizes: 7B, 13B, 33B and 65B parameters.", "license": "", "link": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "access": "open", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "llama"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-07-11T05:35:49.870Z", "update_at": "2023-07-11T05:35:49.870Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1412907e072b8aecf4", "name": "stabilityai/stablelm-base-alpha-3b", "display_name": "StableLM-Base-Alpha (3B)", "display_type": "language", "description": "Decoder-only language model pre-trained on a diverse collection of English and Code datasets with a sequence length of 4096.", "license": "", "link": "", "creator_organization": "Stability AI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 25, "output": 25, "hourly": 0}, "created_at": "2023-06-23T20:22:44.907Z", "update_at": "2023-06-23T20:22:44.907Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1512907e072b8aecf5", "name": "stabilityai/stablelm-base-alpha-7b", "display_name": "StableLM-Base-Alpha (7B)", "display_type": "language", "description": "Decoder-only language model pre-trained on a diverse collection of English and Code datasets with a sequence length of 4096.", "license": "", "link": "", "creator_organization": "Stability AI", "hardware_label": "A40 48GB", "pricing_tier": "supported", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:45.249Z", "update_at": "2023-06-23T20:22:45.249Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64e78e89589782acafe1781c", "name": "togethercomputer/CodeLlama-7b-Python", "display_name": "Code Llama Python (7B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-08-24T17:08:25.379Z", "update_at": "2023-08-24T17:08:25.379Z", "renamed": "codellama/CodeLlama-7b-Python-hf", "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64f67987bc372ce719b97f07", "name": "defog/sqlcoder", "display_name": "Sqlcoder (15B)", "display_type": "language", "description": "Defog's SQLCoder is a state-of-the-art LLM for converting natural language questions to SQL queries, fine-tuned from Bigcode's Starcoder 15B model.", "license": "", "creator_organization": "Defog", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 15000000000, "show_in_playground": true, "isFeaturedModel": false, "context_length": 8192, "config": {"stop": ["<|endoftext|>"], "prompt_format": "### Instructions:\n\n{prompt}\n\n### Response:\n"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-09-05T00:42:47.496Z", "update_at": "2023-09-05T00:42:47.496Z", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64acef6e227f790586239d3f", "name": "bigcode/starcoder", "display_name": "StarCoder (16B)", "display_type": "code", "description": "Trained on 80+ coding languages, uses Multi Query Attention, an 8K context window, and was trained using the Fill-in-the-Middle objective on 1T tokens.", "license": "", "link": "", "creator_organization": "BigCode", "hardware_label": "A100 80GB", "pricing_tier": "supported", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 8192, "config": {"stop": ["<|endoftext|>", "<|end|>"]}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:58:06.486Z", "update_at": "2023-07-11T05:58:06.486Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1112907e072b8aecb7", "name": "databricks/dolly-v2-7b", "display_name": "Dolly v2 (7B)", "display_type": "chat", "description": "An instruction-following LLM based on pythia-7b, and trained on ~15k instruction/response fine tuning records generated by Databricks employees.", "license": "", "link": "", "creator_organization": "Databricks", "hardware_label": "A40 48GB", "pricing_tier": "featured", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["### End"], "prompt_format": "### Instruction:\n{prompt}\n### Response:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Instruction:\n' + message['content'] + '\n' }}{% else %}{{ '### Response:\n' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Response:' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:41.565Z", "update_at": "2023-06-23T20:22:41.565Z", "access": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace8a3227f790586239d02", "name": "togethercomputer/guanaco-33b", "display_name": "Guanaco (33B) ", "display_type": "chat", "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks.", "license": "", "link": "", "creator_organization": "<PERSON>", "hardware_label": "A100 80GB", "pricing_tier": "Supported", "access": "open", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["###"], "prompt_format": "### Human: {prompt} ### Assistant:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Human: ' + message['content'] + ' ' }}{% else %}{{ '### Assistant: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant:' }}"}, "pricing": {"input": 200, "output": 200, "hourly": 0}, "created_at": "2023-07-11T05:29:07.717Z", "update_at": "2023-07-11T05:29:07.717Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace9b1227f790586239d07", "name": "togethercomputer/Koala-13B", "display_name": "Koala (13B)", "display_type": "chat", "description": "Chatbot trained by fine-tuning LLaMA on dialogue data gathered from the web.", "license": "", "link": "", "creator_organization": "LM Sys", "hardware_label": "A40 48GB", "pricing_tier": "supported", "access": "open", "num_parameters": 1**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["</s>"], "prompt_format": "USER: {prompt} GPT:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ 'USER: ' + message['content'] + ' ' }}{% else %}{{ 'GPT: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ 'GPT:' }}"}, "pricing": {"input": 75, "output": 75, "hourly": 0}, "created_at": "2023-07-11T05:33:37.737Z", "update_at": "2023-07-11T05:33:37.737Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6495ff1312907e072b8aece2", "name": "togethercomputer/GPT-JT-6B-v1", "display_name": "GPT-JT (6B)", "display_type": "language", "description": "Fork of GPT-J instruction tuned to excel at few-shot prompts (blog post).", "descriptionLink": "https://www.together.xyz/blog/releasing-v1-of-gpt-jt-powered-by-open-source-ai", "license": "", "link": "", "creator_organization": "Together", "hardware_label": "A40 48GB", "pricing_tier": "featured", "access": "open", "num_parameters": **********, "release_date": "2022-11-29T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"chat_template_name": "gpt"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-06-23T20:22:43.617Z", "update_at": "2023-06-23T20:22:43.617Z"}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64b7165fcccc52103e2f07e6", "name": "togethercomputer/llama-2-7b-chat", "display_name": "LLaMA-2 Chat (7B)", "display_type": "chat", "description": "Llama 2-chat leverages publicly available instruction datasets and over 1 million human annotations. Available in three sizes: 7B, 13B and 70B parameters", "license": "", "link": "", "creator_organization": "Meta", "pricing_tier": "Featured", "access": "open", "num_parameters": "**********", "show_in_playground": true, "finetuning_supported": true, "isFeaturedModel": false, "context_length": 4096, "config": {"prompt_format": "[INST] {prompt} [/INST]", "stop": ["[/INST]", "</s>"], "chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-18T22:46:55.042Z", "update_at": "2023-07-18T22:46:55.042Z", "renamed": "meta-llama/Llama-2-7b-chat-hf", "hardware_label": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "662b250e246deee9aefbcc50", "name": "togethercomputer/SOLAR-10.7B-Instruct-v1.0-int4", "display_name": "Upstage SOLAR Instruct v1 (11B)-Int4", "display_type": "chat", "description": "Built on the Llama2 architecture, SOLAR-10.7B incorporates the innovative Upstage Depth Up-Scaling", "license": "", "creator_organization": "upstage", "hardware_label": "A100B", "pricing_tier": "Featured", "num_parameters": 10700000000, "release_date": "2023-12-01T00:00:00.000Z", "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"add_generation_prompt": true, "stop": ["<|im_end|>", "<|im_start|>"], "chat_template": "{% for message in messages %}{{'<|im_start|>'}}{% if message['role'] == 'user' %}{{'user\n' + message['content'] + '<|im_end|>\n'}}{% elif message['role'] == 'assistant' %}{{'assistant\n' + message['content'] + '<|im_end|>\n'}}{% elif message['role'] == 'system' %}{{'system\n' + message['content'] + '<|im_end|>\n'}}{% endif %}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}"}, "pricing": {"input": 75, "output": 75}, "created_at": "2024-04-26T03:52:46.866Z", "update_at": "2024-04-26T03:52:46.866Z", "instances": [], "access": "", "link": "", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "64ace8ed227f790586239d04", "name": "togethercomputer/guanaco-7b", "display_name": "Guanaco (7B) ", "display_type": "chat", "description": "Instruction-following language model built on LLaMA. Expanding upon the initial 52K dataset from the Alpaca model, an additional 534,530 focused on multi-lingual tasks. ", "license": "", "link": "", "creator_organization": "<PERSON>", "hardware_label": "A40 48GB", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 2048, "config": {"stop": ["###"], "prompt_format": "### Human: {prompt} ### Assistant:", "chat_template": "{% for message in messages %}{% if message['role'] == 'user' %}{{ '### Human: ' + message['content'] + ' ' }}{% else %}{{ '### Assistant: ' + message['content'] + '\n' }}{% endif %}{% endfor %}{{ '### Assistant:' }}"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-07-11T05:30:21.531Z", "update_at": "2023-07-11T05:30:21.531Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "6532f0faf94bacfc629b4cf7", "name": "EleutherAI/llemma_7b", "display_name": "Llemma (7B)", "display_type": "language", "description": "Llemma 7B is a language model for mathematics. It was initialized with Code Llama 7B weights, and trained on the Proof-Pile-2 for 200B tokens.", "license": "", "link": "", "creator_organization": "EleutherAI", "hardware_label": "A100 80GB", "pricing_tier": "Featured", "access": "open", "num_parameters": **********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 4096, "config": {"chat_template_name": "llama"}, "pricing": {"input": 50, "output": 50, "hourly": 0}, "created_at": "2023-10-20T21:28:26.403Z", "update_at": "2023-10-24T17:42:38.630Z", "descriptionLink": ""}, {"modelInstanceConfig": {"appearsIn": [], "order": 0}, "_id": "65a6de96e620478cfa144262", "name": "codellama/CodeLlama-34b-hf", "display_name": "Code Llama (34B)", "display_type": "code", "description": "Code Llama is a family of large language models for code based on Llama 2 providing infilling capabilities, support for large input contexts, and zero-shot instruction following ability for programming tasks.", "license": "", "creator_organization": "Meta", "hardware_label": "A100 80GB", "num_parameters": 3**********, "show_in_playground": true, "isFeaturedModel": false, "context_length": 16384, "config": {"stop": ["</s>"], "chat_template_name": "llama"}, "pricing": {"input": 194, "output": 194, "hourly": 0}, "created_at": "2023-08-24T17:28:42.172Z", "update_at": "2023-08-24T17:28:42.172Z", "access": "", "link": "", "descriptionLink": ""}]
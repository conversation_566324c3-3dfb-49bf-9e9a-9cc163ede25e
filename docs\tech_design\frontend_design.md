# 前端技术设计文档

## 1. 框架选择

**选定框架:** React

**选择原因:**

1.  **组件化:** React 的核心是组件化，非常适合构建复杂的用户界面，可以将 UI 分解为独立、可复用的部分。
2.  **生态系统:** 拥有庞大且成熟的生态系统，有丰富的第三方库和工具，可以加速开发过程。
3.  **性能:** 通过虚拟 DOM 和优化机制，React 在处理大量数据和频繁更新时通常能提供良好的性能。
4.  **社区支持:** 广泛的应用和活跃的社区意味着更容易找到解决方案和获取帮助。
5.  **灵活性:** React 本身只是视图层库，可以与其他库（如路由、状态管理）灵活组合。

## 2. 总体架构

前端应用将采用单页应用 (SPA) 的架构模式，使用 React Router 进行客户端路由管理。

主要模块包括：

*   用户认证 (登录/注册)
*   角色创建/编辑
*   角色展示/社区浏览
*   聊天互动
*   个人中心

## 3. 组件结构

应用将遵循组件化原则进行构建。核心组件可能包括：

*   **Layout Components:** 页面布局，如 Header, Footer, Sidebar。
*   **Page Components:** 对应不同的页面，如 `CharacterCreationPage`, `ChatPage`, `CommunityPage`。
*   **Feature Components:** 实现特定功能，如 `CharacterForm`, `ChatMessageList`, `CharacterCard`。
*   **UI Components:** 可复用的基础 UI 元素，如 Button, Input, Modal。

组件之间通过 props 传递数据和函数，通过状态管理库处理跨层级或全局状态。

## 4. 状态管理

对于应用的状态管理，可以考虑以下方案：

*   **React Context API:** 适用于简单到中等复杂度的应用，或用于管理局部共享状态。
*   **Redux / Zustand / Recoil:** 适用于复杂应用或需要严格状态管理和可预测性时。Zustand 和 Recoil 相对更现代化和简洁。

初步阶段可以优先使用 React Context API 结合 `useState` 和 `useReducer`，如果应用复杂度增加，可以迁移到 Redux 或其他库。

## 5. API 集成

前端将通过 HTTP 请求与后端 API 进行通信。可以使用浏览器内置的 `fetch` API 或第三方库如 `axios` 来发送请求。可以创建一个专门的 API 服务模块来统一管理 API 调用、错误处理和认证。

数据交互格式将遵循后端 API 设计文档 (`docs/tech_design/backend_api_design.md`) 中定义的 JSON 格式。

## 6. 样式方案

样式方案可以根据团队偏好和项目需求选择：

*   **CSS Modules:** 避免样式冲突，易于管理组件级别的样式。
*   **Sass/Less:** 使用预处理器增强 CSS 功能，如变量、嵌套、混合。
*   **Styled Components / Emotion (CSS-in-JS):** 将样式直接写在 JavaScript/TypeScript 代码中，提供更强的动态性和组件封装。
*   **Tailwind CSS (Utility-First CSS):** 通过组合预设的工具类来构建界面，开发速度快。

建议在项目初期选择一种方案并保持一致性。例如，可以从 CSS Modules 开始，或者直接使用 Tailwind CSS 加速开发。

## 7. 潜在库/工具

*   **React Router:** 用于客户端路由。
*   **Axios:** 用于 HTTP 请求。
*   **状态管理库:** Context API（内置）或 Zustand/Redux 等。
*   **UI 组件库:** 如 Ant Design, Material UI, Chakra UI，可以加速界面开发（根据设计需求选择）。
*   **表单库:** 如 Formik, React Hook Form，简化表单处理。
*   **构建工具:** Create React App, Next.js, Vite 等。考虑到项目的需求和未来的 SSR/SSG 可能性，可以考虑 Next.js 或 Vite。

## 8. 待细化项

-   **具体的状态管理方案选择和设计：**
    -   **选择：** 考虑到项目的复杂度和未来扩展性，推荐使用 **Zustand** 作为主要的状态管理库。它比 Redux 更轻量、更简洁，同时提供了强大的功能，如中间件、Immer 集成等，非常适合 React 项目。
    -   **设计：**
        -   **全局状态：** 用于存储用户认证状态、全局通知信息、主题设置等。
        -   **模块状态：** 针对特定业务模块（如角色创建表单、聊天界面）的状态，可以使用 Zustand 创建独立的 store，或结合 React 的 `useState`/`useReducer`。
        -   **数据获取状态：** 对于后端 API 数据，可以使用 `react-query` (TanStack Query) 等库来管理数据获取、缓存、更新和错误处理，与 Zustand 配合使用。
-   **具体的 UI 组件库选择与定制：**
    -   **选择：** 考虑到二次元风格和快速开发，推荐使用 **Ant Design** (AntD) 或 **Chakra UI**。
        -   **Ant Design：** 拥有丰富的企业级组件，风格偏向简洁专业，通过定制主题可以调整为二次元风格。
        -   **Chakra UI：** 提供了强大的样式系统和可组合的组件，更灵活，易于定制，适合快速构建。
    -   **定制：**
        -   **主题定制：** 根据产品 UI 设计稿，定制 UI 组件库的主题（颜色、字体、圆角等），使其符合二次元风格。
        -   **自定义组件：** 对于特定业务场景或复杂交互的组件，可以基于 UI 组件库的基础组件进行封装和开发，形成项目独有的组件库。
-   **如何处理前端路由和导航：**
    -   **路由库：** 使用 **React Router DOM**，它是 React 应用中最常用的路由库。
    -   **路由配置：** 集中配置所有路由，包括公共路由、需要认证的路由和嵌套路由。使用 `BrowserRouter`。
    -   **导航：**
        -   **Header/Sidebar 导航：** 根据设计稿实现顶部导航和侧边导航，使用 React Router 的 `Link` 组件进行声明式导航。
        -   **程序化导航：** 在某些业务逻辑完成后，使用 `useNavigate` hook 进行程序化路由跳转。
        -   **动态路由：** 对于角色详情页 (`/characters/:id`)，使用动态路由参数。
-   **前端性能优化策略：**
    -   **代码分割 (Code Splitting):** 使用 React 的 `lazy` 和 `Suspense` 或路由懒加载，按需加载组件和代码，减少初始加载时间。
    -   **图片优化：** 对角色图片进行压缩、使用 WebP 等现代化格式、按需加载（懒加载）、响应式图片。
    -   **虚拟列表 (Virtualization):** 对于大量的角色卡片列表或聊天消息列表，使用虚拟列表库（如 `react-window`, `react-virtualized`）渲染可见区域的元素，提高滚动性能。
    -   **避免不必要的重新渲染：** 合理使用 `React.memo`, `useCallback`, `useMemo`，减少不必要的组件重新渲染。
    -   **数据缓存：** 利用 `react-query` 等库对后端数据进行缓存，减少重复请求。
    -   **CDN：** 静态资源（图片、JS/CSS 文件）部署到 CDN 加速分发。
-   **前端安全性考虑：**
    -   **HTTPS：** 前后端通信全程使用 HTTPS 加密，防止数据窃听。
    -   **Token 安全存储：** 用户登录后获得的 `access_token` 存储在 `HttpOnly` 的 `Cookie` 中（推荐），或者 `localStorage` (次选，需防范 XSS 攻击)。避免直接在 JS 代码中暴露敏感信息。
    -   **XSS 防护：** 对用户输入的所有内容进行严格的转义或过滤，避免渲染恶意脚本。
    -   **CSRF 防护：** 对于 `POST`, `PUT`, `DELETE` 等非幂等操作，通过 CSRF Token 机制进行防护（如果后端框架支持）。
    -   **输入验证：** 前端对用户输入进行初步验证，减轻后端压力并提升用户体验。但最终验证仍以后端为准。
    -   **依赖安全：** 定期审查和更新第三方库，避免引入已知安全漏洞。
-   **构建工具和部署流程：**
    -   **构建工具：** 推荐使用 **Vite**。它提供了极快的冷启动和热更新速度，并且开箱即用地支持 React，构建效率高。如果未来需要 SSR/SSG，可以考虑 Next.js。
    -   **包管理：** 使用 `npm` 或 `yarn`。
    -   **部署流程：**
        -   **CI/CD：** 设置自动化 CI/CD 流水线，在代码提交后自动执行测试、构建、部署。
        -   **容器化：** 将前端应用打包成 Docker 镜像，便于部署到云服务器或 Kubernetes 集群。
        -   **静态资源托管：** 将构建后的前端静态文件部署到云存储服务（如阿里云 OSS）并配置 CDN 加速。
-   **国际化 (i18n) / 本地化 (l10n) 策略 (未来考虑)：**
    -   **库选择：** 使用 `react-i18next` 或 `react-intl`。
    -   **文本管理：** 将所有用户可见的文本（包括 UI 界面、错误消息等）提取到 JSON 文件中，按语言进行组织。
    -   **语言切换：** 提供 UI 界面供用户切换语言。
-   **前端错误日志收集与监控 (与后端错误日志统一)：**
    -   **错误捕获：** 使用 `ErrorBoundary` 组件捕获 React 组件渲染阶段的错误。
    -   **日志上报：** 将前端运行时错误（JS 错误、网络请求错误等）上报到后端日志服务或专门的前端错误监控服务（如 Sentry）。
    -   **用户体验：** 发生错误时，向用户显示友好的错误提示，并引导用户进行操作或反馈。
    -   **性能监控：** 集成前端性能监控工具，收集页面加载时间、资源加载失败率等指标。

这份文档概述了前端的技术选型和初步设计思路。在实际开发中，会根据具体需求进一步细化。 
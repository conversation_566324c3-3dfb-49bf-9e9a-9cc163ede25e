/* 首页容器 */
.homepage-container {
  background: transparent;
  min-height: 100vh;
  padding: 0;
  width: 100%;
  max-width: 1400px; /* 限制最大宽度，配合MainLayout居中 */
  margin: 0 auto; /* 确保居中 */
}

/* 欢迎区域 */
.welcome-section {
  background: var(--gradient-primary);
  padding: var(--spacing-3xl) var(--spacing-lg);
  text-align: center;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.welcome-content {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-title {
  color: var(--color-text-primary) !important;
  font-size: var(--font-size-3xl) !important;
  font-weight: 700 !important;
  margin-bottom: var(--spacing-md) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-description {
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-lg) !important;
  margin-bottom: var(--spacing-xl) !important;
  line-height: 1.6;
}

/* 搜索区域 */
.search-section {
  margin-bottom: var(--spacing-xl);
}

.main-search {
  max-width: 500px;
  margin: 0 auto;
}

.main-search .ant-input {
  background: var(--color-bg-container) !important;
  border-color: var(--color-border-primary) !important;
  color: var(--color-text-primary) !important;
  font-size: var(--font-size-base) !important;
  height: 48px !important;
}

.main-search .ant-input::placeholder {
  color: var(--color-text-tertiary) !important;
}

.main-search .ant-btn {
  height: 48px !important;
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

/* 统计信息 */
.stats-section {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--color-bg-elevated);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.stat-item .ant-typography {
  color: var(--color-text-primary) !important;
  margin: 0 !important;
}

/* 分类导航区域 */
.categories-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--color-bg-container);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.categories-container {
  width: 100%;
  margin: 0;
}

.categories-list {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  justify-content: center;
}

.category-btn {
  border-radius: var(--border-radius-lg) !important;
  height: 40px !important;
  padding: 0 var(--spacing-md) !important;
  font-weight: 500 !important;
  transition: var(--transition-fast) !important;
  border-color: var(--color-border-primary) !important;
  background: var(--color-bg-base) !important;
  color: var(--color-text-primary) !important;
}

.category-btn:hover {
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.category-btn.active {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: white !important;
  box-shadow: var(--shadow-primary);
}

.category-count {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  margin-left: var(--spacing-xs);
}

/* 角色展示区域 */
.characters-section {
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--color-bg-base);
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto;
}

.character-grid {
  width: 100% !important;
  margin: 0 !important;
  gap: var(--spacing-lg) !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
}

.character-card {
  border-radius: var(--border-radius-lg) !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 1px solid var(--color-border-primary) !important;
  background: var(--color-bg-base) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.character-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--color-primary) !important;
}

.character-card .ant-card-body {
  padding: var(--spacing-md) !important;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.character-image-container {
  position: relative;
  width: 100%;
  height: 280px;
  overflow: hidden;
  flex-shrink: 0;
  background: var(--color-bg-elevated);
}

.character-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.character-card:hover .character-image {
  transform: scale(1.08);
}

/* 响应式Grid布局 */
@media (min-width: 1200px) {
  .character-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .character-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .character-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 575px) {
  .character-grid {
    grid-template-columns: 1fr !important;
  }

  .characters-section {
    padding: var(--spacing-md);
  }

  .character-image-container {
    height: 240px;
  }
}

.character-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-fast);
}

.character-card:hover .character-overlay {
  opacity: 1;
}

.chat-btn {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  border-radius: var(--border-radius-lg) !important;
  font-weight: 500 !important;
  box-shadow: var(--shadow-primary) !important;
}

.chat-btn:hover {
  transform: scale(1.05) !important;
}

/* 角色卡片内容 */
.character-card .ant-card-meta-title {
  color: var(--color-text-primary) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-base) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.character-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.character-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.character-tags .ant-tag {
  margin: 0 !important;
  border-radius: var(--border-radius-sm) !important;
  font-size: var(--font-size-xs) !important;
  border: none !important;
  font-weight: 500 !important;
}

.character-creator {
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-sm) !important;
  margin-top: auto;
  padding-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 卡片操作区域 */
.character-card .ant-card-actions {
  background: var(--color-bg-elevated) !important;
  border-top-color: var(--color-border-primary) !important;
}

.card-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--color-text-secondary) !important;
  font-size: var(--font-size-sm) !important;
}

.card-action:hover {
  color: var(--color-primary) !important;
  transform: scale(1.05);
}

.card-action .liked {
  color: var(--color-primary) !important;
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-container {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-container .ant-empty-description {
  color: var(--color-text-secondary) !important;
}

/* 移除移动端适配 - 专注桌面端体验 */

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .homepage-container {
    padding: 0 var(--spacing-xl);
  }

  .character-grid {
    gap: var(--spacing-lg);
  }

  .character-image-container {
    height: 320px;
  }
}

@media (min-width: 2560px) {
  .homepage-container {
    padding: 0 var(--spacing-2xl);
  }

  .character-image-container {
    height: 360px;
  }
}

/* 额外的视觉优化 */
.character-card .ant-card-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.character-card .ant-card-meta-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.character-card .ant-card-meta-description {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 强制覆盖任何可能的全局样式冲突 */
.homepage-container .character-grid {
  display: flex !important;
  flex-wrap: wrap !important;
}

.homepage-container .character-grid .ant-col {
  display: flex !important;
  flex-direction: column !important;
}

.homepage-container .character-card {
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
}

/* 加载状态优化 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: var(--color-bg-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  width: 100%;
}

/* 空状态优化 */
.empty-container {
  padding: var(--spacing-2xl) !important;
  background: var(--color-bg-base) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
}

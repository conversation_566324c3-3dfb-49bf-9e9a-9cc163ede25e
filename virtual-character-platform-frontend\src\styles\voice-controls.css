/* 语音控制组件样式 */
.voice-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.voice-button {
  min-width: 120px;
  height: 48px;
  border-radius: 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.voice-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.voice-button.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-color: #ff6b6b;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

.voice-error {
  text-align: center;
  max-width: 200px;
  word-wrap: break-word;
}

/* 语音状态指示器 */
.voice-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.voice-indicator.active {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
  transition: all 0.3s ease;
}

.voice-indicator.active .indicator-dot {
  background: #ff6b6b;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.indicator-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.voice-indicator.active .indicator-text {
  color: #ff6b6b;
}

/* 情感状态显示 */
.emotion-display {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.emotion-icon {
  font-size: 16px;
  line-height: 1;
}

.emotion-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 沉浸式模式下的语音控制 */
.immersive-voice-controls {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.immersive-voice-controls .voice-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.immersive-voice-controls .voice-button:hover {
  transform: translateY(-4px) scale(1.05);
}

.immersive-voice-controls .voice-button.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border-color: transparent;
}

/* 语音控制的最小化模式 */
.voice-controls-minimal {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  backdrop-filter: blur(10px);
}

.voice-controls-minimal .voice-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  min-width: unset;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移除移动端适配 - 专注桌面端体验 */

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .voice-indicator,
  .emotion-display {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
  }
  
  .indicator-text,
  .emotion-text {
    color: #ccc;
  }
  
  .voice-indicator.active .indicator-text {
    color: #ff6b6b;
  }
  
  .immersive-voice-controls .voice-button {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    border-color: rgba(255, 255, 255, 0.2);
  }
}

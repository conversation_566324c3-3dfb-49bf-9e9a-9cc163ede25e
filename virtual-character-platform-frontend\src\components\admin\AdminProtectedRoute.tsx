import React from 'react';
import { Navigate } from 'react-router-dom';
import useAdminAuthStore from '../../store/adminAuthStore';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { isAdminLoggedIn } = useAdminAuthStore();
  
  if (!isAdminLoggedIn) {
    return <Navigate to="/admin/login" replace />;
  }
  
  return <>{children}</>;
};

export default AdminProtectedRoute; 
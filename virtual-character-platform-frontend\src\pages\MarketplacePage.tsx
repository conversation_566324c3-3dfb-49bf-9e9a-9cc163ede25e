import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Card, Row, Col, Spin, message, Empty, Input, Select, Button, Tooltip, Tag, Rate } from 'antd';
import { SearchOutlined, HeartOutlined, HeartFilled, MessageOutlined, UserOutlined, CalendarOutlined, EyeOutlined, DownloadOutlined, StarOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { createStyles } from 'antd-style';
import { Flexbox } from 'react-layout-kit';
import { Grid, SearchBar, TabsNav } from '@lobehub/ui';
import { useTranslation } from 'react-i18next';

import { characterAPIAdapter } from '../services/characterAPIAdapter';
import { UnifiedCharacter, RoleCategoryEnum } from '../types/agent';
import LazyImage from '../components/LazyImage';
import { processImageUrl } from '../utils/imageUtils';
import RoleCard from '../components/RoleCard';

const { Content } = Layout;
const { Option } = Select;

const useStyles = createStyles(({ css, token, responsive }) => ({
  container: css`
    overflow-y: auto;
    width: 100%;
    height: 100%;
    min-height: 500px;
    padding: 0 32px 16px;
    max-width: 1400px;
    margin: 0 auto;

    ${responsive.mobile} {
      padding: 0 16px;
    }
  `,
  header: css`
    margin-bottom: 24px;
    text-align: center;
    padding: 24px 0;
    background: linear-gradient(135deg, ${token.colorPrimary}15, ${token.colorPrimaryBg});
    border-radius: ${token.borderRadiusLG}px;
    border: 1px solid ${token.colorBorder};
  `,
  title: css`
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 8px;
    background: linear-gradient(135deg, ${token.colorPrimary}, ${token.colorPrimaryActive});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  `,
  description: css`
    font-size: 16px;
    color: ${token.colorTextSecondary};
    margin-bottom: 24px;
  `,
  filters: css`
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;

    ${responsive.mobile} {
      flex-direction: column;
      gap: 12px;
    }
  `,
  searchBar: css`
    flex: 1;
    min-width: 300px;

    ${responsive.mobile} {
      width: 100%;
      min-width: auto;
    }
  `,
  characterGrid: css`
    margin-bottom: 32px;
  `,
  characterCard: css`
    height: 100%;
    transition: all 0.3s ease;
    border-radius: ${token.borderRadiusLG}px;
    overflow: hidden;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: ${token.boxShadowSecondary};
    }
  `,
  cardCover: css`
    height: 200px;
    overflow: hidden;
    position: relative;
  `,
  cardImage: css`
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  `,
  cardTag: css`
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
  `,
  officialBadge: css`
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #000;
    border: none;
    font-weight: bold;
  `,
  cardActions: css`
    display: flex;
    justify-content: space-around;
    padding: 12px 0;
    border-top: 1px solid ${token.colorBorder};
  `,
  cardAction: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    padding: 8px;
    border-radius: ${token.borderRadius}px;
    transition: all 0.2s ease;
    color: ${token.colorTextSecondary};
    
    &:hover {
      background: ${token.colorBgTextHover};
      color: ${token.colorPrimary};
    }
    
    &.liked {
      color: #ff4d4f;
    }
  `,
  stats: css`
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 12px;
    color: ${token.colorTextTertiary};
  `,
  loadingContainer: css`
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  `,
  emptyContainer: css`
    margin: 64px 0;
    padding: 48px;
    text-align: center;
  `
}));

// 分类选项
const CATEGORIES = [
  { key: 'all', label: '全部' },
  { label: '游戏', key: RoleCategoryEnum.GAME },
  { label: 'Vroid', key: RoleCategoryEnum.VROID },
  { label: '动漫', key: RoleCategoryEnum.ANIME },
  { label: '动物', key: RoleCategoryEnum.ANIMAL },
  { label: '书籍', key: RoleCategoryEnum.BOOK },
  { label: '历史', key: RoleCategoryEnum.HISTORY },
  { label: '电影', key: RoleCategoryEnum.MOVIE },
  { label: '现实', key: RoleCategoryEnum.REALISTIC },
  { label: 'VTuber', key: RoleCategoryEnum.VTUBER },
];

const MarketplacePage: React.FC = () => {
  const navigate = useNavigate();
  const { styles } = useStyles();
  const { t } = useTranslation();

  // 状态管理
  const [characters, setCharacters] = useState<UnifiedCharacter[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('latest');
  const [stats, setStats] = useState({
    totalCharacters: 0,
    totalCreators: 0,
    totalDownloads: 0
  });

  // 加载角色列表
  const loadCharacters = useCallback(async () => {
    setLoading(true);
    try {
      const response = await characterAPIAdapter.getPublicCharacters(1, 50, searchKeyword, sortBy);
      setCharacters(response);
      
      // 更新统计信息
      setStats({
        totalCharacters: response.length,
        totalCreators: new Set(response.map(c => c.creator.id)).size,
        totalDownloads: response.reduce((sum, c) => sum + (c.downloadCount || 0), 0)
      });
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  }, [searchKeyword, sortBy]);

  // 初始加载
  useEffect(() => {
    loadCharacters();
  }, [loadCharacters]);

  // 筛选角色
  const filteredCharacters = characters.filter((character) => {
    const matchCategory = activeCategory === 'all' || character.meta.category === activeCategory;
    const matchSearch = 
      character.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      character.meta.description.toLowerCase().includes(searchKeyword.toLowerCase());
    return matchCategory && matchSearch;
  });

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchKeyword(value);
  }, []);

  // 处理排序变化
  const handleSortChange = useCallback((value: string) => {
    setSortBy(value);
  }, []);

  // 处理点赞
  const handleLike = async (character: UnifiedCharacter) => {
    try {
      // 乐观更新UI
      const updatedCharacters = characters.map(c => {
        if (c.id === character.id) {
          return {
            ...c,
            likes: c.isLiked ? c.likes - 1 : c.likes + 1,
            isLiked: !c.isLiked
          };
        }
        return c;
      });

      setCharacters(updatedCharacters);
      message.success(character.isLiked ? '已取消点赞' : '点赞成功');
    } catch (error) {
      console.error('更新点赞状态失败:', error);
      message.error('操作失败，请稍后再试');
      // 恢复原状态
      loadCharacters();
    }
  };

  // 处理聊天
  const handleChat = (character: UnifiedCharacter) => {
    navigate(`/chat/${character.id}`);
  };

  // 处理下载/订阅
  const handleDownload = async (character: UnifiedCharacter) => {
    try {
      // 这里可以实现下载或订阅逻辑
      message.success(`已添加 ${character.name} 到我的角色`);
    } catch (error) {
      console.error('下载角色失败:', error);
      message.error('操作失败，请稍后再试');
    }
  };

  // 格式化时间
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    return `${Math.floor(diffInSeconds / 86400)}天前`;
  };

  return (
    <Content className={styles.container}>
      {/* 头部区域 */}
      <div className={styles.header}>
        <h1 className={styles.title}>角色商场</h1>
        <p className={styles.description}>
          发现精彩的AI角色，开启有趣的对话体验
        </p>
        
        {/* 统计信息 */}
        <div className={styles.stats}>
          <span>共 {stats.totalCharacters} 个角色</span>
          <span>•</span>
          <span>{stats.totalCreators} 位创作者</span>
          <span>•</span>
          <span>{stats.totalDownloads} 次下载</span>
        </div>
      </div>

      {/* 筛选区域 */}
      <div className={styles.filters}>
        <SearchBar
          placeholder="搜索角色名称或描述..."
          value={searchKeyword}
          className={styles.searchBar}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
        
        <Select
          value={sortBy}
          onChange={handleSortChange}
          style={{ width: 120 }}
        >
          <Option value="latest">最新</Option>
          <Option value="popular">最热门</Option>
          <Option value="rating">评分最高</Option>
          <Option value="downloads">下载最多</Option>
        </Select>
      </div>

      {/* 分类导航 */}
      <TabsNav
        activeKey={activeCategory}
        items={CATEGORIES}
        onChange={(key) => setActiveCategory(key)}
        style={{ marginBottom: 24 }}
      />

      {/* 角色网格 */}
      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin size="large" tip="正在加载角色列表...">
            <div style={{ minHeight: '300px' }} />
          </Spin>
        </div>
      ) : filteredCharacters.length > 0 ? (
        <Grid maxItemWidth={280} gap={24} className={styles.characterGrid}>
          {filteredCharacters.map((character) => (
            <RoleCard
              key={character.id}
              agent={character}
              onClick={() => handleChat(character)}
            />
          ))}
        </Grid>
      ) : (
        <Empty
          description="暂无符合条件的角色"
          image={Empty.PRESENTED_IMAGE_DEFAULT}
          className={styles.emptyContainer}
        >
          <Button
            type="primary"
            size="large"
            onClick={() => navigate('/create-character')}
          >
            创建角色
          </Button>
        </Empty>
      )}
    </Content>
  );
};

export default MarketplacePage;

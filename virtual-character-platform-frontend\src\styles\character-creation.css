.character-creation-container {
  padding: 20px;
  background: #f9f9f9;
  min-height: 80vh;
}

/* 卡片样式 */
.creation-card,
.preview-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 表单区域 */
.creation-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-section {
  margin-bottom: 16px;
}

.form-section h3 {
  margin-bottom: 12px;
  color: #eb2f96;
  font-weight: 500;
}

.prompt-input {
  margin-bottom: 16px;
  border-radius: 8px;
}

.generate-btn {
  width: 100%;
}

/* 预览区域 */
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
  background: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.character-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.empty-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  color: #999;
  background: #f5f5f5;
  border-radius: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  height: 100%;
  padding: 20px;
  text-align: center;
}

/* 按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.save-btn {
  min-width: 120px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .preview-container {
    height: 500px;
  }
}

@media (max-width: 992px) {
  .preview-container {
    height: 450px;
  }
}

/* 移除移动端适配 - 专注桌面端体验 */
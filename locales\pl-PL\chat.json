{"ModelSelect": {"featureTag": {"custom": "Model niesta<PERSON><PERSON><PERSON>, do<PERSON><PERSON>lne ustawienia obsługują zarówno wywołania funkcji, jak i rozpoznawanie wizualne. Proszę zweryfikować dostępność powyższych możliwości w zależności od rzeczywistych potrzeb", "file": "Ten model obsługuje przesyłanie plików do odczytu i rozpoznawania", "functionCall": "Ten model obsługu<PERSON> wywołania funk<PERSON>ji (Function Call)", "tokens": "Ten model obsługuje maksymalnie {{tokens}} tokenów w pojedynczej sesji", "vision": "Ten model obsług<PERSON><PERSON> rozpoznawanie wizualne"}, "removed": "Ten model nie znajduje się na liście, je<PERSON><PERSON>, zostanie automatycznie usunięty"}, "actions": {"add": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copySuccess": "Kopiowanie zakończone sukcesem", "del": "Usuń", "delAndRegenerate": "Usuń i regeneruj", "edit": "<PERSON><PERSON><PERSON><PERSON>", "goBottom": "Przejdź na dół", "regenerate": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "share": "Udostępnij", "tts": "<PERSON><PERSON><PERSON>"}, "agentInfo": "Informacje o roli", "agentMarket": "Rynek postaci", "animation": {"animationList": "Lista animacji", "postureList": "Lista postaw", "totalCount": "Łącznie {{total}} pozycji"}, "apiKey": {"addProxy": "Dodaj adres proxy OpenAI (opcjonalnie)", "closeTip": "Zamknij wskazówkę", "confirmRetry": "Potwierdź i spróbuj ponownie", "proxyDocs": "<PERSON><PERSON>, jak ubiegać się o klucz API?", "startDesc": "Wprowadź swój klucz API OpenAI, aby roz<PERSON> rozmowę. Aplikacja nie będzie rejestrować twojego klucza API.", "startTitle": "Dostosuj klucz API"}, "background": {"backgroundList": "Lista tła", "totalCount": "Łącznie {{total}} pozycji"}, "callOff": "Zakończ połączenie", "camera": "Wideokonferencja", "chat": "<PERSON><PERSON><PERSON>", "chatList": "Lista czatów", "clear": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kontekst", "alert": "<PERSON>zy na pewno chcesz usunąć historię wiadomości?", "tip": "Ta operacja jest ni<PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> d<PERSON> ostrożnie"}, "danceList": "Lista tańców", "danceMarket": "Rynek tańca", "delSession": "<PERSON><PERSON><PERSON> sesję", "delSessionAlert": "<PERSON>zy na pewno chcesz usunąć rozmowę? Po usunięciu nie będzie można jej przyw<PERSON><PERSON><PERSON>, proszę działać ostrożnie!", "editRole": {"action": "<PERSON><PERSON><PERSON><PERSON>"}, "enableHistoryCount": {"alias": "Brak ogranicz<PERSON>ń", "limited": "Zawiera tylko {{number}} wiadomoś<PERSON> sesji", "setlimited": "Ustaw liczbę wiadomości w historii", "title": "Ogranicz liczbę wiadomości w historii", "unlimited": "Brak ograniczeń w historii wiadomości"}, "info": {"background": "Tło", "chat": "czat", "dance": "<PERSON>ie<PERSON>", "motions": "ruchy", "posture": "postawa", "stage": "<PERSON><PERSON>"}, "input": {"alert": "Pamiętaj: <PERSON><PERSON><PERSON><PERSON><PERSON>, co mówi inteligentny agent, jest generowane przez <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> treść, a<PERSON> r<PERSON><PERSON><PERSON> czat", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warp": "Złamanie linii"}, "interactive": "Interaktywny", "noDanceList": "Brak dostępnych list odtwarzania. Możesz subskrybować ulubione tańce na rynku.", "noRoleList": "<PERSON><PERSON> dostępnych ról", "noSession": "Brak a<PERSON><PERSON><PERSON><PERSON>, moż<PERSON>z stworzyć własną rolę przez + lub dodać rolę przez stronę odkrywania", "selectModel": "Wybierz model", "sessionCreate": "Utw<PERSON><PERSON> c<PERSON>t", "sessionList": "<PERSON><PERSON> se<PERSON><PERSON>", "share": {"downloadScreenshot": "Pobierz zrzut ekranu", "imageType": "Format obrazu", "screenshot": "<PERSON>rzut ekranu", "share": "Udostępnij", "shareGPT": "Udostępnij GPT", "shareToGPT": "Generuj link do udostępnienia ShareGPT", "withBackground": "Zawiera tło", "withFooter": "Zawiera stopkę", "withSystemRole": "Zawiera ustawienia roli asystenta"}, "stage": {"stageList": "Lista scen", "totalCount": "Łącznie {{total}} pozycji"}, "token": {"overload": "<PERSON><PERSON> p<PERSON>", "remained": "Pozostałe Tokeny", "tokenCount": "Liczba Tokenów", "useToken": "<PERSON><PERSON><PERSON><PERSON><PERSON>, w tym w<PERSON>, ustawienia postaci i kontekstu: {{usedTokens}} / {{maxValue}}", "used": "Tokeny użyte"}, "toolBar": {"axes": "<PERSON><PERSON>", "cameraControl": "<PERSON><PERSON><PERSON><PERSON>", "cameraHelper": "Pomocnik kamery", "downloading": "<PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> c<PERSON>...", "fullScreen": "Przełącz na pełny ekran", "grid": "Siatka", "interactiveOff": "Wyłącz interakcję dot<PERSON>ową", "interactiveOn": "Włącz interakcję dot<PERSON>ą", "resetCamera": "Zresetuj <PERSON>", "resetToIdle": "Zatrzymaj ruch taneczny", "screenShot": "Zrób zdjęcie"}, "tts": {"combine": "Synte<PERSON><PERSON> mowy", "record": "Rozpoznawanie mowy (wymaga dostępu do internetu przez VPN)"}, "voiceOn": "Włącz głos"}
import { normalizeLocale } from '../locales/resources';

export const getAntdLocale = async (lang?: string) => {
  const normalLang = normalizeLocale(lang);
  const antdLang = normalLang.replace('-', '_');

  // 使用显式的导入路径避免Vite警告
  try {
    let locale;
    switch (antdLang) {
      case 'zh_CN':
        locale = await import('antd/locale/zh_CN.js');
        break;
      case 'zh_TW':
        locale = await import('antd/locale/zh_TW.js');
        break;
      case 'en_US':
        locale = await import('antd/locale/en_US.js');
        break;
      case 'ja_JP':
        locale = await import('antd/locale/ja_JP.js');
        break;
      case 'ko_KR':
        locale = await import('antd/locale/ko_KR.js');
        break;
      case 'ru_RU':
        locale = await import('antd/locale/ru_RU.js');
        break;
      case 'fr_FR':
        locale = await import('antd/locale/fr_FR.js');
        break;
      case 'de_DE':
        locale = await import('antd/locale/de_DE.js');
        break;
      case 'es_ES':
        locale = await import('antd/locale/es_ES.js');
        break;
      case 'pt_BR':
        locale = await import('antd/locale/pt_BR.js');
        break;
      case 'it_IT':
        locale = await import('antd/locale/it_IT.js');
        break;
      case 'ar_EG':
        locale = await import('antd/locale/ar_EG.js');
        break;
      default:
        // 默认使用英文
        locale = await import('antd/locale/en_US.js');
        break;
    }
    return locale.default;
  } catch (error) {
    console.warn(`Failed to load antd locale ${antdLang}, falling back to en_US`);
    const fallback = await import('antd/locale/en_US.js');
    return fallback.default;
  }
};

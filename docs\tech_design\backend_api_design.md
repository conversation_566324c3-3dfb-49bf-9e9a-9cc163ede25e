# 后端 API 接口设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目的后端 API 接口设计，定义了前端与后端进行数据交互的方式，包括接口路径、HTTP 方法、请求参数和响应格式。本设计旨在支持产品需求文档 (PRD) 中定义的各项功能，并为前后端并行开发提供依据。

## 2. 设计原则

- 遵循 RESTful 风格，使用标准 HTTP 方法（GET, POST, PUT, DELETE）表示资源操作。
- API 路径清晰、有意义，反映所操作的资源。
- 使用 JSON 格式进行请求和响应数据的传输。
- 提供明确的错误码和错误信息。

## 3. API 接口定义

### 3.1 角色生成 (抽卡)

用于根据用户选择的基础设定生成新的角色形象（调用星火 API）。

- **URL:** `/api/characters/generate`
- **Method:** `POST`
- **Description:** 根据用户输入的基础设定触发图片生成。
- **Request Body (JSON):**
    ```json
    {
      "race": "string", // 可选，用户选择的种族
      "anime_name": "string", // 可选，用户输入的动漫名字
      "age": "integer", // 用户设定的年龄
      "identity": "string", // 用户设定的身份
      "personality": "string" // 用户设定的性格
      // 可能包含其他影响基础生成的参数
    }
    ```
- **Response Body (JSON):**
    ```json
    {
      "code": 0, // 0: 成功, 非0: 错误码
      "message": "string", // 错误信息或成功提示
      "data": {
        "image_url": "string", // 生成的角色形象图片 URL
        "appearance_params": {}, // JSON 对象，存储本次生成对应的外观参数（待细化结构）
        "generated_prompts": { // 存储本次生成使用的提示词信息
            "image_prompt": "string", // 调用图片 API 的提示词
            "dialogue_prompt_system": "string" // 对话 AI 的系统提示词
        }
        // 可能包含其他与本次生成相关的元数据
      }
    }
    ```

### 3.2 角色定制化 (参数调整 / 语言描述)

用于根据用户对参数的调整或语言描述修改已生成的角色形象。

- **URL:** `/api/characters/{character_id}/customize`
- **Method:** `PUT`
- **Description:** 根据用户指令重新生成或修改角色形象。
- **Request Body (JSON):**
    ```json
    {
      "customization_type": "enum", // "params_adjust" 或 "language_description"
      "adjustment_params": {}, // JSON 对象，如果 customization_type 是 params_adjust，包含要调整的参数和新值（待细化结构）
      "description_text": "string" // 如果 customization_type 是 language_description，包含用户的语言描述文本
      // 可能包含其他辅助信息，例如用户希望修改的区域等
    }
    ```
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": {
        "image_url": "string", // 更新后的角色形象图片 URL
        "appearance_params": {}, // 更新后的外观参数（待细化结构）
        "generated_prompts": { // 存储本次定制化使用的提示词信息
            "image_prompt": "string",
            "dialogue_prompt_system": "string"
        }
        // 可能包含其他与本次更新相关的元数据
      }
    }
    ```

### 3.3 保存角色

用于用户保存当前满意的角色形象和设定。

- **URL:** `/api/characters`
- **Method:** `POST`
- **Description:** 保存一个新的虚拟角色到用户账号下。
- **Request Body (JSON):**
    ```json
    {
      "name": "string", // 角色名称
      "image_url": "string", // 要保存的图片 URL (来自之前的 generate 或 customize 响应)
      "age": "integer", // 年龄设定
      "identity": "string", // 身份设定
      "personality": "string", // 性格设定
      "appearance_params": {}, // 外观参数（待细化结构）
      "settings": {} // 其他设定（待细化结构）
      // 可能包含是否公开等信息
    }
    ```
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": {
        "character_id": "integer" // 保存成功后返回的角色 ID
      }
    }
    ```

### 3.4 获取用户角色列表

用于获取当前用户已保存的所有角色。

- **URL:** `/api/users/{user_id}/characters`
- **Method:** `GET`
- **Description:** 获取指定用户的所有虚拟角色列表。
- **Request Parameters:** 无（或分页参数）
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": [
        { // 角色对象，包含核心信息
          "id": "integer",
          "name": "string",
          "image_url": "string",
          "age": "integer",
          "identity": "string",
          "personality": "string",
          "public": "boolean"
          // 可能包含其他概览信息
        }
        // ... 更多角色对象
      ]
    }
    ```

### 3.5 获取特定角色详情

用于获取某个角色的详细信息，包括所有设定和参数。

- **URL:** `/api/characters/{character_id}`
- **Method:** `GET`
- **Description:** 获取指定 ID 的虚拟角色的详细信息。
- **Request Parameters:** 无
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": {
        "id": "integer",
        "user_id": "integer",
        "name": "string",
        "age": "integer",
        "identity": "string",
        "personality": "string",
        "image_url": "string",
        "created_at": "timestamp",
        "updated_at": "timestamp",
        "public": "boolean",
        "appearance_params": {}, // 详细的外观参数（待细化结构）
        "settings": {} // 详细的其他设定（待细化结构）
        // 可能包含其他详细信息
      }
    }
    ```

### 3.6 获取社区公开角色列表

用于获取社区中所有公开展示的角色列表。

- **URL:** `/api/community/characters`
- **Method:** `GET`
- **Description:** 获取社区中公开展示的虚拟角色列表。
- **Request Parameters:** 分页参数 (page, size)，可选的过滤/搜索参数 (race, identity, personality 等，待细化)
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": [
        { // 角色对象，包含社区展示所需的核心信息
          "id": "integer",
          "name": "string",
          "image_url": "string",
          "age": "integer",
          "identity": "string",
          "personality": "string"
          // 可能包含点赞数等社区相关信息
        }
        // ... 更多角色对象
      ],
      "pagination": { // 分页信息
          "total": "integer",
          "page": "integer",
          "size": "integer"
      }
    }
    ```

### 3.7 发送聊天消息

用于用户向特定角色发送消息并获取回复。

- **URL:** `/api/characters/{character_id}/chat`
- **Method:** `POST`
- **Description:** 向指定角色发送一条聊天消息并获取 AI 回复。
- **Request Body (JSON):**
    ```json
    {
      "user_message": "string" // 用户输入的聊天文本
    }
    ```
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "string",
      "data": {
        "character_response": "string", // 角色（AI）的文本回复
        "audio_url": "string" // 未来，如果实现语音，可能返回语音文件 URL
        // 可能包含其他对话相关信息
      }
    }
    ```

## 4. 待细化项

-   **用户认证和授权相关的 API 接口：**
    -   **用户注册:**
        -   **URL:** `/api/auth/register`
        -   **Method:** `POST`
        -   **Request Body:** `{"username": "string", "email": "string", "password": "string"}`
        -   **Response Body:** `{"code": 0, "message": "string", "data": {"user_id": "integer", "username": "string"}}`
    -   **用户登录:**
        -   **URL:** `/api/auth/login`
        -   **Method:** `POST`
        -   **Request Body:** `{"username": "string", "password": "string"}` (或 `email`)
        -   **Response Body:** `{"code": 0, "message": "string", "data": {"access_token": "string", "token_type": "Bearer", "expires_in": "integer"}}` (参考 `docs/tech_design/user_auth_authorization.md` 中的 Token 机制)
    -   **用户注销:**
        -   **URL:** `/api/auth/logout`
        -   **Method:** `POST`
        -   **Description:** 使当前用户的 Token 失效。
-   **`appearance_params` 和 `settings` 字段在请求和响应中的具体 JSON 结构：**
    -   **`appearance_params` (外观参数):** 定义为灵活的 JSON 对象，包含常见的二次元角色外观属性，例如：
        ```json
        {
          "gender": "string", // "male", "female"
          "height_category": "string", // "tall", "medium", "short"
          "body_type": "string", // "slim", "athletic", "curvy", "busty"
          "face_shape": "string", // "oval", "round", "heart"
          "hair_style": "string", // "long straight", "short bob", "ponytail"
          "hair_color": "string", // "black", "blonde", "pink"
          "eye_color": "string", // "blue", "red", "green"
          "eye_shape": "string", // "large", "narrow"
          "clothing_style": "string", // "casual", "formal", "fantasy"
          "accessories": ["string"] // 例如 ["glasses", "hat"]
          // 可根据需求增加更多细粒度参数
        }
        ```
    -   **`settings` (其他设定):** 定义为灵活的 JSON 对象，包含角色其他非外观的设定，例如：
        ```json
        {
          "voice_type_id": "string", // 关联到 TTS 服务中预设的声音 ID
          "speaking_speed": "number", // 语速 (0.5 - 1.5)
          "speaking_pitch": "number", // 音调 (0.5 - 1.5)
          "background_story": "string", // 角色的背景故事
          "hobbies": ["string"] // 角色的爱好
          // 可根据需求增加更多设定
        }
        ```
-   **参数调整 (`adjustment_params`) 和语言描述 (`description_text`) 如何具体转化为后端处理逻辑和提示词：**
    -   后端在接收到定制化请求后，会将其转发给**提示词工程模块** (`docs/tech_design/prompt_engineering.md`)。
    -   **参数调整:** `adjustment_params` 中的键值对将直接映射或通过预设规则转化为图片生成 API (如星火 API) 所需的特定提示词片段或权重。例如，`{"hair_color": "green"}` 将被转化为 `green hair` 或 `(green hair:1.2)`。
    -   **语言描述:** `description_text` 将通过 NLP 方式（在提示词工程模块内）进行解析，提取出关键修改指令（如"把头发变绿"，"增加胸部大小"），然后将其转化为相应的提示词片段，并与现有角色的外观参数进行合并或覆盖，最终生成新的图片生成提示词。
-   **社区功能的其他 API：**
    -   **点赞角色:**
        -   **URL:** `/api/characters/{character_id}/like`
        -   **Method:** `POST`
        -   **Description:** 用户点赞指定角色。
    -   **取消点赞角色:**
        -   **URL:** `/api/characters/{character_id}/unlike`
        -   **Method:** `POST`
        -   **Description:** 用户取消点赞指定角色。
    -   **获取角色评论列表 (未来考虑):**
        -   **URL:** `/api/characters/{character_id}/comments`
        -   **Method:** `GET`
    -   **发表评论 (未来考虑):**
        -   **URL:** `/api/characters/{character_id}/comments`
        -   **Method:** `POST`
    -   **角色搜索/筛选:**
        -   **URL:** `/api/community/characters/search`
        -   **Method:** `GET`
        -   **Request Parameters:** `query=string`, `age_min=integer`, `age_max=integer`, `personality=string` (多选), `identity=string` (多选), `sort_by=string` (例如 `likes_count`, `created_at`), `order=string` (`asc`, `desc`), `page=integer`, `size=integer`
-   **错误码的详细定义和规范：**
    -   将定义一个全局的错误码体系，例如：
        -   `10000 - 19999`: 通用系统错误 (如 `10001` - 内部服务器错误，`10002` - 无效参数)
        -   `20000 - 29999`: 用户认证/授权错误 (如 `20001` - 用户名或密码错误，`20002` - 未授权)
        -   `30000 - 39999`: 角色相关业务错误 (如 `30001` - 角色不存在，`30002` - 无权操作该角色)
        -   `40000 - 49999`: AI 服务集成错误 (如 `40001` - AI 服务内部错误，`40002` - AI 内容审核不通过)
    -   每个错误码都会有对应的 `message` 字段，提供用户友好的提示信息。
    -   详细的错误码列表将在 `docs/tech_design/error_handling_logging.md` 中统一维护和更新。
-   **API 版本管理：**
    -   采用 URL 版本化，例如 `/api/v1/characters`。
    -   当 API 发生重大不兼容变更时，会发布新版本 `/api/v2/characters`，并维护旧版本一段时间，以便前端有时间进行适配。
-   **安全性方面的考虑：**
    -   **输入验证:** 所有接收用户输入的 API 接口都将进行严格的输入验证和数据清洗，防止 SQL 注入、XSS 攻击等。
    -   **身份验证:** 强制要求敏感 API 接口进行 Token 验证，确保请求来自已认证的用户。
    -   **授权:** 对用户操作的资源进行权限检查，确保用户仅能操作其拥有的或被授权的资源。
    -   **速率限制:** 对频繁调用的 API 接口（如登录、生成）实施速率限制，防止恶意请求和滥用。
    -   **HTTPS:** 所有 API 通信强制使用 HTTPS。
    -   **敏感信息处理:** 避免在 API 响应中暴露敏感的用户或系统信息。日志中对敏感信息进行脱敏处理。
    -   **跨域资源共享 (CORS):** 正确配置 CORS 策略，允许前端应用访问后端 API。

## 5. 已实现文件

目前项目中已实现了部分后端API服务组件：

- 星火API集成服务：
  - `backend-services/services/spark_api_client.py` - 星火API客户端
  - `backend-services/services/spark_auth_utils.py` - 星火API认证工具
  - `backend-services/services/spark_error_handler.py` - 星火API错误处理
  - `backend-services/services/spark_image_service.py` - 星火图像生成服务
  - `backend-services/services/oss_client.py` - 对象存储客户端

- 前端布局组件：
  - `virtual-character-platform-frontend/src/components/Header.tsx` - 页面顶部导航
  - `virtual-character-platform-frontend/src/components/Footer.tsx` - 页面底部
  - `virtual-character-platform-frontend/src/components/Sidebar.tsx` - 侧边栏导航
  - `virtual-character-platform-frontend/src/components/MainLayout.tsx` - 主布局组件
  - `virtual-character-platform-frontend/src/App.tsx` - 应用主入口及路由配置

## 6. 角色背景图片API

### 6.1 获取角色背景图片列表

用于获取指定角色的所有背景图片。

- **URL:** `/api/characters/{character_id}/backgrounds/`
- **Method:** `GET`
- **Description:** 获取角色的背景场景图片列表，支持分页和筛选。
- **Request Parameters:**
  - `status` (可选): 筛选生成状态 (`pending`, `generating`, `completed`, `failed`)
  - `scene_type` (可选): 筛选场景类型 (如 `classroom`, `library` 等)
  - `page` (可选): 页码，默认为 1
  - `page_size` (可选): 每页数量，默认为 20，最大 50
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "success",
      "data": {
        "backgrounds": [
          {
            "id": 1,
            "scene_type": "classroom",
            "scene_name": "教室",
            "image_url": "https://example.com/bg1.jpg",
            "generation_status": "completed",
            "created_at": "2024-01-01T10:00:00Z",
            "updated_at": "2024-01-01T10:05:00Z",
            "error_message": null
          }
        ],
        "pagination": {
          "page": 1,
          "page_size": 20,
          "total": 4,
          "total_pages": 1
        },
        "character_id": 123,
        "character_name": "角色名称",
        "generation_status": {
          "total": 4,
          "status_counts": {
            "pending": 0,
            "generating": 0,
            "completed": 3,
            "failed": 1
          },
          "is_complete": true,
          "success_rate": 0.75
        }
      }
    }
    ```

### 6.2 重试失败的背景图片生成

用于重新生成失败的背景图片。

- **URL:** `/api/characters/{character_id}/backgrounds/retry/`
- **Method:** `POST`
- **Description:** 重试角色的失败背景图片生成任务。
- **Request Body:** 无
- **Response Body (JSON):**
    ```json
    {
      "code": 0,
      "message": "重试任务已启动",
      "data": {
        "character_id": 123,
        "character_name": "角色名称"
      }
    }
    ```

### 6.3 背景图片生成状态说明

背景图片生成采用异步处理机制：

1. **触发时机**: 角色保存成功后自动触发
2. **生成数量**: 每个角色生成 3-5 张背景图片
3. **生成状态**:
   - `pending`: 待生成
   - `generating`: 生成中
   - `completed`: 已完成
   - `failed`: 生成失败

4. **场景类型**: 根据角色身份按概率随机选择：
   - 高中生: 教室(30%)、图书馆(25%)、体育馆(20%)、校园(15%)、宿舍(10%)
   - 偶像: 舞台(35%)、录音室(25%)、音乐厅(20%)、后台(20%)
   - 魔法使: 魔法塔(30%)、魔法森林(25%)、炼金实验室(25%)、魔法图书馆(20%)
   - 等等...

5. **错误处理**: 单张图片生成失败不影响其他图片，支持重试机制

---

这份文档是后端 API 接口的初步设计，旨在覆盖核心功能流程。后续将根据需求细化和具体实现进行完善。
import React, { useState } from 'react';
import { Form, Input, Select, Button, Card, Space, message } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../../store/agent';

const { TextArea } = Input;
const { Option } = Select;

// 预设的角色模板
const ROLE_TEMPLATES = [
  {
    name: '友善助手',
    systemRole: '你是一个友善、耐心的AI助手，总是乐于帮助用户解决问题。你的回答准确、有用，语气温和友好。',
    personality: '友善、耐心、乐于助人',
    background: '专业的AI助手，具有丰富的知识和经验'
  },
  {
    name: '专业顾问',
    systemRole: '你是一个专业的顾问，具有深厚的专业知识和丰富的实践经验。你能够提供准确、专业的建议和指导。',
    personality: '专业、严谨、权威',
    background: '资深专业顾问，在相关领域有多年经验'
  },
  {
    name: '创意伙伴',
    systemRole: '你是一个富有创意和想象力的伙伴，善于激发灵感，提供创新的想法和解决方案。',
    personality: '创意、活泼、富有想象力',
    background: '创意工作者，热爱艺术和创新'
  },
  {
    name: '学习导师',
    systemRole: '你是一个耐心的学习导师，善于解释复杂概念，能够根据学习者的水平调整教学方式。',
    personality: '耐心、博学、善于教导',
    background: '经验丰富的教育工作者'
  }
];

const RoleTab: React.FC = () => {
  const [form] = Form.useForm();
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  
  // 获取当前角色数据
  const [currentAgent, updateAgentConfig] = useAgentStore((s) => [
    agentSelectors.currentAgentItem(s),
    s.updateAgentConfig,
  ]);

  // 处理系统角色变更
  const handleSystemRoleChange = (value: string) => {
    updateAgentConfig({ systemRole: value });
  };

  // 应用角色模板
  const handleApplyTemplate = () => {
    const template = ROLE_TEMPLATES.find(t => t.name === selectedTemplate);
    if (!template) return;

    updateAgentConfig({ 
      systemRole: template.systemRole 
    });
    
    form.setFieldsValue({
      systemRole: template.systemRole
    });
    
    message.success(`已应用"${template.name}"模板`);
  };

  // 重置角色设定
  const handleReset = () => {
    const defaultRole = '你是一个友善的AI助手，总是乐于帮助用户解决问题。';
    updateAgentConfig({ systemRole: defaultRole });
    form.setFieldsValue({ systemRole: defaultRole });
    message.success('已重置为默认角色设定');
  };

  return (
    <div className="role-tab">
      <Flexbox gap={24} style={{ padding: '24px' }}>
        {/* 角色模板选择 */}
        <div className="role-section">
          <h3>角色模板</h3>
          <p className="section-desc">选择预设模板快速配置角色设定</p>
          
          <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
            <Select
              placeholder="选择角色模板"
              value={selectedTemplate}
              onChange={setSelectedTemplate}
              style={{ flex: 1 }}
            >
              {ROLE_TEMPLATES.map(template => (
                <Option key={template.name} value={template.name}>
                  {template.name}
                </Option>
              ))}
            </Select>
            <Button 
              type="primary"
              onClick={handleApplyTemplate}
              disabled={!selectedTemplate}
            >
              应用模板
            </Button>
          </Space.Compact>

          {selectedTemplate && (
            <Card size="small" className="template-preview">
              {(() => {
                const template = ROLE_TEMPLATES.find(t => t.name === selectedTemplate);
                return template ? (
                  <div>
                    <p><strong>性格特征:</strong> {template.personality}</p>
                    <p><strong>背景设定:</strong> {template.background}</p>
                    <p><strong>系统角色:</strong> {template.systemRole}</p>
                  </div>
                ) : null;
              })()}
            </Card>
          )}
        </div>

        {/* 系统角色设定 */}
        <div className="role-section">
          <h3>系统角色设定</h3>
          <p className="section-desc">定义角色的基本行为模式和回应风格</p>
          
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              systemRole: currentAgent?.systemRole || ''
            }}
          >
            <Form.Item
              label="系统角色"
              name="systemRole"
              rules={[
                { required: true, message: '请输入系统角色设定' },
                { min: 10, message: '系统角色设定至少需要10个字符' },
                { max: 2000, message: '系统角色设定不能超过2000个字符' }
              ]}
            >
              <TextArea
                placeholder="描述角色的身份、性格、行为方式、专业领域等..."
                rows={8}
                showCount
                maxLength={2000}
                onChange={(e) => handleSystemRoleChange(e.target.value)}
              />
            </Form.Item>
          </Form>

          <Space>
            <Button onClick={handleReset}>
              重置为默认
            </Button>
          </Space>
        </div>

        {/* 角色行为指南 */}
        <div className="role-section">
          <h3>角色行为指南</h3>
          <Card>
            <div className="behavior-guide">
              <h4>系统角色设定建议：</h4>
              <ul>
                <li><strong>身份定位</strong>: 明确角色的身份和职业背景</li>
                <li><strong>性格特征</strong>: 描述角色的性格特点和行为风格</li>
                <li><strong>专业领域</strong>: 说明角色擅长的领域和技能</li>
                <li><strong>交流方式</strong>: 定义角色的语言风格和交流习惯</li>
                <li><strong>价值观念</strong>: 体现角色的价值观和行为准则</li>
              </ul>

              <h4>示例模板：</h4>
              <div className="example-template">
                <p>
                  "你是一名经验丰富的心理咨询师，名叫李医生。你性格温和、善于倾听，
                  具有深厚的心理学专业知识。在与来访者交流时，你总是保持耐心和同理心，
                  用温暖的语调提供专业的心理支持和建议。你重视保密原则，
                  始终以来访者的福祉为首要考虑。"
                </p>
              </div>

              <h4>注意事项：</h4>
              <ul>
                <li>避免过于复杂或矛盾的设定</li>
                <li>确保设定符合使用场景和用户期望</li>
                <li>定期测试和优化角色表现</li>
                <li>保持设定的一致性和连贯性</li>
              </ul>
            </div>
          </Card>
        </div>
      </Flexbox>
    </div>
  );
};

export default RoleTab;

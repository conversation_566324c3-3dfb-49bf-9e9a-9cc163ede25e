import { characterAPI } from './characterAPI';
import { processImageUrl } from '../utils/imageUtils';
import { UnifiedCharacter, MarketplaceCharacter, RoleCategoryEnum } from '../types/agent';

/**
 * 角色API适配器 - 统一现有API和Lobe风格API
 */
export class CharacterAPIAdapter {
  /**
   * 获取公开角色列表（现有API格式）
   */
  async getPublicCharacters(
    page: number = 1, 
    size: number = 12, 
    query: string = '', 
    sort: string = 'latest'
  ): Promise<UnifiedCharacter[]> {
    try {
      const response = await characterAPI.getPublicCharacters(page, size, query, sort);
      
      if (Array.isArray(response)) {
        return this.transformToUnified(response);
      } else if (response && response.characters) {
        return this.transformToUnified(response.characters);
      }
      
      return [];
    } catch (error) {
      console.error('获取角色列表失败:', error);
      // 返回模拟数据用于开发
      return this.getMockCharacters();
    }
  }

  /**
   * 获取代理索引（Lobe风格API）
   */
  async getAgentIndex(locale: string = 'zh-CN'): Promise<{ agents: UnifiedCharacter[] }> {
    const characters = await this.getPublicCharacters(1, 100, '', 'latest');
    return {
      agents: characters.map(char => this.toLobeFormat(char))
    };
  }

  /**
   * 获取单个角色详情
   */
  async getAgentDetail(agentId: string, locale: string = 'zh-CN'): Promise<UnifiedCharacter | null> {
    try {
      const response = await characterAPI.getCharacterDetail(agentId);
      if (response) {
        return this.transformSingleToUnified(response);
      }
      return null;
    } catch (error) {
      console.error('获取角色详情失败:', error);
      return null;
    }
  }

  /**
   * 数据格式转换：后端数据 -> 统一格式
   */
  private transformToUnified(data: any[]): UnifiedCharacter[] {
    return data.map(char => this.transformSingleToUnified(char));
  }

  /**
   * 单个角色数据转换
   */
  private transformSingleToUnified(char: any): UnifiedCharacter {
    const baseData = {
      // 现有字段映射
      id: char.id?.toString() || char.agentId || '',
      name: char.name || '',
      imageUrl: processImageUrl(char.image_url || char.meta?.cover || ''),
      creator: {
        id: char.creator_id?.toString() || '1',
        username: char.creator_name || char.author || '系统用户'
      },
      personality: char.personality || '',
      identity: char.identity || '',
      createdAt: char.created_at || char.createAt || new Date().toISOString(),
      likes: char.likes || Math.floor(Math.random() * 100),
      isLiked: char.is_liked || false,
      chatCount: char.chat_count || Math.floor(Math.random() * 50),
      viewCount: char.view_count || Math.floor(Math.random() * 200),
      tags: char.tags || [],
      isPublic: char.is_public !== false,

      // Lobe格式映射
      agentId: char.id?.toString() || char.agentId || '',
      meta: {
        name: char.name || '',
        description: this.buildDescription(char),
        cover: processImageUrl(char.image_url || char.meta?.cover || ''),
        avatar: processImageUrl(char.image_url || char.meta?.avatar || char.meta?.cover || ''),
        category: this.mapCategory(char.tags || char.meta?.category),
        gender: char.gender || char.meta?.gender || 'Female',
        model: char.model || char.meta?.model,
        readme: char.description || char.meta?.readme || char.personality || ''
      },
      author: char.creator_name || char.author || '系统用户',
      homepage: char.homepage,
      createAt: char.created_at || char.createAt || new Date().toISOString(),
      greeting: char.greeting || `你好，我是${char.name || '角色'}！`,
      systemRole: char.system_role || char.systemRole || char.personality || '',

      // 商场功能字段
      isSubscribed: char.is_subscribed || false,
      downloadCount: char.download_count || Math.floor(Math.random() * 1000),
      rating: char.rating || (Math.random() * 2 + 3), // 3-5分随机评分
      isOfficial: char.is_official || false,

      // LLM配置
      llmConfig: {
        provider: char.provider || 'openai',
        model: char.model || 'gpt-3.5-turbo',
        temperature: char.temperature || 0.7,
        maxTokens: char.max_tokens || 2000
      }
    };

    return baseData;
  }

  /**
   * 转换为Lobe格式（用于兼容Lobe组件）
   */
  private toLobeFormat(char: UnifiedCharacter): UnifiedCharacter {
    return {
      ...char,
      agentId: char.id,
      meta: {
        ...char.meta,
        name: char.name,
        description: char.meta.description || this.buildDescription(char),
        cover: char.imageUrl
      },
      author: char.creator.username,
      createAt: char.createdAt
    };
  }

  /**
   * 构建角色描述
   */
  private buildDescription(char: any): string {
    const parts = [];
    if (char.personality) parts.push(char.personality);
    if (char.identity) parts.push(char.identity);
    if (char.description) parts.push(char.description);
    
    return parts.join(' | ') || '这是一个有趣的角色';
  }

  /**
   * 映射分类
   */
  private mapCategory(tags: string[] | string): RoleCategoryEnum {
    if (typeof tags === 'string') {
      return this.getCategoryFromString(tags);
    }
    
    if (Array.isArray(tags) && tags.length > 0) {
      return this.getCategoryFromString(tags[0]);
    }
    
    return RoleCategoryEnum.ANIME; // 默认分类
  }

  /**
   * 从字符串获取分类
   */
  private getCategoryFromString(tag: string): RoleCategoryEnum {
    const tagLower = tag.toLowerCase();
    
    if (tagLower.includes('game') || tagLower.includes('游戏')) return RoleCategoryEnum.GAME;
    if (tagLower.includes('anime') || tagLower.includes('动漫')) return RoleCategoryEnum.ANIME;
    if (tagLower.includes('vtuber') || tagLower.includes('虚拟主播')) return RoleCategoryEnum.VTUBER;
    if (tagLower.includes('book') || tagLower.includes('书籍')) return RoleCategoryEnum.BOOK;
    if (tagLower.includes('history') || tagLower.includes('历史')) return RoleCategoryEnum.HISTORY;
    if (tagLower.includes('movie') || tagLower.includes('电影')) return RoleCategoryEnum.MOVIE;
    if (tagLower.includes('animal') || tagLower.includes('动物')) return RoleCategoryEnum.ANIMAL;
    if (tagLower.includes('vroid')) return RoleCategoryEnum.VROID;
    if (tagLower.includes('realistic') || tagLower.includes('现实')) return RoleCategoryEnum.REALISTIC;
    
    return RoleCategoryEnum.ANIME;
  }

  /**
   * 获取模拟数据（开发用）
   */
  private getMockCharacters(): UnifiedCharacter[] {
    const personalities = ['温柔', '活泼', '傲娇', '冷酷', '神秘', '开朗'];
    const identities = ['高中生', '魔法师', '偶像', '公主', '骑士', '学者'];
    const categories = Object.values(RoleCategoryEnum);
    
    return Array(12).fill(0).map((_, index) => ({
      id: `mock-${index + 1}`,
      name: `角色${index + 1}`,
      imageUrl: `/placeholder-character.svg`,
      creator: {
        id: '1',
        username: '系统用户'
      },
      personality: personalities[Math.floor(Math.random() * personalities.length)],
      identity: identities[Math.floor(Math.random() * identities.length)],
      createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
      likes: Math.floor(Math.random() * 100),
      isLiked: Math.random() > 0.5,
      chatCount: Math.floor(Math.random() * 50),
      viewCount: Math.floor(Math.random() * 200),
      tags: ['可爱', '聪明'],
      isPublic: true,
      
      agentId: `mock-${index + 1}`,
      meta: {
        name: `角色${index + 1}`,
        description: `${personalities[Math.floor(Math.random() * personalities.length)]} | ${identities[Math.floor(Math.random() * identities.length)]}`,
        cover: `/placeholder-character.svg`,
        avatar: `/placeholder-character.svg`,
        category: categories[Math.floor(Math.random() * categories.length)],
        gender: Math.random() > 0.5 ? 'Female' : 'Male',
        readme: '这是一个有趣的角色'
      },
      author: '系统用户',
      createAt: new Date().toISOString(),
      greeting: `你好，我是角色${index + 1}！`,
      systemRole: personalities[Math.floor(Math.random() * personalities.length)],
      
      isSubscribed: false,
      downloadCount: Math.floor(Math.random() * 1000),
      rating: Math.random() * 2 + 3,
      isOfficial: index < 3,
      
      llmConfig: {
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000
      }
    }));
  }
}

// 导出单例实例
export const characterAPIAdapter = new CharacterAPIAdapter();

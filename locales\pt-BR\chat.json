{"ModelSelect": {"featureTag": {"custom": "Modelo personalizado, a configuração padrão suporta chamadas de função e reconhecimento visual, por favor, verifique a disponibilidade dessas capacidades de acordo com a situação real", "file": "Este modelo suporta upload de arquivos para leitura e reconhecimento", "functionCall": "Este modelo suporta chamadas de função (Function Call)", "tokens": "Este modelo suporta até {{tokens}} Tokens por sessão", "vision": "Este modelo suporta reconhecimento visual"}, "removed": "Este modelo não está na lista, se desmarcado, será removido automaticamente"}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copySuccess": "Cópia be<PERSON>-sucedida", "del": "Excluir", "delAndRegenerate": "Excluir e regenerar", "edit": "<PERSON><PERSON>", "goBottom": "Ir para o final", "regenerate": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "share": "Compartilhar", "tts": "Voz"}, "agentInfo": "Informações do papel", "agentMarket": "<PERSON><PERSON><PERSON> de Person<PERSON>ns", "animation": {"animationList": "Lista de Ações", "postureList": "Lista de Posturas", "totalCount": "Total de {{total}} itens"}, "apiKey": {"addProxy": "Adicionar endereço de proxy OpenAI (opcional)", "closeTip": "<PERSON><PERSON>r dica", "confirmRetry": "Confirmar e tentar novamente", "proxyDocs": "Não sei como solicitar uma chave de API?", "startDesc": "Insira sua chave de API OpenAI para iniciar a conversa. O aplicativo não registrará sua chave de API.", "startTitle": "Chave de API personalizada"}, "background": {"backgroundList": "Lista de Fundos", "totalCount": "Total de {{total}} itens"}, "callOff": "En<PERSON><PERSON>", "camera": "Chamada de vídeo", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatList": "Lista de Chats", "clear": {"action": "Limpar <PERSON>", "alert": "Tem certeza de que deseja excluir as mensagens históricas?", "tip": "Esta ação é irreversível, por favor, proceda com cautela"}, "danceList": "Lista de Danças", "danceMarket": "Mercado de Danças", "delSession": "Excluir se<PERSON>ão", "delSessionAlert": "Você tem certeza de que deseja excluir a conversa? Após a exclusão, não será possível recuperar. Por favor, proceda com cautela!", "editRole": {"action": "<PERSON><PERSON> p<PERSON>"}, "enableHistoryCount": {"alias": "Sem restrições", "limited": "Apenas inclui {{number}} mensagens de sessão", "setlimited": "Usar número de mensagens históricas", "title": "Limitar número de mensagens históricas", "unlimited": "Sem limite de mensagens históricas"}, "info": {"background": "Fundo", "chat": "bate-papo", "dance": "<PERSON><PERSON><PERSON>", "motions": "movimentos", "posture": "postura", "stage": "palco"}, "input": {"alert": "Por favor, lembre-se: tudo o que o agente diz é gerado por IA", "placeholder": "Digite o conteúdo para começar a conversar", "send": "Enviar", "warp": "<PERSON><PERSON><PERSON> linha"}, "interactive": "Interativo", "noDanceList": "Nenhuma lista de reprodução disponível no momento. Você pode assinar suas danças favoritas no mercado.", "noRoleList": "Nenhuma lista de papéis disponível", "noSession": "Nenhuma sessão disponível, você pode criar um papel personalizado através de + ou adicionar papéis pela página de descoberta", "selectModel": "Por favor, selecione um modelo", "sessionCreate": "Criar chat", "sessionList": "Lista de Sessões", "share": {"downloadScreenshot": "Baixar captura de tela", "imageType": "Formato da imagem", "screenshot": "Captura de tela", "share": "Compartilhar", "shareGPT": "Compartilhar GPT", "shareToGPT": "Gerar link de compartilhamento ShareGPT", "withBackground": "Incluir imagem de fundo", "withFooter": "<PERSON><PERSON>ir <PERSON>", "withSystemRole": "Incluir configuração de papel do assistente"}, "stage": {"stageList": "Lista de Palcos", "totalCount": "Total de {{total}} itens"}, "token": {"overload": "Token excedido", "remained": "Token restante", "tokenCount": "Quantidade de Token", "useToken": "<PERSON><PERSON><PERSON><PERSON><PERSON> da quantidade de Token consumida, incluindo mensagens, configurações de personagem e contexto: {{usedTokens}} / {{maxValue}}", "used": "Token utilizado"}, "toolBar": {"axes": "Eixos", "cameraControl": "Controle da Câmera", "cameraHelper": "<PERSON><PERSON><PERSON> da Câmera", "downloading": "<PERSON><PERSON><PERSON> modelo, por favor aguarde...", "fullScreen": "Alternar para tela cheia", "grid": "Grade", "interactiveOff": "Desativar interação por toque", "interactiveOn": "Ativar interação por toque", "resetCamera": "Redefinir <PERSON>", "resetToIdle": "Parar Ação de Dança", "screenShot": "Captura de Tela"}, "tts": {"combine": "Síntese de voz", "record": "Reconhecimento de voz (necessita de acesso à internet com VPN)"}, "voiceOn": "Ativar voz"}
import { DiscordIcon, Icon } from '@lobehub/ui';
import type { ItemType } from 'antd/es/menu/interface';
import { Book, Download, Feather, LifeBuoy, Mail, Settings2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';

import type { MenuProps } from '../../components/Menu';
import { DISCORD, DOCUMENTS_REFER_URL, EMAIL_SUPPORT, GITHUB_ISSUES } from '../../constants/url';
// import DataImporter from '@/features/DataImporter';
import { usePWAInstall } from '../../hooks/usePWAInstall';
import { SettingsTabs } from '../../store/global';

// import { configService } from '@/services/config';

export const useMenu = () => {
  const { canInstall, install } = usePWAInstall();
  const { t } = useTranslation(['common', 'setting']);
  const navigate = useNavigate();

  const settings: MenuProps['items'] = [
    {
      label: t('userPanel.setting'),
      onClick: () => navigate(urlJoin('/settings', SettingsTabs.Common)),
      icon: <Icon icon={Settings2} />,
      key: 'setting',
    },
    {
      type: 'divider',
    },
  ];

  /* ↓ cloud slot ↓ */

  /* ↑ cloud slot ↑ */

  const pwa: MenuProps['items'] = [
    {
      icon: <Icon icon={Download} />,
      key: 'pwa',
      label: t('installPWA'),
      onClick: () => install(),
    },
    {
      type: 'divider',
    },
  ];

  // const data = [
  //   // {
  //   //   icon: <Icon icon={HardDriveDownload} />,
  //   //   key: 'import',
  //   //   label: <DataImporter>{t('import')}</DataImporter>,
  //   // },
  //   {
  //     children: [
  //       // {
  //       //   key: 'allAgent',
  //       //   label: t('exportType.allAgent'),
  //       //   onClick: configService.exportAgents,
  //       // },
  //       // {
  //       //   key: 'allAgentWithMessage',
  //       //   label: t('exportType.allAgentWithMessage'),
  //       //   onClick: configService.exportSessions,
  //       // },
  //       // {
  //       //   key: 'globalSetting',
  //       //   label: t('exportType.globalSetting'),
  //       //   onClick: configService.exportSettings,
  //       // },
  //       {
  //         type: 'divider',
  //       },
  //       // {
  //       //   key: 'all',
  //       //   label: t('exportType.all'),
  //       //   onClick: configService.exportAll,
  //       // },
  //     ],
  //     icon: <Icon icon={HardDriveUpload} />,
  //     key: 'export',
  //     label: t('export'),
  //   },
  //   {
  //     type: 'divider',
  //   },
  // ].filter(Boolean) as ItemType[];

  const helps: MenuProps['items'] = [
    {
      icon: <Icon icon={DiscordIcon} />,
      key: 'discord',
      label: (
        <a href={DISCORD} target={'_blank'} rel="noopener noreferrer">
          {t('userPanel.discord')}
        </a>
      ),
    },
    {
      children: [
        {
          icon: <Icon icon={Book} />,
          key: 'docs',
          label: (
            <a href={DOCUMENTS_REFER_URL} target={'_blank'} rel="noopener noreferrer">
              {t('userPanel.docs')}
            </a>
          ),
        },
        {
          icon: <Icon icon={Feather} />,
          key: 'feedback',
          label: (
            <a href={GITHUB_ISSUES} target={'_blank'} rel="noopener noreferrer">
              {t('userPanel.feedback')}
            </a>
          ),
        },
        {
          icon: <Icon icon={Mail} />,
          key: 'email',
          label: (
            <a href={EMAIL_SUPPORT} target={'_blank'} rel="noopener noreferrer">
              {t('userPanel.email')}
            </a>
          ),
        },
      ],
      icon: <Icon icon={LifeBuoy} />,
      key: 'help',
      label: t('userPanel.help'),
    },
    {
      type: 'divider',
    },
  ].filter(Boolean) as ItemType[];

  const mainItems = [
    {
      type: 'divider',
    },
    ...settings,
    ...(canInstall ? pwa : []),
    // ...data,
    ...helps,
  ].filter(Boolean) as MenuProps['items'];

  return { mainItems };
};

import React, { useState } from 'react';
import { Upload, Avatar, Button, message } from 'antd';
import { UploadOutlined, UserOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

interface AvatarUploadProps {
  value?: string;
  onChange?: (url: string) => void;
  size?: number;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  value,
  onChange,
  size = 100
}) => {
  const [loading, setLoading] = useState(false);

  // 处理文件上传前的验证
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }
    
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    
    return true;
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    if (!beforeUpload(file)) {
      return false;
    }

    try {
      setLoading(true);
      
      // 创建本地URL用于预览
      const imageUrl = URL.createObjectURL(file);
      
      // 这里可以添加实际的上传逻辑
      // const uploadedUrl = await uploadToServer(file);
      
      onChange?.(imageUrl);
      message.success('头像上传成功');
      
    } catch (error) {
      console.error('头像上传失败:', error);
      message.error('头像上传失败');
    } finally {
      setLoading(false);
    }
    
    return false; // 阻止默认上传行为
  };

  const uploadProps: UploadProps = {
    beforeUpload: handleUpload,
    showUploadList: false,
    accept: 'image/*',
  };

  return (
    <div className="avatar-upload">
      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
        <Avatar
          size={size}
          src={value}
          icon={!value && <UserOutlined />}
          style={{
            backgroundColor: !value ? '#f0f0f0' : undefined,
            border: '1px solid #d9d9d9'
          }}
        />
        
        <div>
          <Upload {...uploadProps}>
            <Button 
              icon={<UploadOutlined />}
              loading={loading}
            >
              {value ? '更换头像' : '上传头像'}
            </Button>
          </Upload>
          
          <div style={{ 
            fontSize: '12px', 
            color: '#999', 
            marginTop: 4 
          }}>
            支持 JPG、PNG 格式，大小不超过 2MB
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarUpload;

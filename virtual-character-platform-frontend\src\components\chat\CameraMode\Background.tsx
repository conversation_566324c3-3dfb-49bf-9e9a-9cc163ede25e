import React from 'react';
import { useGlobalStore } from '../../../store/global';

const Background: React.FC = () => {
  const { backgroundUrl } = useGlobalStore();

  if (!backgroundUrl) return null;

  return (
    <div 
      className="camera-background"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: `url(${backgroundUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        zIndex: 0,
        opacity: 0.3,
      }}
    />
  );
};

export default Background;

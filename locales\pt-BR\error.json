{"apiKeyMiss": "A chave da API OpenAI está vazia. Por favor, adicione uma chave da API OpenAI personalizada.", "dancePlayError": "Falha ao reproduzir o arquivo de dança, por favor, tente novamente mais tarde.", "error": "Erro", "errorTip": {"clearSession": "Limpar mensagens da sessão", "description": "O projeto está atualmente em construção, não garantimos a estabilidade dos dados. Se você encontrar problemas, pode tentar", "forgive": " , pedimos desculpas pelo inconveniente", "or": "ou", "problem": "A página encontrou um pequeno problema...", "resetSystem": "Redefinir configurações do sistema"}, "fileUploadError": "Falha ao enviar o arquivo, por favor tente novamente mais tarde", "formValidationFailed": "Falha na validação do formulário:", "goBack": "Voltar para a página inicial", "openaiError": "Erro da API OpenAI, por favor verifique se a chave da API OpenAI e o endpoint estão corretos", "reload": "<PERSON><PERSON><PERSON><PERSON>", "response": {"400": "<PERSON><PERSON><PERSON><PERSON>, o servidor não entendeu sua solicitação. Por favor, verifique se os parâmetros da sua solicitação estão corretos.", "401": "<PERSON><PERSON><PERSON><PERSON>, o servidor recusou sua solicitação, possivelmente devido a permissões insuficientes ou falta de autenticação válida.", "403": "<PERSON><PERSON><PERSON><PERSON>, o servidor recusou sua solicitação. Você não tem permissão para acessar este conteúdo.", "404": "<PERSON><PERSON><PERSON><PERSON>, o servidor não conseguiu encontrar a página ou recurso solicitado. Por favor, verifique se sua URL está correta.", "405": "<PERSON><PERSON><PERSON><PERSON>, o servidor não suporta o método de solicitação que você está usando. Por favor, verifique se seu método de solicitação está correto.", "406": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode completar a solicitação com base nas características do conteúdo que você solicitou.", "407": "<PERSON><PERSON><PERSON><PERSON>, você precisa de autenticação de proxy antes de continuar com esta solicitação.", "408": "<PERSON><PERSON><PERSON><PERSON>, o servidor excedeu o tempo de espera enquanto aguardava a solicitação. Por favor, verifique sua conexão de rede e tente novamente.", "409": "<PERSON><PERSON><PERSON><PERSON>, houve um conflito na solicitação que não pode ser processado, possivelmente devido ao estado do recurso ser incompatível com a solicitação.", "410": "<PERSON><PERSON><PERSON><PERSON>, o recurso que você solicitou foi removido permanentemente e não pode ser encontrado.", "411": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode processar uma solicitação sem um comprimento de conteúdo válido.", "412": "<PERSON><PERSON><PERSON><PERSON>, sua solicitação não atendeu às condições do servidor e não pode ser completada.", "413": "<PERSON><PERSON><PERSON><PERSON>, o volume de dados da sua solicitação é muito grande e o servidor não pode processá-lo.", "414": "Desculpe, a URI da sua solicitação é muito longa e o servidor não pode processá-la.", "415": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode processar o formato de mídia anexado à solicitação.", "416": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode atender ao intervalo solicitado.", "417": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode atender às suas expectativas.", "422": "<PERSON><PERSON><PERSON><PERSON>, o formato da sua solicitação está correto, mas contém erros semânticos e não pode ser respondido.", "423": "<PERSON><PERSON><PERSON><PERSON>, o recurso que você solicitou está bloqueado.", "424": "<PERSON><PERSON><PERSON><PERSON>, a solicitação atual não pode ser completada devido à falha na solicitação anterior.", "426": "<PERSON><PERSON><PERSON><PERSON>, o servidor exige que seu cliente seja atualizado para uma versão de protocolo mais recente.", "428": "<PERSON><PERSON><PERSON><PERSON>, o servidor exige condições prévias e sua solicitação deve incluir os cabeçalhos de condição corretos.", "429": "<PERSON><PERSON><PERSON><PERSON>, você fez muitas solicitações. O servidor está um pouco sobrecarregado, por favor, tente novamente mais tarde.", "431": "<PERSON><PERSON><PERSON><PERSON>, os campos do cabeçalho da sua solicitação são muito grandes e o servidor não pode processá-los.", "451": "<PERSON><PERSON><PERSON><PERSON>, por razões legais, o servidor se recusa a fornecer este recurso.", "500": "<PERSON><PERSON><PERSON><PERSON>, o servidor parece ter encontrado algumas dificuldades e não pode completar sua solicitação no momento. Por favor, tente novamente mais tarde.", "501": "<PERSON><PERSON><PERSON><PERSON>, o servidor ainda não sabe como processar esta solicitação. Por favor, verifique se sua operação está correta.", "502": "<PERSON><PERSON><PERSON><PERSON>, o servidor parece ter se perdido e não pode fornecer serviço no momento. Por favor, tente novamente mais tarde.", "503": "<PERSON><PERSON><PERSON><PERSON>, o servidor não pode processar sua solicitação no momento, possivelmente devido a sobrecarga ou manutenção. Por favor, tente novamente mais tarde.", "504": "<PERSON><PERSON><PERSON><PERSON>, o servidor não recebeu uma resposta do servidor upstream a tempo. Por favor, tente novamente mais tarde.", "505": "<PERSON><PERSON><PERSON><PERSON>, o servidor não suporta a versão HTTP que você está usando. Por favor, atualize e tente novamente.", "506": "<PERSON><PERSON><PERSON><PERSON>, houve um problema de configuração no servidor. Por favor, entre em contato com o administrador para resolver.", "507": "<PERSON><PERSON><PERSON><PERSON>, o servidor não tem espaço de armazenamento suficiente para processar sua solicitação. Por favor, tente novamente mais tarde.", "509": "<PERSON><PERSON><PERSON><PERSON>, a largura de banda do servidor foi esgotada. Por favor, tente novamente mais tarde.", "510": "<PERSON><PERSON><PERSON><PERSON>, o servidor não suporta a funcionalidade de extensão solicitada. Por favor, entre em contato com o administrador.", "524": "<PERSON><PERSON><PERSON><PERSON>, o servidor excedeu o tempo de espera ao aguardar uma resposta, possivelmente devido à lentidão da resposta. Por favor, tente novamente mais tarde.", "AgentRuntimeError": "Erro na execução do Lobe AI Runtime. Por favor, verifique as informações abaixo ou tente novamente.", "FreePlanLimit": "Atualmente você é um usuário gratuito e não pode usar esta funcionalidade. Por favor, faça upgrade para um plano pago para continuar usando.", "InvalidAccessCode": "A senha está incorreta ou vazia. Por favor, insira a senha de acesso correta ou adicione uma chave de API personalizada.", "InvalidBedrockCredentials": "A autenticação do Bedrock falhou. Por favor, verifique o AccessKeyId/SecretAccessKey e tente novamente.", "InvalidClerkUser": "<PERSON><PERSON><PERSON><PERSON>, você ainda não está logado. <PERSON>r favor, fa<PERSON> login ou registre uma conta antes de continuar.", "InvalidGithubToken": "O Github PAT está incorreto ou vazio. Por favor, verifique o Github PAT e tente novamente.", "InvalidOllamaArgs": "A configuração do Ollama está incorreta. Por favor, verifique a configuração do Ollama e tente novamente.", "InvalidProviderAPIKey": "{{provider}} A chave de API está incorreta ou vazia. Por favor, verifique a chave de API {{provider}} e tente novamente.", "LocationNotSupportError": "<PERSON><PERSON><PERSON><PERSON>, sua região não suporta este serviço de modelo, possivelmente devido a restrições regionais ou serviço não ativado. Por favor, confirme se a região atual suporta o uso deste serviço ou tente mudar para outra região e tente novamente.", "OllamaBizError": "Ocorreu um erro ao solicitar o serviço Ollama. Por favor, verifique as informações abaixo ou tente novamente.", "OllamaServiceUnavailable": "A conexão com o serviço Ollama falhou. Por favor, verifique se o Ollama está funcionando corretamente ou se a configuração de CORS do Ollama está correta.", "PermissionDenied": "<PERSON><PERSON><PERSON><PERSON>, você não tem permissão para acessar este serviço. Por favor, verifique se sua chave tem permissão de acesso.", "PluginApiNotFound": "Desculpe, a API não existe no manifesto do plugin. Por favor, verifique se seu método de solicitação corresponde à API do manifesto do plugin.", "PluginApiParamsError": "Desculpe, a verificação dos parâmetros de entrada da solicitação do plugin falhou. Por favor, verifique se os parâmetros correspondem às informações da descrição da API.", "PluginFailToTransformArguments": "<PERSON><PERSON>l<PERSON>, a análise dos parâmetros de chamada do plugin falhou. Por favor, tente regenerar a mensagem do assistente ou troque para um modelo de IA mais poderoso e tente novamente.", "PluginGatewayError": "<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro no gateway do plugin. Por favor, verifique se a configuração do gateway do plugin está correta.", "PluginManifestInvalid": "Desculpe, a verificação do manifesto do plugin falhou. Por favor, verifique se o formato do manifesto está correto.", "PluginManifestNotFound": "<PERSON><PERSON><PERSON><PERSON>, o servidor não conseguiu encontrar o manifesto do plugin (manifest.json). Por favor, verifique se o endereço do arquivo de descrição do plugin está correto.", "PluginMarketIndexInvalid": "Desculpe, a verificação do índice do plugin falhou. Por favor, verifique se o formato do arquivo do índice está correto.", "PluginMarketIndexNotFound": "<PERSON><PERSON><PERSON><PERSON>, o servidor não conseguiu encontrar o índice do plugin. Por favor, verifique se o endereço do índice está correto.", "PluginMetaInvalid": "Desculpe, a verificação das metainformações do plugin falhou. Por favor, verifique se o formato das metainformações do plugin está correto.", "PluginMetaNotFound": "<PERSON><PERSON><PERSON><PERSON>, não foi possível encontrar o plugin no índice. <PERSON>r favor, verifique as informações de configuração do plugin no índice.", "PluginOpenApiInitError": "Desculpe, a inicialização do cliente OpenAPI falhou. Por favor, verifique se as informações de configuração do OpenAPI estão corretas.", "PluginServerError": "O servidor do plugin retornou um erro na solicitação. Por favor, verifique seu arquivo de descrição do plugin, configuração do plugin ou implementação do servidor com base nas informações de erro abaixo.", "PluginSettingsInvalid": "Este plugin precisa ser configurado corretamente antes de ser usado. Por favor, verifique se sua configuração está correta.", "ProviderBizError": "Ocorreu um erro ao solicitar o serviço {{provider}}. Por favor, verifique as informações abaixo ou tente novamente.", "QuotaLimitReached": "<PERSON><PERSON><PERSON><PERSON>, o uso atual de tokens ou o número de solicitações atingiu o limite de cota da chave. Por favor, aumente a cota da chave ou tente novamente mais tarde.", "StreamChunkError": "Erro na análise do bloco de mensagem da solicitação em fluxo. Por favor, verifique se a interface da API atual está em conformidade com as normas ou entre em contato com seu fornecedor de API para consulta.", "SubscriptionPlanLimit": "Seu limite de assinatura foi atingido e você não pode usar esta funcionalidade. Por favor, faça upgrade para um plano superior ou compre um pacote de recursos para continuar usando.", "UnknownChatFetchError": "<PERSON><PERSON><PERSON><PERSON>, ocorreu um erro desconhecido na solicitação. Por favor, verifique as informações abaixo ou tente novamente."}, "s3envError": "As variáveis de ambiente S3 não estão completamente configuradas, por favor verifique suas variáveis de ambiente", "serverError": "Erro no servidor, por favor entre em contato com o administrador", "triggerError": "Erro acionado", "ttsTransformFailed": "Falha na conversão de voz, por favor verifique a conexão com a internet ou ative a chamada do cliente nas configurações e tente novamente.", "unknownError": "<PERSON><PERSON>conhe<PERSON>", "unlock": {"addProxyUrl": "Adicionar endereço de proxy OpenAI (opcional)", "apiKey": {"description": "Insira sua chave de API {{name}} para começar a conversa", "title": "Usar chave de API {{name}} personalizada"}, "closeMessage": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar e tentar novamente", "oauth": {"description": "O administrador ativou a autenticação unificada, clique no botão abaixo para fazer login e desbloquear o aplicativo", "success": "<PERSON>gin bem-sucedido", "title": "Fazer login na conta", "welcome": "Bem-vindo!"}, "password": {"description": "O administrador ativou a criptografia do aplicativo, insira a senha do aplicativo para desbloqueá-lo. A senha precisa ser preenchida apenas uma vez", "placeholder": "Digite a senha", "title": "Digite a senha para desbloquear o aplicativo"}, "tabs": {"apiKey": "Chave de API personalizada", "password": "<PERSON><PERSON>"}}}
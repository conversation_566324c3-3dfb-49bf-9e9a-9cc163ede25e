import React, { lazy, Suspense } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import SimpleHomePage from './pages/SimpleHomePage';
import MainLayout from './components/MainLayout';
import useAuthStore from './store/authStore';
import AppTheme from './layout/AppTheme';
import StoreHydration from './layout/StoreHydration';
import Locale from './layout/Locale';
import StyleRegistry from './layout/StyleRegistry';
 
// 使用React.lazy懒加载页面组件
const LoginPage = lazy(() => import('./pages/LoginPage'));
const HomePage = lazy(() => import('./pages/HomePage'));
const CharacterCreationPage = lazy(() => import('./pages/CharacterCreationPage'));
const StandaloneChatPage = lazy(() => import('./pages/StandaloneChatPage'));
const CommunityPage = lazy(() => import('./pages/CommunityPage'));
const MarketplacePage = lazy(() => import('./pages/MarketplacePage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const SettingsPage = lazy(() => import('./pages/settings'));

const ImmersiveVoiceChatPage = lazy(() => import('./pages/ImmersiveVoiceChatPage'));
const UnifiedChatPage = lazy(() => import('./pages/UnifiedChatPage'));
const RoleEditPage = lazy(() => import('./pages/RoleEditPage'));
const IntegratedRolePage = lazy(() => import('./pages/IntegratedRolePage'));

const NotFoundPage = lazy(() => import('./pages/404'));
const TextDirectionDebugPage = lazy(() => import('./pages/TextDirectionDebugPage'));

// 管理员相关组件
const AdminLoginPage = lazy(() => import('./pages/admin/AdminLoginPage'));
const AdminLayout = lazy(() => import('./components/admin/AdminLayout'));
const AdminProtectedRoute = lazy(() => import('./components/admin/AdminProtectedRoute'));
const DashboardPage = lazy(() => import('./pages/admin/DashboardPage'));
const CharacterListPage = lazy(() => import('./pages/admin/CharacterListPage'));
const CharacterEditPage = lazy(() => import('./pages/admin/CharacterEditPage'));
const PromptListPage = lazy(() => import('./pages/admin/PromptListPage'));
const PromptEditPage = lazy(() => import('./pages/admin/PromptEditPage'));
const PromptTestPage = lazy(() => import('./pages/admin/PromptTestPage'));

// 临时页面占位组件
const AdminCharacterDetailPage = () => <div>角色详情页面</div>;
const AdminUsersPage = () => <div>用户管理页面</div>;
const AdminSettingsPage = () => <div>系统设置页面</div>;

// 加载中组件
const LoadingComponent = () => (
  <div className="page-loading">
    <Spin size="large" tip="页面加载中...">
      <div style={{ minHeight: '200px' }} />
    </Spin>
  </div>
);

// 受保护路由组件
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  console.log('🛡️ ProtectedRoute rendering...');

  const { isLoggedIn } = useAuthStore();
  console.log('🔐 Auth state:', { isLoggedIn });
  console.log('📍 Current path:', window.location.pathname);

  if (!isLoggedIn) {
    const currentPath = window.location.pathname + window.location.search;
    console.log('🚫 User not logged in, redirecting to login from:', currentPath);
    return <Navigate to={`/login?from=${encodeURIComponent(currentPath)}`} replace />;
  }

  console.log('✅ User is logged in, rendering protected content');
  return <>{children}</>;
};

function App() {
  console.log('🎯 App component rendering...');

  console.log('📦 Rendering simplified App...');
  return (
    <StyleRegistry>
      <StoreHydration />
      <AppTheme>
        <Locale>
          <BrowserRouter>
            <Suspense fallback={<LoadingComponent />}>
              <Routes>
          {/* 管理员路由 */}
          <Route path="/admin/login" element={<AdminLoginPage />} />
          
          <Route path="/admin" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <Navigate to="/admin/dashboard" replace />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/dashboard" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <DashboardPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/characters" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <CharacterListPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/characters/:id" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <AdminCharacterDetailPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/characters/create" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <CharacterEditPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/characters/:id/edit" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <CharacterEditPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/prompts" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <PromptListPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/prompts/create" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <PromptEditPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/prompts/:id" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <PromptEditPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/prompts/:id/edit" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <PromptEditPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/prompts/:id/test" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <PromptTestPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/users" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <AdminUsersPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          
          <Route path="/admin/settings" element={
            <AdminProtectedRoute>
              <AdminLayout>
                <AdminSettingsPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />

          {/* 用户路由 */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/simple" element={<SimpleHomePage />} />
            <Route path="/immersive-chat/:characterId" element={<ImmersiveVoiceChatPage />} />
          
          <Route path="/create-character" element={
                <ProtectedRoute>
              <MainLayout>
                  <CharacterCreationPage />
              </MainLayout>
                </ProtectedRoute>
          } />
          
          {/* 聊天页面路由 - 全屏显示，不使用MainLayout */}
          <Route path="/chat" element={
                <ProtectedRoute>
                  <StandaloneChatPage />
                </ProtectedRoute>
          } />

          <Route path="/chat/:characterId" element={
                <ProtectedRoute>
                  <StandaloneChatPage />
                </ProtectedRoute>
          } />

          {/* 新的统一聊天页面 - 支持双模式切换 */}
          <Route path="/unified-chat/:characterId" element={
                <ProtectedRoute>
                  <UnifiedChatPage />
                </ProtectedRoute>
          } />

          {/* 新的角色编辑页面 - 专业编辑器 */}
          <Route path="/role/edit/:characterId" element={
                <ProtectedRoute>
                  <RoleEditPage />
                </ProtectedRoute>
          } />

          {/* 集成的角色管理页面 - 使用移植的模块 */}
          <Route path="/role/integrated" element={
                <ProtectedRoute>
                  <IntegratedRolePage />
                </ProtectedRoute>
          } />



          <Route path="/community" element={
                <ProtectedRoute>
              <MainLayout>
                  <CommunityPage />
              </MainLayout>
                </ProtectedRoute>
          } />

          <Route path="/marketplace" element={
                <ProtectedRoute>
              <MainLayout>
                  <MarketplacePage />
              </MainLayout>
                </ProtectedRoute>
          } />
          
          <Route path="/profile" element={
                <ProtectedRoute>
              <MainLayout>
                  <ProfilePage />
              </MainLayout>
                </ProtectedRoute>
          } />

          <Route path="/settings" element={
                <ProtectedRoute>
              <MainLayout>
                  <SettingsPage />
              </MainLayout>
                </ProtectedRoute>
          } />
          
          <Route path="/" element={
            <ProtectedRoute>
              <MainLayout>
                <HomePage />
              </MainLayout>
            </ProtectedRoute>
          } />

          {/* 文字方向调试页面 */}
          <Route path="/text-debug" element={
            <MainLayout>
              <TextDirectionDebugPage />
            </MainLayout>
          } />

          {/* 系统状态页面 - 仅用于调试 */}
          <Route path="/system-status" element={
            <div style={{
              padding: '20px',
              minHeight: '100vh',
              background: 'var(--gradient-primary, #f0f2f5)',
              color: 'var(--color-text-primary, #333)',
              fontFamily: 'var(--font-family-primary, Arial, sans-serif)'
            }}>
              <h1 style={{ color: 'var(--color-primary, #1890ff)' }}>🎉 虚拟角色平台</h1>
              <div style={{
                background: 'var(--color-bg-container, white)',
                padding: '20px',
                borderRadius: '8px',
                boxShadow: 'var(--shadow-md, 0 4px 6px rgba(0,0,0,0.1))',
                margin: '20px 0',
                color: 'var(--color-text-primary, #333)'
              }}>
                <h2>✅ 系统状态</h2>
                <ul style={{ color: 'var(--color-text-primary, #333)' }}>
                  <li>✅ React应用正常启动</li>
                  <li>✅ 路由系统正常工作</li>
                  <li>✅ TypeScript编译通过</li>
                  <li>✅ 依赖包安装完成</li>
                  <li>✅ 样式系统正常加载</li>
                  <li>✅ Store系统正常工作</li>
                  <li>✅ 国际化系统正常工作</li>
                </ul>
              </div>
              <div style={{
                background: 'var(--color-bg-container, white)',
                padding: '20px',
                borderRadius: '8px',
                boxShadow: 'var(--shadow-md, 0 4px 6px rgba(0,0,0,0.1))',
                margin: '20px 0',
                color: 'var(--color-text-primary, #333)'
              }}>
                <h2>🔗 快速导航</h2>
                <ul style={{ color: 'var(--color-text-primary, #333)' }}>
                  <li><a href="/simple" style={{ color: 'var(--color-primary, #1890ff)' }}>简单测试页面</a></li>
                  <li><a href="/simple-test" style={{ color: 'var(--color-primary, #1890ff)' }}>系统诊断页面</a></li>
                  <li><a href="/test" style={{ color: 'var(--color-primary, #1890ff)' }}>功能测试页面</a></li>
                  <li><a href="/debug" style={{ color: 'var(--color-primary, #1890ff)' }}>调试页面</a></li>
                  <li><a href="/admin/login" style={{ color: 'var(--color-primary, #1890ff)' }}>管理员登录</a></li>
                  <li><a href="/chat" style={{ color: 'var(--color-primary, #1890ff)' }}>聊天页面</a></li>
                  <li><a href="/community" style={{ color: 'var(--color-primary, #1890ff)' }}>社区页面</a></li>
                </ul>
              </div>
              <div style={{
                background: 'var(--color-bg-container, white)',
                padding: '20px',
                borderRadius: '8px',
                boxShadow: 'var(--shadow-md, 0 4px 6px rgba(0,0,0,0.1))',
                margin: '20px 0',
                color: 'var(--color-text-primary, #333)'
              }}>
                <h2>📊 环境信息</h2>
                <p style={{ color: 'var(--color-text-primary, #333)' }}><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
                <p style={{ color: 'var(--color-text-primary, #333)' }}><strong>开发模式:</strong> {import.meta.env.DEV ? '是' : '否'}</p>
                <p style={{ color: 'var(--color-text-primary, #333)' }}><strong>Node环境:</strong> {import.meta.env.NODE_ENV}</p>
                <p style={{ color: 'var(--color-text-primary, #333)' }}><strong>Vite模式:</strong> {import.meta.env.MODE}</p>
              </div>
            </div>
          } />

            {/* 404页面 */}
            <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
        </Locale>
      </AppTheme>
    </StyleRegistry>
  );
}

export default App;
{"apiKeyMiss": "OpenAI API Anahtarı boş, lütfen özel OpenAI API Anahtarınızı ekleyin", "dancePlayError": "<PERSON>s dos<PERSON>ı oynatılamadı, lüt<PERSON> daha sonra tekrar deneyin.", "error": "<PERSON><PERSON>", "errorTip": {"clearSession": "Oturum mesajını temizle", "description": "<PERSON><PERSON> anda in<PERSON><PERSON>, veri istikrarı garanti edilmemektedir. <PERSON><PERSON><PERSON> ka<PERSON>laşırsanız deneyebilirsiniz", "forgive": " , verdiğimiz rahatsızlıktan dolayı özür dileriz", "or": "veya", "problem": "Sayfada bir sorunla karşılaşıldı...", "resetSystem": "Sistem ayarlarını sıfırla"}, "fileUploadError": "<PERSON><PERSON><PERSON> hat<PERSON>, lüt<PERSON> daha sonra tekrar deneyin.", "formValidationFailed": "Form doğrulaması başarısız oldu:", "goBack": "<PERSON> <PERSON><PERSON> d<PERSON>n", "openaiError": "OpenAI API hatası, lütfen OpenAI API Anahtarını ve Uç Noktayı kontrol edin.", "reload": "<PERSON><PERSON><PERSON>", "response": {"400": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON>u isteğinizi anlayamadı, lütfen istek parametrelerinizin doğru olduğundan emin olun.", "401": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> is<PERSON>, bu muh<PERSON>elen yetersiz izinlerinizden veya geçerli bir kimlik doğrulama sağlamadığınızdan kaynaklanıyor.", "403": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, bu içeriğe erişim izniniz yok.", "404": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> talep ettiğiniz sayfayı veya kaynağı bulamıyor, lütfen URL'nizin doğru olduğundan emin olun.", "405": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ı<PERSON><PERSON>z istek yöntemini desteklemiyor, lütfen istek yönteminizin doğru olduğundan emin olun.", "406": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>u isteğinizin içerik özelliklerine göre isteği tamamlayamıyor.", "407": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu iste<PERSON><PERSON> devam ettirmek için proxy kimlik doğrulaması yapmanız gerekiyor.", "408": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> is<PERSON>ğ<PERSON> beklerken zaman aşımına uğradı, lütfen ağ bağlantınızı kontrol edip tekrar deneyin.", "409": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, istekte bir çelişki var, bu muhtemelen kaynak durumu ile isteğin uyumsuz olmasından kaynaklanıyor.", "410": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, talep ettiğiniz kaynak kalıcı olarak kaldırıldı, bulunamıyor.", "411": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>u geçerli bir içerik uzunluğu içermeyen isteği işleyemiyor.", "412": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, iste<PERSON><PERSON>z <PERSON>ucu tarafındaki koşulları karşılamıyor, iste<PERSON><PERSON> tamamlayamıyor.", "413": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isteğinizin veri miktarı çok büyük, sun<PERSON>u bunu işleyemiyor.", "414": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isteğinizin URI'si çok uzun, sun<PERSON><PERSON> bunu işleyemiyor.", "415": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON>u isteğe ekli medya formatını işleyemiyor.", "416": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunucu isteğinizin aralığını karşılayamıyor.", "417": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> beklentilerinizi karşılayamıyor.", "422": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isteğinizin formatı doğru ancak anlamsal hatalar içeriyor, bu nedenle yanıt verilemiyor.", "423": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, talep ettiğiniz kaynak kilitlenmiş.", "424": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>nceki isteğin başar<PERSON>s<PERSON>z olması nedeniyle mevcut istek tamamlanamıyor.", "426": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> istemcinizin daha yüksek bir protokol sürümüne yükseltilmesini talep ediyor.", "428": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunucu ön koşullar talep ediyor, isteğinizin doğru koşul başlıklarını içermesini istiyor.", "429": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> fazla istekte b<PERSON>, <PERSON><PERSON><PERSON> bi<PERSON>un, <PERSON><PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>in.", "431": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isteğinizin başlık alanı çok büyük, sun<PERSON>u bunu işleyemiyor.", "451": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yasal neden<PERSON>den dolayı sunucu bu kaynağı sağlamayı reddetti.", "500": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bazı <PERSON>ş<PERSON>şıyo<PERSON>, isteğ<PERSON>zi geçici olarak tamamlayamıyor, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "501": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunucu bu isteği nasıl işleyeceğini hen<PERSON>z bilmiyor, lütfen işleminizin doğru olduğundan emin olun.", "502": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON> ka<PERSON> gibi gö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ge<PERSON><PERSON> olarak hizmet veremiyor, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "503": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> anda isteğ<PERSON> işleyemiyor, bu muh<PERSON><PERSON>n aşırı yüklenme veya bakım neden<PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin.", "504": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunucu üst akış sunucusunun yanıtın<PERSON> be<PERSON>medi, lü<PERSON><PERSON> daha sonra tekrar deneyin.", "505": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ınız HTTP sürümünü desteklemiyor, lütfen güncelleyip tekrar deneyin.", "506": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>sı<PERSON> bir sorun var, lütfen yöneticinizle iletişime geçin.", "507": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> de<PERSON>, is<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "509": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> bant g<PERSON> tü<PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin.", "510": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> talep edilen genişletme işlevini desteklemiyor, lütfen yöneticinizle iletişime geçin.", "524": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> yanıt beklerken zaman aşımına uğradı, bu muhtem<PERSON>n yanıtın çok yavaş olmasından kaynaklanıyor, lü<PERSON><PERSON> daha sonra tekrar deneyin.", "AgentRuntimeError": "Lobe AI Runtime yürütme hatası, lütfen aşağıdaki bilgileri kontrol edin veya tekrar deneyin.", "FreePlanLimit": "Şu anda ücretsiz kullanıcı olduğunuz için bu özelliği kullanamazsınız, lütfen devam etmek için ücretli plana geçin.", "InvalidAccessCode": "Şifre yanlış veya boş, lütfen doğru erişim şifresini girin veya özel API Anahtarı ekleyin.", "InvalidBedrockCredentials": "Bedrock kimlik doğrulaması geçmedi, lütfen AccessKeyId/SecretAccessKey'i kontrol edip tekrar deneyin.", "InvalidClerkUser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> anda giri<PERSON>, lüt<PERSON> önce giriş yapın veya bir hesap kaydedin ve ardından işlemlere devam edin.", "InvalidGithubToken": "Github PAT yanlış veya boş, lütfen Github PAT'yi kontrol edip tekrar deneyin.", "InvalidOllamaArgs": "Ollama yapılandırması yanlış, lütfen Ollama yapılandırmasını kontrol edip tekrar deneyin.", "InvalidProviderAPIKey": "{{provider}} API Anahtarı yanlış veya boş, lütfen {{provider}} API Anahtarını kontrol edip tekrar deneyin.", "LocationNotSupportError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bulunduğunuz bölge bu model hiz<PERSON><PERSON> desteklemiyor, bu muhtem<PERSON>n bölge<PERSON> kısıtlamalar veya hizmetin açılmamış olmasından kaynaklanıyor. Lütfen mevcut bölgenizin bu hizmeti kullanıp kullanmadığını kontrol edin veya başka bir bölgeye geçmeyi deneyin.", "OllamaBizError": "Ollama hizmetine istek hatası, lütfen aşağıdaki bilgileri kontrol edin veya tekrar deneyin.", "OllamaServiceUnavailable": "Ollama hizmetine bağlantı sağlanamadı, lütfen Ollama'nın düzgün çalışıp çalışmadığını kontrol edin veya Ollama'nın çapraz alan ya<PERSON>ılandırmasını doğru ayarlayıp ayarlamadığını kontrol edin.", "PermissionDenied": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu hizmete erişim izniniz yok, lütfen anahtarınızın erişim iznine sahip olup olmadığını kontrol edin.", "PluginApiNotFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eklenti tanım dosyasında bu API mevcut değil, lütfen istek yönteminizin eklenti tanım API'si ile eşleşip eşleşmediğini kontrol edin.", "PluginApiParamsError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu e<PERSON><PERSON><PERSON> isteği için giriş parametreleri doğrulaması geçmedi, lütfen giriş parametreleri ile API tanım bilgilerini kontrol edin.", "PluginFailToTransformArguments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eklenti çağrı parametreleri ayrıştırılamadı, lütfen yardımcı mesajı yeniden oluşturmayı deneyin veya daha güçlü bir AI modeli ile tekrar deneyin.", "PluginGatewayError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eklenti geçidi hatası oluştu, lütfen eklenti geçidi yapılandırmasının doğru olup olmadığını kontrol edin.", "PluginManifestInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu e<PERSON><PERSON><PERSON> tanım dosyası doğrulaması geçmedi, lütfen tanım dosyası formatının standartlara uygun olup olmadığını kontrol edin.", "PluginManifestNotFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> bu eklentinin tanım dosyasını (manifest.json) bulamadı, lütfen eklenti tanım dosyası adresinin doğru olup olmadığını kontrol edin.", "PluginMarketIndexInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eklenti dizini doğrulaması geçmedi, lütfen dizin dosyası formatının standartlara uygun olup olmadığını kontrol edin.", "PluginMarketIndexNotFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> eklenti dizinini bulamadı, lütfen dizin adresinin doğru olup olmadığını kontrol edin.", "PluginMetaInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu eklentinin meta bilgileri doğrulaması geçmedi, lütfen eklenti meta bilgi formatının standartlara uygun olup olmadığını kontrol edin.", "PluginMetaNotFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dizinde bu eklentiyi bulamadık, l<PERSON><PERSON><PERSON> eklentinin dizindeki yapılandırma bilgilerini kontrol edin.", "PluginOpenApiInitError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OpenAPI istemcisi başlatılamadı, lütfen OpenAPI yapılandırma bilgilerinin doğru olup olmadığını kontrol edin.", "PluginServerError": "Eklenti sunucu isteği hata ile döndü, lütfen aşağıdaki hata bilgilerine göre eklenti tanım dosyanızı, eklenti yapılandırmanızı veya sunucu uygulamanızı kontrol edin.", "PluginSettingsInvalid": "<PERSON>u eklentinin kullanılabilmesi için doğru bir şekilde yapılandırılması gerekiyor, lütfen yapılandırmanızın doğru olup olmadığını kontrol edin.", "ProviderBizError": "{{provider}} hiz<PERSON>ine istek hatası, lütfen aşağıdaki bilgileri kontrol edin veya tekrar deneyin.", "QuotaLimitReached": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mevcut Token kullanımı veya istek sayısı bu anahtarın kota (quota) üst sınırına ulaştı, lütfen bu anahtarın kotasını artırın veya daha sonra tekrar deneyin.", "StreamChunkError": "Akış isteği mesaj bloğu ayrıştırma hatası, lütfen mevcut API arayüzünün standartlara uygun olup olmadığını kontrol edin veya API sağlayıcınızla iletişime geçin.", "SubscriptionPlanLimit": "Abonelik limitiniz doldu, bu <PERSON>zelliği kull<PERSON>z<PERSON>, lütfen daha yüksek bir plana geçin veya kaynak paketi satın alarak devam edin.", "UnknownChatFetchError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bilinmeyen bir istek hatasıyla ka<PERSON>şılaştık, lütfen aşağıdaki bilgileri kontrol edin veya tekrar deneyin."}, "s3envError": "S3 ortam değişkenleri tam olarak ayarlanmamış, lütfen ortam değişkenlerinizi kontrol edin", "serverError": "<PERSON><PERSON><PERSON>, lütfen yönetici ile iletişime geçin", "triggerError": "<PERSON><PERSON> teti<PERSON>", "ttsTransformFailed": "Ses dönüştürme başarısız oldu, lütfen ağı kontrol edin veya ayarlardan istemci çağrısını açmayı deneyin.", "unknownError": "Bilinmeyen hata", "unlock": {"addProxyUrl": "OpenAI proxy ad<PERSON><PERSON> (isteğe bağlı)", "apiKey": {"description": "So<PERSON>bete başlamak için {{name}} API Anahtarınızı girin", "title": "Özel {{name}} API Anahtarını kullan"}, "closeMessage": "Mesajı kapat", "confirm": "<PERSON><PERSON><PERSON> ve tekrar dene", "oauth": {"description": "Yönetici tek oturum açma kimlik doğrulamasını etkinleştirdi, aşağıdaki düğmeye tıklayarak giriş yapın ve uygulamayı açın", "success": "<PERSON><PERSON><PERSON> başarılı", "title": "Hesabınıza giriş yapın", "welcome": "Hoş geldiniz!"}, "password": {"description": "Yönetici uygulama şifrelemesini etkinleştirdi, uygulama şifresini girdikten sonra uygulamayı açabilirsiniz. Şifreyi yalnızca bir kez girmeniz yeterlidir", "placeholder": "<PERSON><PERSON><PERSON><PERSON> girin", "title": "Uygulamayı açmak için şifreyi girin"}, "tabs": {"apiKey": "Özel API Anahtarı", "password": "Şifre"}}}
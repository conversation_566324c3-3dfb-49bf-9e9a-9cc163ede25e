{"common": {"chat": {"avatar": {"desc": "<PERSON><PERSON> profil resmi", "title": "<PERSON><PERSON>"}, "nickName": {"desc": "<PERSON><PERSON> takma ad", "placeholder": "Lütfen takma adınızı girin", "title": "Takma Ad"}, "title": "<PERSON><PERSON><PERSON>"}, "system": {"clear": {"action": "<PERSON><PERSON>", "alert": "Tüm sohbet mesajlarını temizlemek istediğinize emin misiniz?", "desc": "Tüm sohbet ve karakter verilerini, sohbet listesini, karakter listesini, sohbet mesajlarını vb. temizleyecektir.", "success": "Başarıyla te<PERSON>", "tip": "İşlem geri <PERSON>, te<PERSON><PERSON><PERSON><PERSON> sonra veriler geri <PERSON>, lütfen dikkatl<PERSON> o<PERSON>.", "title": "<PERSON><PERSON>m So<PERSON>bet <PERSON>ını Temizle"}, "clearCache": {"action": "<PERSON><PERSON> temizle", "alert": "<PERSON>üm önbelleği temizlemek istediğinize emin misiniz?", "calculating": "Önbellek boyutu hesaplanıyor...", "desc": "Uygulama tarafından indirilen veri önbelleğini temizleyecektir, bu, karakter model verileri, ses verileri, dans model verileri, ses dosyaları vb. dahil olmak ü<PERSON>e", "success": "<PERSON><PERSON>zleme başarılı", "tip": "İşlem geri <PERSON>, temizledikten sonra verilerin yeniden indirilmesi gerekecektir, lütfen dikkatli olun", "title": "<PERSON><PERSON> temizle"}, "reset": {"action": "<PERSON><PERSON>", "alert": "Tüm sistem ayarlarını sıfırlamak istediğinize emin misiniz?", "desc": "<PERSON>üm sistem a<PERSON>ını, tema a<PERSON>, so<PERSON><PERSON> a<PERSON>nı, dil modeli ayarlarını vb. sıfırlayacaktır.", "success": "Başarıyla sıfırlandı", "tip": "İşlem geri <PERSON>, sı<PERSON><PERSON>rl<PERSON><PERSON>kt<PERSON> sonra veriler geri get<PERSON>, lütfen dikkatli olun.", "title": "Sistem Ayarlarını Sıfırla"}, "title": "Sistem Ayarları"}, "theme": {"backgroundEffect": {"desc": "Arka plan efektini özelleştir", "glow": "Parıltı", "none": "Arka plan yok", "title": "Arka Plan Efekti"}, "locale": {"auto": "<PERSON><PERSON><PERSON> et", "desc": "Sistem dilini özelleştir", "title": "Dil"}, "neutralColor": {"desc": "Farklı renk eğilimlerine göre gri tonlarını özelleştir", "title": "<PERSON><PERSON><PERSON>"}, "primaryColor": {"desc": "<PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "title": "<PERSON>ma <PERSON>"}, "title": "<PERSON><PERSON>"}, "header": {"desc": "Tercihler ve Model Ayarları", "global": "<PERSON><PERSON><PERSON><PERSON>", "session": "Oturum Ayarları", "sessionDesc": "Rol Ayarları ve Oturum Tercihleri", "sessionWithName": "Oturum Ayarları · {{name}}", "title": "<PERSON><PERSON><PERSON>"}, "llm": {"aesGcm": "Anahtarınız ve proxy adresi gibi bilgiler <1>AES-GCM</1> şifreleme algoritması ile şifrelenecektir.", "apiKey": {"desc": "Lütfen {{name}} API Anahtarınızı girin", "placeholder": "{{name}} API Anahtarı", "title": "API Anahtarı"}, "checker": {"button": "Ko<PERSON><PERSON>", "desc": "API Anahtarının ve proxy adresinin doğru girilip g<PERSON> test edin", "error": "Ko<PERSON>rol <PERSON>ı<PERSON>", "pass": "<PERSON><PERSON><PERSON> b<PERSON>şarılı", "title": "Bağlantı Kontrolü"}, "customModelCards": {"addNew": "{{id}} model<PERSON> oluştur ve ekle", "config": "<PERSON><PERSON>", "confirmDelete": "Bu özel modeli silmek üzeresiniz, silind<PERSON><PERSON> sonra geri al<PERSON>, lütfen dikkatli o<PERSON>.", "modelConfig": {"azureDeployName": {"extra": "Azure OpenAI'de gerçek istek için kullanılan alan", "placeholder": "Lütfen Azure'daki model da<PERSON><PERSON><PERSON><PERSON><PERSON> adını girin", "title": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "displayName": {"placeholder": "Lütfen modelin gösterim adı<PERSON> girin, <PERSON><PERSON><PERSON><PERSON>tGP<PERSON>, GPT-4 vb.", "title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "files": {"extra": "Mevcut dosya yükleme uygulaması yalnızca bir Hack çözümüdür, yalnızca kendi denemeniz için geçerlidir. Tam dosya yükleme yeteneği için lütfen sonraki uygulamayı bekleyin.", "title": "<PERSON><PERSON><PERSON>"}, "functionCall": {"extra": "Bu yapılandırma yalnızca uygulamadaki fonksiyon çağırma yeteneğini açacaktır, fonksiyon çağırma desteği tamamen modele bağlıdır, lütfen bu modelin fonksiyon çağırma yeteneğini kendiniz test edin.", "title": "Fonksiyon Çağırmayı Destekle"}, "id": {"extra": "Model etiketi olarak gösterilecektir", "placeholder": "Lütfen model id'sini girin, <PERSON><PERSON><PERSON><PERSON> gpt-4-turbo-preview veya claude-2.1", "title": "Model ID"}, "modalTitle": "Özel Model Yapılandırması", "tokens": {"title": "Maks<PERSON>um token sayısı", "unlimited": "Sınırsız"}, "vision": {"extra": "Bu yapılandırma yalnızca uygulamadaki resim yükleme yapılandırmasını açacaktır, tanıma desteği tamamen modele bağlıdır, lütfen bu modelin görsel tanıma yeteneğini kendiniz test edin.", "title": "Görsel Tanımayı Destekle"}}}, "fetchOnClient": {"desc": "İstemci talep modu, oturum taleplerini doğrudan ta<PERSON> başlatır ve yanıt hızını artırabilir", "title": "İstemci talep modunu kullan"}, "fetcher": {"fetch": "Model listesini al", "fetching": "Model listesi alınıyor...", "latestTime": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>: {{time}}", "noLatestTime": "Henüz liste alınmadı"}, "helpDoc": "Yapılandırma kı<PERSON>uzu", "modelList": {"desc": "Oturumda gösterilecek modeli seçin, seçilen model model listesinde gösterilecektir", "placeholder": "Lütfen listeden bir model seçin", "title": "Model listesi", "total": "Toplam {{count}} model mevcut"}, "proxyUrl": {"desc": "Varsayılan adres dışında, http(s):// içermelidir", "title": "API proxy adresi"}, "title": "<PERSON><PERSON> Modeli", "waitingForMore": "Daha fazla model <1>planlanıyor</1>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>"}, "systemAgent": {"customPrompt": {"addPrompt": "<PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sistem asistanı içerik oluştururken özel ipucunu kullanacaktır", "placeholder": "Lütfen özel ipucu kelimelerini girin", "title": "<PERSON><PERSON>"}, "emotionAnalysis": {"label": "<PERSON><PERSON><PERSON>", "modelDesc": "Duygu analizi için kullanılacak modeli belirtin", "title": "Otomatik Duygu Ana<PERSON>"}, "title": "Sistem Ajanı"}, "touch": {"title": "Dokunma Ayarları"}, "tts": {"clientCall": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, is<PERSON>ci ses sentez hizmetini çağıracak, ses sentez hızı daha hızlı olacak, ancak bilimsel bir şekilde internete erişim veya dış ağa erişim yeteneği gerektirir.", "title": "İstemci Çağrısı"}, "title": "<PERSON><PERSON>"}}
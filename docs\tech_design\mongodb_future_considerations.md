# MongoDB 未来考虑事项

本文档详细说明了虚拟角色平台在未来版本中可能引入 MongoDB 的考虑因素、评估指标和潜在使用场景。在 MVP 阶段，项目将仅使用 PostgreSQL 作为唯一的数据库系统。

## 为何在 MVP 阶段不使用 MongoDB

在虚拟角色平台的 MVP（最小可行产品）阶段，我们决定暂不引入 MongoDB，理由如下：

1. **技术栈简化**：仅使用一种数据库系统（PostgreSQL）可以简化开发、部署和维护流程。
2. **PostgreSQL 的 JSONB 能力**：PostgreSQL 提供的 JSONB 类型已经能够满足我们对半结构化数据（如角色的外观参数和设置）的存储需求。
3. **避免数据一致性问题**：使用单一数据库可以避免跨数据库事务的复杂性和潜在的数据一致性问题。
4. **减少学习成本**：团队对 PostgreSQL 更为熟悉，引入 MongoDB 会增加学习和适应成本。
5. **降低运维复杂度**：维护单个数据库系统的成本明显低于维护两个异构系统。

## 未来可能引入 MongoDB 的场景

随着项目的发展，我们可能会在以下场景考虑引入 MongoDB：

### 1. 对话历史与上下文管理

当角色与用户的对话历史记录增长到一定规模，且需要更灵活的查询和存储方式时：

```javascript
// MongoDB 对话记录集合示例
{
  "_id": ObjectId("..."),
  "character_id": "12345",
  "user_id": "67890",
  "messages": [
    {
      "sender_type": "user",
      "content": "今天天气真好",
      "timestamp": ISODate("2023-06-01T10:15:30Z"),
      "sentiment": "positive",
      "entities": ["天气"]
    },
    {
      "sender_type": "character",
      "content": "是啊，阳光明媚的日子最适合出去走走了",
      "timestamp": ISODate("2023-06-01T10:15:35Z"),
      "sentiment": "positive",
      "generated_params": {
        "temperature": 0.7,
        "model": "gpt-4"
      }
    }
    // ... 更多消息
  ],
  "context": {
    "location": "公园",
    "weather": "晴天",
    "character_mood": "开心",
    "recent_topics": ["天气", "户外活动"]
  },
  "metadata": {
    "total_messages": 245,
    "last_interaction": ISODate("2023-06-01T11:30:22Z"),
    "conversation_duration": 3600  // 秒
  }
}
```

### 2. 角色记忆与知识图谱

当需要为角色构建复杂的记忆系统和知识图谱时：

```javascript
// MongoDB 角色记忆集合示例
{
  "_id": ObjectId("..."),
  "character_id": "12345",
  "user_id": "67890",
  "memories": [
    {
      "type": "fact",
      "content": "用户喜欢巧克力口味的冰淇淋",
      "source": "conversation",
      "conversation_id": "conv123",
      "timestamp": ISODate("2023-05-15T14:30:00Z"),
      "importance": 0.8,
      "last_recalled": ISODate("2023-06-01T10:15:30Z"),
      "recall_count": 3
    },
    {
      "type": "event",
      "content": "用户提到他下周要参加一场考试",
      "details": {
        "event_type": "exam",
        "date": "2023-06-10",
        "importance_to_user": "high"
      },
      "source": "conversation",
      "conversation_id": "conv145",
      "timestamp": ISODate("2023-06-01T10:20:15Z"),
      "importance": 0.9,
      "last_recalled": null,
      "recall_count": 0
    }
    // ... 更多记忆
  ],
  "relationships": {
    "user": {
      "familiarity": 0.75,
      "affection": 0.6,
      "trust": 0.8,
      "development_history": [
        {"metric": "familiarity", "value": 0.3, "timestamp": ISODate("2023-04-01T00:00:00Z")},
        {"metric": "familiarity", "value": 0.5, "timestamp": ISODate("2023-05-01T00:00:00Z")},
        {"metric": "familiarity", "value": 0.75, "timestamp": ISODate("2023-06-01T00:00:00Z")}
        // ... 更多历史记录
      ]
    },
    "mentioned_entities": [
      {"name": "Sarah", "type": "person", "relation_to_user": "friend", "importance": 0.7},
      {"name": "Fluffy", "type": "pet", "relation_to_user": "dog", "importance": 0.6}
      // ... 更多实体
    ]
  }
}
```

### 3. 内容分析与推荐系统

当平台发展出内容推荐和分析功能时：

```javascript
// MongoDB 用户行为与推荐数据集合示例
{
  "_id": ObjectId("..."),
  "user_id": "67890",
  "character_interactions": [
    {
      "character_id": "12345",
      "interaction_count": 156,
      "total_duration": 18900,  // 秒
      "last_interaction": ISODate("2023-06-01T11:30:22Z"),
      "affinity_score": 0.85,
      "topics": [
        {"name": "音乐", "frequency": 23},
        {"name": "动漫", "frequency": 45},
        {"name": "游戏", "frequency": 18}
        // ... 更多话题
      ]
    },
    // ... 更多角色交互
  ],
  "content_preferences": {
    "personality_preferences": ["傲娇", "元气"],
    "identity_preferences": ["魔法使", "虚拟歌姬"],
    "appearance_preferences": {
      "hair_color": ["pink", "blue"],
      "eye_color": ["purple"],
      "clothing_style": ["fantasy", "jk_uniform"]
    }
  },
  "recommended_characters": [
    {"character_id": "abcde", "score": 0.92, "reason": "personality_match"},
    {"character_id": "fghij", "score": 0.87, "reason": "similar_to_favorites"}
    // ... 更多推荐
  ]
}
```

## 引入 MongoDB 的评估指标

在考虑引入 MongoDB 前，我们将评估以下指标：

1. **PostgreSQL JSONB 性能指标**：
   - 查询响应时间是否超过 100ms
   - 是否出现频繁的表锁定
   - 索引大小是否超过合理范围

2. **数据规模阈值**：
   - 单个角色的聊天记录是否超过 10,000 条
   - 单个用户的记忆条目是否超过 1,000 条
   - 平台总角色数是否超过 100,000 个

3. **功能需求**：
   - 是否需要实现复杂的文本搜索功能（MongoDB 的文本搜索能力）
   - 是否需要地理空间查询功能
   - 是否需要时间序列数据分析

4. **开发和运维资源**：
   - 团队是否已具备 MongoDB 相关技能
   - 是否有足够的服务器资源支持多数据库系统
   - 是否有完善的数据备份和恢复方案

## MongoDB 引入步骤（未来参考）

如果未来决定引入 MongoDB，我们将遵循以下步骤：

1. **概念验证**：
   - 选择一个非关键功能进行 MongoDB 的概念验证
   - 评估性能和开发体验
   - 确定最佳实践和设计模式

2. **数据模型设计**：
   - 确定哪些数据适合存储在 MongoDB 中
   - 设计文档结构和索引策略
   - 制定数据验证规则

3. **集成策略**：
   - 开发 MongoDB 与 PostgreSQL 之间的数据同步机制
   - 实现跨数据库的事务处理策略
   - 构建统一的数据访问层

4. **迁移计划**：
   - 开发数据迁移工具和脚本
   - 制定无缝迁移策略，确保服务不中断
   - 建立回滚机制以应对迁移失败

5. **监控和维护**：
   - 部署 MongoDB 监控工具
   - 制定备份和恢复策略
   - 建立性能优化指南

## 结论

在 MVP 阶段，虚拟角色平台将专注于使用 PostgreSQL 实现核心功能，确保产品快速交付和稳定运行。随着项目的发展和用户规模的增长，我们将根据本文档中的评估指标和场景分析，在适当的时机考虑引入 MongoDB 以增强平台的数据存储和处理能力。 
import React, { useState, useEffect } from 'react';
import { Card, Button, Tabs, Input, Space, message } from 'antd';
import { PlayCircleOutlined, MessageOutlined, EyeOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../store/agent';
import { useGlobalStore } from '../../store/global';
import AgentViewer from '../../features/AgentViewer';

const { TextArea } = Input;

const RolePreview: React.FC = () => {
  const [activeTab, setActiveTab] = useState('3d');
  const [testMessage, setTestMessage] = useState('你好，请介绍一下你自己。');
  const [chatHistory, setChatHistory] = useState<Array<{role: string, content: string}>>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // 获取当前角色和全局状态
  const currentAgent = useAgentStore((s) => agentSelectors.currentAgentItem(s));
  const { viewer } = useGlobalStore();

  // 当角色变更时重置聊天历史
  useEffect(() => {
    setChatHistory([]);
  }, [currentAgent?.agentId]);

  // 测试对话
  const handleTestChat = async () => {
    if (!testMessage.trim() || !currentAgent) return;

    try {
      setIsGenerating(true);
      
      // 添加用户消息到历史
      const newHistory = [
        ...chatHistory,
        { role: 'user', content: testMessage }
      ];
      setChatHistory(newHistory);
      
      // 模拟AI回复（实际应该调用AI API）
      setTimeout(() => {
        const mockReply = `作为${currentAgent.meta.name}，${currentAgent.systemRole.slice(0, 100)}...这是一个测试回复。`;
        setChatHistory(prev => [
          ...prev,
          { role: 'assistant', content: mockReply }
        ]);
        setIsGenerating(false);
      }, 1500);
      
      setTestMessage('');
      
    } catch (error) {
      console.error('测试对话失败:', error);
      message.error('测试对话失败');
      setIsGenerating(false);
    }
  };

  // 清空聊天历史
  const handleClearChat = () => {
    setChatHistory([]);
  };

  // 测试语音
  const handleTestVoice = () => {
    if (!currentAgent?.tts) {
      message.error('未配置语音设置');
      return;
    }

    const testText = `你好，我是${currentAgent.meta.name}`;
    
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(testText);
      utterance.rate = currentAgent.tts.speed || 1;
      utterance.pitch = currentAgent.tts.pitch || 0;
      speechSynthesis.speak(utterance);
    } else {
      message.error('浏览器不支持语音合成');
    }
  };

  if (!currentAgent) {
    return (
      <div className="role-preview-empty">
        <p>请选择或创建一个角色进行预览</p>
      </div>
    );
  }

  const tabItems = [
    {
      key: '3d',
      label: '3D预览',
      children: (
        <div className="preview-3d">
          <div className="viewer-container" style={{ height: '400px' }}>
            <AgentViewer
              agentId={currentAgent.agentId}
              interactive={true}
              toolbar={false}
              height="100%"
              width="100%"
            />
          </div>
          
          <div className="viewer-controls" style={{ marginTop: 16 }}>
            <Space>
              <Button 
                icon={<PlayCircleOutlined />}
                onClick={handleTestVoice}
              >
                测试语音
              </Button>
              <Button 
                icon={<EyeOutlined />}
                onClick={() => viewer?.resetCamera()}
              >
                重置视角
              </Button>
            </Space>
          </div>
        </div>
      ),
    },
    {
      key: 'chat',
      label: '对话测试',
      children: (
        <div className="preview-chat">
          <div className="chat-history" style={{ 
            height: '300px', 
            overflowY: 'auto',
            border: '1px solid #d9d9d9',
            borderRadius: 6,
            padding: 12,
            marginBottom: 16,
            backgroundColor: '#fafafa'
          }}>
            {chatHistory.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                color: '#999',
                padding: '40px 0'
              }}>
                开始测试对话
              </div>
            ) : (
              chatHistory.map((msg, index) => (
                <div 
                  key={index}
                  style={{
                    marginBottom: 12,
                    padding: 8,
                    borderRadius: 6,
                    backgroundColor: msg.role === 'user' ? '#e6f7ff' : '#f6ffed',
                    border: `1px solid ${msg.role === 'user' ? '#91d5ff' : '#b7eb8f'}`
                  }}
                >
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#666',
                    marginBottom: 4,
                    fontWeight: 'bold'
                  }}>
                    {msg.role === 'user' ? '用户' : currentAgent.meta.name}
                  </div>
                  <div>{msg.content}</div>
                </div>
              ))
            )}
            
            {isGenerating && (
              <div style={{ 
                textAlign: 'center', 
                color: '#999',
                padding: 8
              }}>
                {currentAgent.meta.name} 正在思考...
              </div>
            )}
          </div>
          
          <div className="chat-input">
            <Space.Compact style={{ width: '100%' }}>
              <TextArea
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="输入测试消息..."
                rows={2}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleTestChat();
                  }
                }}
              />
              <Button
                type="primary"
                icon={<MessageOutlined />}
                onClick={handleTestChat}
                loading={isGenerating}
                disabled={!testMessage.trim()}
              >
                发送
              </Button>
            </Space.Compact>
            
            <div style={{ marginTop: 8, textAlign: 'right' }}>
              <Button 
                size="small" 
                onClick={handleClearChat}
                disabled={chatHistory.length === 0}
              >
                清空历史
              </Button>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'info',
      label: '角色信息',
      children: (
        <div className="preview-info">
          <div className="info-item">
            <h4>基本信息</h4>
            <p><strong>名称:</strong> {currentAgent.meta.name}</p>
            <p><strong>描述:</strong> {currentAgent.meta.description || '暂无描述'}</p>
            {currentAgent.meta.tags && currentAgent.meta.tags.length > 0 && (
              <p><strong>标签:</strong> {currentAgent.meta.tags.join(', ')}</p>
            )}
          </div>
          
          <div className="info-item">
            <h4>系统角色</h4>
            <p style={{ 
              whiteSpace: 'pre-wrap',
              backgroundColor: '#f5f5f5',
              padding: 12,
              borderRadius: 6,
              fontSize: '13px'
            }}>
              {currentAgent.systemRole}
            </p>
          </div>
          
          <div className="info-item">
            <h4>模型配置</h4>
            <p><strong>提供商:</strong> {currentAgent.provider}</p>
            <p><strong>模型:</strong> {currentAgent.model}</p>
            {currentAgent.tts && (
              <p><strong>语音:</strong> {currentAgent.tts.voice}</p>
            )}
          </div>
          
          {currentAgent.chatConfig && (
            <div className="info-item">
              <h4>对话配置</h4>
              <p><strong>历史消息数:</strong> {currentAgent.chatConfig.historyCount}</p>
              <p><strong>温度:</strong> {currentAgent.chatConfig.temperature || 0.7}</p>
              <p><strong>最大令牌:</strong> {currentAgent.chatConfig.maxTokens || 2000}</p>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="role-preview">
      <Card 
        title={`预览: ${currentAgent.meta.name}`}
        size="small"
        style={{ height: '100%' }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="small"
        />
      </Card>
    </div>
  );
};

export default RolePreview;

import React from 'react';
import { Form, Select, Slider, Switch, InputNumber, Card, Divider } from 'antd';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../../store/agent';

const { Option } = Select;

// 可用的AI模型提供商
const MODEL_PROVIDERS = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'google', label: 'Google' },
  { value: 'local', label: '本地模型' },
];

// OpenAI模型选项
const OPENAI_MODELS = [
  { value: 'gpt-4', label: 'GPT-4', description: '最强大的模型，适合复杂对话' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', description: '更快的GPT-4版本' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '平衡性能和成本' },
  { value: 'gpt-3.5-turbo-16k', label: 'GPT-3.5 Turbo 16K', description: '支持更长上下文' },
];

// Anthropic模型选项
const ANTHROPIC_MODELS = [
  { value: 'claude-3-opus', label: 'Claude 3 Opus', description: '最强大的Claude模型' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet', description: '平衡性能的Claude模型' },
  { value: 'claude-3-haiku', label: 'Claude 3 Haiku', description: '快速响应的Claude模型' },
];

const LangModelTab: React.FC = () => {
  // 获取当前角色数据
  const [currentAgent, updateAgentConfig] = useAgentStore((s) => [
    agentSelectors.currentAgentItem(s),
    s.updateAgentConfig,
  ]);

  // 当前配置
  const currentConfig = {
    provider: currentAgent?.provider || 'openai',
    model: currentAgent?.model || 'gpt-3.5-turbo',
    chatConfig: {
      historyCount: 10,
      compressThreshold: 1000,
      enableCompressThreshold: true,
      enableHistoryCount: true,
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      ...currentAgent?.chatConfig,
    },
  };

  // 处理提供商变更
  const handleProviderChange = (provider: string) => {
    let defaultModel = 'gpt-3.5-turbo';
    if (provider === 'anthropic') {
      defaultModel = 'claude-3-sonnet';
    } else if (provider === 'google') {
      defaultModel = 'gemini-pro';
    }
    
    updateAgentConfig({ 
      provider,
      model: defaultModel 
    });
  };

  // 处理模型变更
  const handleModelChange = (model: string) => {
    updateAgentConfig({ model });
  };

  // 处理聊天配置变更
  const handleChatConfigChange = (field: string, value: any) => {
    const newChatConfig = {
      ...currentConfig.chatConfig,
      [field]: value,
    };
    updateAgentConfig({ chatConfig: newChatConfig });
  };

  // 获取当前提供商的模型选项
  const getCurrentModels = () => {
    switch (currentConfig.provider) {
      case 'anthropic':
        return ANTHROPIC_MODELS;
      case 'openai':
      default:
        return OPENAI_MODELS;
    }
  };

  return (
    <div className="langmodel-tab">
      <Flexbox gap={24} style={{ padding: '24px' }}>
        {/* 模型提供商选择 */}
        <div className="model-section">
          <h3>模型提供商</h3>
          <p className="section-desc">选择AI模型的提供商</p>
          
          <Form layout="vertical">
            <Form.Item label="提供商">
              <Select
                value={currentConfig.provider}
                onChange={handleProviderChange}
              >
                {MODEL_PROVIDERS.map(provider => (
                  <Option key={provider.value} value={provider.value}>
                    {provider.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="模型">
              <Select
                value={currentConfig.model}
                onChange={handleModelChange}
              >
                {getCurrentModels().map(model => (
                  <Option key={model.value} value={model.value}>
                    <div>
                      <div>{model.label}</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        {model.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </div>

        {/* 对话配置 */}
        <div className="model-section">
          <h3>对话配置</h3>
          <p className="section-desc">配置对话的基本参数</p>
          
          <Form layout="vertical">
            <Form.Item label="历史消息数量">
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Switch
                  checked={currentConfig.chatConfig.enableHistoryCount}
                  onChange={(checked) => handleChatConfigChange('enableHistoryCount', checked)}
                />
                <InputNumber
                  min={1}
                  max={50}
                  value={currentConfig.chatConfig.historyCount}
                  onChange={(value) => handleChatConfigChange('historyCount', value)}
                  disabled={!currentConfig.chatConfig.enableHistoryCount}
                />
                <span>条</span>
              </div>
            </Form.Item>

            <Form.Item label="压缩阈值">
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Switch
                  checked={currentConfig.chatConfig.enableCompressThreshold}
                  onChange={(checked) => handleChatConfigChange('enableCompressThreshold', checked)}
                />
                <InputNumber
                  min={500}
                  max={5000}
                  value={currentConfig.chatConfig.compressThreshold}
                  onChange={(value) => handleChatConfigChange('compressThreshold', value)}
                  disabled={!currentConfig.chatConfig.enableCompressThreshold}
                />
                <span>字符</span>
              </div>
            </Form.Item>
          </Form>
        </div>

        {/* 高级参数 */}
        <div className="model-section">
          <h3>高级参数</h3>
          <p className="section-desc">调节模型的生成参数</p>
          
          <Form layout="vertical">
            <Form.Item label={`温度 (Temperature): ${currentConfig.chatConfig.temperature}`}>
              <Slider
                min={0}
                max={2}
                step={0.1}
                value={currentConfig.chatConfig.temperature}
                onChange={(value) => handleChatConfigChange('temperature', value)}
                marks={{
                  0: '保守',
                  1: '平衡',
                  2: '创意'
                }}
              />
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                控制回答的随机性，值越高越有创意，值越低越保守
              </p>
            </Form.Item>

            <Form.Item label="最大令牌数">
              <InputNumber
                min={100}
                max={4000}
                value={currentConfig.chatConfig.maxTokens}
                onChange={(value) => handleChatConfigChange('maxTokens', value)}
                style={{ width: '100%' }}
              />
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                限制单次回复的最大长度
              </p>
            </Form.Item>

            <Form.Item label={`Top P: ${currentConfig.chatConfig.topP}`}>
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={currentConfig.chatConfig.topP}
                onChange={(value) => handleChatConfigChange('topP', value)}
                marks={{
                  0: '0',
                  0.5: '0.5',
                  1: '1'
                }}
              />
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                控制词汇选择的多样性
              </p>
            </Form.Item>

            <Form.Item label={`频率惩罚: ${currentConfig.chatConfig.frequencyPenalty}`}>
              <Slider
                min={-2}
                max={2}
                step={0.1}
                value={currentConfig.chatConfig.frequencyPenalty}
                onChange={(value) => handleChatConfigChange('frequencyPenalty', value)}
                marks={{
                  '-2': '-2',
                  0: '0',
                  2: '2'
                }}
              />
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                减少重复词汇的使用
              </p>
            </Form.Item>

            <Form.Item label={`存在惩罚: ${currentConfig.chatConfig.presencePenalty}`}>
              <Slider
                min={-2}
                max={2}
                step={0.1}
                value={currentConfig.chatConfig.presencePenalty}
                onChange={(value) => handleChatConfigChange('presencePenalty', value)}
                marks={{
                  '-2': '-2',
                  0: '0',
                  2: '2'
                }}
              />
              <p style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                鼓励谈论新话题
              </p>
            </Form.Item>
          </Form>
        </div>

        {/* 配置说明 */}
        <div className="model-section">
          <h3>配置说明</h3>
          <Card>
            <div className="model-guide">
              <h4>模型选择建议：</h4>
              <ul>
                <li><strong>GPT-4</strong>: 最强大但成本较高，适合复杂对话场景</li>
                <li><strong>GPT-3.5 Turbo</strong>: 性价比高，适合大多数应用场景</li>
                <li><strong>Claude 3</strong>: 在某些任务上表现优异，支持更长上下文</li>
              </ul>

              <h4>参数调节指南：</h4>
              <ul>
                <li><strong>温度</strong>: 0.7-0.9适合创意对话，0.1-0.3适合事实性回答</li>
                <li><strong>历史消息</strong>: 5-15条适合大多数对话，过多会影响响应速度</li>
                <li><strong>最大令牌</strong>: 根据需要调节，过大会增加成本和延迟</li>
              </ul>

              <h4>性能优化：</h4>
              <ul>
                <li>启用消息压缩可以减少token使用量</li>
                <li>合理设置历史消息数量平衡上下文和性能</li>
                <li>根据角色特点调节创意性参数</li>
                <li>定期监控API使用量和成本</li>
              </ul>
            </div>
          </Card>
        </div>
      </Flexbox>
    </div>
  );
};

export default LangModelTab;

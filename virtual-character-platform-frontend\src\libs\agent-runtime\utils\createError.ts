import type { ILobeAgentRuntimeErrorType } from '../error';
import type { AgentInitErrorPayload, ChatCompletionErrorPayload } from '../types';

export const AgentRuntimeError = {
  chat: (error: ChatCompletionErrorPayload): ChatCompletionErrorPayload => error,
  createError: (
    errorType: ILobeAgentRuntimeErrorType | string | number,
    error?: any,
  ): AgentInitErrorPayload => ({ error, errorType }),
  textToImage: (error: any): any => error,
};

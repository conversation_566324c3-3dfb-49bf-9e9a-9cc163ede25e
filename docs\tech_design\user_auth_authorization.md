# 用户认证和授权模块设计文档

## 1. 文档目的

本文档详细描述了虚拟角色平台项目中用户认证和授权模块的设计。该模块负责管理用户身份，包括用户注册、登录、会话管理，并确保用户仅能访问其拥有权限的资源和执行允许的操作。

## 2. 模块概述

用户认证和授权模块是平台安全的核心。它验证用户的身份（认证），并根据用户的身份确定其在系统中的权限（授权）。这个模块将与前端进行交互以处理用户登录/注册请求，与数据存储模块交互以存取用户信息，并为后端业务逻辑层提供权限检查服务。

## 3. 核心功能

用户认证和授权模块的核心功能涵盖用户生命周期管理和访问控制：

- **用户注册:**
    - 接收用户提供的必要信息（例如，用户名、邮箱、密码）。
    - 对输入数据进行验证。
    - 安全地存储用户信息，特别是对密码进行加盐哈希处理。
    - 在数据库中创建新用户记录（通过数据存储模块）。
    - 成功后返回注册结果。
- **用户登录:**
    - 接收用户提供的身份凭证（例如，用户名/邮箱和密码）。
    - 根据用户名/邮箱查找用户记录（通过数据存储模块）。
    - 对用户输入的密码进行哈希处理，并与数据库中存储的哈希值进行比较，验证密码是否正确。
    - 如果验证成功，生成一个会话标识符（例如，Token 或 Session ID）。
    - 将会话标识符发送给前端。
    - 记录登录时间和状态。
- **会话管理:**
    - 后端维护用户会话的状态（例如，使用 Token 或服务器端 Session）。
    - 接收前端在后续请求中携带的会话标识符。
    - 验证会话标识符的有效性。
    - 根据会话标识符识别当前用户身份。
    - 支持会话过期或注销。
- **用户注销:**
    - 使当前用户的会话标识符失效。
    - 清理服务器端的会话信息。
    - 返回注销结果。
- **密码管理:**
    - 提供修改密码功能（需要验证当前密码或提供找回密码流程 - 未来考虑）。
    - 提供找回密码功能（例如，通过邮箱发送验证码 - 未来考虑）。
- **授权检查 (初步):**
    - 提供接口供后端业务逻辑调用，检查当前用户是否拥有执行某个操作或访问某个资源的权限。
    - 初步权限可能基于用户是否已登录（例如，只有登录用户才能创建角色）。更复杂的权限模型（例如，角色拥有者才能修改角色）将在业务逻辑层或进一步细化的授权设计中考虑。

## 4. 技术实现考量

- **密码存储:** 必须使用安全的哈希算法（如 bcrypt, scrypt）对用户密码进行加盐处理后再存储，绝不能存储明文密码。
- **会话管理方式:**
    - **基于 Session:** 服务器端存储会话信息，前端使用 Session ID（通常存在 Cookie 中）。适用于单体应用，但分布式部署可能需要共享 Session 存储。
    - **基于 Token:** 服务器端生成 Token（例如 JWT - JSON Web Token），前端存储 Token 并在每次请求时携带。服务器端无状态（或只存储 Token 黑名单），适合分布式和移动应用。Token 需要包含用户信息和过期时间，需要安全地生成和验证。
    - **推荐:** 考虑到前后端分离和未来的扩展性，**基于 Token** 的方式（如 JWT）通常更灵活。
- **传输安全:** 必须使用 **HTTPS** 来加密客户端与后端之间的通信，防止敏感信息（如密码、Token）在传输过程中被截获。
- **Token 存储安全 (前端):** 前端需要安全地存储 Token（例如，HttpOnly 的 Cookie 或 Web Storage），并采取措施防止 XSS、CSRF 攻击。
- **速率限制:** 对登录和注册接口进行速率限制，防止暴力破解和恶意注册。

## 5. 模块与其他模块的交互

- **接收来自:** 前端（用户注册、登录、注销请求）。
- **发送给:**
    - 数据存储模块（用于存取用户信息）。
    - 后端业务逻辑层（提供用户身份和权限信息）。
    - 日志记录模块。
- **被调用者:** 被后端业务逻辑层调用进行权限检查。

## 6. 待细化项

-   **具体的用户注册、登录流程和字段验证规则：**
    -   **注册流程：**
        1.  用户在前端填写用户名、邮箱、密码。
        2.  前端进行初步格式验证，并发送至后端 `/api/auth/register`。
        3.  后端接收请求，对 `username` (唯一性、长度、字符限制)、`email` (格式、唯一性)、`password` (长度、复杂度，例如包含大小写字母、数字、特殊字符) 进行严格验证。
        4.  如果验证失败，返回详细错误信息（参考 `backend_api_design.md` 中的错误码）。
        5.  如果验证成功，对密码进行加盐哈希（使用 bcrypt/scrypt），并调用数据存储模块 (`docs/tech_design/data_storage.md`) 创建用户记录。
        6.  返回注册成功响应。
    -   **登录流程：**
        1.  用户在前端填写用户名/邮箱、密码。
        2.  前端发送至后端 `/api/auth/login`。
        3.  后端接收请求，首先根据用户名/邮箱从数据存储模块获取用户记录。
        4.  比较用户输入的密码哈希值与数据库中存储的哈希值。
        5.  如果密码不匹配或用户不存在，返回统一的登录失败错误信息（避免泄露具体是用户名还是密码错误）。
        6.  如果验证成功，生成 JWT Token（参考 `backend_api_design.md` 和下方 Token 机制），返回给前端。
        7.  记录登录日志。
-   **会话管理方式的具体实现细节（Token 的格式、有效期、刷新机制）：**
    -   **方式选择：** 采用 **JWT (JSON Web Token)** 作为会话管理机制，因为其无状态、易于扩展、适用于前后端分离和微服务架构。
    -   **Token 格式：** JWT 包含三部分：Header (头部)、Payload (载荷)、Signature (签名)。
        -   **Header：** 包含 Token 类型 (JWT) 和签名算法 (如 HS256)。
        -   **Payload：** 包含用户信息（如 `user_id`, `username`）、过期时间 (`exp`)、签发时间 (`iat`) 等。**不应包含敏感信息。**
        -   **Signature：** 使用密钥对 Header 和 Payload 进行签名，用于验证 Token 的完整性和真实性。
    -   **有效期：**
        -   **Access Token (访问令牌)：** 设置较短的有效期（例如 15分钟 - 2小时），用于访问受保护的 API 资源。过期后需要刷新或重新登录。
        -   **Refresh Token (刷新令牌) (未来考虑)：** 设置较长的有效期（例如 7天 - 30天），用于在 Access Token 过期后获取新的 Access Token，避免用户频繁登录。Refresh Token 仅用于刷新 Access Token，本身不用于访问受保护资源，且应存储在更安全的地方（如 HttpOnly Cookie）。
    -   **刷新机制 (未来考虑)：**
        1.  当 Access Token 过期时，前端使用 Refresh Token 向 `/api/auth/refresh_token` 发送请求。
        2.  后端验证 Refresh Token 的有效性，如果有效，则颁发新的 Access Token 和 Refresh Token。
        3.  如果 Refresh Token 被盗用或过期，用户需要重新登录。
-   **密码找回和修改的具体流程设计：**
    -   **密码修改 (已登录用户)：**
        1.  用户在前端输入旧密码和新密码。
        2.  后端验证旧密码是否正确，并对新密码进行复杂度检查和加盐哈希。
        3.  更新数据库中的密码哈希值。
        4.  为了安全，修改密码后强制当前所有会话失效，要求用户重新登录。
    -   **密码找回 (未登录用户，未来考虑，需要邮箱服务)：**
        1.  用户在登录页选择"忘记密码"，输入注册邮箱。
        2.  后端向该邮箱发送一个包含重置链接或验证码的邮件。链接中包含一个带有时效性的 Token。
        3.  用户点击链接或输入验证码，进入重置密码页面。
        4.  用户输入新密码，前端发送至后端重置密码 API。
        5.  后端验证 Token/验证码的有效性和时效性，对新密码进行哈希，更新数据库。
        6.  返回密码重置成功信息。
-   **详细的授权规则和实现方式：**
    -   **基于角色或权限的访问控制 (RBAC / PBAC)：**
        -   **MVP 阶段：** 主要权限基于用户是否已登录 (认证)。所有登录用户均可创建、定制、保存自己的角色，并与角色聊天。查看社区公开角色无需登录。
        -   **未来扩展 (RBAC)：** 引入用户角色（如 `admin`, `moderator`, `premium_user`, `normal_user`）。
            -   **数据库设计：** `users` 表可增加 `role_id` 或 `roles` (JSONB) 字段。或创建 `roles` 表和 `user_roles` 关联表。
            -   **权限粒度：** 定义不同角色可访问的 API 资源和操作（例如，管理员可删除任何用户创建的角色）。
            -   **后端实现：** 在 API 接口层使用装饰器或中间件进行权限检查，根据用户的 Token 中包含的角色信息进行授权判断。
    -   **基于资源的授权 (Resource-Based Authorization)：**
        -   对于角色资源，需要确保用户只能修改或删除自己创建的角色。这将在业务逻辑层进行检查：在处理 `PUT /api/characters/{character_id}/customize` 或 `DELETE /api/characters/{character_id}` 请求时，获取当前用户的 `user_id` 和 `{character_id}` 对应的 `user_id`，进行比对。
-   **如何处理并发登录和会话冲突：**
    -   **JWT 默认无状态：** JWT 默认不处理并发登录，同一 Token 可以在多个设备上同时使用。这通常是可接受的，因为它提供了灵活性。
    -   **单点登录/强制下线 (未来考虑)：** 如果需要强制用户在同一时间只能在一个设备上登录，可以在用户登录时记录其会话 ID 或 Token 信息到数据库或缓存中。当新设备登录时，使旧会话的 Token 失效（加入黑名单）。
    -   **Token 黑名单：** 在用户注销、修改密码或被强制下线时，将 Access Token（或其 JTI 标识）加入一个过期时间与 Token 有效期相同的黑名单缓存（如 Redis），阻止该 Token 在过期前继续使用。
-   **安全性方面的更多细节：**
    -   **TLS/HTTPS：** 强制所有通信使用 HTTPS，防止中间人攻击。
    -   **HttpOnly Cookies：** 将 JWT Token 存储在 `HttpOnly` 的 Cookie 中（如果使用 Cookie 传输），防止 XSS 攻击获取 Token。
    -   **CSRF 防护：** 对于状态修改类请求 (POST/PUT/DELETE)，使用 CSRF Token 机制。后端生成 Token，前端在请求中携带，后端验证。
    -   **输入验证：** 所有用户输入都应在后端进行严格的验证和清洗，防止 SQL 注入、XSS、命令注入等攻击。
    -   **速率限制：** 对注册、登录、密码重置等接口进行速率限制，防止暴力破解和拒绝服务攻击。
    -   **内容安全策略 (CSP)：** 在前端配置 CSP HTTP 头，限制页面可以加载的资源来源，减少 XSS 风险。
    -   **敏感信息处理：** 避免在日志、错误信息中暴露敏感信息。数据库中存储的密码必须是哈希值。
    -   **安全头：** 配置 HSTS, X-Content-Type-Options, X-Frame-Options 等安全相关的 HTTP 响应头。
-   **如果需要，用户权限与社区功能的结合：**
    -   **社区内容发布：** 只有登录用户才能点赞、评论（如果未来实现）角色。发布角色必须是登录用户。
    -   **内容管理：** 普通用户只能管理自己的角色和评论。管理员/版主可以管理所有用户的角色和评论（删除、修改可见性）。
    -   **举报功能：** 用户可以举报社区中的违规角色或评论（参考 `docs/tech_design/content_moderation.md`），举报本身不需特殊权限，但处理举报需要管理员权限。
    -   **可见性控制：** 角色表中的 `public` 字段控制角色的社区可见性。用户可以自由设置自己角色的公开/私有状态。

这份文档是用户认证和授权模块的初步设计。具体的安全策略和实现细节将在开发过程中根据所选技术栈和安全要求进行完善。 
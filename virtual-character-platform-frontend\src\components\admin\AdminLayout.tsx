import React, { useState } from 'react';
import { Layout, <PERSON>u, Button, Avatar, Dropdown, theme, Breadcrumb } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  RobotOutlined,
  FileTextOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import useAdminAuthStore from '../../store/adminAuthStore';

const { Header, Sider, Content } = Layout;

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { logoutAdmin, adminInfo } = useAdminAuthStore();
  const { token } = theme.useToken();

  const handleLogout = () => {
    logoutAdmin();
    navigate('/admin/login');
  };

  // 当前路径对应的菜单项key
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.startsWith('/admin/dashboard')) return '1';
    if (path.startsWith('/admin/characters')) return '2';
    if (path.startsWith('/admin/prompts')) return '3';
    if (path.startsWith('/admin/users')) return '4';
    if (path.startsWith('/admin/settings')) return '5';
    return '1'; // 默认选中仪表盘
  };

  // 当前页面面包屑
  const getBreadcrumb = () => {
    const path = location.pathname;
    const breadcrumbMap: Record<string, string[]> = {
      '/admin/dashboard': ['仪表盘'],
      '/admin/characters': ['角色管理', '角色列表'],
      '/admin/characters/create': ['角色管理', '创建角色'],
      '/admin/prompts': ['提示词管理', '提示词列表'],
      '/admin/prompts/create': ['提示词管理', '创建提示词'],
      '/admin/users': ['用户管理', '用户列表'],
      '/admin/settings': ['系统设置'],
    };

    // 处理详情页面
    if (path.match(/\/admin\/characters\/\d+/)) {
      return ['角色管理', '角色详情'];
    }
    if (path.match(/\/admin\/prompts\/\d+/)) {
      return ['提示词管理', '提示词详情'];
    }

    return breadcrumbMap[path] || ['仪表盘'];
  };

  const userDropdownItems = [
    {
      key: 'profile',
      label: <span>个人资料</span>,
      icon: <UserOutlined />,
    },
    {
      key: 'settings',
      label: <span>偏好设置</span>,
      icon: <SettingOutlined />,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: <span>退出登录</span>,
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1000,
        }}
        theme="dark"
      >
        <div style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '16px' }}>
          <h1 style={{ color: token.colorPrimary, margin: 0, fontSize: collapsed ? '16px' : '18px', whiteSpace: 'nowrap' }}>
            {collapsed ? '虚拟角色' : '虚拟角色管理系统'}
          </h1>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={[getSelectedKey()]}
          items={[
            {
              key: '1',
              icon: <DashboardOutlined />,
              label: <Link to="/admin/dashboard">仪表盘</Link>,
            },
            {
              key: '2',
              icon: <RobotOutlined />,
              label: <Link to="/admin/characters">角色管理</Link>,
            },
            {
              key: '3',
              icon: <FileTextOutlined />,
              label: <Link to="/admin/prompts">提示词管理</Link>,
            },
            {
              key: '4',
              icon: <UserOutlined />,
              label: <Link to="/admin/users">用户管理</Link>,
            },
            {
              key: '5',
              icon: <SettingOutlined />,
              label: <Link to="/admin/settings">系统设置</Link>,
            },
          ]}
        />
      </Sider>
      <Layout style={{ marginLeft: collapsed ? 80 : 200, transition: 'all 0.2s' }}>
        <Header style={{ 
          padding: 0, 
          background: token.colorBgContainer,
          position: 'sticky',
          top: 0,
          zIndex: 999,
          boxShadow: '0 1px 4px rgba(0,21,41,.08)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          <div style={{ display: 'flex', alignItems: 'center', marginRight: 24 }}>
            <Button type="text" icon={<BellOutlined />} style={{ marginRight: 12 }} />
            <Dropdown menu={{ items: userDropdownItems }} placement="bottomRight">
              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
                <span>{adminInfo?.username || '管理员'}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content style={{ margin: '16px 16px 0', overflow: 'initial' }}>
          <Breadcrumb style={{ marginBottom: 16 }}>
            <Breadcrumb.Item>管理系统</Breadcrumb.Item>
            {getBreadcrumb().map((item, index) => (
              <Breadcrumb.Item key={index}>{item}</Breadcrumb.Item>
            ))}
          </Breadcrumb>
          <div style={{ 
            padding: 24, 
            background: token.colorBgContainer,
            borderRadius: 8,
            minHeight: 'calc(100vh - 140px)',
          }}>
            {children}
          </div>
        </Content>
        <Layout.Footer style={{ textAlign: 'center', background: 'transparent' }}>
          虚拟角色平台管理系统 ©2023 Created by Admin Team
        </Layout.Footer>
      </Layout>
    </Layout>
  );
};

export default AdminLayout; 
{"apiKeyMiss": "OpenAI API-sleutel is leeg, voeg een aangepaste OpenAI API-sleutel toe", "dancePlayError": "Het a<PERSON><PERSON><PERSON> van het dansbestand is mislukt, probeer het later opnieuw.", "error": "Fout", "errorTip": {"clearSession": "Sessieberichten wissen", "description": "Het project is momenteel in aanbouw, de stabiliteit van de gegevens kan niet worden gegarandeerd. Als u problemen ondervindt, kunt u proberen", "forgive": "Onze excuses voor het ongemak.", "or": "of", "problem": "Er is een klein probleem met de pagina...", "resetSystem": "Systeeminstellingen resetten"}, "fileUploadError": "<PERSON><PERSON> uploaden mislukt, probeer het later opnieuw", "formValidationFailed": "Formuliervalidatie is mislukt:", "goBack": "Terug naar de startpagina", "openaiError": "OpenAI API-fout, controleer of de OpenAI API-sleutel en het eindpunt correct zijn", "reload": "Opnieuw laden", "response": {"400": "Het spijt ons, de server begrijpt uw verzoek niet. Controleer of uw verzoekparameters correct zijn.", "401": "<PERSON>t spijt ons, de server heeft uw verzoek geweigerd, mogelijk omdat u niet voldoende rechten heeft of geen geldige authenticatie heeft verstrekt.", "403": "Het spijt ons, de server heeft uw verzoek geweigerd. U heeft geen toegang tot deze inhoud.", "404": "Het spijt ons, de server kan de door u aangevraagde pagina of bron niet vinden. Controleer of uw URL correct is.", "405": "Het spijt ons, de server ondersteunt de door u gebruikte verzoekmethode niet. Controleer of uw verzoekmethode correct is.", "406": "Het spijt ons, de server kan het verzoek niet voltooien op basis van de eigenschappen van de door u aangevraagde inhoud.", "407": "Het spijt ons, u moet proxy-authenticatie uitvoeren voordat u dit verzoek kunt voortzetten.", "408": "Het spijt ons, de server heeft een time-out bereikt terwijl hij op het verzoek wacht. Controleer uw netwerkverbinding en probeer het opnieuw.", "409": "Het spijt ons, er is een conflict met het verzoek dat niet kan worden verwerkt, mogelijk omdat de status van de bron niet compatibel is met het verzoek.", "410": "Het spijt ons, de door u aangevraagde bron is permanent verwijderd en kan niet worden gevonden.", "411": "Het spijt ons, de server kan een verzoek zonder geldige inhouds lengte niet verwerken.", "412": "Het spijt ons, uw verzoek voldoet niet aan de voorwaarden van de server en kan niet worden voltooid.", "413": "Het spijt ons, de hoeveelheid gegeven<PERSON> in uw verzoek is te groot, de server kan het niet verwerken.", "414": "<PERSON>t spijt ons, de URI van uw verz<PERSON>k is te lang, de server kan het niet verwerken.", "415": "Het spijt ons, de server kan het bijgevoegde mediaformaat van het verzoek niet verwerken.", "416": "Het spijt ons, de server kan niet voldoen aan het bereik dat u heeft aangevraagd.", "417": "Het spijt ons, de server kan niet voldoen aan uw verwachtingen.", "422": "Het spijt ons, uw ve<PERSON><PERSON><PERSON> is correct gef<PERSON><PERSON><PERSON>d, maar kan niet worden beantwoord vanwege semantische fouten.", "423": "Het spijt ons, de door u aangevraagde bron is vergrendeld.", "424": "Het spijt ons, de huidige aanvraag kan niet worden voltooid vanwege een mislukte eerdere aanvraag.", "426": "Het spijt ons, de server vereist dat uw client wordt geüpgraded naar een hogere protocolversie.", "428": "Het spijt ons, de server vereist voorafgaande voorwaarden en vraagt dat uw verzoek de juiste voorwaarde-headers bevat.", "429": "Het spijt ons, u heeft te veel verzoeken gedaan, de server is een beetje moe. Probeer het later opnieuw.", "431": "Het spijt ons, uw ve<PERSON><PERSON><PERSON><PERSON><PERSON> is te groot, de server kan het niet verwerken.", "451": "Het spijt ons, om juridische redenen heeft de server geweigerd deze bron te verstrekken.", "500": "Het spijt ons, de server lijkt enkele problemen te hebben en kan uw verzoek tijdelijk niet voltooien. Probeer het later opnieuw.", "501": "Het spijt ons, de server weet nog niet hoe dit verzoek moet worden verwerkt. Controleer of uw handelingen correct zijn.", "502": "Het spijt ons, de server lijkt de weg kwijt te zijn en kan tijdelijk geen service bieden. Probeer het later opnieuw.", "503": "Het spijt ons, de server kan uw verzoek momenteel niet verwerken, mogelijk door overbelasting of onderhoud. <PERSON>beer het later opnieuw.", "504": "Het spijt ons, de server heeft geen antwoord ontvangen van de upstream server. Probeer het later opnieuw.", "505": "Het spijt ons, de server ondersteunt de door u gebruikte HTTP-versie niet. Werk bij en probeer het opnieuw.", "506": "Het spijt ons, er is een probleem met de serverconfiguratie. Neem contact op met de beheerder voor een oplossing.", "507": "Het spijt ons, de server heeft onvoldoende opslagruimte om uw verzoek te verwerken. Probeer het later opnieuw.", "509": "Het spijt ons, de bandbreedte van de server is op. <PERSON><PERSON>r het later opnieuw.", "510": "Het spijt ons, de server ondersteunt de gevraagde uitbreidingsfunctie niet. Neem contact op met de beheerder.", "524": "Het spijt ons, de server heeft een time-out bereikt terwijl hij op een antwoord wacht. Dit kan komen door een trage respons. Pro<PERSON>r het later opnieuw.", "AgentRuntimeError": "Er is een fout opgetreden bij de uitvoering van Lobe AI Runtime. Controleer de onderstaande informatie of probeer het opnieuw.", "FreePlanLimit": "U bent momenteel een gratis gebruiker en kunt deze functie niet gebruiken. Upgrade naar een betaald plan om door te gaan.", "InvalidAccessCode": "<PERSON><PERSON> wachtwo<PERSON> is onjuist of leeg. Voer het juiste toegangswachtwoord in of voeg een aangepaste API-sleutel toe.", "InvalidBedrockCredentials": "Bedrock-authenticatie is mislukt. Controleer de AccessKeyId/SecretAccessKey en probeer het opnieuw.", "InvalidClerkUser": "Het spijt ons, u bent momenteel niet ingelogd. Log eerst in of registreer een account om door te gaan.", "InvalidGithubToken": "Github PAT is onjuist of leeg. Controleer de Github PAT en probeer het opnieuw.", "InvalidOllamaArgs": "<PERSON><PERSON><PERSON>-configuratie is onjuist. Control<PERSON> <PERSON>-configuratie en probeer het opnieuw.", "InvalidProviderAPIKey": "{{provider}} API-sleutel is onjuist of leeg. Controleer de {{provider}} API-sleutel en probeer het opnieuw.", "LocationNotSupportError": "Het spijt ons, uw locatie ondersteunt deze modelservice niet, mogelijk vanwege regionale beperkingen of omdat de service niet is ingeschakeld. Controleer of uw huidige locatie deze service ondersteunt of probeer het opnieuw na het overschakelen naar een andere locatie.", "OllamaBizError": "Er is een fout opgetreden bij het verzoek aan de Ollama-service. Controleer de onderstaande informatie of probeer het opnieuw.", "OllamaServiceUnavailable": "De verbinding met de Ollama-service is mislukt. Controleer of Ollama correct werkt of of de cross-origin-configuratie van <PERSON> correct is ingesteld.", "PermissionDenied": "Het spijt ons, u heeft geen toestemming om deze service te gebruiken. Controleer of uw sleutel toegang heeft.", "PluginApiNotFound": "Het spijt ons, deze API bestaat niet in het manifest van de plugin. Controleer of uw verzoekmethode overeenkomt met de API van het pluginmanifest.", "PluginApiParamsError": "Het spijt ons, de validatie van de invoerparameters van de plugin is mislukt. Controleer of de invoerparameters overeenkomen met de API-beschrijving.", "PluginFailToTransformArguments": "Het spijt ons, de parameteranalyse van de plugin is mislukt. <PERSON><PERSON><PERSON> de assistentbericht opnieuw te genereren of probeer het opnieuw met een krachtiger AI-model voor Tools Calling.", "PluginGatewayError": "Het spijt ons, er is een fout opgetreden bij de plugin-gateway. Controleer of de configuratie van de plugin-gateway correct is.", "PluginManifestInvalid": "Het spijt ons, de validatie van het manifest van de plugin is mislukt. Controleer of het formaat van het manifest correct is.", "PluginManifestNotFound": "Het spijt ons, de server heeft het manifest (manifest.json) van de plugin niet gevonden. Controleer of het adres van het pluginbeschrijvingsbestand correct is.", "PluginMarketIndexInvalid": "Het spijt ons, de validatie van de pluginindex is mislukt. Controleer of het indexbestand correct is geformatteerd.", "PluginMarketIndexNotFound": "Het spijt ons, de server heeft de pluginindex niet gevonden. Controleer of het indexadres correct is.", "PluginMetaInvalid": "Het spijt ons, de validatie van de metadata van de plugin is mislukt. Controleer of het formaat van de pluginmetadata correct is.", "PluginMetaNotFound": "Het spijt ons, de plugin is niet gevonden in de index. Controleer de configuratie-informatie van de plugin in de index.", "PluginOpenApiInitError": "Het spijt ons, de initialisatie van de OpenAPI-client is mislukt. Controleer of de configuratie-informatie van de OpenAPI correct is.", "PluginServerError": "Er is een fout opgetreden bij het verzoek aan de server van de plugin. Controleer uw pluginbeschrijvingsbestand, pluginconfiguratie of serverimplementatie op basis van de onderstaande foutinformatie.", "PluginSettingsInvalid": "Deze plugin moet correct worden geconfigureerd voordat deze kan worden gebruikt. Controleer of uw configuratie correct is.", "ProviderBizError": "Er is een fout opgetreden bij het verzoek aan de {{provider}} service. Controleer de onderstaande informatie of probeer het opnieuw.", "QuotaLimitReached": "Het spijt ons, het huidige gebruik van tokens of het aantal verzoeken heeft de quota-limiet van deze sleutel bereikt. Verhoog de quota van deze sleutel of probeer het later opnieuw.", "StreamChunkError": "Er is een fout opgetreden bij het parseren van de berichtblokken van de streamingaanroep. Controleer of de huidige API-interface aan de standaardnormen voldoet of neem contact op met uw API-leverancier voor advies.", "SubscriptionPlanLimit": "Uw abonnementslimiet is bereikt en u kunt deze functie niet geb<PERSON>iken. Upgrade naar een hoger plan of koop een resourcepakket om door te gaan.", "UnknownChatFetchError": "Het spijt ons, er is een onbekende fout opgetreden bij het verzoek. Controleer de onderstaande informatie of probeer het opnieuw."}, "s3envError": "S3-omgeving variabelen zijn niet volledig ingeste<PERSON>, controleer uw omgevingsvariabelen", "serverError": "<PERSON><PERSON><PERSON>, neem contact op met de beheerder", "triggerError": "Fout activeren", "ttsTransformFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mislukt, controleer het netwerk of probeer het opnieuw nadat u de clientaanroep in de instellingen hebt ingeschakeld.", "unknownError": "Onbekende fout", "unlock": {"addProxyUrl": "Voeg OpenAI proxy-adres toe (optioneel)", "apiKey": {"description": "<PERSON><PERSON><PERSON> je {{name}} API-sleutel in om een gesprek te starten", "title": "Gebruik aangepaste {{name}} API-sleutel"}, "closeMessage": "Sluit melding", "confirm": "Bevestigen en opnieuw proberen", "oauth": {"description": "De beheerder heeft een uniforme inlogauthenticatie ingeschakeld. Klik op de onderstaande knop om in te loggen en de applicatie te ontgrendelen", "success": "Inloggen geslaagd", "title": "Inloggen", "welcome": "Welkom!"}, "password": {"description": "De beheerder heeft applicatieversleuteling ingeschakeld. Voer het applicatiewachtwoord in om de applicatie te ontgrendelen. Het wachtwoord hoeft maar één keer te worden ingevoerd", "placeholder": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "title": "<PERSON><PERSON><PERSON> wachtwo<PERSON> in om de applicatie te ontgrendelen"}, "tabs": {"apiKey": "Aangepaste API-sleutel", "password": "Wachtwoord"}}}
import React from 'react';
import { escapeHtml, createSafeHtml } from '../utils/security';

interface SafeContentProps {
  content: string;
  allowHtml?: boolean;
  className?: string;
}

/**
 * 安全内容显示组件
 * 用于安全地显示用户生成的内容，防止XSS攻击
 */
const SafeContent: React.FC<SafeContentProps> = ({
  content,
  allowHtml = false,
  className = ''
}) => {
  // 如果允许HTML内容（仅用于可信内容，如管理员编辑的内容）
  if (allowHtml) {
    return (
      <div 
        className={`safe-content ${className}`}
        dangerouslySetInnerHTML={createSafeHtml(content)}
      />
    );
  }
  
  // 默认情况下，转义所有HTML标签
  const safeContent = escapeHtml(content);
  
  // 将换行符转换为<br>标签
  const contentWithBreaks = safeContent.replace(/\n/g, '<br />');
  
  return (
    <div 
      className={`safe-content ${className}`}
      dangerouslySetInnerHTML={createSafeHtml(contentWithBreaks)}
    />
  );
};

export default SafeContent; 
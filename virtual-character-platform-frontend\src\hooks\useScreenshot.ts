import dayjs from 'dayjs';
import { domToJpeg, domToPng, domToSvg, domToWebp } from 'modern-screenshot';
import { useCallback, useState } from 'react';

// import { ImageType } from '@/app/chat/ChatMode/ChatHeader/actions/ShareButton/type';
// import { useSessionStore } from '@/store/session';
// import { sessionSelectors } from '@/store/session/selectors';

// 临时类型定义，待后续集成完整的类型系统
export enum ImageType {
  JPG = 'jpg',
  PNG = 'png',
  SVG = 'svg',
  WEBP = 'webp'
}

export const useScreenshot = (imageType: ImageType, selector: string, title?: string) => {
  const [loading, setLoading] = useState(false);
  // const currentAgent = useSessionStore(sessionSelectors.currentAgent);
  // const title = currentAgent?.meta?.name;
  const defaultTitle = title || '角色截图';

  const handleDownload = useCallback(async () => {
    setLoading(true);
    try {
      let screenshotFn: any;
      switch (imageType) {
        case ImageType.JPG: {
          screenshotFn = domToJpeg;
          break;
        }
        case ImageType.PNG: {
          screenshotFn = domToPng;
          break;
        }
        case ImageType.SVG: {
          screenshotFn = domToSvg;
          break;
        }
        case ImageType.WEBP: {
          screenshotFn = domToWebp;
          break;
        }
      }

      const dataUrl = await screenshotFn(document.querySelector(selector) as HTMLDivElement, {
        features: {
          // 不启用移除控制符，否则会导致 safari emoji 报错
          removeControlCharacter: false,
        },
        scale: 2,
      });
      const link = document.createElement('a');
      link.download = `VirtualCharacter_${defaultTitle}_${dayjs().format('YYYY-MM-DD')}.${imageType}`;
      link.href = dataUrl;
      link.click();
      setLoading(false);
    } catch (error) {
      console.error('Failed to download image', error);
      setLoading(false);
    }
  }, [imageType, defaultTitle]);

  return {
    loading,
    onDownload: handleDownload,
    title: defaultTitle,
  };
};

import api from './api';

// 管理员登录接口参数
export interface AdminLoginParams {
  username: string;
  password: string;
}

// 角色创建/编辑参数
export interface AdminCharacterParams {
  name: string;
  identity?: string;
  personality?: string;
  imageUrl?: string;
  params?: Record<string, any>;
  public?: boolean;
  systemPrompt?: string;
}

// 提示词模板参数
export interface PromptTemplateParams {
  name: string;
  type: string; // 'image', 'chat', 'system', etc.
  category?: string;
  content: string;
  description?: string;
  is_active?: boolean;
  version?: string;
  variables?: Record<string, any>;
  examples?: Record<string, any>;
}

// 管理员API服务
const adminAPI = {
  // 管理员认证
  login: (params: AdminLoginParams) => 
    api.post('/api/admin/login', params),
  
  // 仪表盘数据
  getDashboard: () => 
    api.get('/api/admin/dashboard'),
  
  // 角色管理
  getCharacters: (page = 1, pageSize = 10, filters = {}) => 
    api.get('/api/admin/characters', { params: { page, pageSize, ...filters } }),
  
  getCharacterDetail: (characterId: number) => 
    api.get(`/api/admin/characters/${characterId}`),
  
  createCharacter: (params: AdminCharacterParams) => 
    api.post('/api/admin/characters', params),
  
  updateCharacter: (characterId: number, params: AdminCharacterParams) => 
    api.put(`/api/admin/characters/${characterId}`, params),
  
  deleteCharacter: (characterId: number) => 
    api.delete(`/api/admin/characters/${characterId}`),
  
  generateCharacterImage: (params: { prompt: string }) => 
    api.post('/api/admin/characters/generate-image', params),
  
  // 提示词模板管理
  getPromptTemplates: (filters = {}) => 
    api.get('/api/admin/prompt-templates', { params: { ...filters } }),
  
  getPromptTemplateDetail: (templateId: number) => 
    api.get(`/api/admin/prompt-templates/${templateId}`),
  
  createPromptTemplate: (params: PromptTemplateParams) => 
    api.post('/api/admin/prompt-templates', params),
  
  updatePromptTemplate: (templateId: number, params: PromptTemplateParams) => 
    api.put(`/api/admin/prompt-templates/${templateId}`, params),
  
  deletePromptTemplate: (templateId: number) => 
    api.delete(`/api/admin/prompt-templates/${templateId}`),
  
  testPromptTemplate: (params: { content: string, testParams?: Record<string, any> }) => 
    api.post('/api/admin/prompt-templates/test', params),
  
  // 操作日志
  getOperationLogs: (page = 1, pageSize = 10, filters = {}) => 
    api.get('/api/admin/logs', { params: { page, pageSize, ...filters } }),
  
  // 系统配置
  getSystemConfigs: () => 
    api.get('/api/admin/config'),
  
  updateSystemConfig: (params: { key: string, value: string, description?: string }) => 
    api.post('/api/admin/config', params),
};

export default adminAPI; 
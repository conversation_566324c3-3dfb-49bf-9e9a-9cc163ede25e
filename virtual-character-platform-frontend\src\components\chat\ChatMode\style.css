/* 聊天模式样式 */
.chat-mode-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-bg-container);
}

.chat-mode-content {
  flex: 1;
  overflow: hidden;
}

/* 聊天列表 */
.chat-list-container {
  background: var(--color-bg-layout);
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 消息输入区域 */
.chat-mode-input-docker {
  background: var(--color-bg-container);
  border-top: 1px solid var(--color-border);
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.chat-mode-input-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.message-input-container {
  width: 100%;
}

.message-input-wrapper {
  width: 100%;
}

.message-input-text {
  flex: 1;
  min-width: 0;
}

.message-textarea {
  resize: none;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
}

.message-textarea:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.message-input-actions {
  flex-shrink: 0;
}

.message-input-actions .ant-btn {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.message-input-actions .ant-btn:hover {
  transform: translateY(-1px);
}

/* 语音录制指示器 */
.voice-recording-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: var(--color-success-bg);
  border: 1px solid var(--color-success-border);
  border-radius: 6px;
  color: var(--color-success);
  font-size: 12px;
  animation: fadeIn 0.3s ease-out;
}

.recording-dot {
  width: 8px;
  height: 8px;
  background: var(--color-success);
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-mode-input-docker {
    padding: 12px;
  }
  
  .message-input-actions .ant-btn {
    width: 40px;
    height: 40px;
  }
  
  .message-textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 深色模式适配 */
[data-theme='dark'] .chat-list-container {
  background: var(--color-bg-container);
}

[data-theme='dark'] .chat-mode-input-docker {
  background: var(--color-bg-elevated);
  border-top-color: var(--color-border-secondary);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
}

[data-theme='dark'] .message-textarea {
  background: var(--color-bg-container);
  border-color: var(--color-border-secondary);
}

[data-theme='dark'] .voice-recording-indicator {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.3);
}

/* 滚动条样式 */
.chat-list-container::-webkit-scrollbar {
  width: 6px;
}

.chat-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-list-container::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.chat-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-secondary);
}

/* 平滑滚动 */
.chat-list-container {
  scroll-behavior: smooth;
}

/* 输入框聚焦动画 */
.message-input-wrapper:focus-within .message-input-actions .ant-btn {
  border-color: var(--color-primary);
}

/* 发送按钮特殊样式 */
.message-input-actions .ant-btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, #40a9ff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.message-input-actions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, var(--color-primary) 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

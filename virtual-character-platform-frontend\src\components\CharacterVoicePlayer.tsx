import React, { useEffect, useRef, useState, useCallback } from 'react';
import { message } from 'antd';
import { AudioPlayer } from '../libs/audio/AudioPlayer';

interface CharacterVoicePlayerProps {
  audioUrl?: string;
  onPlayStart?: () => void;
  onPlayEnd?: () => void;
  onVolumeChange?: (volume: number) => void;
  onError?: (error: Error) => void;
  autoPlay?: boolean;
  enableLipSync?: boolean;
  className?: string;
}

export const CharacterVoicePlayer: React.FC<CharacterVoicePlayerProps> = ({
  audioUrl,
  onPlayStart,
  onPlayEnd,
  onVolumeChange,
  onError,
  autoPlay = true,
  enableLipSync = true,
  className = ''
}) => {
  const audioPlayerRef = useRef<AudioPlayer | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 初始化音频播放器
  useEffect(() => {
    const initializePlayer = async () => {
      try {
        audioPlayerRef.current = new AudioPlayer();
        await audioPlayerRef.current.initialize();
        setIsInitialized(true);
        console.log('CharacterVoicePlayer: 音频播放器初始化完成');
      } catch (error) {
        console.error('CharacterVoicePlayer: 音频播放器初始化失败:', error);
        if (onError) {
          onError(error as Error);
        }
      }
    };

    initializePlayer();

    // 清理函数
    return () => {
      if (audioPlayerRef.current) {
        audioPlayerRef.current.dispose();
      }
    };
  }, [onError]);

  // 播放音频的函数
  const playAudio = useCallback(async (url: string) => {
    if (!audioPlayerRef.current || !isInitialized) {
      console.warn('CharacterVoicePlayer: 音频播放器未初始化');
      return;
    }

    try {
      setIsPlaying(true);
      if (onPlayStart) {
        onPlayStart();
      }

      console.log('CharacterVoicePlayer: 开始播放音频:', url);

      await audioPlayerRef.current.playWithLipSync(url, {
        enableLipSync,
        onEnded: () => {
          console.log('CharacterVoicePlayer: 音频播放结束');
          setIsPlaying(false);
          if (onPlayEnd) {
            onPlayEnd();
          }
        },
        onError: (error) => {
          console.error('CharacterVoicePlayer: 播放错误:', error);
          setIsPlaying(false);
          message.error('音频播放失败');
          if (onError) {
            onError(error);
          }
        },
        onVolumeChange: (volume) => {
          if (onVolumeChange) {
            onVolumeChange(volume);
          }
        }
      });

    } catch (error) {
      console.error('CharacterVoicePlayer: 播放失败:', error);
      setIsPlaying(false);
      message.error('音频播放失败');
      if (onError) {
        onError(error as Error);
      }
    }
  }, [isInitialized, enableLipSync, onPlayStart, onPlayEnd, onVolumeChange, onError]);

  // 停止播放
  const stopAudio = useCallback(() => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.stop();
      setIsPlaying(false);
      if (onPlayEnd) {
        onPlayEnd();
      }
    }
  }, [onPlayEnd]);

  // 当audioUrl变化时自动播放
  useEffect(() => {
    if (audioUrl && autoPlay && isInitialized) {
      playAudio(audioUrl);
    }
  }, [audioUrl, autoPlay, isInitialized, playAudio]);

  // 处理用户交互恢复音频上下文
  const handleUserInteraction = useCallback(async () => {
    if (audioPlayerRef.current) {
      try {
        await audioPlayerRef.current.resumeAudioContext();
      } catch (error) {
        console.warn('CharacterVoicePlayer: 恢复音频上下文失败:', error);
      }
    }
  }, []);

  // 监听用户交互事件
  useEffect(() => {
    const events = ['click', 'touchstart', 'keydown'];
    
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [handleUserInteraction]);

  // 暴露控制方法给父组件
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    play: (url: string) => playAudio(url),
    stop: stopAudio,
    isPlaying,
    isInitialized
  }));

  return (
    <div className={`character-voice-player ${className}`}>
      {/* 可选的可视化指示器 */}
      {isPlaying && (
        <div className="voice-playing-indicator">
          <div className="voice-wave">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      )}
      
      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="voice-debug-info" style={{ fontSize: '10px', color: '#999' }}>
          <div>初始化: {isInitialized ? '✅' : '❌'}</div>
          <div>播放中: {isPlaying ? '🔊' : '🔇'}</div>
          {audioUrl && <div>URL: {audioUrl.substring(0, 50)}...</div>}
        </div>
      )}
    </div>
  );
};

export default CharacterVoicePlayer;

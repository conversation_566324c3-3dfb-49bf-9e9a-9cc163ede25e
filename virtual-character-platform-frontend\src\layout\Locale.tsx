import { ConfigProvider } from 'antd';
import { memo, useEffect, useState } from 'react';
import type { PropsWithChildren } from 'react';

import { createI18nNext } from '../locales/create';
import { getAntdLocale } from '../utils/locale';

interface LocaleLayoutProps extends PropsWithChildren {
  antdLocale?: any;
  defaultLang?: string;
}

const Locale = memo<LocaleLayoutProps>(({ children, defaultLang, antdLocale }) => {
  const [i18n] = useState(createI18nNext(defaultLang));
  const [lang, setLang] = useState(defaultLang);
  const [locale, setLocale] = useState(antdLocale);

  // 客户端应用：只初始化一次i18n实例
  if (!i18n.instance.isInitialized) {
    i18n.init().then(() => {
      console.debug('i18n initialized');
    });
  }

  // handle i18n instance language change
  useEffect(() => {
    const handleLang = async (lng: string) => {
      setLang(lng);

      if (lang === lng) return;

      const newLocale = await getAntdLocale(lng);
      setLocale(newLocale);
    };

    i18n.instance.on('languageChanged', handleLang);
    return () => {
      i18n.instance.off('languageChanged', handleLang);
    };
  }, [i18n, lang]);

  return <ConfigProvider locale={locale}>{children}</ConfigProvider>;
});

Locale.displayName = 'Locale';

export default Locale;

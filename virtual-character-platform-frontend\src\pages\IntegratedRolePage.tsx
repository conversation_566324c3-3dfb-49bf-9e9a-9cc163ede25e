import React, { memo, useState } from 'react';
import { Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';
import { useNavigate } from 'react-router-dom';

// 使用适配的组件
import AdaptedRoleEdit from '../components/role/AdaptedRoleEdit';
import AdaptedRoleSideBar from '../components/role/AdaptedRoleSideBar';

// 导入样式
import '../styles/role-edit.css';

// 集成的角色管理页面
const IntegratedRolePage: React.FC = () => {
  const navigate = useNavigate();
  const [showSidebar, setShowSidebar] = useState(true);

  const handleBack = () => {
    navigate('/community');
  };

  return (
    <div className="role-edit-container">
      {/* 顶部工具栏 */}
      <div className="role-edit-header">
        <div className="header-left">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            返回
          </Button>
          <span className="role-name">角色管理</span>
        </div>

        <div className="header-right">
          <Button
            onClick={() => setShowSidebar(!showSidebar)}
          >
            {showSidebar ? '隐藏' : '显示'}角色列表
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Flexbox
        flex={1}
        horizontal
        className="role-edit-content"
        style={{ overflow: 'hidden' }}
      >
        {/* 左侧边栏 */}
        <AdaptedRoleSideBar
          visible={showSidebar}
          onClose={() => setShowSidebar(false)}
        />

        {/* 中间编辑区域 */}
        <Flexbox flex={1} className="role-edit-main">
          <AdaptedRoleEdit
            onToggleSidebar={() => setShowSidebar(!showSidebar)}
          />
        </Flexbox>
      </Flexbox>
    </div>
  );
};

export default memo(IntegratedRolePage);

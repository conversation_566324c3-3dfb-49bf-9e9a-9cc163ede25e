{"azure": {"azureApiVersion": {"desc": "Wersja API Azure, w formacie YYYY-MM-DD, sprawd<PERSON> [najnowszą wersję](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)", "fetch": "<PERSON><PERSON><PERSON>", "title": "Wersja API Azure"}, "empty": "Wprowadź identyfikator modelu, a<PERSON> <PERSON><PERSON><PERSON> pier<PERSON> model", "endpoint": {"desc": "Możesz znaleźć tę wartość w sekcji 'Klucze i punkty końcowe' w portalu Azure", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Adres API Azure"}, "modelListPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lub dodaj swój wdrożony model OpenAI", "title": "Azure OpenAI", "token": {"desc": "Możesz znaleźć tę wartość w sekcji 'Klucze i punkty końcowe' w portalu Azure. Możesz użyć KEY1 lub KEY2", "placeholder": "Klucz API Azure", "title": "Klucz API"}}, "bedrock": {"accessKeyId": {"desc": "Wprowadź identyfikator klucza dostępu AWS", "placeholder": "Identyfikator klucza dostępu AWS", "title": "Identyfikator klucza dostępu AWS"}, "checker": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy AccessKeyId / SecretAccessKey są poprawnie wypełnione"}, "region": {"desc": "Wprowadź region AWS", "placeholder": "Region AWS", "title": "Region AWS"}, "secretAccessKey": {"desc": "Wprowadź tajny klucz dostępu AWS", "placeholder": "Tajny klucz dostępu AWS", "title": "Tajny klucz dostępu AWS"}, "sessionToken": {"desc": "<PERSON><PERSON><PERSON> używasz AWS SSO/STS, wprowadź swój token sesji AWS", "placeholder": "Token sesji AWS", "title": "Token <PERSON>ji <PERSON> (opcjonalnie)"}, "title": "Bedrock", "unlock": {"customRegion": "Niestandardowy region usługi", "customSessionToken": "Niestandardowy token sesji", "description": "Wprowadź swój AccessKeyId / SecretAccessKey, aby r<PERSON> sesję. Aplikacja nie zapisuje twojej konfiguracji uwierzytelniania", "title": "Użyj niestandardowych informacji uwierzytelniających Bedrock"}}, "github": {"personalAccessToken": {"desc": "Wprowadź swój Github PAT, klik<PERSON><PERSON> [tutaj](https://github.com/settings/tokens), a<PERSON> u<PERSON><PERSON><PERSON><PERSON>", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "W<PERSON>rowadź swój token HuggingFace, klik<PERSON>j [tutaj](https://huggingface.co/settings/tokens), a<PERSON> u<PERSON><PERSON><PERSON><PERSON>", "placeholder": "hf_xxxxxxxxx", "title": "Token <PERSON>"}}, "ollama": {"checker": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy adres proxy jest poprawnie wypełniony", "title": "Sprawdzenie łączności"}, "customModelName": {"desc": "<PERSON><PERSON><PERSON> model, oddzielając wiele modeli przecinkiem (,)", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "Nazwa niestandardowego modelu"}, "download": {"desc": "Ollama pobiera ten model, postaraj się nie zamykać tej strony. Ponowne pobieranie przerwie kontynuację", "remainingTime": "Pozostały czas", "speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć pobierania", "title": "Pobieranie modelu {{model}}"}, "endpoint": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> adres proxy interfe<PERSON><PERSON>, poz<PERSON><PERSON> puste, je<PERSON><PERSON> lokalnie nie określono dodatkowo", "title": "<PERSON><PERSON>"}, "setup": {"cors": {"description": "Z powodu ograniczeń bezpieczeństwa przeglądarki musisz s<PERSON>ć CORS dla Ollama, aby móc go używać.", "linux": {"env": "Dodaj `Environment` w sekcji [Service], dodaj zmienną środowiskową OLLAMA_ORIGINS:", "reboot": "Przeładuj systemd i uruchom ponownie Ollama", "systemd": "<PERSON><PERSON><PERSON><PERSON><PERSON> systemd, aby edyt<PERSON><PERSON> usługę ollama:"}, "macos": "Otwórz aplikację 'Terminal' i wklej poniższe polecenie, a następnie naciśnij Enter", "reboot": "Uruchom ponownie usługę Ollama po zakończeniu", "title": "Skonfiguru<PERSON>, aby zezwolić na dostęp CORS", "windows": "Na <PERSON>ie kliknij 'Panel sterowania', aby ed<PERSON><PERSON><PERSON> zmienne środowiskowe systemu. Utwórz nową zmienną środowiskową o nazwie 'OLLAMA_ORIGINS' z wartością *, klik<PERSON><PERSON> 'OK/Zastosuj', aby z<PERSON><PERSON><PERSON>"}, "install": {"description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> nie, przej<PERSON><PERSON> na stronę <1>pobierz</1> <PERSON><PERSON><PERSON>", "docker": "<PERSON><PERSON><PERSON> w<PERSON>z <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> ofer<PERSON>je oficjalny obraz <PERSON>, kt<PERSON><PERSON> moż<PERSON> pobrać za pomocą poniższego polecenia:", "linux": {"command": "Zainstaluj za pomocą poniższego polecenia:", "manual": "Lub możesz zapoznać się z <1>podręcznikiem instalacji ręcznej dla Linuxa</1>, aby z<PERSON><PERSON><PERSON> samodzielnie"}, "title": "Zainstaluj i uruchom aplikację Ollama lokalnie", "windowsTab": "Windows (wersja podglądowa)"}}, "title": "Ollama", "unlock": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "description": "W<PERSON><PERSON><PERSON><PERSON> etykietę modelu <PERSON>, aby k<PERSON><PERSON><PERSON><PERSON> sesję", "downloaded": "{{completed}} / {{total}}", "starting": "Rozpoczynam pobieranie...", "title": "<PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON>"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "Wprowadź identyfikator klucza dostępu SenseNova", "placeholder": "Identyfikator klu<PERSON>a dos<PERSON>ę<PERSON>ova", "title": "Identyfikator klucza dostępu"}, "sensenovaAccessKeySecret": {"desc": "Wprowadź tajny klucz dostępu SenseNova", "placeholder": "Tajny klucz dostępu SenseNova", "title": "Tajny klucz dostępu"}, "unlock": {"description": "Wprowadź swój identyfikator klucza dostępu / tajny klucz dostępu, aby rozpo<PERSON>ć sesję. Aplikacja nie zapisuje twojej konfiguracji uwierzytelniania", "title": "Użyj niestandardowych informacji uwierzytelniają<PERSON>ch <PERSON>ova"}}, "wenxin": {"accessKey": {"desc": "Wprowadź klucz dostępu z platformy Baidu Qianfan", "placeholder": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "checker": {"desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> / SecretAccess są poprawnie wypełnione"}, "secretKey": {"desc": "Wprowadź tajny klucz z platformy Baidu Qianfan", "placeholder": "Tajny klucz Qianfan", "title": "Tajny klucz"}, "unlock": {"customRegion": "Niestandardowy region usługi", "description": "Wprowadź swój <PERSON>ey / SecretKey, aby r<PERSON><PERSON> sesję. Aplikacja nie zapisuje twojej konfiguracji uwierzytelniania", "title": "Użyj niestandardowych informacji uwierzytelniaj<PERSON><PERSON><PERSON>"}}, "zeroone": {"title": "01.AI Zero One"}, "zhipu": {"title": "<PERSON><PERSON><PERSON>"}}
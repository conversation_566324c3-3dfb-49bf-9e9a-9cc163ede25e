# 🚀 虚拟角色平台对话服务使用指南

## 📋 服务概览

### 当前已配置的服务
- ✅ **星火AI对话服务** - 基于讯飞星火大模型的智能对话
- ✅ **TTS语音合成服务** - 支持讯飞和阿里云TTS
- ✅ **语音识别服务** - 基于Web Speech API
- ✅ **3D角色渲染** - VRM模型支持，表情动画
- ✅ **背景图片生成** - AI生成角色背景
- ✅ **用户认证系统** - 完整的登录注册功能

## 🔧 环境配置

### 1. 当前已配置的服务 ✅

项目根目录的 `.env` 文件已包含以下配置：

```bash
# ✅ 已配置 - 星火AI对话服务
SPARK_APP_ID=2c25d0fb
SPARK_API_KEY=9b6134381f1fec8857c8b02f06e627aa
SPARK_API_SECRET=ODc3MTQzNDhhODA2NWI5YmJiMjU2NzM1

# ✅ 已配置 - 阿里云OSS存储服务
OSS_ACCESS_KEY_ID=LTAI5tS7LV3r9HgZT7YB4p2X
OSS_ACCESS_KEY_SECRET=******************************
OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
OSS_BUCKET_NAME=aigo

# ✅ 已配置 - Django基础配置
SECRET_KEY=IlvwxGaerOBEqxCAqubW:maUqAMgaXqibbjWTySvJ
DEBUG=True
DB_ENGINE=django.db.backends.sqlite3
```

### 2. 可选配置（如需扩展功能）

如果需要添加更多TTS服务，可以补充以下配置：

```bash
# 讯飞TTS配置（可选）
XUNFEI_TTS_APP_ID=your_xunfei_tts_app_id
XUNFEI_TTS_API_KEY=your_xunfei_tts_api_key
XUNFEI_TTS_API_SECRET=your_xunfei_tts_api_secret

# 阿里云TTS配置（可选）
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret

# TTS服务配置
TTS_DEFAULT_PROVIDER=xunfei  # 或 aliyun
```

### 2. 数据库初始化

```bash
# 进入项目根目录
cd d:\app\虚拟角色1.0.0

# 激活虚拟环境
venv\Scripts\activate

# 运行数据库迁移
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser
```

## 🚀 启动服务

### 1. 启动后端服务

```bash
# 方法1：使用开发脚本
python start_dev.py

# 方法2：手动启动
python manage.py runserver 8000
```

### 2. 启动前端服务

```bash
# 进入前端目录
cd virtual-character-platform-frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev
```

服务启动后：
- 后端API：http://localhost:8000
- 前端界面：http://localhost:5173

## 💬 对话服务使用方法

### 1. 创建角色

1. 访问 http://localhost:5173
2. 注册/登录账户
3. 点击"创建角色"
4. 填写角色信息：
   - 角色名称
   - 年龄、性别
   - 性格特质（如：温柔、活泼、成熟等）
   - 身份设定（如：学生、职场人士、艺术家等）
5. 生成角色头像（AI自动生成）
6. 保存角色

### 2. 文字对话

#### 前端调用方式：
```typescript
import { characterAPI } from '@/services/characterAPI';

// 发送消息
const response = await characterAPI.sendMessage({
  characterId: 'your_character_id',
  message: '你好，今天心情怎么样？',
  enable_tts: true,  // 启用语音回复
  voice_mode: false  // 文字模式
});

console.log('角色回复:', response.character_response);
console.log('语音URL:', response.audio_url);
```

#### API接口：
```
POST /api/characters/{character_id}/chat/
Content-Type: application/json

{
  "user_message": "你好，今天心情怎么样？",
  "enable_tts": true,
  "voice_mode": false
}
```

#### 响应格式：
```json
{
  "character_response": "你好！我今天心情很好呢，看到你就更开心了~",
  "audio_url": "https://your-oss-bucket.com/audio/response_123.mp3",
  "emotion": "happy",
  "success": true
}
```

### 3. 语音对话（沉浸式模式）

#### 访问沉浸式聊天页面：
```
http://localhost:5173/immersive-chat/{character_id}
```

#### 功能特点：
- 🎤 **语音输入** - 点击麦克风按钮开始语音识别
- 🔊 **语音输出** - 角色自动语音回复
- 🎭 **表情同步** - 根据对话情感自动切换表情
- 💋 **口型同步** - 语音播放时实时口型匹配
- 🖼️ **背景切换** - 随机显示角色背景图片

#### 前端语音交互代码：
```typescript
import { useSpeechRecognition } from '@/hooks/useSpeechRecognition';

const {
  isListening,
  startListening,
  stopListening,
  isSupported
} = useSpeechRecognition({
  onResult: (transcript, isFinal) => {
    if (isFinal) {
      // 发送语音识别结果到后端
      handleSendMessage(transcript);
    }
  },
  language: 'zh-CN'
});
```

## 🎵 TTS语音服务

### 1. 支持的音色

#### 讯飞TTS音色：
- **女声**：小娟（甜美）、小燕（可爱）、小美（温柔）、小丽（成熟）
- **男声**：小峰（温暖）、小明（成熟）

#### 语音参数：
- **语速**：慢速、正常、快速
- **音量**：0-100
- **音调**：0-100
- **情感**：中性、开心、悲伤、愤怒、惊讶

### 2. 智能音色推荐

系统会根据角色特征自动推荐合适的音色：

```python
# 后端自动推荐逻辑
def get_voice_by_character_traits(gender, age, personality):
    if gender == 'female':
        if age < 25:
            if '可爱' in personality:
                return 'female_cute'
            else:
                return 'female_sweet'
        else:
            return 'female_gentle'
    else:  # male
        if age < 30:
            return 'male_warm'
        else:
            return 'male_mature'
```

### 3. TTS API使用

#### 获取音色列表：
```
GET /api/tts/voices/
```

#### 音色推荐：
```
POST /api/tts/recommend/
{
  "gender": "female",
  "age": 22,
  "personality": "温柔可爱"
}
```

#### 语音预览：
```
POST /api/tts/preview/
{
  "text": "你好，我是你的虚拟角色",
  "voice": "female_sweet",
  "speed": "normal",
  "emotion": "happy"
}
```

## 🎭 表情动画系统

### 支持的表情类型：
- `happy` - 开心 😊
- `sad` - 悲伤 😢
- `caring` - 关怀 🤗
- `listening` - 倾听 👂
- `thinking` - 思考 🤔
- `neutral` - 平静 😐

### 前端表情控制：
```typescript
// 在VidolChatComponent中
<VidolChatComponent
  character={selectedCharacter}
  emotion="happy"  // 设置表情
  audioUrl={audioUrl}
  onLipSyncUpdate={(volume) => {
    // 口型同步回调
    console.log('当前音量:', volume);
  }}
/>
```

## 🔍 故障排除

### 常见问题：

1. **语音识别不工作**
   - 检查浏览器是否支持Web Speech API
   - 确保麦克风权限已授权
   - 使用HTTPS或localhost访问

2. **TTS语音合成失败**
   - 检查环境变量配置
   - 验证API密钥是否正确
   - 查看后端日志错误信息

3. **3D角色不显示**
   - 检查VRM模型文件是否存在
   - 确保Three.js依赖正确安装
   - 查看浏览器控制台错误

4. **对话无响应**
   - 检查星火AI配置
   - 验证网络连接
   - 查看后端API日志

### 日志查看：
```bash
# 查看应用日志
tail -f logs/app.log

# 查看Django日志
tail -f logs/django.log

# 查看错误日志
tail -f logs/error.log
```

## 📊 性能监控

### 关键指标：
- **响应延迟**：< 3秒
- **语音识别准确率**：> 90%
- **TTS合成成功率**：> 95%
- **3D渲染帧率**：> 30fps

### 监控方法：
```javascript
// 前端性能监控
console.time('对话响应时间');
const response = await characterAPI.sendMessage(params);
console.timeEnd('对话响应时间');
```

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

{"azure": {"azureApiVersion": {"desc": "Azure의 API 버전, YYYY-MM-DD 형식을 따릅니다. [최신 버전](https://learn.microsoft.com/zh-cn/azure/ai-services/openai/reference#chat-completions)을 참조하세요.", "fetch": "목록 가져오기", "title": "Azure API 버전"}, "empty": "모델 ID를 입력하여 첫 번째 모델을 추가하세요.", "endpoint": {"desc": "Azure 포털에서 리소스를 확인할 때 '키 및 끝점' 섹션에서 이 값을 찾을 수 있습니다.", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API 주소"}, "modelListPlaceholder": "배포한 OpenAI 모델을 선택하거나 추가하세요.", "title": "Azure OpenAI", "token": {"desc": "Azure 포털에서 리소스를 확인할 때 '키 및 끝점' 섹션에서 이 값을 찾을 수 있습니다. KEY1 또는 KEY2를 사용할 수 있습니다.", "placeholder": "Azure API 키", "title": "API 키"}}, "bedrock": {"accessKeyId": {"desc": "AWS Access Key Id를 입력하세요.", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "AccessKeyId / SecretAccessKey가 올바르게 입력되었는지 테스트합니다."}, "region": {"desc": "AWS 리전을 입력하세요.", "placeholder": "AWS 리전", "title": "AWS 리전"}, "secretAccessKey": {"desc": "AWS Secret Access Key를 입력하세요.", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "AWS SSO/STS를 사용 중이라면 AWS Session Token을 입력하세요.", "placeholder": "AWS Session Token", "title": "AWS Session Token (선택 사항)"}, "title": "Bedrock", "unlock": {"customRegion": "사용자 정의 서비스 지역", "customSessionToken": "사용자 정의 세션 토큰", "description": "AWS AccessKeyId / SecretAccessKey를 입력하면 세션을 시작할 수 있습니다. 애플리케이션은 인증 구성을 기록하지 않습니다.", "title": "사용자 정의 Bedrock 인증 정보 사용"}}, "github": {"personalAccessToken": {"desc": "당신의 Github PAT를 입력하세요. [여기](https://github.com/settings/tokens)를 클릭하여 생성하세요.", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "당신의 HuggingFace Token을 입력하세요. [여기](https://huggingface.co/settings/tokens)를 클릭하여 생성하세요.", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "ollama": {"checker": {"desc": "프록시 주소가 올바르게 입력되었는지 테스트합니다.", "title": "연결성 검사"}, "customModelName": {"desc": "사용자 정의 모델을 추가하세요. 여러 모델은 쉼표(,)로 구분합니다.", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "사용자 정의 모델 이름"}, "download": {"desc": "Ollama가 해당 모델을 다운로드 중입니다. 이 페이지를 닫지 마세요. 재다운로드 시 중단된 곳에서 계속됩니다.", "remainingTime": "남은 시간", "speed": "다운로드 속도", "title": "{{model}} 모델 다운로드 중"}, "endpoint": {"desc": "Ollama 인터페이스 프록시 주소를 입력하세요. 로컬에서 추가로 지정하지 않았다면 비워두세요.", "title": "Ollama 서비스 주소"}, "setup": {"cors": {"description": "브라우저 보안 제한으로 인해 Ollama에 대해 교차 출처 구성을 해야 정상적으로 사용할 수 있습니다.", "linux": {"env": "[Service] 섹션 아래에 `Environment`를 추가하고 OLLAMA_ORIGINS 환경 변수를 추가하세요:", "reboot": "systemd를 재로드하고 Ollama를 재시작하세요.", "systemd": "systemd를 호출하여 ollama 서비스를 편집하세요:"}, "macos": "터미널 애플리케이션을 열고 아래 명령어를 붙여넣고 Enter를 눌러 실행하세요.", "reboot": "작업이 완료된 후 Ollama 서비스를 재시작하세요.", "title": "Ollama의 교차 출처 접근 허용 구성", "windows": "Windows에서 '제어판'을 클릭하고 시스템 환경 변수를 편집하세요. 사용자 계정에 'OLLAMA_ORIGINS'라는 이름의 환경 변수를 새로 만들고 값은 *로 설정한 후 '확인/적용'을 클릭하여 저장하세요."}, "install": {"description": "Ollama가 활성화되어 있는지 확인하세요. Ollama를 다운로드하지 않았다면 공식 웹사이트<1>에서 다운로드</1>하세요.", "docker": "Docker를 사용하는 것을 선호한다면 Ollama는 공식 Docker 이미지를 제공합니다. 아래 명령어로 가져올 수 있습니다:", "linux": {"command": "아래 명령어로 설치하세요:", "manual": "또는 <1>Linux 수동 설치 가이드</1>를 참조하여 직접 설치할 수 있습니다."}, "title": "로컬에 Ollama 애플리케이션 설치 및 활성화", "windowsTab": "Windows (미리보기)"}}, "title": "Ollama", "unlock": {"cancel": "다운로드 취소", "confirm": "다운로드", "description": "Ollama 모델 태그를 입력하면 세션을 계속 진행할 수 있습니다.", "downloaded": "{{completed}} / {{total}}", "starting": "다운로드 시작 중...", "title": "지정된 Ollama 모델 다운로드"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "SenseNova Access Key ID를 입력하세요.", "placeholder": "SenseNova Access Key ID", "title": "Access Key ID"}, "sensenovaAccessKeySecret": {"desc": "SenseNova Access Key Secret을 입력하세요.", "placeholder": "SenseNova Access Key Secret", "title": "Access Key Secret"}, "unlock": {"description": "Access Key ID / Access Key Secret을 입력하면 세션을 시작할 수 있습니다. 애플리케이션은 인증 구성을 기록하지 않습니다.", "title": "사용자 정의 SenseNova 인증 정보 사용"}}, "wenxin": {"accessKey": {"desc": "바이두 천범 플랫폼의 Access Key를 입력하세요.", "placeholder": "Qianfan Access Key", "title": "Access Key"}, "checker": {"desc": "AccessKey / SecretAccess가 올바르게 입력되었는지 테스트합니다."}, "secretKey": {"desc": "바이두 천범 플랫폼 Secret Key를 입력하세요.", "placeholder": "<PERSON><PERSON>fan Secret Key", "title": "Secret Key"}, "unlock": {"customRegion": "사용자 정의 서비스 지역", "description": "AccessKey / SecretKey를 입력하면 세션을 시작할 수 있습니다. 애플리케이션은 인증 구성을 기록하지 않습니다.", "title": "사용자 정의 문심일언 인증 정보 사용"}}, "zeroone": {"title": "01.AI 제로일 만물"}, "zhipu": {"title": "지표"}}
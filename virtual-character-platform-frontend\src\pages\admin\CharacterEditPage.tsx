import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Spin, 
  message, 
  Row, 
  Col, 
  Typography, 
  Tabs, 
  Select, 
  InputNumber, 
  Switch, 
  Upload, 
  Divider,
  Space,
  Image
} from 'antd';
import { 
  UploadOutlined, 
  SaveOutlined, 
  RollbackOutlined, 
  SyncOutlined,
  PlusOutlined,
  MinusCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import adminAPI from '../../services/adminAPI';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

// 性格选项
const PERSONALITY_OPTIONS = [
  "傲娇", "病娇", "元气", "沉稳", "冷酷", "温柔", "活泼", 
  "腼腆", "高冷", "毒舌", "健气系", "哥哥系", "姐姐系"
];

// 身份选项
const IDENTITY_OPTIONS = [
  "高中生", "大学生", "偶像", "虚拟歌姬", "咖啡店店员", "魔法使", 
  "女仆", "赛博朋克侦探", "异世界公主", "游戏NPC", "虚拟心理咨询师"
];

// 发型选项
const HAIR_STYLE_OPTIONS = ['长直发', '短发', '双马尾', '丸子头', '卷发', '辫子', '蓬松'];

// 发色选项
const HAIR_COLOR_OPTIONS = ['黑色', '金色', '银色', '红色', '蓝色', '粉色', '紫色', '绿色', '白色'];

// 瞳色选项
const EYE_COLOR_OPTIONS = ['蓝色', '红色', '金色', '绿色', '紫色', '粉色', '黑色', '异色瞳'];

// 表情选项
const EXPRESSION_OPTIONS = ['微笑', '严肃', '害羞', '自信', '傲娇', '惊讶', '伤心'];

// 服装选项
const OUTFIT_OPTIONS = ['校服', '休闲装', 'JK制服', '洛丽塔', '运动装', '泳装', '正装', '魔法少女', '战斗服'];

// 默认外观参数
const DEFAULT_APPEARANCE_PARAMS = {
  race: '人类',
  hairStyle: '长直发',
  hairColor: '黑色',
  eyeColor: '蓝色',
  expression: '微笑',
  outfit: '校服',
  bodyType: 5,
};

interface CharacterFormData {
  name: string;
  identity: string;
  personality: string;
  age?: number;
  public: boolean;
  imageUrl?: string;
  appearanceParams: {
    race: string;
    hairStyle: string;
    hairColor: string;
    eyeColor: string;
    expression: string;
    outfit: string;
    bodyType: number;
    [key: string]: any;
  };
  systemPrompt?: string;
  settings?: Record<string, any>;
}

const CharacterEditPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [imageGenerating, setImageGenerating] = useState(false);
  const [characterData, setCharacterData] = useState<any>(null);
  const [previewImage, setPreviewImage] = useState<string>('');
  const [selectedPromptTemplate, setSelectedPromptTemplate] = useState<number | null>(null);
  const [promptTemplates, setPromptTemplates] = useState<any[]>([]);
  
  // 加载角色数据
  useEffect(() => {
    if (isEditMode && id) {
      fetchCharacterData(parseInt(id));
    }
    fetchPromptTemplates();
  }, [id]);
  
  // 获取提示词模板列表
  const fetchPromptTemplates = async () => {
    try {
      const response = await adminAPI.getPromptTemplates({ type: 'system' });
      setPromptTemplates(response.data?.results || []);
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      message.error('获取提示词模板失败');
    }
  };
  
  // 获取角色详情
  const fetchCharacterData = async (characterId: number) => {
    try {
      setLoading(true);
      const response = await adminAPI.getCharacterDetail(characterId);
      const characterData = response;
      setCharacterData(characterData);
      
      // 填充表单
      form.setFieldsValue({
        name: characterData.name,
        age: characterData.age,
        identity: characterData.identity,
        personality: characterData.personality,
        public: characterData.public,
        systemPrompt: characterData.settings?.systemPrompt || '',
        appearanceParams: characterData.appearance_params || DEFAULT_APPEARANCE_PARAMS,
        settings: characterData.settings || {},
      });
      
      setPreviewImage(characterData.image_url);
    } catch (error) {
      console.error('获取角色详情失败:', error);
      message.error('获取角色详情失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 保存角色
  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      const characterData: any = {
        name: values.name,
        identity: values.identity,
        personality: values.personality,
        age: values.age || 18,
        public: values.public,
        appearance_params: values.appearanceParams,
        settings: {
          ...values.settings,
          systemPrompt: values.systemPrompt,
        },
      };
      
      // 如果有图片URL
      if (previewImage) {
        characterData.image_url = previewImage;
      }
      
      if (isEditMode && id) {
        await adminAPI.updateCharacter(parseInt(id), characterData);
        message.success('角色更新成功');
      } else {
        await adminAPI.createCharacter(characterData);
        message.success('角色创建成功');
      }
      
      navigate('/admin/characters');
    } catch (error) {
      console.error('保存角色失败:', error);
      message.error('保存角色失败');
    } finally {
      setSubmitting(false);
    }
  };
  
  // 生成角色图片
  const generateCharacterImage = async () => {
    try {
      setImageGenerating(true);
      
      // 获取当前表单数据
      const values = form.getFieldsValue();
      
      // 构建提示词
      const promptParts = [
        `a anime-style character illustration of a ${values.appearanceParams.race} ${values.identity}`,
        `with ${values.appearanceParams.hairColor} ${values.appearanceParams.hairStyle}`,
        `${values.appearanceParams.eyeColor} eyes`,
        `wearing ${values.appearanceParams.outfit}`,
        `with ${values.appearanceParams.expression} expression`,
        `${values.personality} personality`,
        `high quality, detailed, beautiful composition`
      ];
      
      const prompt = promptParts.join(', ');
      
      // 调用API生成图片
      const response = await adminAPI.generateCharacterImage({ prompt });

      if (response?.image_url) {
        setPreviewImage(response.image_url);
        message.success('图片生成成功');
      } else {
        message.error('图片生成失败');
      }
    } catch (error) {
      console.error('生成图片失败:', error);
      message.error('生成图片失败');
    } finally {
      setImageGenerating(false);
    }
  };
  
  // 加载提示词模板
  const handleLoadTemplate = async () => {
    if (!selectedPromptTemplate) {
      message.warning('请先选择提示词模板');
      return;
    }
    
    try {
      const response = await adminAPI.getPromptTemplateDetail(selectedPromptTemplate);
      const templateData = response.data;
      
      if (templateData?.content) {
        form.setFieldsValue({
          systemPrompt: templateData.content
        });
        message.success('提示词模板加载成功');
      }
    } catch (error) {
      console.error('加载提示词模板失败:', error);
      message.error('加载提示词模板失败');
    }
  };
  
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }
  
  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={3}>{isEditMode ? '编辑角色' : '创建角色'}</Title>
        </Col>
        <Col>
          <Button 
            icon={<RollbackOutlined />} 
            onClick={() => navigate('/admin/characters')}
            style={{ marginRight: 8 }}
          >
            返回列表
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />} 
            onClick={() => form.submit()}
            loading={submitting}
          >
            保存
          </Button>
        </Col>
      </Row>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          name: '',
          age: 18,
          identity: '高中生',
          personality: '温柔',
          public: false,
          appearanceParams: DEFAULT_APPEARANCE_PARAMS,
          systemPrompt: '',
          settings: {},
        }}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1">
            <Card>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="角色名称"
                    rules={[{ required: true, message: '请输入角色名称' }]}
                  >
                    <Input placeholder="请输入角色名称" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="age"
                    label="年龄"
                  >
                    <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="public"
                    label="是否公开"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="公开" unCheckedChildren="私有" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="identity"
                    label="身份"
                    rules={[{ required: true, message: '请选择身份' }]}
                  >
                    <Select placeholder="请选择身份">
                      {IDENTITY_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="personality"
                    label="性格"
                    rules={[{ required: true, message: '请选择性格' }]}
                  >
                    <Select placeholder="请选择性格">
                      {PERSONALITY_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>角色图片</Text>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                    {previewImage ? (
                      <Image 
                        src={previewImage} 
                        alt="角色图片" 
                        style={{ width: 200, height: 200, objectFit: 'cover', borderRadius: 4 }} 
                      />
                    ) : (
                      <div style={{ 
                        width: 200, 
                        height: 200, 
                        background: '#f5f5f5', 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center',
                        borderRadius: 4
                      }}>
                        <Text type="secondary">暂无图片</Text>
                      </div>
                    )}
                    <div style={{ marginLeft: 16 }}>
                      <Space direction="vertical">
                        <Button 
                          type="primary" 
                          icon={<SyncOutlined />} 
                          onClick={generateCharacterImage}
                          loading={imageGenerating}
                        >
                          生成图片
                        </Button>
                        <Upload
                          accept="image/*"
                          showUploadList={false}
                          customRequest={({ file, onSuccess }) => {
                            // 此处可以实现自定义上传逻辑
                            // 例如，将文件上传到服务器，获取URL
                            // 然后设置previewImage
                            setTimeout(() => {
                              onSuccess?.("ok");
                            }, 0);
                          }}
                          onChange={(info) => {
                            if (info.file.status === 'done') {
                              // 获取上传文件的URL
                              const reader = new FileReader();
                              reader.onload = (e) => {
                                if (e.target?.result) {
                                  setPreviewImage(e.target.result as string);
                                }
                              };
                              reader.readAsDataURL(info.file.originFileObj as Blob);
                            }
                          }}
                        >
                          <Button icon={<UploadOutlined />}>上传图片</Button>
                        </Upload>
                      </Space>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          </TabPane>
          
          <TabPane tab="外观参数" key="2">
            <Card>
              <Form.Item
                name={['appearanceParams', 'race']}
                label="种族"
              >
                <Input placeholder="请输入种族" />
              </Form.Item>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'hairStyle']}
                    label="发型"
                  >
                    <Select placeholder="请选择发型">
                      {HAIR_STYLE_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'hairColor']}
                    label="发色"
                  >
                    <Select placeholder="请选择发色">
                      {HAIR_COLOR_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'eyeColor']}
                    label="瞳色"
                  >
                    <Select placeholder="请选择瞳色">
                      {EYE_COLOR_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'expression']}
                    label="表情"
                  >
                    <Select placeholder="请选择表情">
                      {EXPRESSION_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'outfit']}
                    label="服装"
                  >
                    <Select placeholder="请选择服装">
                      {OUTFIT_OPTIONS.map(option => (
                        <Option key={option} value={option}>{option}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name={['appearanceParams', 'bodyType']}
                    label="体型(1-10)"
                  >
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Divider orientation="left">自定义参数</Divider>
              
              <Form.List name={['appearanceParams', 'custom']}>
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Row key={key} gutter={16} style={{ marginBottom: 8 }}>
                        <Col span={8}>
                          <Form.Item
                            {...restField}
                            name={[name, 'name']}
                            rules={[{ required: true, message: '请输入参数名称' }]}
                          >
                            <Input placeholder="参数名称" />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[{ required: true, message: '请输入参数值' }]}
                          >
                            <Input placeholder="参数值" />
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <MinusCircleOutlined onClick={() => remove(name)} style={{ marginTop: 8 }} />
                        </Col>
                      </Row>
                    ))}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                        添加自定义参数
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Card>
          </TabPane>
          
          <TabPane tab="系统设定" key="3">
            <Card>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={16}>
                  <Select
                    placeholder="选择提示词模板"
                    style={{ width: '100%' }}
                    onChange={(value) => setSelectedPromptTemplate(value)}
                  >
                    {promptTemplates.map(template => (
                      <Option key={template.id} value={template.id}>{template.name}</Option>
                    ))}
                  </Select>
                </Col>
                <Col span={8}>
                  <Button onClick={handleLoadTemplate}>加载模板</Button>
                </Col>
              </Row>
              
              <Form.Item
                name="systemPrompt"
                label="系统提示词"
              >
                <TextArea rows={10} placeholder="请输入系统提示词" />
              </Form.Item>
              
              <Divider orientation="left">其他设定</Divider>
              
              <Form.List name={['settings', 'custom']}>
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Row key={key} gutter={16} style={{ marginBottom: 8 }}>
                        <Col span={8}>
                          <Form.Item
                            {...restField}
                            name={[name, 'name']}
                            rules={[{ required: true, message: '请输入设定名称' }]}
                          >
                            <Input placeholder="设定名称" />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[{ required: true, message: '请输入设定值' }]}
                          >
                            <Input placeholder="设定值" />
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <MinusCircleOutlined onClick={() => remove(name)} style={{ marginTop: 8 }} />
                        </Col>
                      </Row>
                    ))}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                        添加自定义设定
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Card>
          </TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default CharacterEditPage; 
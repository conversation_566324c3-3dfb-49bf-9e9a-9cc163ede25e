import axios from 'axios';
import type { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import useAuthStore from '../store/authStore';
import useAdminAuthStore from '../store/adminAuthStore';
import { errorService } from './errorService';

const baseURL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000/api';

// 创建axios实例
const api = axios.create({
  baseURL,
  timeout: 60000, // 增加到60秒，因为图片生成需要较长时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 不需要token的接口列表
    const noAuthUrls = [
      '/auth/register/',
      '/auth/login/',
      '/admin/login/',
      '/characters/generate/',  // 角色生成API不需要认证
      '/characters/public_list/',  // 公开角色列表不需要认证
      '/personalities/',  // 性格列表不需要认证
      '/identities/'  // 身份列表不需要认证
    ];
    const isNoAuthUrl = noAuthUrls.some(url => config.url?.includes(url));

    if (!isNoAuthUrl) {
      // 获取用户Token
      const token = useAuthStore.getState().userToken;
      // 获取管理员Token
      const adminToken = useAdminAuthStore.getState().adminToken;

      // 优先使用管理员Token（如果请求是管理员API）
      if (adminToken && config.url?.startsWith('/api/admin') && config.headers) {
        config.headers.Authorization = `Bearer ${adminToken}`;
      } else if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => {
    // 请求错误处理
    errorService.reportNetworkError(
      error, 
      error.config?.url || 'unknown-url',
      { phase: 'request', message: 'Request interceptor error' }
    );
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error: AxiosError) => {
    const { response, config } = error;
    const requestUrl = config?.url || 'unknown-url';

    // 上报网络错误（但不包括401等业务错误）
    if (!response || (response.status !== 401 && response.status !== 403)) {
      errorService.reportNetworkError(
        error,
        requestUrl,
        {
          status: response?.status,
          statusText: response?.statusText,
          data: response?.data,
          method: config?.method
        }
      );
    }

    // 处理未授权错误 (401)
    if (response && response.status === 401) {
      // 检查是否是管理员API请求
      const isAdminRequest = config?.url?.startsWith('/api/admin');

      if (isAdminRequest) {
        useAdminAuthStore.getState().logoutAdmin();
        // 延迟跳转，避免在错误处理中立即跳转
        setTimeout(() => {
          window.location.href = '/admin/login';
        }, 100);
      } else {
        useAuthStore.getState().logout();
        // 延迟跳转，避免在错误处理中立即跳转
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      }
    }

    // 处理网络错误
    if (!response) {
      console.error('Network Error:', error.message);
      // 可以在这里添加网络错误的用户提示
    } else {
      // 统一处理错误消息
      const errorData = response.data as { message?: string } | undefined;
      const errorMessage = errorData?.message || '请求失败，请稍后再试';

      // 只在开发环境输出详细错误信息
      if (import.meta.env.DEV) {
        console.error('API Error:', {
          url: requestUrl,
          status: response.status,
          message: errorMessage,
          data: response.data
        });
      }
    }

    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  register: (userData: { username: string; email: string; password: string; password_confirm: string }) =>
    api.post('/auth/register/', userData),

  login: (credentials: { username: string; password: string }) =>
    api.post('/auth/login/', credentials),

  logout: () => api.post('/auth/logout/'),
};

export default api;
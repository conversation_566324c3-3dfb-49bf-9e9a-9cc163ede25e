import React, { useEffect, useState } from 'react';
import { notification } from 'antd';
import { ExclamationCircleOutlined, WifiOutlined } from '@ant-design/icons';

interface ErrorNotification {
  id: string;
  type: 'network' | 'javascript' | 'general';
  message: string;
  timestamp: number;
}

/**
 * 全局错误处理组件
 * 监听全局错误并显示用户友好的提示
 */
const GlobalErrorHandler: React.FC = () => {
  const [, setIsOnline] = useState(navigator.onLine);
  const [, setErrorHistory] = useState<ErrorNotification[]>([]);

  useEffect(() => {
    // 监听网络状态变化
    const handleOnline = () => {
      setIsOnline(true);
      notification.success({
        message: '网络已连接',
        description: '网络连接已恢复，您可以继续使用应用。',
        icon: <WifiOutlined style={{ color: '#52c41a' }} />,
        duration: 3,
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      notification.warning({
        message: '网络连接断开',
        description: '请检查您的网络连接，某些功能可能无法正常使用。',
        icon: <WifiOutlined style={{ color: '#faad14' }} />,
        duration: 0, // 不自动关闭
        key: 'offline-notification',
      });
    };

    // 监听未处理的错误
    const handleError = (event: ErrorEvent) => {
      // 过滤掉一些不重要的错误
      if (shouldIgnoreError(event.message, event.filename)) {
        return;
      }

      const errorId = Date.now().toString();
      const errorNotification: ErrorNotification = {
        id: errorId,
        type: 'javascript',
        message: event.message,
        timestamp: Date.now(),
      };

      setErrorHistory(prev => [...prev.slice(-4), errorNotification]); // 只保留最近5个错误

      // 显示用户友好的错误提示
      notification.error({
        message: '页面出现错误',
        description: '页面运行时出现了一些问题，如果问题持续存在，请尝试刷新页面。',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 5,
        key: `error-${errorId}`,
      });
    };

    // 监听未处理的Promise错误
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const message = event.reason?.message || String(event.reason) || 'Promise rejection';
      
      if (shouldIgnoreError(message)) {
        return;
      }

      const errorId = Date.now().toString();
      const errorNotification: ErrorNotification = {
        id: errorId,
        type: 'general',
        message,
        timestamp: Date.now(),
      };

      setErrorHistory(prev => [...prev.slice(-4), errorNotification]);

      // 对于Promise错误，显示更温和的提示
      notification.warning({
        message: '操作可能未完成',
        description: '某个操作可能没有正确完成，请检查结果或重试。',
        duration: 4,
        key: `promise-error-${errorId}`,
      });
    };

    // 添加事件监听器
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      // 清理事件监听器
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return null; // 这个组件不渲染任何UI
};

/**
 * 判断是否应该忽略某些错误
 */
function shouldIgnoreError(message?: string, source?: string): boolean {
  if (!message) return false;

  const ignorePatterns = [
    // 浏览器扩展相关错误
    /extension\//i,
    /chrome-extension/i,
    /moz-extension/i,
    
    // 第三方脚本错误
    /Script error/i,
    
    // 网络相关的非关键错误
    /Loading chunk \d+ failed/i,
    /ChunkLoadError/i,
    
    // 开发工具相关错误
    /triangle/i,
    /devtools/i,
    
    // 广告拦截器相关错误
    /adblock/i,
    /ublock/i,
    
    // 其他常见的非关键错误
    /Non-Error promise rejection captured/i,
    /ResizeObserver loop limit exceeded/i,
  ];

  const shouldIgnoreMessage = ignorePatterns.some(pattern => pattern.test(message));
  
  const shouldIgnoreSource = source && (
    source.includes('chrome-extension') ||
    source.includes('moz-extension') ||
    source.includes('extension') ||
    !source.includes(window.location.origin)
  );

  return shouldIgnoreMessage || Boolean(shouldIgnoreSource);
}

export default GlobalErrorHandler;

/* Lobe Vidol 3D聊天组件样式 */

.vidol-chat-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  overflow: hidden;
}

.vidol-3d-canvas {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 8px;
}

.vidol-controls-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 16px;
  color: white;
}

.vidol-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.vidol-loading-spinner {
  margin-bottom: 12px;
}

.vidol-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 16px;
}

/* 3D模式下的聊天页面调整 */
.standalone-chat-container.with-3d {
  padding-right: 320px; /* 为3D组件留出空间 */
}

.vidol-floating-panel {
  position: fixed;
  right: 20px;
  top: 80px;
  width: 300px;
  height: 400px;
  z-index: 1000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
}

/* 桌面端优化 */
@media (max-width: 1400px) {
  .vidol-floating-panel {
    width: 280px;
    height: 380px;
  }

  .standalone-chat-container.with-3d {
    padding-right: 300px;
  }
}

@media (max-width: 1200px) {
  .vidol-floating-panel {
    width: 250px;
    height: 350px;
  }

  .standalone-chat-container.with-3d {
    padding-right: 270px;
  }
}

/* 3D控制按钮样式 */
.vidol-control-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.vidol-control-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.vidol-control-button.active {
  background: rgba(24, 144, 255, 0.8);
  border-color: #1890ff;
}

/* 动画效果 */
.vidol-fade-in {
  animation: vidolFadeIn 0.3s ease-in-out;
}

.vidol-fade-out {
  animation: vidolFadeOut 0.3s ease-in-out;
}

@keyframes vidolFadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes vidolFadeOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

/* 加载动画 */
.vidol-loading-dots {
  display: inline-block;
}

.vidol-loading-dots::after {
  content: '';
  animation: vidolLoadingDots 1.5s infinite;
}

@keyframes vidolLoadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* 3D模式指示器 */
.vidol-mode-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

/* 性能优化：减少重绘 */
.vidol-chat-container * {
  will-change: transform;
}

.vidol-3d-canvas {
  transform: translateZ(0);
}

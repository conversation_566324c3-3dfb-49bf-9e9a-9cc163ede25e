{"azure": {"azureApiVersion": {"desc": "Azure 的 API 版本，遵循 YYYY-MM-DD 格式，查閱[最新版本](https://learn.microsoft.com/zh-tw/azure/ai-services/openai/reference#chat-completions)", "fetch": "獲取列表", "title": "Azure API 版本"}, "empty": "請輸入模型 ID 以添加第一個模型", "endpoint": {"desc": "從 Azure 入口網站檢查資源時，可在「金鑰和端點」部分中找到此值", "placeholder": "https://docs-test-001.openai.azure.com", "title": "Azure API 地址"}, "modelListPlaceholder": "請選擇或添加你部署的 OpenAI 模型", "title": "Azure OpenAI", "token": {"desc": "從 Azure 入口網站檢查資源時，可在「金鑰和端點」部分中找到此值。可以使用 KEY1 或 KEY2", "placeholder": "Azure API 金鑰", "title": "API 金鑰"}}, "bedrock": {"accessKeyId": {"desc": "填入 AWS Access Key Id", "placeholder": "AWS Access Key Id", "title": "AWS Access Key Id"}, "checker": {"desc": "測試 AccessKeyId / SecretAccessKey 是否填寫正確"}, "region": {"desc": "填入 AWS 區域", "placeholder": "AWS 區域", "title": "AWS 區域"}, "secretAccessKey": {"desc": "填入 AWS Secret Access Key", "placeholder": "AWS Secret Access Key", "title": "AWS Secret Access Key"}, "sessionToken": {"desc": "如果你正在使用 AWS SSO/STS，請輸入你的 AWS Session Token", "placeholder": "AWS Session Token", "title": "AWS Session Token (可選)"}, "title": "Bedrock", "unlock": {"customRegion": "自定義服務區域", "customSessionToken": "自定義 Session Token", "description": "輸入你的 AWS AccessKeyId / SecretAccessKey 即可開始會話。應用不會記錄你的鑑權配置", "title": "使用自定義 Bedrock 鑑權信息"}}, "github": {"personalAccessToken": {"desc": "填入你的 Github PAT，點擊 [這裡](https://github.com/settings/tokens) 創建", "placeholder": "ghp_xxxxxx", "title": "Github PAT"}}, "huggingface": {"accessToken": {"desc": "填入你的 Hu<PERSON><PERSON><PERSON>，點擊 [這裡](https://huggingface.co/settings/tokens) 創建", "placeholder": "hf_xxxxxxxxx", "title": "Hu<PERSON><PERSON><PERSON>"}}, "ollama": {"checker": {"desc": "測試代理地址是否正確填寫", "title": "連通性檢查"}, "customModelName": {"desc": "增加自定義模型，多個模型使用逗號（,）隔開", "placeholder": "vicuna,llava,codellama,llama2:13b-text", "title": "自定義模型名稱"}, "download": {"desc": "<PERSON><PERSON><PERSON> 正在下載該模型，請盡量不要關閉本頁面。重新下載時將會中斷處繼續", "remainingTime": "剩餘時間", "speed": "下載速度", "title": "正在下載模型 {{model}} "}, "endpoint": {"desc": "填入 Ollama 接口代理地址，本地未額外指定可留空", "title": "Ollama 服務地址"}, "setup": {"cors": {"description": "因瀏覽器安全限制，你需要為 Ollama 進行跨域配置後方可正常使用。", "linux": {"env": "在 [Service] 部分下添加 `Environment`，添加 OLLAMA_ORIGINS 環境變量：", "reboot": "重載 systemd 並重啟 Ollama", "systemd": "調用 systemd 編輯 ollama 服務："}, "macos": "請打開「終端」應用程序，並粘貼以下指令，並按回車運行", "reboot": "請在執行完成後重啟 Ollama 服務", "title": "配置 Ollama 允許跨域訪問", "windows": "在 Windows 上，點擊「控制面板」，進入編輯系統環境變量。為您的用戶賬戶新建名為「OLLAMA_ORIGINS」的環境變量，值為 *，點擊「確定/應用」保存"}, "install": {"description": "請確認你已經開啟 Ollama，如果沒有下載 Ollama，請前往官網<1>下載</1>", "docker": "如果你更傾向於使用 Docker，Ollama 也提供了官方 Docker 鏡像，你可以通過以下命令拉取：", "linux": {"command": "通過以下命令安裝：", "manual": "或者，你也可以參考 <1>Linux 手動安裝指南</1> 自行安裝"}, "title": "在本地安裝並開啟 Ollama 應用", "windowsTab": "Windows (預覽版)"}}, "title": "Ollama", "unlock": {"cancel": "取消下載", "confirm": "下載", "description": "輸入你的 Ollama 模型標籤，完成即可繼續會話", "downloaded": "{{completed}} / {{total}}", "starting": "開始下載...", "title": "下載指定的 Ollama 模型"}}, "sensenova": {"sensenovaAccessKeyID": {"desc": "填入 SenseNova Access Key ID", "placeholder": "SenseNova Access Key ID", "title": "Access Key ID"}, "sensenovaAccessKeySecret": {"desc": "填入 SenseNova Access Key Secret", "placeholder": "SenseNova Access Key Secret", "title": "Access Key Secret"}, "unlock": {"description": "輸入你的 Access Key ID / Access Key Secret 即可開始會話。應用不會記錄你的鑑權配置", "title": "使用自定義 SenseNova 鑑權信息"}}, "wenxin": {"accessKey": {"desc": "填入百度千帆平台的 Access Key", "placeholder": "千帆 Access Key", "title": "Access Key"}, "checker": {"desc": "測試 AccessKey / Secret Access 是否填寫正確"}, "secretKey": {"desc": "填入百度千帆平台 Secret Key", "placeholder": "千帆 Secret Key", "title": "Secret Key"}, "unlock": {"customRegion": "自定義服務區域", "description": "輸入你的 AccessKey / SecretKey 即可開始會話。應用不會記錄你的鑑權配置", "title": "使用自定義文心一言鑑權信息"}}, "zeroone": {"title": "01.AI 零一萬物"}, "zhipu": {"title": "智譜"}}
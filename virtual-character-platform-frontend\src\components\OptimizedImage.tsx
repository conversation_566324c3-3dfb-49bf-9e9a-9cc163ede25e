import React, { useState, useRef, useEffect } from 'react';
import { Spin } from 'antd';
import '../styles/optimized-image.css';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  placeholderColor?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * 优化的图片组件
 * 支持懒加载、渐进式加载和加载状态显示
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholderColor = '#f5f5f5',
  objectFit = 'cover',
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  // 使用IntersectionObserver实现懒加载
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 } // 当图片有10%进入视口时加载
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => {
      observer.disconnect();
    };
  }, []);
  
  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };
  
  // 处理图片加载失败
  const handleError = () => {
    setIsError(true);
    if (onError) onError();
  };
  
  // 图片容器样式
  const containerStyle: React.CSSProperties = {
    width: width || '100%',
    height: height || 'auto',
    backgroundColor: placeholderColor,
    position: 'relative',
    overflow: 'hidden'
  };
  
  // 图片样式
  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit,
    opacity: isLoaded ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out'
  };

  return (
    <div style={containerStyle} className={`optimized-image-container ${className}`}>
      {/* 加载状态 */}
      {!isLoaded && !isError && (
        <div className="image-loading">
          <Spin size="small" />
        </div>
      )}
      
      {/* 错误状态 */}
      {isError && (
        <div className="image-error">
          <span>加载失败</span>
        </div>
      )}
      
      {/* 图片元素 */}
      <img
        ref={imgRef}
        src={isInView ? src : ''}
        alt={alt}
        style={imageStyle}
        onLoad={handleLoad}
        onError={handleError}
        loading="lazy" // 原生懒加载属性（现代浏览器支持）
      />
    </div>
  );
};

export default OptimizedImage; 
import React from 'react';
import { Button, Avatar, Typography } from 'antd';
import { 
  MenuOutlined, 
  InfoCircleOutlined,
  VideoCameraOutlined 
} from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';
import { sessionSelectors, useSessionStore } from '../../../store/session';
import { useGlobalStore } from '../../../store/global';

const { Text } = Typography;

interface ChatHeaderProps {
  onToggleSidebar: () => void;
  onToggleChatInfo: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  onToggleSidebar,
  onToggleChatInfo
}) => {
  const currentAgent = useSessionStore((s) => sessionSelectors.currentAgent(s));
  const { setChatMode, setVoiceOn } = useGlobalStore();

  const switchToCameraMode = () => {
    setChatMode('camera');
    setVoiceOn(true);
  };

  return (
    <div className="chat-header">
      <Flexbox horizontal align="center" justify="space-between" style={{ padding: '12px 16px' }}>
        <Flexbox horizontal align="center" gap={12}>
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={onToggleSidebar}
          />
          
          <Avatar 
            src={currentAgent?.meta?.avatar} 
            size={32}
          >
            {currentAgent?.meta?.name?.[0]}
          </Avatar>
          
          <div>
            <Text strong>{currentAgent?.meta?.name || '角色'}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              在线
            </Text>
          </div>
        </Flexbox>

        <Flexbox horizontal align="center" gap={8}>
          <Button
            type="text"
            icon={<VideoCameraOutlined />}
            onClick={switchToCameraMode}
            title="切换到摄像头模式"
          />
          
          <Button
            type="text"
            icon={<InfoCircleOutlined />}
            onClick={onToggleChatInfo}
            title="角色信息"
          />
        </Flexbox>
      </Flexbox>
    </div>
  );
};

export default ChatHeader;

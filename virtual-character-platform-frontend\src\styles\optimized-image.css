.optimized-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.image-loading,
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  z-index: 1;
}

.image-error {
  color: #ff4d4f;
  font-size: 14px;
}

.image-error span {
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

/* 图片渐入效果 */
.optimized-image-container img {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.optimized-image-container img:not([src]) {
  opacity: 0;
}

/* 悬停效果 */
.optimized-image-container:hover img {
  transform: scale(1.05);
} 
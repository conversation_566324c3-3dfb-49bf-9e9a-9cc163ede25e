import React from 'react';
import { Layout } from 'antd';
import Sidebar from './Sidebar';
import { useLocation } from 'react-router-dom';

const { Content } = Layout;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  console.log('🏗️ MainLayout rendering...');

  const location = useLocation();
  console.log('📍 MainLayout location:', location.pathname);

  // 登录页面和管理员页面不显示主布局
  if (location.pathname === '/login' || location.pathname.startsWith('/admin')) {
    console.log('🚫 Bypassing MainLayout for:', location.pathname);
    return <>{children}</>;
  }

  console.log('✅ Rendering MainLayout with Sidebar');

  return (
    <Layout
      style={{
        minHeight: '100vh',
        background: 'var(--gradient-primary)',
      }}
    >
      {/* 左侧固定侧边栏 */}
      <Sidebar />

      {/* 主内容区域 */}
      <Layout
        style={{
          marginLeft: 240, // 侧边栏宽度
          background: 'transparent',
        }}
      >
        <Content
          style={{
            padding: 0,
            background: 'transparent',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center', // 让内容居中
          }}
        >
          {/* 内容容器 */}
          <div
            style={{
              flex: 1,
              padding: 'var(--spacing-lg)',
              width: '100%',
              maxWidth: '1400px', // 限制最大宽度
              background: 'transparent',
              minHeight: '100vh',
            }}
          >
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
import React, { useEffect, useRef } from 'react';
import { Empty, Spin } from 'antd';
import { Flexbox } from 'react-layout-kit';
import { isEqual } from 'lodash-es';

import ChatItem from '../../../features/ChatItem';
import { sessionSelectors, useSessionStore } from '../../../store/session';
import WelcomeMessage from './WelcomeMessage';

const ChatList: React.FC = () => {
  // 获取聊天数据
  const [currentChats, chatLoading] = useSessionStore(
    (s) => [sessionSelectors.currentChats(s), !!s.chatLoadingId],
    isEqual,
  );

  const scrollRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [currentChats]);

  return (
    <Flexbox
      flex={1}
      style={{
        overflowX: 'hidden',
        overflowY: 'auto',
        position: 'relative',
        padding: '16px',
      }}
      width="100%"
      ref={scrollRef}
      className="chat-list-container"
    >
      {currentChats.length === 0 ? (
        <WelcomeMessage />
      ) : (
        <div className="chat-messages">
          {currentChats.map((chat) => (
            <div key={chat.id} className="chat-message-item">
              <ChatItem
                id={chat.id}
                showTitle={true}
                type="pure"
              />
            </div>
          ))}
          
          {/* 加载指示器 */}
          {chatLoading && (
            <div className="chat-loading">
              <Spin size="small" />
              <span style={{ marginLeft: 8 }}>AI正在思考...</span>
            </div>
          )}
        </div>
      )}
    </Flexbox>
  );
};

export default ChatList;

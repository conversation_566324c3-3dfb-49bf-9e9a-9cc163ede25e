import type { LipSyncAnalyzeResult } from './lipSyncAnalyzeResult.ts';

const TIME_DOMAIN_DATA_LENGTH = 2048;

export class LipSync {
  public readonly audio: AudioContext;
  public readonly analyser: AnalyserNode;
  public readonly timeDomainData: Float32Array;
  public bufferSource: AudioBufferSourceNode | undefined;

  public constructor(audio: AudioContext) {
    this.audio = audio;
    this.bufferSource = undefined;

    this.analyser = audio.createAnalyser();
    this.timeDomainData = new Float32Array(TIME_DOMAIN_DATA_LENGTH);
  }

  public update(): LipSyncAnalyzeResult {
    this.analyser.getFloatTimeDomainData(this.timeDomainData);

    let volume = 0;
    for (let i = 0; i < TIME_DOMAIN_DATA_LENGTH; i++) {
      volume = Math.max(volume, Math.abs(this.timeDomainData[i]));
    }

    // cook - 使用sigmoid函数平滑音量变化
    volume = 1 / (1 + Math.exp(-45 * volume + 5));
    if (volume < 0.1) volume = 0;

    return {
      volume,
    };
  }

  public async playFromArrayBuffer(buffer: ArrayBuffer, onEnded?: () => void) {
    try {
      const audioBuffer = await this.audio.decodeAudioData(buffer);

      this.bufferSource = this.audio.createBufferSource();
      this.bufferSource.buffer = audioBuffer;

      this.bufferSource.connect(this.audio.destination);
      this.bufferSource.connect(this.analyser);
      this.bufferSource.start();
      
      if (onEnded) {
        this.bufferSource.addEventListener('ended', onEnded);
      }
    } catch (error) {
      console.error('LipSync: 播放音频失败:', error);
      if (onEnded) {
        onEnded();
      }
    }
  }

  public stopPlay() {
    if (this.bufferSource) {
      try {
        this.bufferSource.stop();
      } catch (error) {
        console.warn('LipSync: 停止播放时出错:', error);
      }
      this.bufferSource = undefined;
    }
  }

  public async playFromURL(url: string, onEnded?: () => void) {
    try {
      console.log('LipSync: 开始从URL播放音频:', url);
      const res = await fetch(url);
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      const buffer = await res.arrayBuffer();
      await this.playFromArrayBuffer(buffer, onEnded);
    } catch (error) {
      console.error('LipSync: 从URL播放音频失败:', error);
      if (onEnded) {
        onEnded();
      }
    }
  }

  // 检查是否正在播放
  public isPlaying(): boolean {
    return !!this.bufferSource;
  }

  // 获取音频上下文状态
  public getAudioContextState(): AudioContextState {
    return this.audio.state;
  }

  // 恢复音频上下文（用户交互后）
  public async resumeAudioContext(): Promise<void> {
    if (this.audio.state === 'suspended') {
      await this.audio.resume();
      console.log('LipSync: 音频上下文已恢复');
    }
  }
}

/**
 * 动作文件类型
 */
export enum MotionFileType {
  VRM = 'vrm',
  VMD = 'vmd',
  FBX = 'fbx',
  GLTF = 'gltf',
  VRMA = 'vrma'
}

/**
 * 动作预设名称类型
 */
export type MotionPresetName = string;

/**
 * 动作预设配置接口
 */
export interface MotionPresetConfig {
  url?: string;
  type: MotionFileType;
  name: string;
  description: string;
  duration?: number; // 动作持续时间（秒）
  loop?: boolean; // 是否循环播放
  weight?: number; // 动作权重 (0-1)
}

/**
 * 扩展的动作预设名称
 */
export enum ExtendedMotionPresetName {
  // 基础动作
  Idle = 'idle',
  Talking = 'talking',

  // 情感动作
  Happy = 'happy',
  Sad = 'sad',
  Surprised = 'surprised',
  Thinking = 'thinking',
  Angry = 'angry',
  Excited = 'excited',

  // 社交动作
  Greeting = 'greeting',
  Waving = 'waving',
  Nodding = 'nodding',
  Shaking = 'shaking',

  // 手势动作
  Pointing = 'pointing',
  Clapping = 'clapping',
  Thumbsup = 'thumbsup',

  // 姿态动作
  Standing = 'standing',
  Sitting = 'sitting',
  Relaxed = 'relaxed',
  Confident = 'confident',

  // 兼容现有触摸系统的动作预设
  // 女性角色动作
  FemaleHappy = 'FemaleHappy',
  FemaleAngry = 'FemaleAngry',
  FemaleGreeting = 'FemaleGreeting',
  FemaleCoverChest = 'FemaleCoverChest',
  FemaleCoverUndies = 'FemaleCoverUndies',

  // 男性角色动作
  MaleHappy = 'MaleHappy',
  MaleAngry = 'MaleAngry'
}

/**
 * 动作预设映射表
 * 注意：由于我们使用的是简化版本，大部分动作通过程序化生成
 * 而不是加载外部动画文件
 */
export const motionPresetMap: Record<string, MotionPresetConfig> = {
  // 基础动作
  idle: {
    type: MotionFileType.VRM,
    name: '待机',
    description: '自然的待机姿态',
    duration: 0, // 持续播放
    loop: true,
    weight: 1.0
  },
  
  talking: {
    type: MotionFileType.VRM,
    name: '说话',
    description: '说话时的自然姿态',
    duration: 0,
    loop: true,
    weight: 0.8
  },
  
  // 情感动作
  happy: {
    type: MotionFileType.VRM,
    name: '开心',
    description: '开心愉悦的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },
  
  sad: {
    type: MotionFileType.VRM,
    name: '悲伤',
    description: '悲伤沮丧的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },
  
  surprised: {
    type: MotionFileType.VRM,
    name: '惊讶',
    description: '惊讶意外的姿态',
    duration: 2,
    loop: false,
    weight: 1.0
  },
  
  thinking: {
    type: MotionFileType.VRM,
    name: '思考',
    description: '思考沉思的姿态',
    duration: 5,
    loop: true,
    weight: 0.7
  },
  
  angry: {
    type: MotionFileType.VRM,
    name: '生气',
    description: '生气愤怒的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },
  
  excited: {
    type: MotionFileType.VRM,
    name: '兴奋',
    description: '兴奋激动的姿态',
    duration: 4,
    loop: false,
    weight: 1.0
  },
  
  // 社交动作
  greeting: {
    type: MotionFileType.VRM,
    name: '问候',
    description: '友好的问候姿态',
    duration: 2,
    loop: false,
    weight: 1.0
  },
  
  waving: {
    type: MotionFileType.VRM,
    name: '挥手',
    description: '挥手告别或问候',
    duration: 3,
    loop: false,
    weight: 1.0
  },
  
  nodding: {
    type: MotionFileType.VRM,
    name: '点头',
    description: '表示同意的点头',
    duration: 1,
    loop: false,
    weight: 0.8
  },
  
  shaking: {
    type: MotionFileType.VRM,
    name: '摇头',
    description: '表示否定的摇头',
    duration: 1,
    loop: false,
    weight: 0.8
  },
  
  // 手势动作
  pointing: {
    type: MotionFileType.VRM,
    name: '指向',
    description: '指向某个方向',
    duration: 2,
    loop: false,
    weight: 0.9
  },
  
  clapping: {
    type: MotionFileType.VRM,
    name: '鼓掌',
    description: '鼓掌庆祝',
    duration: 3,
    loop: true,
    weight: 1.0
  },
  
  thumbsup: {
    type: MotionFileType.VRM,
    name: '点赞',
    description: '竖起大拇指点赞',
    duration: 2,
    loop: false,
    weight: 1.0
  },
  
  // 姿态动作
  standing: {
    type: MotionFileType.VRM,
    name: '站立',
    description: '标准的站立姿态',
    duration: 0,
    loop: true,
    weight: 1.0
  },
  
  sitting: {
    type: MotionFileType.VRM,
    name: '坐着',
    description: '坐着的姿态',
    duration: 0,
    loop: true,
    weight: 1.0
  },
  
  relaxed: {
    type: MotionFileType.VRM,
    name: '放松',
    description: '放松休闲的姿态',
    duration: 0,
    loop: true,
    weight: 0.8
  },
  
  confident: {
    type: MotionFileType.VRM,
    name: '自信',
    description: '自信满满的姿态',
    duration: 0,
    loop: true,
    weight: 1.0
  },

  // 兼容现有触摸系统的动作预设
  // 女性角色动作
  FemaleHappy: {
    type: MotionFileType.VRM,
    name: '女性开心',
    description: '女性角色开心的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },

  FemaleAngry: {
    type: MotionFileType.VRM,
    name: '女性生气',
    description: '女性角色生气的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },

  FemaleGreeting: {
    type: MotionFileType.VRM,
    name: '女性问候',
    description: '女性角色问候的姿态',
    duration: 2,
    loop: false,
    weight: 1.0
  },

  FemaleCoverChest: {
    type: MotionFileType.VRM,
    name: '女性遮胸',
    description: '女性角色遮胸的保护姿态',
    duration: 2,
    loop: false,
    weight: 1.0
  },

  FemaleCoverUndies: {
    type: MotionFileType.VRM,
    name: '女性遮挡',
    description: '女性角色遮挡私密部位的姿态',
    duration: 2,
    loop: false,
    weight: 1.0
  },

  // 男性角色动作
  MaleHappy: {
    type: MotionFileType.VRM,
    name: '男性开心',
    description: '男性角色开心的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  },

  MaleAngry: {
    type: MotionFileType.VRM,
    name: '男性生气',
    description: '男性角色生气的姿态',
    duration: 3,
    loop: false,
    weight: 1.0
  }
};

/**
 * 根据情感类型获取对应的动作预设
 */
export function getMotionByEmotion(emotion: string): string {
  const emotionMotionMap: Record<string, string> = {
    'happy': 'happy',
    'joy': 'happy',
    'excited': 'excited',
    'sad': 'sad',
    'disappointed': 'sad',
    'angry': 'angry',
    'frustrated': 'angry',
    'surprised': 'surprised',
    'amazed': 'surprised',
    'thinking': 'thinking',
    'confused': 'thinking',
    'neutral': 'idle',
    'calm': 'relaxed',
    'confident': 'confident',
    'greeting': 'greeting',
    'caring': 'relaxed',
    'listening': 'thinking'
  };
  
  return emotionMotionMap[emotion.toLowerCase()] || 'idle';
}

/**
 * 获取动作预设配置
 */
export function getMotionPreset(motionName: string): MotionPresetConfig | null {
  return motionPresetMap[motionName] || null;
}

/**
 * 获取所有可用的动作预设名称
 */
export function getAllMotionPresets(): string[] {
  return Object.keys(motionPresetMap);
}

/**
 * 根据动作类型筛选动作预设
 */
export function getMotionsByType(type: 'basic' | 'emotion' | 'social' | 'gesture' | 'posture' | 'female' | 'male'): string[] {
  const typeMap: Record<string, string[]> = {
    basic: ['idle', 'talking'],
    emotion: ['happy', 'sad', 'surprised', 'thinking', 'angry', 'excited'],
    social: ['greeting', 'waving', 'nodding', 'shaking'],
    gesture: ['pointing', 'clapping', 'thumbsup'],
    posture: ['standing', 'sitting', 'relaxed', 'confident'],
    female: ['FemaleHappy', 'FemaleAngry', 'FemaleGreeting', 'FemaleCoverChest', 'FemaleCoverUndies'],
    male: ['MaleHappy', 'MaleAngry']
  };

  return typeMap[type] || [];
}
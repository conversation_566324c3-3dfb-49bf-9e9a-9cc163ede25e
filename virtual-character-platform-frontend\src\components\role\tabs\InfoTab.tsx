import React, { useState } from 'react';
import { Form, Input, Upload, Button, Tag, Space, message } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../../store/agent';
import AvatarUpload from '../components/AvatarUpload';

const { TextArea } = Input;

const InfoTab: React.FC = () => {
  const [form] = Form.useForm();
  const [newTag, setNewTag] = useState('');
  
  // 获取当前角色数据
  const [currentAgent, updateAgentMeta] = useAgentStore((s) => [
    agentSelectors.currentAgentItem(s),
    s.updateAgentMeta,
  ]);

  // 处理表单变更
  const handleFormChange = (changedFields: any, allFields: any) => {
    const updates: any = {};
    
    changedFields.forEach((field: any) => {
      const { name, value } = field;
      if (name.length === 1) {
        updates[name[0]] = value;
      }
    });
    
    if (Object.keys(updates).length > 0) {
      updateAgentMeta(updates);
    }
  };

  // 添加标签
  const handleAddTag = () => {
    if (!newTag.trim()) return;
    
    const currentTags = currentAgent?.meta.tags || [];
    if (currentTags.includes(newTag.trim())) {
      message.warning('标签已存在');
      return;
    }
    
    const newTags = [...currentTags, newTag.trim()];
    updateAgentMeta({ tags: newTags });
    setNewTag('');
  };

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = currentAgent?.meta.tags || [];
    const newTags = currentTags.filter(tag => tag !== tagToRemove);
    updateAgentMeta({ tags: newTags });
  };

  // 处理头像上传
  const handleAvatarChange = (avatarUrl: string) => {
    updateAgentMeta({ avatar: avatarUrl });
  };

  return (
    <div className="info-tab">
      <Flexbox gap={24} style={{ padding: '24px' }}>
        {/* 头像上传 */}
        <div className="info-section">
          <h3>角色头像</h3>
          <AvatarUpload
            value={currentAgent?.meta.avatar}
            onChange={handleAvatarChange}
          />
        </div>

        {/* 基本信息表单 */}
        <div className="info-section">
          <h3>基本信息</h3>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: currentAgent?.meta.name,
              description: currentAgent?.meta.description,
              readme: currentAgent?.meta.readme,
            }}
            onFieldsChange={handleFormChange}
          >
            <Form.Item
              label="角色名称"
              name="name"
              rules={[
                { required: true, message: '请输入角色名称' },
                { max: 50, message: '角色名称不能超过50个字符' }
              ]}
            >
              <Input 
                placeholder="输入角色名称"
                showCount
                maxLength={50}
              />
            </Form.Item>

            <Form.Item
              label="角色描述"
              name="description"
              rules={[
                { max: 200, message: '角色描述不能超过200个字符' }
              ]}
            >
              <TextArea
                placeholder="简短描述角色的特点和背景"
                rows={3}
                showCount
                maxLength={200}
              />
            </Form.Item>

            <Form.Item
              label="详细介绍"
              name="readme"
              rules={[
                { max: 1000, message: '详细介绍不能超过1000个字符' }
              ]}
            >
              <TextArea
                placeholder="详细介绍角色的背景故事、性格特征、能力等"
                rows={6}
                showCount
                maxLength={1000}
              />
            </Form.Item>
          </Form>
        </div>

        {/* 标签管理 */}
        <div className="info-section">
          <h3>角色标签</h3>
          <div className="tag-input">
            <Space.Compact style={{ display: 'flex' }}>
              <Input
                placeholder="添加标签"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onPressEnter={handleAddTag}
                maxLength={20}
              />
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddTag}
              >
                添加
              </Button>
            </Space.Compact>
          </div>
          
          <div className="tag-list" style={{ marginTop: 12 }}>
            {(currentAgent?.meta.tags || []).map((tag, index) => (
              <Tag
                key={index}
                closable
                onClose={() => handleRemoveTag(tag)}
                style={{ marginBottom: 8 }}
              >
                {tag}
              </Tag>
            ))}
          </div>
          
          {(!currentAgent?.meta.tags || currentAgent.meta.tags.length === 0) && (
            <div className="empty-tags">
              <p style={{ color: '#999', fontStyle: 'italic' }}>
                暂无标签，添加标签有助于角色分类和搜索
              </p>
            </div>
          )}
        </div>

        {/* 使用提示 */}
        <div className="info-section">
          <h3>配置提示</h3>
          <div className="tips">
            <ul>
              <li>角色名称将显示在聊天界面和角色列表中</li>
              <li>角色描述用于快速了解角色特点，建议简洁明了</li>
              <li>详细介绍可以包含角色的背景故事、性格特征等</li>
              <li>标签有助于角色分类和搜索，建议添加相关标签</li>
              <li>头像建议使用清晰的正方形图片，支持JPG、PNG格式</li>
            </ul>
          </div>
        </div>
      </Flexbox>
    </div>
  );
};

export default InfoTab;

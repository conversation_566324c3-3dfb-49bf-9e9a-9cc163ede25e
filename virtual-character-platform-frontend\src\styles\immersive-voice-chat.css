/* 沉浸式语音聊天页面样式 */
.immersive-voice-chat {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

/* 背景遮罩 */
.immersive-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: 1;
}

/* 3D角色显示区域 */
.immersive-character {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* 顶部控制栏 */
.immersive-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, transparent 100%);
  z-index: 10;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.character-name {
  font-size: 24px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.top-controls {
  display: flex;
  gap: 8px;
}

.immersive-control-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
}

.immersive-control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  color: white;
}

/* 底部语音控制区域 */
.immersive-voice-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 40px 32px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  z-index: 10;
}

.immersive-voice-input {
  transform: scale(1.2);
}

.immersive-voice-input .voice-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 28px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 3px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.immersive-voice-input .voice-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.immersive-voice-input .voice-button.listening {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border-color: transparent;
  animation: pulse-immersive 2s infinite;
}

@keyframes pulse-immersive {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

/* 处理指示器 */
.processing-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 25px;
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 语音指示器在沉浸式模式下的样式 */
.immersive-voice-controls .voice-indicator {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
}

.immersive-voice-controls .voice-indicator.active {
  background: rgba(255, 107, 107, 0.3);
  border-color: rgba(255, 107, 107, 0.5);
}

.immersive-voice-controls .emotion-display {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
}

/* 加载状态 */
.immersive-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  z-index: 1000;
}

/* 错误状态 */
.immersive-error {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  color: white;
  padding: 40px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.error-content h3 {
  color: white;
  margin-bottom: 16px;
  font-size: 24px;
}

.error-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24px;
  font-size: 16px;
}

/* 帮助内容样式 */
.help-content h4 {
  color: #1890ff;
  margin-top: 20px;
  margin-bottom: 12px;
}

.help-content ul {
  margin-bottom: 16px;
}

.help-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 桌面端优化 */
@media (max-width: 1200px) {
  .immersive-top-bar {
    padding: 0 24px;
  }

  .character-name {
    font-size: 20px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .immersive-voice-chat {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .immersive-voice-controls .voice-indicator,
  .immersive-voice-controls .emotion-display {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* 动画效果 */
.immersive-voice-chat * {
  transition: all 0.3s ease;
}

/* 进入动画 */
.immersive-voice-chat {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 语音控制区域进入动画 */
.immersive-voice-controls {
  animation: slideUp 0.6s ease-out 0.2s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 顶部控制栏进入动画 */
.immersive-top-bar {
  animation: slideDown 0.6s ease-out 0.1s both;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

{"name": "virtual-character-platform-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/lodash-es": "^4.17.12", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "prettier": "^3.5.3", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@anthropic-ai/sdk": "^0.56.0", "@aws-sdk/client-bedrock-runtime": "^3.846.0", "@aws-sdk/client-s3": "^3.846.0", "@azure/core-auth": "^1.5.0", "@azure/openai": "^2.0.0", "@baiducloud/qianfan": "^0.2.3", "@gltf-transform/core": "^4.1.0", "@google/generative-ai": "^0.24.1", "@huggingface/inference": "^4.5.1", "@lobehub/chat-plugin-sdk": "^1.32.4", "@lobehub/icons": "^1.54.0", "@lobehub/tts": "^1.25.8", "@lobehub/ui": "^1.171.0", "@pixiv/three-vrm": "2.1.2", "@pixiv/three-vrm-core": "2.1.2", "@react-spring/web": "^9.7.5", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@types/ua-parser-js": "^0.7.39", "@vercel/analytics": "^1.5.0", "@xenova/transformers": "^2.17.2", "ahooks": "^3.8.4", "ai": "^4.3.19", "ali-oss": "^6.23.0", "antd": "^5.26.4", "antd-style": "^3.7.1", "axios": "^1.7.9", "classnames": "^2.5.1", "consola": "^3.4.2", "dayjs": "^1.11.13", "fast-deep-equal": "^3.1.3", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-resources-to-backend": "^1.2.1", "immer": "^10.1.1", "jose": "^6.0.12", "js-tiktoken": "^1.0.20", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "lucide-react": "^0.395.0", "mime": "^4.0.7", "mmd-parser": "^1.0.4", "modern-screenshot": "^4.5.5", "nanoid": "^5.0.9", "ollama": "^0.5.16", "pino": "^9.7.0", "polished": "^4.3.1", "pwa-install-handler": "^2.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "14.0.2", "react-intersection-observer": "^9.13.1", "react-layout-kit": "^1.9.1", "react-lazy-load": "^4.0.1", "react-router-dom": "^7.6.2", "react-virtuoso": "^4.12.3", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "superjson": "^2.2.2", "swr": "^2.2.5", "three": "0.164.1", "ua-parser-js": "^2.0.4", "use-merge-value": "^1.2.0", "utility-types": "^3.11.0", "uuid": "^10.0.0", "vitest": "^3.2.4", "zod": "^3.25.76", "zustand": "^4.5.5"}}
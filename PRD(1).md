# 虚拟角色平台产品需求文档 (PRD)

## 1. 项目愿景与目标

- **愿景:** 打造一个领先的二次元虚拟角色平台，让用户能够轻松创造个性化角色，并与之进行情感交互。
- **目标:** 成为 18-30 岁动漫爱好者的首选虚拟社交/创作平台，提供独特的角色定制和智能交互体验。

## 2. 目标用户画像与核心需求

- **用户群体:** 主要集中在 18-30 岁的动漫爱好者。
- **核心需求:**
    - 快速、便捷地创建具有二次元风格的个性化虚拟角色。
    - 对角色的外观（身材、容貌、胸部、面貌、年龄等）有较高的定制化需求。
    - 希望与自己创建的虚拟角色进行情感层面的交互（初期文本，后期语音）。
    - 希望在社区中展示和分享自己的角色。

## 3. 核心功能模块

### 3.1 虚拟角色搭建

- **核心理念:** 简化流程，避免用户流失；提供丰富的预设和灵活的定制。
- **主要功能点:**
    - **基础生成 (抽卡):**
        - 用户可以选择"种族"或输入"动漫名字"作为基础。
        - 通过"抽卡"机制，反复调用图片生成 API (星火 API) 生成不同形象，直到用户满意。
        - 基于预设的提示词库和用户输入（种族/动漫名/年龄/身份/性格）生成 API 调用所需的提示词。
    - **定制化:**
        - 支持在基础生成后进行进一步调整。
        - **参数调整:** 提供可视化控件（如滑块）调整角色的特定外观参数（具体参数待定：例如，发型、瞳色、服装、身体比例等）。
        - **语言描述定制:** 支持用户输入完整的自然语言描述来修改角色细节（例如，"把头发变成绿色"，"给她穿一件JK制服"）。后端需要解析这些语言指令并转化为提示词或参数调整。
    - **声音定制:**
        - 初期：为角色选择或设定声音的类型/风格（具体方式待定）。
        - 未来：考虑支持通过开源 TTS 模型为角色的文本回复生成语音，甚至支持更高级的语音定制或克隆。
    - **性格与身份设定:**
        - 用户可以设定角色的年龄、身份和性格（具体设定方式和选项待定，例如：预设标签、文本描述）。
        - 这些设定将直接影响角色的图像生成和后续的情感交互对话风格和内容。

### 3.2 情感交互

- **核心理念:** 提供与虚拟角色进行真实感和情感化的交流体验。
- **主要功能点:**
    - **文本聊天:**
        - 初期实现与角色的文本对话功能。
        - 对话内容和风格由角色的年龄、身份、性格设定以及底层对话 AI 模型共同决定。
    - **语音交互 (未来):**
        - 考虑集成开源 TTS 模型，将角色的文本回复转化为语音播放。
        - 未来可能考虑支持用户语音输入并由 AI 进行语音回复。
- **技术实现考量:** 需要对接或使用一个对话 AI 模型，并通过提示词工程将角色设定融入对话过程。

### 3.3 社区与分享

- **核心理念:** 增强平台互动性，丰富角色内容，形成用户生态。
- **主要功能点:**
    - **角色展示:** 用户创建并满意的角色可以在社区中公开展示。
    - **浏览与互动:** 其他用户可以浏览、点赞、评论公开的角色（具体互动方式待定）。
    - **搜索与筛选:** 提供按特定条件（如种族、风格、受欢迎度等）搜索和筛选角色的功能（待定）。
- **管理考量:** 需要考虑对社区公开内容的审核和管理机制。

## 4. 技术需求与约束

- **图片生成 API:** 使用"星火"API，通过 HTTP POST 请求，输入文本提示词，返回 Base64 图片数据。需要实现签名鉴权机制。提示词长度上限 1000 字符。
- **语音合成 (TTS):** 使用开源 TTS 模型，将文本转化为语音。需要考虑模型选择、部署或集成方式、以及如何通过提示词控制语音风格。
- **对话 AI 模型:** 需要一个能够支持通过提示词进行角色设定的对话 AI 模型。模型来源待定（商业 API 或自部署开源模型）。
- **后端技术栈:** **Python** (推荐使用 **Django** 或 **Flask** 框架)，需要能够处理 HTTP 请求、调用外部 API、与数据库交互。核心的**提示词工程模块**也将在此实现。
- **前端技术栈:** 推荐使用 **React** 或 **Vue.js**，需要能够构建交互式界面，处理用户输入，显示图片和文本。
- **数据库:** 主要使用**关系型数据库 PostgreSQL** 存储结构化数据，**可选使用非关系型数据库 MongoDB** 存储灵活的角色参数。生成的图片文件存储在**云存储服务**中。
- **存储:** 需要考虑存储生成的图片数据。
- **鉴权:** 用户鉴权和 API 调用鉴权（星火 API 的签名机制）。
- **性能:** 图片生成和语音合成的响应速度是关键的用户体验因素。
- **安全性:** 数据安全（特别是用户数据和敏感 API Key）和内容安全（生成内容的审核）。

## 5. MVP 范围

- **核心 MVP 功能:**
    - 用户能够通过"抽卡"机制基于种族/动漫名/设定（年龄/身份/性格）生成基础角色形象（调用星火 API）。
    - 支持对生成的角色进行有限的**参数调整**（具体参数范围待定，选择最核心的几项）。
    - 用户能够与创建的角色进行**文本聊天**。
    - 用户创建并满意的角色可以**保存**到数据库。
    - 在社区中**展示**用户保存的角色列表（基础列表展示）。
- **MVP 阶段暂不包含但后续迭代考虑的功能:**
    - 语言描述进行定制化。
    - 语音交互（用户语音输入和角色语音回复）。
    - 社区的复杂互动功能（点赞、评论、搜索、筛选）。
    - 更精细和全面的参数调整。
    - 更高级的声音定制（如语音克隆）。
    - 角色的交易或二次创作市场。

## 6. 未来可能的迭代方向

- 集成更强大的图片生成功能（例如 img2img, 局部重绘）。
- 实现全双工语音交互。
- 引入更复杂的角色行为和记忆。
- 扩展社区功能和社交互动。
- 支持角色的动画和表情。
- 探索 VR/AR 中的角色交互。
- - 探索数字人形态和更高级的自然交互（例如，更逼真的形象、实时语音、微表情、肢体语言等）。

## 7. 开放问题与待细化项

- 参数调整的具体范围和实现方式？

参数调整模块：

在生成初始形象基础上，用户可对虚拟角色的外貌特征进行个性化调整，以满足用户对角色个性表达的进一步需求。该功能作为核心交互环节之一，需提供可视化的参数界面，并支持关键属性的精细调节。

具体参数建议包括但不限于以下几类：

发型、发色、瞳色等基础外观元素：提供多种预设选项，供用户直接点击选择。例如，发型可设置为长直、双马尾、短发等；发色支持粉色、银白、浅绿等常见二次元色系；瞳色支持紫、金、蓝等风格搭配。

表情与穿搭风格：可设置冷酷、微笑、傲娇等基础表情，穿搭风格提供JK制服、洛丽塔、未来战士等主题套装，系统内置素材风格与星火API生成能力匹配。

身体比例与细节：滑块形式设置身高、体型、胸围等基础比例，皮肤色、妆容等细节参数作为可选项开放；所有参数修改后即时触发图像更新。

实现上，通过前端界面选项映射生成对应的英文 prompt 模板，传入星火大模型后端进行图像再生成，需保证响应时间可控。建议使用 prompt 模板拼接方式统一管理参数与提示词之间的关系，并支持默认模板回退与当前设定缓存记录，方便用户在多次生成之间快速切换与对比。后续亦可开放自然语言与参数化输入的切换功能。


- 性格和身份设定的具体选项和对对话影响的详细规则？
身份设定：
提供一组结构化标签供用户选择，也支持用户文本自定义输入。示例身份包括：

高中生 / 大学生

偶像 / 虚拟歌姬

咖啡店店员 / 魔法使 / 女仆 / 赛博朋克侦探

异世界公主 / 游戏NPC / 虚拟心理咨询师

身份标签将映射为角色背景相关提示词（如服饰、语境等），并影响AI对话中角色的世界观设定与语境行为。例如，“咖啡店店员”身份下，角色在对话中可能会主动推荐饮品、使用温和礼貌的语气，回应场景围绕日常生活。

性格设定：
提供多个可组合的性格标签，并支持用户自定义描述补充。基础性格标签建议包括：

傲娇 / 病娇 / 元气 / 沉稳 / 冷酷 / 温柔 / 活泼 / 腼腆 / 高冷 / 毒舌 / 健气系 / 哥哥系 / 姐姐系

每个性格标签将内嵌为角色对话 prompt 的人格行为参数（例如语气、用词、主动性等），例如：

傲娇角色在回复中会使用“才、才不是因为你我才来的呢！”类似语气。

温柔系则倾向于使用“我会一直陪着你的~”等亲和表达。

冷酷型则保持简洁、理性，回应简短直接，不易主动发起亲密内容。

实现方式说明：

前端用户界面提供预设标签（可打勾组合），并开放自然语言补充框，系统将所有设定合并为完整的提示词文本。

后端通过提示词工程将性格与身份标签插入到图像生成 prompt（如“a gentle university student girl with blue hair”）和对话AI的 system prompt 中（如“Act as a tsundere high school girl who is secretly caring but pretends to be annoyed.”）。

支持角色性格设定在对话过程中的动态表现（如不同情绪下略有变化），后续可拓展为长期行为记忆和对话风格学习。



- 开源 TTS 模型的具体选择和集成方案？

综合考虑生成质量、部署难度、扩展性和角色多样性，我们考虑了以下主流开源 TTS 模型供集成并适当取舍：

1. Bark（by Suno）

特点：支持多语种、声音风格多变，合成自然度高，支持背景音、情感语调等多模态表达。

优点：无需精准标注的语音数据即可训练自定义角色声音。

适用场景：风格多样的虚拟角色初期语音呈现。

2. VITS / VITS-fast / Coqui TTS

特点：端到端语音合成，支持多说话人模型和情感控制。

优点：推理速度快，部署友好，语音清晰度好，Coqui 社区活跃。

适用场景：对合成速度要求较高，或需快速集成的系统。

3. Tortoise TTS

特点：高质量、情感丰富，但推理速度较慢。

适用场景：需要高保真度音色、适合用于保存音频文件进行角色语音设定或商用视频生成。

集成方案设计：
部署方式：

本地部署（推荐使用 Docker 容器统一管理模型服务）。

使用预训练模型或少量语音样本微调模型（如 Bark 支持）。

GPU 推荐：NVIDIA RTX 3060 及以上，建议使用 NVIDIA Triton Server 实现模型推理服务部署。

前后端通信流程：

前端用户在聊天界面发送文本，后端对话模型返回文本回复。

后端将该文本和角色的声音设定（如音色 ID、语调标签）作为参数发送给 TTS 模型服务。

模型生成 .wav 或 .mp3 文件并返回 URL/音频流给前端，前端播放声音。

角色音色管理方式：

每个角色绑定一个声音配置文件（包括模型类型、语速、语调、音色ID等）。

支持初期预设声音标签选择（如“温柔女声”、“元气少年”、“傲娇萝莉”等），未来可开放上传语音样本用于声音克隆。

- 对话 AI 模型的具体选择和如何进行角色扮演微调？
根据部署能力与目标体验效果，分析了以下大模型

1. OpenChat / Zephyr（基于 Mistral）

优点：体积小（7B）、响应速度快、开源完全可控，适合中小型本地部署。

特点：支持一定程度的指令理解与角色扮演，适合轻量级互动场景。

2. Qwen / DeepSeek / Yi（中英双语能力强）

优点：中文表达能力强，知识覆盖广，便于构建符合国风/ACG风格角色。

特点：适用于有中文拟人化需求的产品。

3. LLaMA3 / GPT-4-Turbo（需 API）

优点：理解能力强、上下文处理佳，支持高质量角色扮演。

特点：适合部署成本高但需构建强交互体验的产品原型或旗舰版。

角色扮演实现方式：
1. 系统提示词（system prompt）设定：
通过为每个角色撰写独立的“人格设定卡”（Character Card），并在对话模型的系统提示中注入，控制角色说话风格、知识结构与行为边界。

2. 微调方式（LoRA / SFT）：

LoRA 微调

技术：低秩适配方法，仅训练少量参数，成本低。

数据：准备 500～3000 条该角色风格对话数据（如 Q: 你今天好吗？ A: 我挺好的，谢谢你关心～）。

工具：使用 Hugging Face 的 PEFT / QLoRA 工具包进行训练。

效果：强化语气、态度、知识结构，使模型“染上”角色风格。

- 数据库的具体类型和 Schema 设计？

主体采用 PostgreSQL 作为关系型数据库，负责管理结构化数据（如用户信息、角色元数据、对话记录索引等）。

辅助采用 MongoDB 存储半结构化或灵活变动的数据（如角色的外观参数 JSON、完整的对话上下文等），提升数据扩展性。

所有生成的媒体文件（如角色图片、语音音频）通过云存储服务托管，数据库中仅保留引用链接。

主要表结构设计（简要 Schema 示意）：

users 表

id（主键）、username、email、password_hash、created_at 等基础信息。

characters 表

id、user_id（外键）、name、base_traits（如年龄/身份/性格）、appearance_config（JSON）、voice_style、created_at。

dialogs 表

id、character_id、user_id、message_type（user / ai）、content、timestamp，用于记录文本聊天记录。

media_assets 表

id、character_id、type（image / audio）、url、created_at，管理角色生成的视觉与语音素材。

community_posts 表（可选，未来扩展）

id、character_id、user_id、title、description、likes_count、created_at，用于展示与互动。

通过 PostgreSQL 与 MongoDB 的组合设计，系统在保证数据一致性的同时具备高扩展性，为后续角色成长记录、社区互动、情感记忆等功能提供良好数据基础。

- 社区互动功能的具体形式？

角色展示与分享： 用户可将自己创建并保存的虚拟角色发布到社区公开展示区，支持角色封面图和基本信息展示。

点赞机制： 用户浏览角色时，可通过点赞表达喜爱和支持，点赞数作为角色受欢迎程度的直观指标。

评论互动： 支持用户在角色展示页下发表评论，与角色创作者及其他用户进行交流和反馈，促进用户之间的情感连接。

角色搜索与筛选： 提供基于种族、风格、热门度等多维度的搜索和筛选功能，帮助用户快速找到感兴趣的角色。

排行榜与推荐： 设立角色人气排行榜，结合用户行为数据推送个性化推荐，激励创作和互动。

内容审核机制： 引入自动与人工相结合的审核流程，保障社区内容健康，防止违规及不当内容传播。

未来扩展考虑：

- 内容审核的具体策略和技术方案？

审核策略：

多层次审核体系：

自动化审核：利用基于关键词过滤、图像识别和自然语言处理（NLP）的内容审核算法，初步筛查敏感、违规或不当内容（包括色情、暴力、政治敏感等）。

人工复核：对自动审核标记为疑似违规内容进行人工复核，确保审核准确性和公平性。

**分级管理机制：**针对不同类型和严重程度的违规行为，设计分级处理流程（警告、删除内容、限制发布权限、封禁账号等）。

**用户举报通道：**设置便捷的举报功能，鼓励社区成员积极参与内容监督，举报结果纳入人工复核流程。

技术方案：

**文本审核：**集成开源或商业NLP模型（如百度文本审核、阿里云内容安全API）实现敏感词检测、语义理解和情感分析，自动过滤不良文本内容。

**图像审核：**调用图像内容审核API（如腾讯云图像审核、AWS Rekognition），识别涉黄、暴力、涉政等违规图片。结合星火API生成的图像元数据辅助判定。

**语音内容审核（未来规划）：**基于语音转文本（ASR）技术，结合文本审核模型对语音内容进行检测。

**审核数据管理：**所有审核结果和日志均存入专门数据库，支持追踪和复查。

**权限与角色设计：**设定不同审核角色权限（审核员、管理员），保障审核流程的规范运行。

通过自动与人工结合的多层次审核体系，辅以先进的AI技术和完善的管理制度，平台能高效、准确地保障社区内容安全，提升用户信任与满意度。

- 前后端具体的技术栈选择？ (已初步确定 Python 后端 + React/Vue 前端)
后端技术栈：

语言与框架： 采用 Python 作为主要开发语言，推荐使用 Django 或 Flask 框架。

功能实现： 负责处理 HTTP 请求，调用外部 API（如星火图片生成API、TTS模型、对话AI模型），实现业务逻辑和提示词工程模块。

数据库交互： 通过 ORM 或数据库驱动对 PostgreSQL 关系型数据库和 MongoDB 非关系型数据库进行数据操作。

鉴权与安全： 实现用户鉴权机制及 API 调用的签名鉴权，保障数据与接口安全。

性能优化： 支持异步处理（如使用 Celery）以提升图片生成与语音合成的响应速度。

前端技术栈：

框架： 选用 React 或 Vue.js，构建高效且交互性强的用户界面。

功能实现： 实现虚拟角色创建界面（包括抽卡流程、参数调整、语言描述定制）、文本聊天窗口、社区角色展示和互动功能。

状态管理： 使用 Redux（React）或 Vuex（Vue）管理应用状态，保证数据流畅同步。

UI 组件库： 可结合 Ant Design、Element UI 等成熟组件库加快开发效率。

网络请求： 使用 Axios 或 Fetch API 实现前后端数据交互。

响应式设计： 支持多终端访问，提升用户体验。

结合 Python 后端与 React/Vue 前端，平台能实现功能完整且用户体验良好的虚拟角色创建与交互系统。


这份文档是项目的起点，我们将在后续的设计和开发过程中不断完善和更新它。接下来，我们可以逐个细化"开放问题与待细化项"中的内容，或者按照文档的结构，先深入讨论"虚拟角色搭建"的具体实现细节。 
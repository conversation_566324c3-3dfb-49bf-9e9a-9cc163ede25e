import { ThemeProvider } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { memo, useEffect } from 'react';
import type { NeutralColors, PrimaryColors } from '@lobehub/ui';
import type { ThemeAppearance } from 'antd-style';
import type { ReactNode } from 'react';

import {
  VIDOL_THEME_APPEARANCE,
  VIDOL_THEME_NEUTRAL_COLOR,
  VIDOL_THEME_PRIMARY_COLOR,
} from '../constants/theme';
import { useGlobalStore } from '../store/global';
import { useSettingStore } from '../store/setting';
import { GlobalStyle } from '../styles';

export interface AppThemeProps {
  children?: ReactNode;
  defaultAppearance?: ThemeAppearance;
  defaultNeutralColor?: NeutralColors;
  defaultPrimaryColor?: PrimaryColors;
}

const useStyles = createStyles(({ css }) => ({
  content: css`
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    /* 移除 align-items: center，让子组件自己控制对齐方式 */

    height: 100%;
    min-height: 100vh;
  `,
}));

const AppTheme = memo((props: AppThemeProps) => {
  const { children, defaultNeutralColor, defaultAppearance, defaultPrimaryColor } = props;
  const [primaryColor, neutralColor] = useSettingStore((s) => [
    s.config.primaryColor,
    s.config.neutralColor,
  ]);

  const themeMode = useGlobalStore((s) => s.themeMode);

  const { styles } = useStyles();

  // 使用localStorage替代Cookie进行主题持久化
  useEffect(() => {
    if (primaryColor) {
      localStorage.setItem(VIDOL_THEME_PRIMARY_COLOR, primaryColor);
    }
  }, [primaryColor]);

  useEffect(() => {
    if (neutralColor) {
      localStorage.setItem(VIDOL_THEME_NEUTRAL_COLOR, neutralColor);
    }
  }, [neutralColor]);

  return (
    <ThemeProvider
      customTheme={{
        primaryColor: primaryColor ?? defaultPrimaryColor,
        neutralColor: neutralColor ?? defaultNeutralColor,
      }}
      defaultAppearance={defaultAppearance}
      onAppearanceChange={(appearance) => {
        localStorage.setItem(VIDOL_THEME_APPEARANCE, appearance);
      }}
      themeMode={themeMode}
    >
      <GlobalStyle />
      <div className={styles.content}>{children}</div>
    </ThemeProvider>
  );
});

export default AppTheme;

# Django 项目配置
SECRET_KEY="your_django_secret_key_here"
DEBUG=True

# 数据库配置 (如果使用PostgreSQL，根据TASK001的描述)
# DATABASE_URL="postgresql://user:password@host:port/dbname"

# 星火 AI API 凭证 (根据TASK002的描述)
SPARK_APP_ID="2c25d0fb"
SPARK_API_KEY="9b6134381f1fec8857c8b02f06e627aa"
SPARK_API_SECRET="IlvwxGaerOBEqxCAqubW:maUqAMgaXqibbjWTySvJ"

# 用于测试的通用变量 (可选，用于开发阶段验证)
TEST_VAR="HelloEnv"

# ===========================================
# AI模型配置 - 至少需要配置一个AI服务才能使用聊天功能
# ===========================================

# OpenAI配置（推荐）
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_PROXY_URL=https://api.openai.com/v1

# 或者使用国内AI服务
# 智谱AI (ChatGLM)
# ZHIPU_API_KEY=your_zhipu_api_key_here

# DeepSeek
# DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 月之暗面 (Moonshot)
# MOONSHOT_API_KEY=your_moonshot_api_key_here

# 通义千问
# QWEN_API_KEY=your_qwen_api_key_here

# 本地Ollama（如果已安装）
# ENABLED_OLLAMA=1
# OLLAMA_PROXY_URL=http://localhost:11434
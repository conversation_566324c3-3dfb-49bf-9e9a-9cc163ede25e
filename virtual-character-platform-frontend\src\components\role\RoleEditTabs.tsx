import React from 'react';
import { Tabs, But<PERSON> } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

// 导入各个配置模块
import InfoTab from './tabs/InfoTab';
import RoleTab from './tabs/RoleTab';
import VoiceTab from './tabs/VoiceTab';
import ShellTab from './tabs/ShellTab';
import LangModelTab from './tabs/LangModelTab';

interface RoleEditTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onToggleSidebar: () => void;
}

const RoleEditTabs: React.FC<RoleEditTabsProps> = ({
  activeTab,
  onTabChange,
  onToggleSidebar
}) => {
  const tabItems = [
    {
      key: 'info',
      label: '基本信息',
      children: <InfoTab />,
    },
    {
      key: 'role',
      label: '角色设定',
      children: <RoleTab />,
    },
    {
      key: 'voice',
      label: '语音配置',
      children: <VoiceTab />,
    },
    {
      key: 'shell',
      label: '外观配置',
      children: <ShellTab />,
    },
    {
      key: 'llm',
      label: '语言模型',
      children: <LangModelTab />,
    },
  ];

  return (
    <div className="role-edit-tabs">
      <Tabs
        activeKey={activeTab}
        onChange={onTabChange}
        items={tabItems}
        tabBarExtraContent={{
          left: (
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={onToggleSidebar}
              title="切换侧边栏"
            />
          ),
        }}
        className="role-tabs"
      />
    </div>
  );
};

export default RoleEditTabs;

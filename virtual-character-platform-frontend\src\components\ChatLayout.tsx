import React from 'react';
import { Layout, Button, Avatar, Typography } from 'antd';
import { ArrowLeftOutlined, SettingOutlined, SoundOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';

const { Header, Content } = Layout;
const { Text } = Typography;

interface ChatLayoutProps {
  children: React.ReactNode;
  selectedCharacter?: {
    id: string;
    name: string;
    imageUrl: string;
  } | null;
  onSettingsClick?: () => void;
  onVoiceToggle?: () => void;
  isVoiceEnabled?: boolean;
  backgroundImageUrl?: string;
  on3DModeToggle?: () => void;
  is3DMode?: boolean;
}

const ChatLayout: React.FC<ChatLayoutProps> = ({
  children,
  selectedCharacter,
  onSettingsClick,
  onVoiceToggle,
  isVoiceEnabled = false,
  backgroundImageUrl,
  on3DModeToggle,
  is3DMode = false
}) => {
  const navigate = useNavigate();
  const { userInfo } = useAuthStore();

  // 生成背景样式
  const backgroundStyle = backgroundImageUrl
    ? {
        minHeight: '100vh',
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url(${backgroundImageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }
    : {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      };

  return (
    <Layout style={backgroundStyle}>
      {/* 聊天页面专用头部 */}
      <Header 
        style={{ 
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* 左侧：返回按钮和角色信息 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
            style={{ 
              display: 'flex', 
              alignItems: 'center',
              color: '#666'
            }}
          >
            返回
          </Button>
          
          {selectedCharacter && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <Avatar 
                src={selectedCharacter.imageUrl} 
                size={40}
                alt={selectedCharacter.name}
              />
              <div>
                <Text strong style={{ fontSize: '16px', color: '#333' }}>
                  {selectedCharacter.name}
                </Text>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    在线 • 准备聊天
                  </Text>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 右侧：功能按钮和用户信息 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {/* 3D模式切换 */}
          <Button
            type={is3DMode ? 'primary' : 'default'}
            icon={<EyeOutlined />}
            onClick={on3DModeToggle}
            style={{
              borderRadius: '20px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {is3DMode ? '3D模式' : '2D模式'}
          </Button>

          {/* TTS语音开关 */}
          <Button
            type={isVoiceEnabled ? 'primary' : 'default'}
            icon={<SoundOutlined />}
            onClick={onVoiceToggle}
            style={{
              borderRadius: '20px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {isVoiceEnabled ? '语音开启' : '语音关闭'}
          </Button>

          {/* 设置按钮 */}
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={onSettingsClick}
            style={{
              borderRadius: '20px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            设置
          </Button>

          {/* 用户信息 */}
          {userInfo && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Avatar size={32} style={{ backgroundColor: '#eb2f96' }}>
                {userInfo.username.charAt(0).toUpperCase()}
              </Avatar>
              <Text style={{ color: '#666', fontSize: '14px' }}>
                {userInfo.username}
              </Text>
            </div>
          )}
        </div>
      </Header>

      {/* 聊天内容区域 */}
      <Content
        style={{
          background: 'transparent',
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100vh - 64px)', // 减去Header高度
          overflow: 'hidden' // 防止内容溢出
        }}
      >
        {children}
      </Content>
    </Layout>
  );
};

export default ChatLayout;

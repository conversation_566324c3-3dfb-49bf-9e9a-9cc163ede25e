import React, { useState, useRef } from 'react';
import { Flexbox } from 'react-layout-kit';
import classNames from 'classnames';

import ChatHeader from './ChatHeader';
import ChatList from './ChatList';
import MessageInput from './MessageInput';
import SideBar from './SideBar';
import ChatInfo from './ChatInfo';
import './style.css';

interface ChatModeProps {
  characterId?: string;
  onVoiceInput?: (transcript: string) => void;
}

const ChatMode: React.FC<ChatModeProps> = ({ 
  characterId, 
  onVoiceInput 
}) => {
  const [showSidebar, setShowSidebar] = useState(false);
  const [showChatInfo, setShowChatInfo] = useState(false);
  const inputRef = useRef<HTMLDivElement>(null);

  return (
    <div className="chat-mode-container">
      <Flexbox
        flex={1}
        className="chat-mode-content"
        horizontal
        style={{ maxWidth: '100%', overflow: 'hidden', position: 'relative' }}
      >
        {/* 左侧边栏 */}
        {showSidebar && (
          <SideBar onClose={() => setShowSidebar(false)} />
        )}

        {/* 主聊天区域 */}
        <Flexbox flex={1} style={{ position: 'relative' }} height="100%" width="100%">
          {/* 聊天头部 */}
          <ChatHeader 
            onToggleSidebar={() => setShowSidebar(!showSidebar)}
            onToggleChatInfo={() => setShowChatInfo(!showChatInfo)}
          />

          {/* 聊天消息列表 */}
          <ChatList />

          {/* 消息输入区域 */}
          <Flexbox align="center" className="chat-mode-input-docker" ref={inputRef}>
            <div className="chat-mode-input-container">
              <MessageInput 
                onVoiceInput={onVoiceInput}
              />
            </div>
          </Flexbox>
        </Flexbox>

        {/* 右侧信息面板 */}
        {showChatInfo && (
          <ChatInfo onClose={() => setShowChatInfo(false)} />
        )}
      </Flexbox>
    </div>
  );
};

export default ChatMode;

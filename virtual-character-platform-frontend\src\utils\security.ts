/**
 * 安全工具类
 * 提供XSS防护、输入验证等安全功能
 */

/**
 * 转义HTML特殊字符，防止XSS攻击
 * @param unsafe 不安全的字符串
 * @returns 转义后的安全字符串
 */
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * 安全地渲染HTML内容（仅用于可信内容）
 * @param html HTML字符串
 * @returns 安全的HTML对象
 */
export function createSafeHtml(html: string): { __html: string } {
  return { __html: html };
}

/**
 * 验证邮箱格式
 * @param email 邮箱字符串
 * @returns 是否为有效邮箱
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

/**
 * 验证密码强度
 * @param password 密码字符串
 * @returns 密码强度评级（0-4，数字越大越强）
 */
export function validatePasswordStrength(password: string): number {
  let strength = 0;
  
  // 长度检查
  if (password.length >= 8) strength += 1;
  
  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1;
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1;
  
  // 包含数字
  if (/[0-9]/.test(password)) strength += 1;
  
  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
  
  return Math.min(strength, 4);
}

/**
 * 获取密码强度描述
 * @param strength 密码强度评级（0-4）
 * @returns 密码强度描述
 */
export function getPasswordStrengthDescription(strength: number): string {
  switch (strength) {
    case 0:
      return '非常弱';
    case 1:
      return '弱';
    case 2:
      return '一般';
    case 3:
      return '强';
    case 4:
      return '非常强';
    default:
      return '未知';
  }
}

/**
 * 验证用户名格式（字母、数字、下划线，长度6-20）
 * @param username 用户名
 * @returns 是否为有效用户名
 */
export function validateUsername(username: string): boolean {
  const usernameRegex = /^[a-zA-Z0-9_]{6,20}$/;
  return usernameRegex.test(username);
}

/**
 * 验证URL格式
 * @param url URL字符串
 * @returns 是否为有效URL
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 清理输入文本，移除潜在的危险字符
 * @param input 输入文本
 * @returns 清理后的文本
 */
export function sanitizeInput(input: string): string {
  // 移除HTML标签
  const withoutTags = input.replace(/<[^>]*>/g, '');
  
  // 移除控制字符和不可见字符
  return withoutTags.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
}

/**
 * 生成CSRF Token（用于表单提交）
 * @returns CSRF Token
 */
export function generateCsrfToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * 检查是否为HTTPS连接
 * @returns 是否为HTTPS连接
 */
export function isSecureConnection(): boolean {
  return window.location.protocol === 'https:';
}

/**
 * 安全地存储敏感数据到sessionStorage
 * @param key 键名
 * @param value 值
 */
export function secureSessionStorage(key: string, value: string): void {
  try {
    // 简单加密（仅用于基本保护，不是真正的加密）
    const encodedValue = btoa(value);
    sessionStorage.setItem(key, encodedValue);
  } catch (e) {
    console.error('Session storage error:', e);
  }
}

/**
 * 从sessionStorage安全地获取敏感数据
 * @param key 键名
 * @returns 解码后的值
 */
export function getSecureSessionStorage(key: string): string | null {
  try {
    const value = sessionStorage.getItem(key);
    if (!value) return null;
    
    // 解码
    return atob(value);
  } catch (e) {
    console.error('Session storage retrieval error:', e);
    return null;
  }
} 
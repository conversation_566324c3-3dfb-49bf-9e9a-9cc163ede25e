{"ModelSelect": {"featureTag": {"custom": "Custom model, the default settings support both function calls and visual recognition. Please verify the availability of the above capabilities based on actual conditions.", "file": "This model supports file upload for reading and recognition.", "functionCall": "This model supports function calls.", "tokens": "This model supports a maximum of {{tokens}} tokens per session.", "vision": "This model supports visual recognition."}, "removed": "This model is not in the list. If unchecked, it will be automatically removed."}, "actions": {"add": "Add", "copy": "Copy", "copySuccess": "Copy Successful", "del": "Delete", "delAndRegenerate": "Delete and Regenerate", "edit": "Edit", "goBottom": "Go to Bottom", "regenerate": "Regenerate", "save": "Save", "share": "Share", "tts": "Voice"}, "agentInfo": "Role Information", "agentMarket": "Character Market", "animation": {"animationList": "Action List", "postureList": "Pose List", "totalCount": "Total {{total}} items"}, "apiKey": {"addProxy": "Add OpenAI proxy address (optional)", "closeTip": "Close tip", "confirmRetry": "Confirm and retry", "proxyDocs": "Not sure how to apply for an API Key?", "startDesc": "Enter your OpenAI API Key to start the conversation. The application will not record your API Key.", "startTitle": "Custom API Key"}, "background": {"backgroundList": "Background List", "totalCount": "Total {{total}} items"}, "callOff": "Hang up", "camera": "Video call", "chat": "Cha<PERSON>", "chatList": "Chat List", "clear": {"action": "Clear Context", "alert": "Are you sure you want to delete the history messages?", "tip": "This action is irreversible, please proceed with caution"}, "danceList": "Dance List", "danceMarket": "Dance Market", "delSession": "Delete Session", "delSessionAlert": "Are you sure you want to delete this conversation? Once deleted, it cannot be recovered, so please proceed with caution!", "editRole": {"action": "Edit Role"}, "enableHistoryCount": {"alias": "No Limit", "limited": "Only includes {{number}} session messages", "setlimited": "Set the number of historical messages", "title": "Limit Historical Message Count", "unlimited": "Unlimited Historical Message Count"}, "info": {"background": "Background", "chat": "Cha<PERSON>", "dance": "Dance", "motions": "Motions", "posture": "Posture", "stage": "Stage"}, "input": {"alert": "Please remember: Everything said by the AI is generated by AI.", "placeholder": "Please enter content to start chatting", "send": "Send", "warp": "Wrap"}, "interactive": "Interactive", "noDanceList": "No playlist available. You can subscribe to your favorite dances through the market.", "noRoleList": "No Role List Available", "noSession": "No sessions available. You can create a custom role by clicking +, or add roles through the discovery page.", "selectModel": "Please select a model", "sessionCreate": "Create Chat", "sessionList": "Session List", "share": {"downloadScreenshot": "Download Screenshot", "imageType": "Image Format", "screenshot": "Screenshot", "share": "Share", "shareGPT": "Share GPT", "shareToGPT": "Generate ShareGPT Share Link", "withBackground": "Include Background Image", "withFooter": "Include Footer", "withSystemRole": "Include Assistant Role Settings"}, "stage": {"stageList": "Stage List", "totalCount": "Total {{total}} items"}, "token": {"overload": "Token Overload", "remained": "Remaining <PERSON>s", "tokenCount": "Number of Tokens", "useToken": "Calculation of Token Consumption, including messages, character settings, and context: {{usedTokens}} / {{maxValue}}", "used": "Tokens Used"}, "toolBar": {"axes": "Axes", "cameraControl": "Camera Control", "cameraHelper": "Camera Helper", "downloading": "Downloading model, please wait...", "fullScreen": "Toggle Full Screen", "grid": "Grid", "interactiveOff": "Disable Touch Interaction", "interactiveOn": "Enable Touch Interaction", "resetCamera": "Reset Camera", "resetToIdle": "Stop Dance Action", "screenShot": "Take Photo"}, "tts": {"combine": "Voice Synthesis", "record": "Voice Recognition (requires VPN access)"}, "voiceOn": "Turn on voice"}
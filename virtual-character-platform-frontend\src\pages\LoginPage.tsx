import React, { useState } from 'react';
import { Form, Input, Button, Card, Tabs, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { authAPI } from '../services/api';
import useAuthStore from '../store/authStore';
import '../styles/login.css';

// 定义响应类型
interface LoginResponse {
  token: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('login');
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuthStore();

  const handleLogin = async (values: { username: string; password: string }) => {
    try {
      setLoading(true);

      // 我们的api.ts中的响应拦截器已经处理了response.data
      const response = await authAPI.login(values);

      // 类型断言
      const { token, user } = response as unknown as LoginResponse;

      if (token && user) {
        login(token, user);
        message.success('登录成功');

        // 获取来源页面，如果没有则跳转到首页
        const searchParams = new URLSearchParams(location.search);
        const from = searchParams.get('from');
        const redirectTo = from ? decodeURIComponent(from) : '/';

        navigate(redirectTo, { replace: true });
      } else {
        message.error('登录响应格式错误');
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // 处理具体的错误信息
      if (error.response?.data?.message) {
        message.error(error.response.data.message);
      } else if (error.response?.status === 401) {
        message.error('用户名或密码错误');
      } else {
        message.error('登录失败，请检查用户名和密码');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (values: { username: string; email: string; password: string; password_confirm: string }) => {
    try {
      setLoading(true);

      const response = await authAPI.register(values);

      // 类型断言，因为注册成功后后端也会返回token和用户信息
      const { token, user } = response as unknown as LoginResponse;

      if (token && user) {
        // 注册成功后直接登录
        login(token, user);
        message.success('注册成功，欢迎使用！');

        // 获取来源页面，如果没有则跳转到聊天页面
        const searchParams = new URLSearchParams(location.search);
        const from = searchParams.get('from');
        const redirectTo = from ? decodeURIComponent(from) : '/chat';

        navigate(redirectTo, { replace: true });
      } else {
        // 如果没有返回token，则切换到登录页面
        message.success('注册成功，请登录');
        setActiveTab('login');
      }
    } catch (error: any) {
      console.error('Register error:', error);

      // 处理具体的错误信息
      if (error.response?.data) {
        const errorData = error.response.data;

        // 如果是字段验证错误
        if (typeof errorData === 'object' && !errorData.message) {
          const errorMessages = [];
          for (const [field, messages] of Object.entries(errorData)) {
            if (Array.isArray(messages)) {
              errorMessages.push(...messages);
            } else {
              errorMessages.push(messages);
            }
          }
          message.error(errorMessages.join('; '));
        } else if (errorData.message) {
          message.error(errorData.message);
        } else {
          message.error('注册失败，请检查输入信息');
        }
      } else {
        message.error('注册失败，请稍后再试');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <h1>虚拟角色平台</h1>
          <p>创建你的专属虚拟角色</p>
        </div>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          centered
          items={[
            {
              key: 'login',
              label: '登录',
              children: (
                <Form onFinish={handleLogin} layout="vertical">
                  <Form.Item
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名' }
                    ]}
                  >
                    <Input prefix={<UserOutlined />} placeholder="用户名" />
                  </Form.Item>
                  <Form.Item
                    name="password"
                    rules={[{ required: true, message: '请输入密码' }]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="密码" />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" block loading={loading}>
                      登录
                    </Button>
                  </Form.Item>
                </Form>
              )
            },
            {
              key: 'register',
              label: '注册',
              children: (
                <Form onFinish={handleRegister} layout="vertical">
                  <Form.Item
                    name="username"
                    rules={[{ required: true, message: '请输入用户名' }]}
                  >
                    <Input prefix={<UserOutlined />} placeholder="用户名" />
                  </Form.Item>
                  <Form.Item
                    name="email"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input prefix={<MailOutlined />} placeholder="邮箱" />
                  </Form.Item>
                  <Form.Item
                    name="password"
                    rules={[
                      { required: true, message: '请输入密码' },
                      { min: 8, message: '密码长度不能少于8个字符' },
                      {
                        pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/,
                        message: '密码需要包含字母、数字和特殊字符，避免使用常见密码'
                      }
                    ]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="密码（至少8位，包含字母、数字和特殊字符）" />
                  </Form.Item>
                  <Form.Item
                    name="password_confirm"
                    dependencies={['password']}
                    rules={[
                      { required: true, message: '请确认密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'));
                        },
                      }),
                    ]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" block loading={loading}>
                      注册
                    </Button>
                  </Form.Item>
                </Form>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default LoginPage;
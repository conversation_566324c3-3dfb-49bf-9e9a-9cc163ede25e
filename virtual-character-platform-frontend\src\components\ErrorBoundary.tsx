import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';
import ErrorRecovery from './ErrorRecovery';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showRecovery: boolean;
}

/**
 * 错误边界组件
 * 用于捕获子组件树中的JavaScript错误，记录错误并展示备用UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showRecovery: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下次渲染显示备用 UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 上报错误到日志服务
    this.logError(error, errorInfo);

    // 在开发环境中显示详细错误信息
    if (import.meta.env.DEV) {
      console.group('🚨 React Error Boundary');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  }
  
  // 上报错误到日志服务
  logError(error: Error, errorInfo: ErrorInfo): void {
    // 收集上下文信息
    const errorDetails = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    // 在控制台输出详细错误信息（开发环境）
    console.error('React Error:', errorDetails);
    
    // 上报到后端日志服务
    // 注意：生产环境中应该实现这个功能
    if (import.meta.env.PROD) {
      try {
        fetch('/api/logs/client-error', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(errorDetails)
        });
      } catch (e) {
        console.error('Error reporting failed:', e);
      }
    }
  }

  // 重置错误状态
  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showRecovery: false
    });
  }

  // 显示错误恢复对话框
  handleShowRecovery = (): void => {
    this.setState({ showRecovery: true });
  }

  // 隐藏错误恢复对话框
  handleHideRecovery = (): void => {
    this.setState({ showRecovery: false });
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 自定义备用UI
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // 默认错误UI
      return (
        <div style={{ padding: '50px', textAlign: 'center' }}>
          <Result
            status="error"
            title="页面出错了"
            subTitle="抱歉，页面渲染过程中发生错误，请尝试刷新页面或返回首页。"
            extra={[
              <Button type="primary" key="refresh" onClick={() => window.location.reload()}>
                刷新页面
              </Button>,
              <Button key="home" onClick={() => window.location.href = '/chat'}>
                返回聊天页面
              </Button>,
              <Button key="reset" onClick={this.handleReset}>
                重试
              </Button>,
              <Button key="recovery" onClick={this.handleShowRecovery}>
                错误恢复
              </Button>
            ]}
          />
          {/* 开发环境显示错误详情 */}
          {import.meta.env.DEV && this.state.error && (
            <details style={{ marginTop: '20px', textAlign: 'left', maxWidth: '800px', margin: '20px auto' }}>
              <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                错误详情 (仅开发环境显示)
              </summary>
              <pre style={{
                background: '#f5f5f5',
                padding: '10px',
                borderRadius: '4px',
                overflow: 'auto',
                fontSize: '12px'
              }}>
                <strong>错误消息:</strong> {this.state.error.message}
                {'\n\n'}
                <strong>错误堆栈:</strong>
                {'\n'}
                {this.state.error.stack}
                {'\n\n'}
                <strong>组件堆栈:</strong>
                {'\n'}
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}

          {/* 错误恢复对话框 */}
          <ErrorRecovery
            visible={this.state.showRecovery}
            onClose={this.handleHideRecovery}
            error={this.state.error || undefined}
            errorInfo={this.state.errorInfo?.componentStack || undefined}
          />
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 
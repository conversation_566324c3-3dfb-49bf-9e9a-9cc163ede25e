import type { 
  Config, 
  NotificationConfig, 
  PrivacyConfig, 
  AccessibilityConfig, 
  TTSConfig 
} from '../types/config';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 验证通知设置
 */
export const validateNotificationConfig = (config: Partial<NotificationConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 通知设置的验证逻辑
  if (config.email !== undefined && typeof config.email !== 'boolean') {
    errors.push('Email notification setting must be a boolean');
  }
  
  if (config.push !== undefined && typeof config.push !== 'boolean') {
    errors.push('Push notification setting must be a boolean');
  }
  
  if (config.chat !== undefined && typeof config.chat !== 'boolean') {
    errors.push('Chat notification setting must be a boolean');
  }
  
  if (config.system !== undefined && typeof config.system !== 'boolean') {
    errors.push('System notification setting must be a boolean');
  }
  
  // 警告：如果所有通知都关闭
  if (config.email === false && config.push === false && 
      config.chat === false && config.system === false) {
    warnings.push('All notifications are disabled. You may miss important updates.');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证隐私设置
 */
export const validatePrivacyConfig = (config: Partial<PrivacyConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (config.profilePublic !== undefined && typeof config.profilePublic !== 'boolean') {
    errors.push('Profile public setting must be a boolean');
  }
  
  if (config.charactersPublic !== undefined && typeof config.charactersPublic !== 'boolean') {
    errors.push('Characters public setting must be a boolean');
  }
  
  if (config.chatHistory !== undefined && typeof config.chatHistory !== 'boolean') {
    errors.push('Chat history setting must be a boolean');
  }
  
  // 警告：如果聊天记录保存关闭
  if (config.chatHistory === false) {
    warnings.push('Chat history is disabled. Your conversations will not be saved.');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证无障碍设置
 */
export const validateAccessibilityConfig = (config: Partial<AccessibilityConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (config.fontSize !== undefined && 
      !['small', 'medium', 'large'].includes(config.fontSize)) {
    errors.push('Font size must be one of: small, medium, large');
  }
  
  if (config.highContrast !== undefined && typeof config.highContrast !== 'boolean') {
    errors.push('High contrast setting must be a boolean');
  }
  
  if (config.reduceMotion !== undefined && typeof config.reduceMotion !== 'boolean') {
    errors.push('Reduce motion setting must be a boolean');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证TTS设置
 */
export const validateTTSConfig = (config: Partial<TTSConfig>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (config.clientCall !== undefined && typeof config.clientCall !== 'boolean') {
    errors.push('Client call setting must be a boolean');
  }
  
  if (config.speed !== undefined) {
    if (typeof config.speed !== 'number') {
      errors.push('TTS speed must be a number');
    } else if (config.speed < 0.1 || config.speed > 3.0) {
      errors.push('TTS speed must be between 0.1 and 3.0');
    }
  }
  
  if (config.pitch !== undefined) {
    if (typeof config.pitch !== 'number') {
      errors.push('TTS pitch must be a number');
    } else if (config.pitch < 0.1 || config.pitch > 2.0) {
      errors.push('TTS pitch must be between 0.1 and 2.0');
    }
  }
  
  if (config.provider !== undefined && typeof config.provider !== 'string') {
    errors.push('TTS provider must be a string');
  }
  
  if (config.voice !== undefined && typeof config.voice !== 'string') {
    errors.push('TTS voice must be a string');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 验证完整的配置
 */
export const validateConfig = (config: Partial<Config>): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];
  
  // 验证各个子配置
  if (config.notifications) {
    const result = validateNotificationConfig(config.notifications);
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  }
  
  if (config.privacy) {
    const result = validatePrivacyConfig(config.privacy);
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  }
  
  if (config.accessibility) {
    const result = validateAccessibilityConfig(config.accessibility);
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  }
  
  if (config.tts) {
    const result = validateTTSConfig(config.tts);
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  }
  
  return {
    valid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings,
  };
};

/**
 * 获取默认设置值
 */
export const getDefaultSettings = () => {
  return {
    notifications: {
      email: true,
      push: true,
      chat: true,
      system: false,
    },
    privacy: {
      profilePublic: true,
      charactersPublic: true,
      chatHistory: false,
    },
    accessibility: {
      fontSize: 'medium' as const,
      highContrast: false,
      reduceMotion: false,
    },
    tts: {
      clientCall: false,
      provider: 'system',
      voice: 'default',
      speed: 1.0,
      pitch: 1.0,
    },
  };
};

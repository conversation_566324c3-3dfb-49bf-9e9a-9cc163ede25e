import api from './api';

// 角色生成参数接口
export interface GenerateCharacterParams {
  prompt: string;
  race?: string;
  anime_name?: string;
  age?: number;
  gender?: string;
  identity?: string;
  personality?: string;
}

// 角色保存参数接口
export interface SaveCharacterParams {
  name: string;
  image_url: string;
  age: number;
  gender?: string;
  personality: string;
  identity: string;
  appearance_params?: Record<string, any>;
  settings?: Record<string, any>;
  public?: boolean;
}

// 发送消息参数接口
export interface SendMessageParams {
  characterId: string;
  message: string;
  enable_tts?: boolean;  // 是否启用TTS
  voice_mode?: boolean;  // 是否为语音模式
}

// 获取聊天历史参数接口
export interface GetChatHistoryParams {
  characterId: string;
  page?: number;
  pageSize?: number;
}

// 分类接口
export interface Category {
  id: string;
  name: string;
  count: number;
  nsfw?: boolean;
  order: number;
}

// 获取推荐角色参数接口
export interface GetFeaturedCharactersParams {
  category?: string;
  query?: string;
  page?: number;
  pageSize?: number;
}

// 角色API服务
export const characterAPI = {
  // 生成角色图像
  generateImage: (params: GenerateCharacterParams) =>
    api.post('/characters/generate/', params),
  
  // 保存角色
  saveCharacter: (params: SaveCharacterParams) =>
    api.post('/characters/save/', params),

  // 获取用户角色列表
  getUserCharacters: () =>
    api.get('/characters/user/'),
  
  // 获取社区公开角色列表
  getPublicCharacters: (page = 1, pageSize = 10, query = '', sortBy = 'latest') =>
    api.get('/characters/public_list/', {
      params: {
        page,
        pageSize,
        query,
        sortBy
      }
    }),
  
  // 获取单个角色详情
  getCharacterDetail: (characterId: string) =>
    api.get(`/characters/${characterId}`),

  // 更新角色信息
  updateCharacter: (characterId: string, data: Partial<SaveCharacterParams>) =>
    api.put(`/characters/${characterId}/`, data),

  // 删除角色
  deleteCharacter: (characterId: string) =>
    api.delete(`/characters/${characterId}/`),

  // 发送消息给角色
  sendMessage: (params: SendMessageParams) =>
    api.post(`/characters/${params.characterId}/chat`, {
      user_message: params.message,
      enable_tts: params.enable_tts || false,
      voice_mode: params.voice_mode || false
    }),
    
  // 获取与角色的聊天历史
  getChatHistory: (params: GetChatHistoryParams) => 
    api.get('/chat/history', { params }),
    
  // 清除与角色的聊天历史
  clearChatHistory: (characterId: string) => 
    api.delete(`/chat/history/${characterId}`),
    
  // 点赞/取消点赞角色
  likeCharacter: (characterId: string, isLike: boolean) =>
    api.post(`/characters/${characterId}/like`, { isLike }),

  // 获取角色分类列表
  getCategories: () =>
    api.get('/categories/'),

  // 获取推荐角色列表（首页用）
  getFeaturedCharacters: (params: GetFeaturedCharactersParams = {}) =>
    api.get('/characters/featured/', { params }),

  // 按分类获取角色列表
  getCharactersByCategory: (categoryId: string, page = 1, pageSize = 20, sortBy = 'latest') =>
    api.get(`/characters/by-category/${categoryId}`, {
      params: { page, pageSize, sortBy }
    }),

  // 上传图片
  uploadImage: (base64Image: string) =>
    api.post('/upload/image/', { image: base64Image }),

  // 获取角色背景图片列表
  getCharacterBackgrounds: (characterId: string, params?: {
    status?: 'pending' | 'generating' | 'completed' | 'failed';
    scene_type?: string;
    page?: number;
    page_size?: number;
  }) =>
    api.get(`/characters/${characterId}/backgrounds/`, { params }),

  // 重试失败的背景图片生成
  retryBackgroundGeneration: (characterId: string) =>
    api.post(`/characters/${characterId}/backgrounds/retry/`),
};

export default characterAPI;
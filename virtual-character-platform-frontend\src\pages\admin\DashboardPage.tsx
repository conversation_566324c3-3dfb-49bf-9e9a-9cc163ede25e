import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Typography, List, Avatar, Tag, Spin } from 'antd';
import { 
  UserOutlined, 
  RobotOutlined, 
  FileTextOutlined, 
  ClockCircleOutlined,
  TeamOutlined,
  CodeOutlined,
  BookOutlined,
  CloudOutlined,
} from '@ant-design/icons';
import adminAPI from '../../services/adminAPI';

const { Title } = Typography;

const DashboardPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>({
    total_users: 0,
    active_users: 0,
    total_characters: 0,
    public_characters: 0,
    total_templates: 0,
    active_templates: 0,
    recent_logs: [],
    recent_users: [],
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await adminAPI.getDashboard();
        setDashboardData(data);
      } catch (error) {
        console.error('获取仪表盘数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // 处理操作日志类型标签颜色
  const getLogTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'create': 'success',
      'update': 'processing',
      'delete': 'error',
      'login': 'warning',
      'other': 'default',
    };
    return colorMap[type] || 'default';
  };

  // 操作日志表格列定义
  const logColumns = [
    {
      title: '操作者',
      dataIndex: 'user',
      key: 'user',
      render: (user: any) => user?.username || '-',
    },
    {
      title: '操作类型',
      dataIndex: 'operation_type',
      key: 'operation_type',
      render: (type: string) => (
        <Tag color={getLogTypeColor(type)}>
          {type === 'create' && '创建'}
          {type === 'update' && '更新'}
          {type === 'delete' && '删除'}
          {type === 'login' && '登录'}
          {type === 'other' && '其他'}
          {!['create', 'update', 'delete', 'login', 'other'].includes(type) && type}
        </Tag>
      ),
    },
    {
      title: '目标类型',
      dataIndex: 'target_type',
      key: 'target_type',
    },
    {
      title: '目标ID',
      dataIndex: 'target_id',
      key: 'target_id',
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div>
      <Title level={3}>仪表盘</Title>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic 
              title="总用户数" 
              value={dashboardData.total_users} 
              prefix={<TeamOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic 
              title="角色总数" 
              value={dashboardData.total_characters} 
              prefix={<RobotOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic 
              title="提示词模板" 
              value={dashboardData.total_templates} 
              prefix={<FileTextOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false}>
            <Statistic 
              title="公开角色" 
              value={dashboardData.public_characters} 
              prefix={<CloudOutlined />} 
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 最近操作日志 */}
        <Col xs={24} lg={16}>
          <Card 
            title={<><ClockCircleOutlined /> 最近操作日志</>} 
            bordered={false}
            style={{ height: '100%' }}
          >
            <Table 
              dataSource={dashboardData.recent_logs} 
              columns={logColumns} 
              rowKey="id" 
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        
        {/* 最近注册用户 */}
        <Col xs={24} lg={8}>
          <Card 
            title={<><UserOutlined /> 最近注册用户</>} 
            bordered={false}
            style={{ height: '100%' }}
          >
            <List
              itemLayout="horizontal"
              dataSource={dashboardData.recent_users}
              renderItem={(user: any) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={user.username}
                    description={`注册时间: ${user.date_joined}`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统信息 */}
      <Card 
        title={<><CodeOutlined /> 系统信息</>} 
        bordered={false}
        style={{ marginTop: 16 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Statistic title="系统版本" value="v1.0.0" />
          </Col>
          <Col span={8}>
            <Statistic title="数据库" value="PostgreSQL" />
          </Col>
          <Col span={8}>
            <Statistic title="API状态" value="正常" valueStyle={{ color: '#3f8600' }} />
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default DashboardPage; 
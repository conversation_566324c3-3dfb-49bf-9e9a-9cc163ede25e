import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import PersonalitySelector from './PersonalitySelector';
import IdentitySelector from './IdentitySelector';
import '../../styles/personality-identity-selector.css';

const { TabPane } = Tabs;

// 组件属性接口
interface PersonalityIdentitySelectorProps {
  personality?: string;
  identity?: string;
  onPersonalityChange?: (value: string) => void;
  onIdentityChange?: (value: string) => void;
}

/**
 * 性格与身份选择组件
 * 整合性格选择器和身份选择器，提供标签页切换
 */
const PersonalityIdentitySelector: React.FC<PersonalityIdentitySelectorProps> = ({
  personality,
  identity,
  onPersonalityChange,
  onIdentityChange
}) => {
  const [activeTab, setActiveTab] = useState<string>('personality');

  // 处理性格变更
  const handlePersonalityChange = (value: string) => {
    if (onPersonalityChange) {
      onPersonalityChange(value);
    }
  };

  // 处理身份变更
  const handleIdentityChange = (value: string) => {
    if (onIdentityChange) {
      onIdentityChange(value);
    }
  };

  return (
    <div className="personality-identity-selector">
      <Card className="selector-card" bordered={false}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          centered
          className="selector-tabs"
        >
          <TabPane 
            tab={
              <div className="tab-title">
                <span className="tab-icon">😊</span>
                <span>性格特质</span>
              </div>
            } 
            key="personality"
          >
            <div className="tab-description">
              <p>选择角色的性格特质，这将影响角色的对话风格和行为模式。</p>
            </div>
            <PersonalitySelector 
              value={personality} 
              onChange={handlePersonalityChange} 
            />
          </TabPane>
          
          <TabPane 
            tab={
              <div className="tab-title">
                <span className="tab-icon">👤</span>
                <span>身份设定</span>
              </div>
            } 
            key="identity"
          >
            <div className="tab-description">
              <p>选择角色的身份设定，这将影响角色的背景故事、知识领域和外观特征。</p>
            </div>
            <IdentitySelector 
              value={identity} 
              onChange={handleIdentityChange} 
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PersonalityIdentitySelector; 
"""
创建管理员用户和角色的脚本。
"""
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_character_platform.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import AdminRole, UserAdminRole

def create_admin_user():
    """创建管理员用户和角色"""
    # 创建超级管理员角色
    super_admin_role, created = AdminRole.objects.get_or_create(
        name='super_admin',
        defaults={'description': '超级管理员，拥有所有权限'}
    )
    if created:
        print(f"创建角色: {super_admin_role.name}")
    else:
        print(f"角色已存在: {super_admin_role.name}")
    
    # 创建内容管理员角色
    content_admin_role, created = AdminRole.objects.get_or_create(
        name='content_admin',
        defaults={'description': '内容管理员，负责管理角色和提示词模板'}
    )
    if created:
        print(f"创建角色: {content_admin_role.name}")
    else:
        print(f"角色已存在: {content_admin_role.name}")
    
    # 创建用户管理员角色
    user_admin_role, created = AdminRole.objects.get_or_create(
        name='user_admin',
        defaults={'description': '用户管理员，负责管理用户'}
    )
    if created:
        print(f"创建角色: {user_admin_role.name}")
    else:
        print(f"角色已存在: {user_admin_role.name}")
    
    # 创建或获取管理员用户
    admin_username = 'admin'
    admin_password = 'Admin@123'
    admin_email = '<EMAIL>'
    
    try:
        admin_user = User.objects.get(username=admin_username)
        print(f"用户已存在: {admin_username}")
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser(
            username=admin_username,
            email=admin_email,
            password=admin_password
        )
        print(f"创建用户: {admin_username}")
    
    # 为管理员用户分配超级管理员角色
    user_role, created = UserAdminRole.objects.get_or_create(
        user=admin_user,
        role=super_admin_role
    )
    if created:
        print(f"为用户 {admin_username} 分配角色: {super_admin_role.name}")
    else:
        print(f"用户 {admin_username} 已拥有角色: {super_admin_role.name}")
    
    print("\n管理员用户和角色创建完成！")
    print(f"用户名: {admin_username}")
    print(f"密码: {admin_password}")
    print(f"角色: {super_admin_role.name}")

if __name__ == '__main__':
    create_admin_user() 
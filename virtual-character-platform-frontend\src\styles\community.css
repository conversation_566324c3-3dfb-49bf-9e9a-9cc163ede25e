/* 社区页面样式 - 适配深色主题 */
.community-container {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  background: transparent;
}

/* 社区头部样式 */
.community-header {
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  padding: var(--spacing-xl) 0;
  background: var(--gradient-accent);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--color-border-light);
  backdrop-filter: blur(10px);
}

.community-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-primary);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.community-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.community-filters {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-lg);
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.search-input {
  width: 400px;
  max-width: 100%;
}

.search-input .ant-input-search {
  border-radius: var(--border-radius-lg);
}

.sort-select {
  width: 180px;
  min-width: 150px;
}

/* 角色卡片样式 */
.character-grid {
  margin-bottom: var(--spacing-2xl);
}

.character-card {
  transition: all var(--transition-normal);
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  background: var(--color-bg-container);
  border: 1px solid var(--color-border-primary);
  backdrop-filter: blur(10px);
  position: relative;
}

.character-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
  z-index: 1;
}

.character-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.character-card:hover::before {
  opacity: 1;
}

.character-image-container {
  height: 320px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: var(--color-bg-elevated);
}

.character-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.character-card:hover .character-image {
  transform: scale(1.08);
}

.character-info {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  position: relative;
  z-index: 2;
}

.character-creator {
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

.character-trait {
  color: var(--color-text-secondary);
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.character-trait::before {
  content: '•';
  color: var(--color-primary);
  font-weight: bold;
}

/* 卡片操作按钮样式 */
.card-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  color: var(--color-text-secondary);
  position: relative;
  z-index: 2;
}

.card-action:hover {
  background: var(--color-primary-light);
  color: var(--color-primary);
  transform: scale(1.05);
}

.card-action .anticon {
  font-size: 20px;
  transition: all var(--transition-fast);
}

.card-action .liked {
  color: var(--color-primary);
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 分页样式 */
.pagination-container {
  text-align: center;
  margin-top: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background: var(--color-bg-container);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-primary);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: var(--color-bg-container);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-primary);
}

/* 空状态样式 */
.empty-container {
  margin: var(--spacing-3xl) 0;
  padding: var(--spacing-2xl);
  background: var(--color-bg-container);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-primary);
}

/* 角色卡片标题样式 */
.character-card .ant-card-meta-title {
  color: var(--color-text-primary);
  font-weight: 600;
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-xs);
}

.character-card .ant-card-meta-description {
  color: var(--color-text-secondary);
}

.character-card .ant-card-body {
  background: transparent;
  border-top: 1px solid var(--color-border-primary);
  position: relative;
  z-index: 2;
}

.character-card .ant-card-actions {
  background: transparent;
  border-top: 1px solid var(--color-border-primary);
  position: relative;
  z-index: 2;
}

.character-card .ant-card-actions li {
  margin: 0;
}

/* 统计信息样式 */
.community-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--color-bg-elevated);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-light);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-primary);
  display: block;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  margin-top: var(--spacing-xs);
}

/* 移除移动端适配 - 专注桌面端体验 */
# TTS服务配置示例
# 复制此文件为 .env 并填入实际的API密钥

# TTS默认服务商 (xun<PERSON><PERSON>, aliyun, baidu, tencent)
TTS_DEFAULT_PROVIDER=xunfei

# 讯飞TTS配置
XUNFEI_TTS_APP_ID=your_xunfei_app_id
XUNFEI_TTS_API_KEY=your_xunfei_api_key
XUNFEI_TTS_API_SECRET=your_xunfei_api_secret

# 阿里云TTS配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret

# 百度TTS配置
BAIDU_TTS_API_KEY=your_baidu_api_key
BAIDU_TTS_SECRET_KEY=your_baidu_secret_key

# 腾讯云TTS配置
TENCENT_SECRET_ID=your_tencent_secret_id
TENCENT_SECRET_KEY=your_tencent_secret_key

# TTS功能开关
TTS_ENABLED=true

# 音频文件存储配置
AUDIO_STORAGE_PATH=/tmp/audio/
AUDIO_BASE_URL=https://your-domain.com/audio/

import React, { useState } from 'react';
import { Upload, Button, Card, Form, Slider, Select, Space, message } from 'antd';
import { UploadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

import { useAgentStore, agentSelectors } from '../../../store/agent';
import { useGlobalStore } from '../../../store/global';

const { Option } = Select;

// 预设的3D模型选项
const PRESET_MODELS = [
  { value: 'default_female', label: '默认女性模型', preview: '/models/default_female.jpg' },
  { value: 'default_male', label: '默认男性模型', preview: '/models/default_male.jpg' },
  { value: 'anime_girl', label: '动漫女孩', preview: '/models/anime_girl.jpg' },
  { value: 'business_woman', label: '职业女性', preview: '/models/business_woman.jpg' },
  { value: 'casual_man', label: '休闲男性', preview: '/models/casual_man.jpg' },
];

// 动画预设
const ANIMATION_PRESETS = [
  { value: 'idle', label: '待机动画' },
  { value: 'talking', label: '说话动画' },
  { value: 'happy', label: '开心动画' },
  { value: 'thinking', label: '思考动画' },
  { value: 'greeting', label: '问候动画' },
];

const ShellTab: React.FC = () => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  
  // 获取当前角色数据和全局状态
  const [currentAgent, updateAgentMeta] = useAgentStore((s) => [
    agentSelectors.currentAgentItem(s),
    s.updateAgentMeta,
  ]);
  
  const { viewer } = useGlobalStore();

  // 当前外观配置
  const currentShell = {
    model: currentAgent?.meta.model || '',
    animationSpeed: 1,
    expressionIntensity: 1,
    defaultAnimation: 'idle',
    ...currentAgent?.params
  };

  // 处理3D模型上传
  const handleModelUpload = async (file: File) => {
    if (!file.name.endsWith('.vrm')) {
      message.error('请上传VRM格式的3D模型文件');
      return false;
    }

    try {
      setUploading(true);
      
      // 创建本地URL用于预览
      const modelUrl = URL.createObjectURL(file);
      
      // 更新角色模型
      updateAgentMeta({ model: modelUrl });
      
      // 加载到3D查看器
      if (viewer) {
        await viewer.loadVrm(modelUrl);
      }
      
      message.success('3D模型上传成功');
      
    } catch (error) {
      console.error('模型上传失败:', error);
      message.error('模型上传失败，请检查文件格式');
    } finally {
      setUploading(false);
    }
    
    return false; // 阻止默认上传行为
  };

  // 选择预设模型
  const handlePresetModelSelect = (modelValue: string) => {
    const preset = PRESET_MODELS.find(m => m.value === modelValue);
    if (preset) {
      updateAgentMeta({ model: `/models/${modelValue}.vrm` });
      message.success(`已选择${preset.label}`);
    }
  };

  // 删除当前模型
  const handleRemoveModel = () => {
    updateAgentMeta({ model: '' });
    message.success('已移除3D模型');
  };

  // 预览模型
  const handlePreviewModel = () => {
    if (currentShell.model && viewer) {
      viewer.loadVrm(currentShell.model);
      message.info('正在加载3D模型预览...');
    }
  };

  // 处理参数变更
  const handleParamChange = (field: string, value: any) => {
    const newParams = {
      ...currentAgent?.params,
      [field]: value,
    };
    updateAgentMeta({ params: newParams });
  };

  return (
    <div className="shell-tab">
      <Flexbox gap={24} style={{ padding: '24px' }}>
        {/* 3D模型管理 */}
        <div className="shell-section">
          <h3>3D模型</h3>
          <p className="section-desc">上传或选择角色的3D模型文件</p>
          
          <Card>
            <div className="model-upload">
              {currentShell.model ? (
                <div className="current-model">
                  <div className="model-info">
                    <p><strong>当前模型:</strong> {currentShell.model.split('/').pop()}</p>
                    <Space>
                      <Button 
                        icon={<EyeOutlined />}
                        onClick={handlePreviewModel}
                      >
                        预览
                      </Button>
                      <Button 
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleRemoveModel}
                      >
                        移除
                      </Button>
                    </Space>
                  </div>
                </div>
              ) : (
                <div className="no-model">
                  <p>暂未设置3D模型</p>
                </div>
              )}
              
              <div style={{ marginTop: 16 }}>
                <Upload
                  accept=".vrm"
                  beforeUpload={handleModelUpload}
                  showUploadList={false}
                >
                  <Button 
                    icon={<UploadOutlined />}
                    loading={uploading}
                  >
                    上传VRM模型
                  </Button>
                </Upload>
              </div>
            </div>
          </Card>
        </div>

        {/* 预设模型选择 */}
        <div className="shell-section">
          <h3>预设模型</h3>
          <p className="section-desc">选择系统提供的预设3D模型</p>
          
          <Select
            placeholder="选择预设模型"
            style={{ width: '100%' }}
            onChange={handlePresetModelSelect}
          >
            {PRESET_MODELS.map(model => (
              <Option key={model.value} value={model.value}>
                {model.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* 动画设置 */}
        <div className="shell-section">
          <h3>动画设置</h3>
          <p className="section-desc">配置角色的动画和表情参数</p>
          
          <Form layout="vertical">
            <Form.Item label="默认动画">
              <Select
                value={currentShell.defaultAnimation}
                onChange={(value) => handleParamChange('defaultAnimation', value)}
              >
                {ANIMATION_PRESETS.map(anim => (
                  <Option key={anim.value} value={anim.value}>
                    {anim.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label={`动画速度: ${currentShell.animationSpeed}`}>
              <Slider
                min={0.5}
                max={2}
                step={0.1}
                value={currentShell.animationSpeed}
                onChange={(value) => handleParamChange('animationSpeed', value)}
                marks={{
                  0.5: '慢',
                  1: '正常',
                  2: '快'
                }}
              />
            </Form.Item>

            <Form.Item label={`表情强度: ${currentShell.expressionIntensity}`}>
              <Slider
                min={0}
                max={2}
                step={0.1}
                value={currentShell.expressionIntensity}
                onChange={(value) => handleParamChange('expressionIntensity', value)}
                marks={{
                  0: '无',
                  1: '正常',
                  2: '强烈'
                }}
              />
            </Form.Item>
          </Form>
        </div>

        {/* 外观配置说明 */}
        <div className="shell-section">
          <h3>配置说明</h3>
          <Card>
            <div className="shell-guide">
              <h4>3D模型要求：</h4>
              <ul>
                <li><strong>文件格式</strong>: 仅支持VRM格式的3D模型文件</li>
                <li><strong>文件大小</strong>: 建议不超过50MB，以确保加载性能</li>
                <li><strong>模型质量</strong>: 建议使用优化过的低面数模型</li>
                <li><strong>纹理贴图</strong>: 确保纹理已正确嵌入VRM文件中</li>
              </ul>

              <h4>动画设置说明：</h4>
              <ul>
                <li><strong>默认动画</strong>: 角色在待机状态下的循环动画</li>
                <li><strong>动画速度</strong>: 控制所有动画的播放速度</li>
                <li><strong>表情强度</strong>: 控制面部表情的变化幅度</li>
              </ul>

              <h4>性能优化建议：</h4>
              <ul>
                <li>使用经过优化的VRM模型以提高渲染性能</li>
                <li>避免过于复杂的材质和特效</li>
                <li>在不同设备上测试3D模型的显示效果</li>
                <li>考虑为低性能设备提供简化版本</li>
              </ul>

              <h4>获取VRM模型：</h4>
              <ul>
                <li><strong>VRoid Studio</strong>: 免费的3D角色创建工具</li>
                <li><strong>Booth</strong>: 日本的3D模型交易平台</li>
                <li><strong>VRChat</strong>: 社区分享的VRM模型资源</li>
                <li><strong>自制模型</strong>: 使用Blender等工具制作并导出为VRM</li>
              </ul>
            </div>
          </Card>
        </div>
      </Flexbox>
    </div>
  );
};

export default ShellTab;

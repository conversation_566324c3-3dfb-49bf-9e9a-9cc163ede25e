{"apiKeyMiss": "La chiave API di OpenAI è vuota, si prega di aggiungere una chiave API di OpenAI personalizzata", "dancePlayError": "Impossibile riprodurre il file di danza, riprova più tardi", "error": "Errore", "errorTip": {"clearSession": "Cancella i messaggi di sessione", "description": "Il progetto è attualmente in fase di sviluppo, non garantiamo la stabilità dei dati. Se riscontri problemi, puoi provare", "forgive": "ci scusiamo per l'inconveniente", "or": "o", "problem": "Si è verificato un piccolo problema con la pagina...", "resetSystem": "Ripristina le impostazioni di sistema"}, "fileUploadError": "Caricamento del file non riuscito, riprova più tardi", "formValidationFailed": "La convalida del modulo è fallita:", "goBack": "Torna alla home", "openaiError": "Erro API OpenAI, si prega di controllare se la chiave API OpenAI e l'endpoint sono corretti", "reload": "Ricarica", "response": {"400": "<PERSON><PERSON> dispiace, il server non comprende la tua richiesta, per favore verifica che i parametri della tua richiesta siano corretti", "401": "<PERSON><PERSON> dispiace, il server ha rifiutato la tua richiesta, potrebbe essere a causa di autorizzazioni insufficienti o di un'autenticazione non valida", "403": "<PERSON><PERSON> dispiace, il server ha rifiutato la tua richiesta, non hai accesso a questo contenuto", "404": "<PERSON><PERSON> dispiace, il server non riesce a trovare la pagina o la risorsa richiesta, per favore verifica che il tuo URL sia corretto", "405": "<PERSON><PERSON> di<PERSON><PERSON>e, il server non supporta il metodo di richiesta che stai utilizzando, per favore verifica che il tuo metodo di richiesta sia corretto", "406": "<PERSON>i dispiace, il server non può completare la richiesta in base alle caratteristiche del contenuto che hai richiesto", "407": "<PERSON>i dispiace, è necessaria un'autenticazione proxy per continuare con questa richiesta", "408": "<PERSON><PERSON> dispiace, il server ha superato il timeout mentre attendeva la richiesta, per favore controlla la tua connessione di rete e riprova", "409": "Ci dispiace, c'è un conflitto nella richiesta che non può essere elaborato, potrebbe essere a causa dello stato della risorsa incompatibile con la richiesta", "410": "Ci dispiace, la risorsa richiesta è stata rimossa permanentemente e non può essere trovata", "411": "<PERSON><PERSON> dispiace, il server non può elaborare una richiesta senza una lunghezza di contenuto valida", "412": "Ci dispiace, la tua richiesta non soddisfa le condizioni del server e non può essere completata", "413": "<PERSON>i dispiace, la quantità di dati nella tua richiesta è troppo grande, il server non può elaborarla", "414": "<PERSON><PERSON> di<PERSON><PERSON>, l'URI della tua richiesta è troppo lungo, il server non può elaborarla", "415": "Ci dispiace, il server non può elaborare il formato media allegato alla richiesta", "416": "<PERSON><PERSON> dispiace, il server non può soddisfare l'intervallo richiesto", "417": "<PERSON><PERSON> dispiace, il server non può soddisfare le tue aspettative", "422": "<PERSON>i dispiace, il formato della tua richiesta è corretto, ma a causa di errori semantici non può essere elaborato", "423": "<PERSON>i dispiace, la risorsa richiesta è bloccata", "424": "Ci dispiace, la richiesta corrente non può essere completata a causa del fallimento della richiesta precedente", "426": "<PERSON><PERSON> dispiace, il server richiede che il tuo client venga aggiornato a una versione di protocollo superiore", "428": "<PERSON><PERSON> dispiace, il server richiede condizioni preliminari, la tua richiesta deve contenere le intestazioni di condizione corrette", "429": "<PERSON><PERSON> dispiace, hai inviato troppe richieste, il server è un po' sovraccarico, per favore riprova più tardi", "431": "<PERSON>i dispiace, i campi dell'intestazione della tua richiesta sono troppo grandi, il server non può elaborarli", "451": "<PERSON><PERSON> dispiace, per motivi legali, il server rifiuta di fornire questa risorsa", "500": "<PERSON><PERSON> dispiace, il server sembra aver incontrato alcune difficoltà e non può completare la tua richiesta, per favore riprova più tardi", "501": "<PERSON><PERSON> dispiace, il server non sa ancora come gestire questa richiesta, per favore verifica che la tua operazione sia corretta", "502": "<PERSON><PERSON> dispiace, il server sembra essersi perso, non può fornire il servizio al momento, per favore riprova più tardi", "503": "<PERSON><PERSON> dispiace, il server attualmente non può elaborare la tua richiesta, potrebbe essere a causa di sovraccarico o manutenzione in corso, per favore riprova più tardi", "504": "<PERSON>i dispiace, il server non ha ricevuto una risposta dal server upstream, per favore riprova più tardi", "505": "<PERSON><PERSON> dispiace, il server non supporta la versione HTTP che stai utilizzando, per favore aggiorna e riprova", "506": "Ci dispiace, c'è un problema di configurazione del server, contatta l'amministratore per risolverlo", "507": "<PERSON><PERSON> dispiace, il server ha esaurito lo spazio di archiviazione e non può elaborare la tua richiesta, per favore riprova più tardi", "509": "<PERSON><PERSON> disp<PERSON>e, la larghezza di banda del server è esaurita, per favore riprova più tardi", "510": "<PERSON>i dispiace, il server non supporta le funzionalità di estensione richieste, contatta l'amministratore", "524": "<PERSON><PERSON> di<PERSON><PERSON>e, il server ha superato il timeout mentre aspettava una risposta, potrebbe essere a causa di una risposta lenta, per favore riprova più tardi", "AgentRuntimeError": "Si è verificato un errore durante l'esecuzione di Lobe AI Runtime, per favore verifica le informazioni qui sotto o riprova", "FreePlanLimit": "Attualmente sei un utente gratuito e non puoi utilizzare questa funzionalità, per favore passa a un piano a pagamento per continuare a utilizzare", "InvalidAccessCode": "La password è errata o vuota, per favore inserisci la password di accesso corretta o aggiungi una chiave API personalizzata", "InvalidBedrockCredentials": "L'autenticazione Bedrock non è riuscita, per favore verifica l'AccessKeyId/SecretAccessKey e riprova", "InvalidClerkUser": "Ci dispiace, non hai ancora effettuato il login, per favore effettua il login o registrati prima di continuare", "InvalidGithubToken": "Il PAT di Github è errato o vuoto, per favore verifica il PAT di Github e riprova", "InvalidOllamaArgs": "La configurazione di Ollama non è corretta, per favore verifica la configurazione di Ollama e riprova", "InvalidProviderAPIKey": "{{provider}} La chiave API è errata o vuota, per favore verifica la chiave API {{provider}} e riprova", "LocationNotSupportError": "Ci dispiace, la tua area non supporta questo servizio di modello, potrebbe essere a causa di restrizioni regionali o del servizio non attivato. Per favore verifica se l'area attuale supporta questo servizio o prova a cambiare area e riprovare.", "OllamaBizError": "Si è verificato un errore nella richiesta del servizio <PERSON>, per favore verifica le informazioni qui sotto o riprova", "OllamaServiceUnavailable": "La connessione al servizio Ollama è fallita, per favore verifica se Ollama è in esecuzione correttamente o se la configurazione CORS di Ollama è impostata correttamente", "PermissionDenied": "<PERSON>i dispiace, non hai il permesso di accedere a questo servizio, per favore verifica che la tua chiave abbia i permessi di accesso", "PluginApiNotFound": "Ci dispiace, l'API non esiste nel manifesto del plugin, per favore verifica che il tuo metodo di richiesta corrisponda all'API del manifesto del plugin", "PluginApiParamsError": "Ci dispiace, la verifica dei parametri di input della richiesta del plugin non è riuscita, per favore verifica che i parametri corrispondano alle informazioni di descrizione dell'API", "PluginFailToTransformArguments": "Ci dispiace, la parsificazione dei parametri di chiamata del plugin è fallita, per favore prova a rigenerare il messaggio dell'assistente o riprova dopo aver cambiato a un modello AI più potente con capacità di chiamata degli strumenti", "PluginGatewayError": "<PERSON>i dispiace, si è verificato un errore nel gateway del plugin, per favore verifica che la configurazione del gateway del plugin sia corretta", "PluginManifestInvalid": "Ci dispiace, la verifica del manifesto del plugin non è riuscita, per favore verifica che il formato del manifesto sia conforme", "PluginManifestNotFound": "<PERSON><PERSON> dispiace, il server non ha trovato il manifesto del plugin (manifest.json), per favore verifica che l'indirizzo del file di descrizione del plugin sia corretto", "PluginMarketIndexInvalid": "Ci dispiace, la verifica dell'indice del plugin non è riuscita, per favore verifica che il formato del file dell'indice sia conforme", "PluginMarketIndexNotFound": "<PERSON><PERSON> dispiace, il server non ha trovato l'indice del plugin, per favore verifica che l'indirizzo dell'indice sia corretto", "PluginMetaInvalid": "Ci dispiace, la verifica delle informazioni meta del plugin non è riuscita, per favore verifica che il formato delle informazioni meta del plugin sia conforme", "PluginMetaNotFound": "Ci dispiace, non è stato trovato il plugin nell'indice, per favore controlla le informazioni di configurazione del plugin nell'indice", "PluginOpenApiInitError": "Ci dispiace, l'inizializzazione del client OpenAPI è fallita, per favore verifica che le informazioni di configurazione di OpenAPI siano corrette", "PluginServerError": "Si è verificato un errore nella richiesta del server del plugin, per favore controlla le informazioni di errore qui sotto per verificare il tuo file di descrizione del plugin, la configurazione del plugin o l'implementazione del server", "PluginSettingsInvalid": "Questo plugin deve essere configurato correttamente prima di poter essere utilizzato, per favore verifica che la tua configurazione sia corretta", "ProviderBizError": "Si è verificato un errore nella richiesta del servizio {{provider}}, per favore verifica le informazioni qui sotto o riprova", "QuotaLimitReached": "<PERSON>i dispiace, l'uso attuale del token o il numero di richieste ha raggiunto il limite di quota per questa chiave, per favore aumenta la quota di questa chiave o riprova più tardi", "StreamChunkError": "Errore nella parsificazione del blocco di messaggi della richiesta in streaming, per favore verifica che l'API corrente sia conforme agli standard o contatta il tuo fornitore di API per assistenza", "SubscriptionPlanLimit": "Il tuo limite di abbonamento è esaurito, non puoi utilizzare questa funzionalità, per favore passa a un piano superiore o acquista un pacchetto risorse per continuare a utilizzare", "UnknownChatFetchError": "<PERSON>i dispiace, si è verificato un errore di richiesta sconosciuto, per favore verifica le informazioni qui sotto o riprova"}, "s3envError": "Le variabili d'ambiente S3 non sono completamente impostate, si prega di controllare le proprie variabili d'ambiente", "serverError": "Erro del server, contattare l'amministratore", "triggerError": "Errore di attivazione", "ttsTransformFailed": "La conversione vocale è fallita, controlla la connessione di rete o prova ad attivare l'app nelle impostazioni.", "unknownError": "<PERSON><PERSON>", "unlock": {"addProxyUrl": "Aggiungi indirizzo proxy OpenAI (opzionale)", "apiKey": {"description": "Inserisci la tua {{name}} API Key per iniziare la conversazione", "title": "Usa la {{name}} API Key personalizzata"}, "closeMessage": "<PERSON>udi il messaggio", "confirm": "Conferma e riprova", "oauth": {"description": "L'amministratore ha attivato l'autenticazione unificata, clicca sul pulsante qui sotto per accedere e sbloccare l'applicazione", "success": "<PERSON><PERSON> r<PERSON>", "title": "Accedi al tuo account", "welcome": "Benvenuto!"}, "password": {"description": "L'amministratore ha attivato la crittografia dell'app, inserisci la password dell'app per sbloccarla. La password deve essere inserita solo una volta", "placeholder": "Inser<PERSON><PERSON> la password", "title": "Inserisci la password per sbloccare l'app"}, "tabs": {"apiKey": "API Key personalizzata", "password": "Password"}}}
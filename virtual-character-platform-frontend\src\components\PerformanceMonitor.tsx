import React, { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

interface PerformanceMonitorProps {
  onMetrics?: (metrics: Partial<PerformanceMetrics>) => void;
  enableLogging?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  onMetrics,
  enableLogging = false
}) => {
  const metricsRef = useRef<Partial<PerformanceMetrics>>({});

  useEffect(() => {
    // 检查浏览器支持
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    const reportMetric = (name: keyof PerformanceMetrics, value: number) => {
      metricsRef.current[name] = value;
      
      if (enableLogging) {
        console.log(`Performance Metric - ${name}:`, value);
      }
      
      onMetrics?.(metricsRef.current);
    };

    // 监控 Paint 指标
    const paintObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          reportMetric('fcp', entry.startTime);
        }
      }
    });

    try {
      paintObserver.observe({ entryTypes: ['paint'] });
    } catch (e) {
      console.warn('Paint observer not supported');
    }

    // 监控 LCP
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      reportMetric('lcp', lastEntry.startTime);
    });

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      console.warn('LCP observer not supported');
    }

    // 监控 FID
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        reportMetric('fid', (entry as any).processingStart - entry.startTime);
      }
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      console.warn('FID observer not supported');
    }

    // 监控 CLS
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
          reportMetric('cls', clsValue);
        }
      }
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      console.warn('CLS observer not supported');
    }

    // 计算 TTFB
    const navigationEntries = performance.getEntriesByType('navigation');
    if (navigationEntries.length > 0) {
      const navEntry = navigationEntries[0] as PerformanceNavigationTiming;
      const ttfb = navEntry.responseStart - navEntry.requestStart;
      reportMetric('ttfb', ttfb);
    }

    // 监控内存使用情况（如果支持）
    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        if (enableLogging) {
          console.log('Memory Usage:', {
            used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
            total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
            limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
          });
        }
      }
    };

    // 每30秒监控一次内存
    const memoryInterval = setInterval(monitorMemory, 30000);

    // 监控长任务
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (enableLogging) {
          console.warn('Long Task detected:', {
            duration: entry.duration,
            startTime: entry.startTime
          });
        }
      }
    });

    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      console.warn('Long task observer not supported');
    }

    // 页面可见性变化监控
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // 页面隐藏时发送最终指标
        onMetrics?.(metricsRef.current);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      paintObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
      longTaskObserver.disconnect();
      clearInterval(memoryInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onMetrics, enableLogging]);

  // 提供性能建议
  useEffect(() => {
    if (!enableLogging) return;

    const checkPerformance = () => {
      const metrics = metricsRef.current;
      const suggestions: string[] = [];

      if (metrics.fcp && metrics.fcp > 2500) {
        suggestions.push('FCP过慢，考虑优化关键渲染路径');
      }

      if (metrics.lcp && metrics.lcp > 4000) {
        suggestions.push('LCP过慢，考虑优化图片加载和服务器响应时间');
      }

      if (metrics.fid && metrics.fid > 300) {
        suggestions.push('FID过高，考虑减少JavaScript执行时间');
      }

      if (metrics.cls && metrics.cls > 0.25) {
        suggestions.push('CLS过高，考虑为图片和广告预留空间');
      }

      if (metrics.ttfb && metrics.ttfb > 800) {
        suggestions.push('TTFB过慢，考虑优化服务器响应时间');
      }

      if (suggestions.length > 0) {
        console.group('Performance Suggestions:');
        suggestions.forEach(suggestion => console.warn(suggestion));
        console.groupEnd();
      }
    };

    const suggestionTimer = setTimeout(checkPerformance, 5000);
    return () => clearTimeout(suggestionTimer);
  }, [enableLogging]);

  return null; // 这是一个无UI的监控组件
};

export default PerformanceMonitor;

import React, { useState } from 'react';
import { Drawer, List, Avatar, Button, Input, Space, Popconfirm, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import { useAgentStore, agentSelectors } from '../../store/agent';

const { Search } = Input;

interface RoleSideBarProps {
  onClose: () => void;
}

const RoleSideBar: React.FC<RoleSideBarProps> = ({ onClose }) => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  
  // 获取角色数据
  const {
    localAgentList,
    currentIdentifier,
    activateAgent,
    removeLocalAgent,
    createNewAgent
  } = useAgentStore();

  // 过滤角色列表
  const filteredAgents = localAgentList.filter(agent =>
    agent.meta.name.toLowerCase().includes(searchText.toLowerCase()) ||
    (agent.meta.description || '').toLowerCase().includes(searchText.toLowerCase()) ||
    (agent.meta.tags || []).some(tag => 
      tag.toLowerCase().includes(searchText.toLowerCase())
    )
  );

  // 创建新角色
  const handleCreateNew = () => {
    navigate('/role/edit/new');
    onClose();
  };

  // 编辑角色
  const handleEditAgent = (agentId: string) => {
    navigate(`/role/edit/${agentId}`);
    onClose();
  };

  // 激活角色
  const handleActivateAgent = (agentId: string) => {
    activateAgent(agentId);
    message.success('已切换角色');
  };

  // 删除角色
  const handleDeleteAgent = async (agentId: string) => {
    try {
      await removeLocalAgent(agentId);
      message.success('角色删除成功');
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error('删除角色失败');
    }
  };

  return (
    <Drawer
      title="角色管理"
      placement="left"
      onClose={onClose}
      open={true}
      width={320}
    >
      <div className="role-sidebar">
        {/* 搜索框 */}
        <div style={{ marginBottom: 16 }}>
          <Search
            placeholder="搜索角色..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </div>

        {/* 创建新角色按钮 */}
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          block
          onClick={handleCreateNew}
          style={{ marginBottom: 16 }}
        >
          创建新角色
        </Button>

        {/* 角色列表 */}
        <List
          dataSource={filteredAgents}
          renderItem={(agent) => {
            const isActive = agent.agentId === currentIdentifier;
            
            return (
              <List.Item
                className={`role-item ${isActive ? 'active' : ''}`}
                style={{
                  cursor: 'pointer',
                  background: isActive ? 'var(--color-primary-bg)' : 'transparent',
                  borderRadius: 8,
                  margin: '4px 0',
                  padding: '12px',
                  border: isActive ? '1px solid var(--color-primary)' : '1px solid transparent'
                }}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      src={agent.meta.avatar} 
                      size={40}
                    >
                      {agent.meta.name?.[0]}
                    </Avatar>
                  }
                  title={
                    <div 
                      onClick={() => handleActivateAgent(agent.agentId)}
                      style={{ 
                        fontWeight: isActive ? 'bold' : 'normal',
                        color: isActive ? 'var(--color-primary)' : 'inherit'
                      }}
                    >
                      {agent.meta.name}
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: '#666',
                        marginBottom: 4,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {agent.meta.description || '暂无描述'}
                      </div>
                      
                      {agent.meta.tags && agent.meta.tags.length > 0 && (
                        <div style={{ fontSize: '11px', color: '#999' }}>
                          {agent.meta.tags.slice(0, 2).join(', ')}
                          {agent.meta.tags.length > 2 && '...'}
                        </div>
                      )}
                    </div>
                  }
                />
                
                <div className="role-actions">
                  <Space size="small">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditAgent(agent.agentId);
                      }}
                      title="编辑角色"
                    />
                    
                    <Popconfirm
                      title="确定要删除这个角色吗？"
                      description="删除后无法恢复"
                      onConfirm={(e) => {
                        e?.stopPropagation();
                        handleDeleteAgent(agent.agentId);
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={(e) => e.stopPropagation()}
                        title="删除角色"
                      />
                    </Popconfirm>
                  </Space>
                </div>
              </List.Item>
            );
          }}
        />

        {filteredAgents.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            color: '#999', 
            padding: '40px 20px' 
          }}>
            {searchText ? '没有找到匹配的角色' : '暂无角色，点击上方按钮创建'}
          </div>
        )}
      </div>
    </Drawer>
  );
};

export default RoleSideBar;

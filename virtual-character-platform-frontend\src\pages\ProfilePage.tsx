import React, { useState, useEffect } from 'react';
import { Layout, Card, Row, Col, Spin, message, Empty, Button, Avatar, Tabs, Modal, Form, Input, Switch, Upload, Tooltip, Tag } from 'antd';
import { UserOutlined, EditOutlined, DeleteOutlined, EyeOutlined, MessageOutlined, PlusOutlined, CameraOutlined, SettingOutlined, HeartOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { characterAPI } from '../services/characterAPI';
import useAuthStore from '../store/authStore';
import '../styles/profile.css';

const { Content } = Layout;
const { TabPane } = Tabs;
const { confirm } = Modal;

// 用户角色类型定义
interface UserCharacter {
  id: string;
  name: string;
  imageUrl: string;
  personality?: string;
  identity?: string;
  createdAt: string;
  likes: number;
  chatCount: number;
  viewCount: number;
  isPublic: boolean;
  tags?: string[];
}

// 用户信息类型定义
interface UserProfile {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  bio?: string;
  joinDate: string;
  totalCharacters: number;
  totalLikes: number;
  totalChats: number;
}

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { userInfo: user } = useAuthStore();
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [userCharacters, setUserCharacters] = useState<UserCharacter[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [activeTab, setActiveTab] = useState('characters');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editForm] = Form.useForm();
  const [avatarUploading, setAvatarUploading] = useState(false);

  // 获取用户资料
  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      // 这里应该调用真实的API
      // const response = await userAPI.getProfile();
      // setUserProfile(response.data);
      
      // 模拟数据
      setUserProfile({
        id: user?.id || '1',
        username: user?.username || '用户名',
        email: user?.email || '<EMAIL>',
        avatar: user?.avatar || '',
        bio: '这是一个简短的个人介绍...',
        joinDate: '2024-01-01',
        totalCharacters: 5,
        totalLikes: 128,
        totalChats: 1024
      });
    } catch (error) {
      message.error('获取用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户创建的角色
  const fetchUserCharacters = async () => {
    try {
      setLoading(true);
      const response = await characterAPI.getUserCharacters();
      setUserCharacters(response.data || []);
    } catch (error) {
      message.error('获取角色列表失败');
      setUserCharacters([]);
    } finally {
      setLoading(false);
    }
  };

  // 删除角色
  const handleDeleteCharacter = async (characterId: string) => {
    confirm({
      title: '确认删除',
      content: '确定要删除这个角色吗？删除后无法恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          await characterAPI.deleteCharacter(characterId);
          message.success('角色删除成功');
          fetchUserCharacters(); // 重新获取列表
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  // 编辑角色
  const handleEditCharacter = (characterId: string) => {
    navigate(`/character-creation?edit=${characterId}`);
  };

  // 查看角色详情
  const handleViewCharacter = (characterId: string) => {
    navigate(`/character/${characterId}`);
  };

  // 与角色聊天
  const handleChatWithCharacter = (characterId: string) => {
    navigate(`/chat/${characterId}`);
  };

  // 切换角色公开状态
  const handleTogglePublic = async (characterId: string, isPublic: boolean) => {
    try {
      await characterAPI.updateCharacter(characterId, { isPublic });
      message.success(`角色已${isPublic ? '公开' : '设为私有'}`);
      fetchUserCharacters(); // 重新获取列表
    } catch (error) {
      message.error('更新失败');
    }
  };

  // 保存用户资料
  const handleSaveProfile = async (values: any) => {
    try {
      // 这里应该调用真实的API
      // await userAPI.updateProfile(values);
      message.success('资料更新成功');
      setEditModalVisible(false);
      fetchUserProfile(); // 重新获取用户资料
    } catch (error) {
      message.error('更新失败');
    }
  };

  // 头像上传处理
  const handleAvatarUpload = async (file: any) => {
    try {
      setAvatarUploading(true);
      // 这里应该调用真实的上传API
      // const response = await uploadAPI.uploadAvatar(file);
      message.success('头像上传成功');
      fetchUserProfile(); // 重新获取用户资料
    } catch (error) {
      message.error('头像上传失败');
    } finally {
      setAvatarUploading(false);
    }
  };

  useEffect(() => {
    fetchUserProfile();
    fetchUserCharacters();
  }, []);

  // 渲染用户信息卡片
  const renderUserInfoCard = () => (
    <Card className="user-info-card">
      <div className="user-info-header">
        <div className="avatar-section">
          <Avatar 
            size={80} 
            src={userProfile?.avatar} 
            icon={<UserOutlined />}
          />
          <Upload
            showUploadList={false}
            beforeUpload={handleAvatarUpload}
            accept="image/*"
          >
            <Button 
              icon={<CameraOutlined />} 
              size="small" 
              className="avatar-upload-btn"
              loading={avatarUploading}
            >
              更换头像
            </Button>
          </Upload>
        </div>
        <div className="user-info-content">
          <h2>{userProfile?.username}</h2>
          <p className="user-email">{userProfile?.email}</p>
          <p className="user-bio">{userProfile?.bio}</p>
          <p className="join-date">加入时间：{userProfile?.joinDate}</p>
        </div>
        <Button 
          icon={<EditOutlined />} 
          onClick={() => setEditModalVisible(true)}
        >
          编辑资料
        </Button>
      </div>
      
      <Row gutter={16} className="user-stats">
        <Col span={8}>
          <div className="stat-item">
            <div className="stat-number">{userProfile?.totalCharacters}</div>
            <div className="stat-label">创建角色</div>
          </div>
        </Col>
        <Col span={8}>
          <div className="stat-item">
            <div className="stat-number">{userProfile?.totalLikes}</div>
            <div className="stat-label">获得点赞</div>
          </div>
        </Col>
        <Col span={8}>
          <div className="stat-item">
            <div className="stat-number">{userProfile?.totalChats}</div>
            <div className="stat-label">聊天次数</div>
          </div>
        </Col>
      </Row>
    </Card>
  );

  // 渲染角色卡片
  const renderCharacterCard = (character: UserCharacter) => (
    <Col xs={24} sm={12} md={8} lg={6} key={character.id}>
      <Card
        className="character-card"
        cover={
          <div className="character-image-container">
            <img 
              alt={character.name} 
              src={character.imageUrl || '/placeholder-character.svg'}
              className="character-image"
            />
            <div className="character-overlay">
              <div className="character-actions">
                <Tooltip title="查看详情">
                  <Button 
                    icon={<EyeOutlined />} 
                    size="small"
                    onClick={() => handleViewCharacter(character.id)}
                  />
                </Tooltip>
                <Tooltip title="开始聊天">
                  <Button 
                    icon={<MessageOutlined />} 
                    size="small"
                    onClick={() => handleChatWithCharacter(character.id)}
                  />
                </Tooltip>
                <Tooltip title="编辑角色">
                  <Button 
                    icon={<EditOutlined />} 
                    size="small"
                    onClick={() => handleEditCharacter(character.id)}
                  />
                </Tooltip>
                <Tooltip title="删除角色">
                  <Button 
                    icon={<DeleteOutlined />} 
                    size="small"
                    danger
                    onClick={() => handleDeleteCharacter(character.id)}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        }
        actions={[
          <div key="stats" className="character-stats">
            <span><HeartOutlined /> {character.likes}</span>
            <span><MessageOutlined /> {character.chatCount}</span>
            <span><EyeOutlined /> {character.viewCount}</span>
          </div>
        ]}
      >
        <Card.Meta
          title={
            <div className="character-title">
              <span>{character.name}</span>
              <Switch
                size="small"
                checked={character.isPublic}
                onChange={(checked) => handleTogglePublic(character.id, checked)}
                checkedChildren="公开"
                unCheckedChildren="私有"
              />
            </div>
          }
          description={
            <div>
              <p className="character-personality">{character.personality}</p>
              <p className="character-identity">{character.identity}</p>
              {character.tags && (
                <div className="character-tags">
                  {character.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              )}
              <p className="character-date">创建于：{character.createdAt}</p>
            </div>
          }
        />
      </Card>
    </Col>
  );

  return (
    <Layout>
      <Content className="profile-page">
        <div className="profile-container">
          {loading ? (
            <div className="loading-container">
              <Spin size="large" tip="加载中..." />
            </div>
          ) : (
            <>
              {renderUserInfoCard()}
              
              <Card className="content-card">
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                  <TabPane tab="我的角色" key="characters">
                    <div className="characters-section">
                      <div className="section-header">
                        <h3>我创建的角色 ({userCharacters.length})</h3>
                        <Button 
                          type="primary" 
                          icon={<PlusOutlined />}
                          onClick={() => navigate('/character-creation')}
                        >
                          创建新角色
                        </Button>
                      </div>
                      
                      {userCharacters.length > 0 ? (
                        <Row gutter={[16, 16]}>
                          {userCharacters.map(renderCharacterCard)}
                        </Row>
                      ) : (
                        <Empty 
                          description="还没有创建任何角色"
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                        >
                          <Button 
                            type="primary" 
                            onClick={() => navigate('/character-creation')}
                          >
                            立即创建
                          </Button>
                        </Empty>
                      )}
                    </div>
                  </TabPane>
                  
                  <TabPane tab="设置" key="settings">
                    <div className="settings-section">
                      <Button 
                        icon={<SettingOutlined />}
                        onClick={() => navigate('/settings')}
                      >
                        前往设置页面
                      </Button>
                    </div>
                  </TabPane>
                </Tabs>
              </Card>
            </>
          )}
        </div>

        {/* 编辑资料模态框 */}
        <Modal
          title="编辑个人资料"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          onOk={() => editForm.submit()}
          okText="保存"
          cancelText="取消"
        >
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleSaveProfile}
            initialValues={userProfile}
          >
            <Form.Item
              label="用户名"
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input />
            </Form.Item>
            
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input />
            </Form.Item>
            
            <Form.Item
              label="个人简介"
              name="bio"
            >
              <Input.TextArea rows={4} placeholder="介绍一下自己..." />
            </Form.Item>
          </Form>
        </Modal>
      </Content>
    </Layout>
  );
};

export default ProfilePage;

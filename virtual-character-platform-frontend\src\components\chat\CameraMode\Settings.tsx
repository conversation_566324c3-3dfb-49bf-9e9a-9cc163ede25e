import React from 'react';
import { Drawer, Switch, Slider, Select, Divider } from 'antd';
import { useGlobalStore } from '../../../store/global';

interface SettingsProps {
  onClose: () => void;
}

const Settings: React.FC<SettingsProps> = ({ onClose }) => {
  const { 
    voiceOn, 
    setVoiceOn,
    interactive,
    setInteractive,
    userPreferences,
    updateUserPreferences
  } = useGlobalStore();

  return (
    <Drawer
      title="摄像头模式设置"
      placement="right"
      onClose={onClose}
      open={true}
      width={320}
    >
      <div className="camera-settings">
        <div className="setting-item">
          <label>语音功能</label>
          <Switch 
            checked={voiceOn} 
            onChange={setVoiceOn}
          />
        </div>

        <div className="setting-item">
          <label>交互模式</label>
          <Switch 
            checked={interactive} 
            onChange={setInteractive}
          />
        </div>

        <Divider />

        <div className="setting-item">
          <label>自动切换到摄像头模式</label>
          <Switch 
            checked={userPreferences.autoSwitchToCamera} 
            onChange={(checked) => updateUserPreferences({ autoSwitchToCamera: checked })}
          />
        </div>

        <div className="setting-item">
          <label>记住上次模式</label>
          <Switch 
            checked={userPreferences.rememberLastMode} 
            onChange={(checked) => updateUserPreferences({ rememberLastMode: checked })}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default Settings;

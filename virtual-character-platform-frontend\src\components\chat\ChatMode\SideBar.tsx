import React from 'react';
import { Drawer, <PERSON>, Avatar, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useSessionStore, sessionSelectors } from '../../../store/session';
import { useAgentStore } from '../../../store/agent';

interface SideBarProps {
  onClose: () => void;
}

const SideBar: React.FC<SideBarProps> = ({ onClose }) => {
  const { sessionList, activeId, switchSession } = useSessionStore();
  const { localAgentList } = useAgentStore();

  const handleSessionSwitch = (agentId: string) => {
    switchSession(agentId);
    onClose();
  };

  return (
    <Drawer
      title="聊天会话"
      placement="left"
      onClose={onClose}
      open={true}
      width={280}
    >
      <div className="chat-sidebar">
        <Button 
          type="dashed" 
          icon={<PlusOutlined />} 
          block 
          style={{ marginBottom: 16 }}
        >
          新建会话
        </Button>

        <List
          dataSource={sessionList}
          renderItem={(session) => {
            const agent = localAgentList.find(a => a.agentId === session.agentId);
            const isActive = session.agentId === activeId;
            
            return (
              <List.Item
                className={`session-item ${isActive ? 'active' : ''}`}
                onClick={() => handleSessionSwitch(session.agentId)}
                style={{
                  cursor: 'pointer',
                  background: isActive ? 'var(--color-primary-bg)' : 'transparent',
                  borderRadius: 8,
                  margin: '4px 0',
                  padding: '8px 12px'
                }}
              >
                <List.Item.Meta
                  avatar={<Avatar src={agent?.meta?.avatar}>{agent?.meta?.name?.[0]}</Avatar>}
                  title={agent?.meta?.name || '未知角色'}
                  description={`${session.messages.length} 条消息`}
                />
              </List.Item>
            );
          }}
        />
      </div>
    </Drawer>
  );
};

export default SideBar;

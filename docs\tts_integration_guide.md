# TTS语音合成集成指南

## 📋 概述

本文档详细说明了如何在虚拟角色平台中集成TTS（Text-to-Speech）语音合成功能。

## 🎯 TTS接入方案对比

### 方案一：API调用方式 ⭐⭐⭐⭐⭐ (推荐)

**优势：**
- 快速集成，与现有星火API集成模式一致
- 维护简单，无需管理模型文件和推理环境
- 扩展性好，可以同时接入多个TTS服务商
- 成本可控，按使用量付费

**推荐的TTS API服务商：**
1. **科大讯飞TTS** - 语音质量最佳，中文支持优秀
2. **阿里云智能语音** - 性价比高，集成简单
3. **腾讯云语音合成** - 稳定性好，多音色选择
4. **百度智能云TTS** - 免费额度较高

### 方案二：本地模型集成 ⭐⭐⭐

**优势：**
- 数据安全，语音数据不出本地
- 长期成本低，无API调用费用
- 响应速度快，无网络延迟

**限制：**
- 硬件要求高，需要GPU加速
- 部署复杂，需要配置推理环境
- 维护成本高，模型更新、环境维护

## 🚀 实现架构

```
前端聊天页面 → Django API → TTS服务层 → 外部TTS API → OSS存储 → 返回音频URL
```

## 📁 文件结构

```
backend-services/services/
├── tts_service.py          # TTS服务主文件
├── tts_providers/          # TTS服务商实现
│   ├── xunfei_tts.py      # 讯飞TTS
│   ├── aliyun_tts.py      # 阿里云TTS
│   └── baidu_tts.py       # 百度TTS
└── audio_utils.py          # 音频处理工具

core/
├── views.py               # 扩展聊天API支持TTS
└── models.py              # 角色模型已支持语音设置

frontend/src/
├── pages/StandaloneChatPage.tsx  # 聊天页面TTS集成
└── services/characterAPI.ts     # API接口更新
```

## ⚙️ 配置说明

### 环境变量配置

复制 `.env.tts.example` 为 `.env` 并配置：

```bash
# TTS默认服务商
TTS_DEFAULT_PROVIDER=xunfei

# 讯飞TTS配置
XUNFEI_TTS_APP_ID=your_app_id
XUNFEI_TTS_API_KEY=your_api_key
XUNFEI_TTS_API_SECRET=your_api_secret
```

### 角色语音设置

角色的 `settings` 字段可以包含语音配置：

```json
{
  "voice_type": "female_sweet",
  "voice_speed": "normal",
  "auto_play": true
}
```

## 🔧 使用方式

### 1. 智能音色推荐

系统会根据角色特征自动推荐合适的音色：

```python
# 根据角色特征推荐音色
POST /api/tts/recommend/
{
  "gender": "female",
  "age": 20,
  "personality": "活泼可爱的高中生"
}

# 响应
{
  "recommended_voice": {
    "id": "female_cute",
    "name": "小燕",
    "description": "可爱女声"
  },
  "reason": "根据female性别、20岁年龄和'活泼可爱的高中生'性格特征推荐"
}
```

### 2. 音色管理API

```python
# 获取所有支持的音色
GET /api/tts/voices/

# 获取角色语音设置
GET /api/characters/{character_id}/voice-settings/

# 更新角色语音设置
PUT /api/characters/{character_id}/voice-settings/
{
  "voice_type": "female_sweet",
  "speed": "normal",
  "emotion": "happy",
  "volume": 70,
  "pitch": 55,
  "auto_play": true
}

# 预览音色效果
POST /api/tts/preview/
{
  "voice_type": "female_sweet",
  "speed": "normal",
  "emotion": "happy",
  "text": "你好，这是语音预览"
}
```

### 3. 聊天API集成

聊天API现在支持智能TTS：

```python
POST /api/characters/{character_id}/chat
{
  "user_message": "你好",
  "enable_tts": true
}
```

响应包含音频URL：

```json
{
  "character_response": "你好！很高兴见到你！",
  "audio_url": "https://your-oss.com/audio/xxx.mp3"
}
```

**智能特性：**
- 自动根据角色特征选择音色
- 根据回复内容智能调整情感
- 支持角色个性化语音设置

### 4. 前端集成

#### 聊天页面TTS功能
- 语音开关按钮控制TTS启用状态
- 自动播放角色回复的语音
- 手动播放按钮重复播放语音

#### 语音设置界面
- 多种音色选择器
- 实时语音预览
- 语速、音调、音量调节
- 情感表达设置

## 🎵 支持的多种音色

### 女声音色系列

| 音色ID | 角色名 | 特点描述 | 适用年龄 | 适用场景 |
|--------|--------|----------|----------|----------|
| `female_sweet` | 小娟 | 甜美女声 | 青年 | 温柔、可爱角色 |
| `female_cute` | 小燕 | 可爱女声 | 青年 | 活泼、俏皮角色 |
| `female_gentle` | 小美 | 温柔女声 | 成年 | 知性、优雅角色 |
| `female_mature` | 小丽 | 成熟女声 | 成熟 | 职场、权威角色 |
| `female_lively` | 小欣 | 活泼女声 | 青年 | 阳光、开朗角色 |
| `female_elegant` | 小雅 | 优雅女声 | 成年 | 高贵、典雅角色 |

### 男声音色系列

| 音色ID | 角色名 | 特点描述 | 适用年龄 | 适用场景 |
|--------|--------|----------|----------|----------|
| `male_warm` | 小峰 | 温暖男声 | 成年 | 友善、亲和角色 |
| `male_mature` | 小明 | 成熟男声 | 成熟 | 稳重、权威角色 |
| `male_young` | 小浩 | 青春男声 | 青年 | 阳光、活力角色 |
| `male_deep` | 小刚 | 低沉男声 | 成熟 | 神秘、深沉角色 |
| `male_gentle` | 小军 | 温和男声 | 成年 | 温文尔雅角色 |
| `male_energetic` | 小伟 | 活力男声 | 青年 | 热情、积极角色 |

### 特殊音色系列

| 音色ID | 角色名 | 特点描述 | 适用年龄 | 适用场景 |
|--------|--------|----------|----------|----------|
| `child_boy` | 小童 | 男童声 | 儿童 | 小男孩角色 |
| `child_girl` | 小妮 | 女童声 | 儿童 | 小女孩角色 |
| `elderly_male` | 老张 | 老年男声 | 老年 | 长者、智者角色 |
| `elderly_female` | 老李 | 老年女声 | 老年 | 慈祥、睿智角色 |

### 语速设置

- `very_slow` - 很慢（适合朗读、教学）
- `slow` - 慢速（适合温柔、深沉角色）
- `normal` - 正常（通用设置）
- `fast` - 快速（适合活泼、急躁角色）
- `very_fast` - 很快（适合兴奋、紧张场景）

### 情感表达

- `neutral` - 中性（默认情感）
- `happy` - 开心（检测到积极词汇时）
- `sad` - 悲伤（检测到消极词汇时）
- `angry` - 愤怒（检测到愤怒词汇时）
- `surprised` - 惊讶（检测到惊讶词汇时）

## 🚧 当前限制

1. **TTS服务实现**：目前只有框架代码，需要根据选择的服务商实现具体的API调用
2. **音频存储**：需要配置OSS或其他云存储服务
3. **错误处理**：TTS失败时的降级策略需要完善
4. **缓存机制**：相同文本的音频可以缓存避免重复生成

## 📝 下一步开发任务

1. **选择TTS服务商**并实现具体的API调用逻辑
2. **配置音频存储**服务（OSS/七牛云等）
3. **完善错误处理**和重试机制
4. **添加音频缓存**机制提高性能
5. **优化前端音频播放**体验
6. **添加语音设置页面**让用户自定义语音偏好

## 🔍 测试建议

1. 先使用免费额度较高的服务商进行测试
2. 测试不同语音类型和语速的效果
3. 测试网络异常时的降级处理
4. 测试音频播放的兼容性

## 💡 优化建议

1. **异步处理**：TTS生成可以异步处理，避免阻塞聊天响应
2. **智能缓存**：对常用回复进行预生成和缓存
3. **语音个性化**：根据角色设定选择合适的语音类型
4. **流式播放**：支持音频流式播放，减少等待时间

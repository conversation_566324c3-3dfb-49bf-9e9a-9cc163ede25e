import React, { useState, useEffect } from 'react';
import { Drawer, Input, Collapse, List, Avatar, Button, Space, Popconfirm, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';

// 导入适配器和store
import { 
  useGlobalStoreAdapter, 
  useAgentStoreAdapter, 
  useSessionStoreAdapter,
  useTranslationAdapter,
  useResponsiveAdapter,
  useRouterAdapter
} from '../../adapters/roleAdapter';

import { useAgentStore, agentSelectors } from '../../store/agent';
import { GenderEnum } from '../../types/agent';

const { Search } = Input;

interface AdaptedRoleSideBarProps {
  visible?: boolean;
  onClose?: () => void;
}

const AdaptedRoleSideBar: React.FC<AdaptedRoleSideBarProps> = ({ 
  visible = true, 
  onClose 
}) => {
  const [searchName, setSearchName] = useState('');
  const { t } = useTranslationAdapter('role');
  const { md = true } = useResponsiveAdapter();
  const router = useRouterAdapter();
  
  // 使用现有的store
  const {
    localAgentList,
    currentIdentifier,
    activateAgent,
    removeLocalAgent,
    createNewAgent
  } = useAgentStore();
  
  const { createSession } = useSessionStoreAdapter();

  // 过滤角色列表
  const filteredAgents = localAgentList.filter(agent =>
    agent.meta.name.toLowerCase().includes(searchName.toLowerCase()) ||
    (agent.meta.description || '').toLowerCase().includes(searchName.toLowerCase())
  );

  // 创建新角色
  const handleCreateNew = () => {
    const newAgent = createNewAgent(GenderEnum.FEMALE);
    activateAgent(newAgent.agentId);
    message.success('已创建新角色');
  };

  // 激活角色
  const handleActivateAgent = (agentId: string) => {
    activateAgent(agentId);
    message.success('已切换角色');
  };

  // 开始聊天
  const handleStartChat = (agent: any) => {
    createSession(agent);
    router.push('/chat');
    message.success('已进入聊天模式');
  };

  // 删除角色
  const handleDeleteAgent = async (agentId: string) => {
    try {
      await removeLocalAgent(agentId);
      message.success('角色删除成功');
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error('删除角色失败');
    }
  };

  const sidebarContent = (
    <div className="adapted-role-sidebar">
      <Flexbox style={{ padding: '0 8px 0' }} gap={8}>
        <Search
          placeholder={t('search')}
          value={searchName}
          onChange={(e) => setSearchName(e.target.value)}
          allowClear
        />
      </Flexbox>
      
      <div className="role-list-content">
        {/* 创建新角色按钮 */}
        <div style={{ padding: '8px' }}>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            block
            onClick={handleCreateNew}
          >
            创建新角色
          </Button>
        </div>

        {/* 角色列表 */}
        <Collapse
          bordered={false}
          defaultActiveKey={'default'}
          ghost
          size="small"
          items={[
            {
              key: 'default',
              label: t('roleList'),
              children: (
                <List
                  dataSource={filteredAgents}
                  renderItem={(agent) => {
                    const isActive = agent.agentId === currentIdentifier;
                    
                    return (
                      <List.Item
                        className={`role-item ${isActive ? 'active' : ''}`}
                        style={{
                          cursor: 'pointer',
                          background: isActive ? 'var(--color-primary-bg)' : 'transparent',
                          borderRadius: 8,
                          margin: '4px 0',
                          padding: '8px 12px',
                          border: isActive ? '1px solid var(--color-primary)' : '1px solid transparent'
                        }}
                        onClick={() => handleActivateAgent(agent.agentId)}
                      >
                        <List.Item.Meta
                          avatar={
                            <Avatar src={agent.meta.avatar} size={32}>
                              {agent.meta.name?.[0]}
                            </Avatar>
                          }
                          title={
                            <div style={{ 
                              fontWeight: isActive ? 'bold' : 'normal',
                              color: isActive ? 'var(--color-primary)' : 'inherit'
                            }}>
                              {agent.meta.name}
                            </div>
                          }
                          description={
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {agent.meta.description || '暂无描述'}
                            </div>
                          }
                        />
                        
                        <div className="role-actions">
                          <Space size="small">
                            <Button
                              type="text"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartChat(agent);
                              }}
                              title={t('startChat')}
                            >
                              💬
                            </Button>
                            
                            <Popconfirm
                              title={t('delAlert')}
                              onConfirm={(e) => {
                                e?.stopPropagation();
                                handleDeleteAgent(agent.agentId);
                              }}
                              okText={t('delete')}
                              cancelText={t('cancel')}
                            >
                              <Button
                                type="text"
                                size="small"
                                danger
                                onClick={(e) => e.stopPropagation()}
                                title={t('delRole')}
                              >
                                🗑️
                              </Button>
                            </Popconfirm>
                          </Space>
                        </div>
                      </List.Item>
                    );
                  }}
                />
              ),
            },
          ]}
        />

        {filteredAgents.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            color: '#999', 
            padding: '40px 20px' 
          }}>
            {searchName ? '没有找到匹配的角色' : t('noRole')}
          </div>
        )}
      </div>
    </div>
  );

  // 如果是移动端，使用Drawer
  if (!md) {
    return (
      <Drawer
        title="角色管理"
        placement="left"
        onClose={onClose}
        open={visible}
        width={280}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  // 桌面端直接显示
  return (
    <div 
      className="adapted-role-sidebar-desktop"
      style={{
        width: 280,
        height: '100%',
        background: 'var(--color-bg-container)',
        borderRight: '1px solid var(--color-border)',
        display: visible ? 'block' : 'none'
      }}
    >
      {sidebarContent}
    </div>
  );
};

export default AdaptedRoleSideBar;
